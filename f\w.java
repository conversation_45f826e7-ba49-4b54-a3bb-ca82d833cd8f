package f;

import android.app.Notification.Builder;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.os.Build.VERSION;
import android.provider.Settings.System;

public abstract class w implements l {
    static class a {
    }

    static class b extends w {
        private int E;
        private int F;

        private b() {
            super(null);
            this.E = 0;
            this.F = w.B;
        }

        b(a w$a0) {
        }

        @Override  // f.w
        public Notification.Builder b(Context context0) {
            return new Notification.Builder(context0).setDefaults(this.E).setPriority(this.F);
        }

        @Override  // f.w
        public w c(int v) {
            this.E = v;
            return this;
        }

        @Override  // f.w
        public w d(int v) {
            this.F = v;
            return this;
        }
    }

    static class c extends w {
        private final NotificationChannel E;
        private boolean F;

        c(String s, CharSequence charSequence0) {
            super(null);
            this.F = false;
            NotificationChannel notificationChannel0 = new NotificationChannel(s, charSequence0, w.B);
            this.E = notificationChannel0;
            notificationChannel0.setShowBadge(false);
            if(Build.VERSION.SDK_INT >= 29) {
                notificationChannel0.setAllowBubbles(false);
            }
        }

        @Override  // f.w
        public Notification.Builder b(Context context0) {
            if(!this.F) {
                ((NotificationManager)context0.getSystemService("notification")).createNotificationChannel(this.E);
                this.F = true;
            }
            return new Notification.Builder(context0, this.E.getId());
        }

        @Override  // f.w
        public w c(int v) {
            boolean z = false;
            this.E.enableLights((v & 4) != 0);
            NotificationChannel notificationChannel0 = this.E;
            if((v & 2) != 0) {
                z = true;
            }
            notificationChannel0.enableVibration(z);
            this.E.setSound(((v & 1) == 0 ? null : Settings.System.DEFAULT_NOTIFICATION_URI), Notification.AUDIO_ATTRIBUTES_DEFAULT);
            return this;
        }

        @Override  // f.w
        public w d(int v) {
            this.E.setImportance(v);
            return this;
        }
    }

    public static final int A;
    public static final int B;
    public static final int C;
    public static final int D;
    public static final int z;

    static {
        if(Build.VERSION.SDK_INT >= 26) {
            w.z = 1;
            w.A = 2;
            w.B = 3;
            w.C = 4;
            w.D = 5;
            return;
        }
        w.z = -2;
        w.A = -1;
        w.B = 0;
        w.C = 1;
        w.D = 2;
    }

    private w() {
    }

    w(a w$a0) {
    }

    public static w a(String s, CharSequence charSequence0) {
        return Build.VERSION.SDK_INT >= 26 ? new c(s, charSequence0) : new b(null);
    }

    public abstract Notification.Builder b(Context arg1);

    public abstract w c(int arg1);

    public abstract w d(int arg1);
}


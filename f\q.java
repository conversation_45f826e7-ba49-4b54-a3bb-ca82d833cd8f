package f;

import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable.ConstantState;
import android.graphics.drawable.Drawable;
import android.os.SystemClock;
import g.g;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;

public abstract class q {
    public static class a {
        public q a(String s) {
            return new b(s);
        }
    }

    public static class b extends q {
        private final File E;

        public b(File file0) {
            this.E = file0;
        }

        public b(String s) {
            this.E = new File(s);
        }

        @Override  // f.q
        public boolean d() {
            return this.E.exists();
        }

        @Override  // f.q
        public long f() {
            return this.E.lastModified();
        }

        @Override  // f.q
        public byte[] j() {
            int v = (int)this.E.length();
            if(v == 0) {
                return q.z;
            }
            byte[] arr_b = new byte[v];
            try(FileInputStream fileInputStream0 = new FileInputStream(this.E)) {
                if(fileInputStream0.read(arr_b, 0, v) != v) {
                    v.d(new IOException("Error read: " + this.E.getPath()));
                }
                return arr_b;
            }
            catch(Throwable throwable0) {
                q.i(throwable0);
                return null;
            }
        }
    }

    private static a A;
    private static long B;
    private static final Hashtable C;
    private static final Hashtable D;
    public static final byte[] z;

    static {
        q.z = new byte[0];
        q.A = new a();
        q.B = 0L;
        q.C = new Hashtable();
        q.D = new Hashtable();
    }

    public static void b() {
        q.C.clear();
        q.D.clear();
    }

    public static q c(String s) {
        return q.A.a(s);
    }

    public abstract boolean d();

    public static a e() {
        return q.A;
    }

    public abstract long f();

    public static List g(String s) {
        List list0 = (List)q.D.get(s);
        if(list0 != null) {
            return list0;
        }
        byte[] arr_b = q.c(s).j();
        if(arr_b == null) {
            return null;
        }
        List list1 = new ArrayList();
        h h0 = new h();
        h0.i(arr_b);
        for(g.q q0 = new g.q(h0); true; q0 = new g.q(h0)) {
            list1.add(q0);
            if(!h0.t()) {
                break;
            }
        }
        h0.m();
        int v = list1.size();
        if(v != 0) {
            if(v != 1) {
                q.D.put(s, list1);
                return list1;
            }
            list1.clear();
        }
        return list1;
    }

    public static Drawable h(String s) {
        Hashtable hashtable0 = q.C;
        Drawable.ConstantState drawable$ConstantState0 = (Drawable.ConstantState)hashtable0.get(s);
        if(drawable$ConstantState0 != null) {
            return drawable$ConstantState0.newDrawable();
        }
        byte[] arr_b = q.c(s).j();
        if(arr_b == null) {
            return null;
        }
        Drawable drawable0 = BitmapDrawable.createFromStream(new ByteArrayInputStream(arr_b), s);
        if(drawable0 != null) {
            g g0 = new g(drawable0);
            Drawable.ConstantState drawable$ConstantState1 = g0.getConstantState();
            if(drawable$ConstantState1 != null) {
                hashtable0.put(s, drawable$ConstantState1);
            }
            return g0;
        }
        return null;
    }

    private static void i(Throwable throwable0) {
        String s1;
        long v = SystemClock.uptimeMillis();
        if(q.B == 0L || v - q.B > 4000L) {
            q.B = v;
            String s = throwable0.getMessage();
            if(s == null) {
                s1 = throwable0.toString();
            }
            else {
                if(s.contains("EACCES")) {
                    s1 = "Cannot access file!!!";
                    v.c(s1);
                    return;
                }
                v.c(s);
                return;
            }
            v.c(s1);
        }
    }

    public abstract byte[] j();

    public static void k(a q$a0) {
        if(q$a0 == null) {
            q$a0 = new a();
        }
        q.A = q$a0;
    }
}


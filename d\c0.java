package d;

import a.b.q;
import a.b.s0;
import a.p.b;
import a.p;
import a.z;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.CompoundButton;
import e.j.f;
import e.j.g;
import e.j.j;
import e.j.k;

public class c0 extends c implements b, a {
    private z M;
    private CharSequence[] N;

    public c0() {
        this.N = null;
    }

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F060116);  // string:gestures "Gestures"
    }

    @Override  // e.j
    protected int B0() {
        this.N = new CharSequence[]{this.u(0x7F060115), this.u(0x7F060105), this.u(0x7F06010A), this.u(0x7F060112), this.u(0x7F060113), this.u(0x7F060114), this.u(0x7F060111)};  // string:gesture_tap "Tap"
        this.M = this.g().h();
        return b.e.length;
    }

    @Override  // d.c
    protected void C1(Bundle bundle0, int v) {
        boolean z = true;
        switch(v) {
            case 1: {
                int v1 = bundle0.getInt("result");
                if(v1 <= 1) {
                    j j$j0 = (j)this.L0(this.E0());
                    if(v1 != 0) {
                        z = false;
                    }
                    j$j0.setChecked(z);
                    return;
                }
                break;
            }
            case 2: {
                Context context0 = this.M0();
                a.b b0 = (a.b)bundle0.getParcelable("result");
                if(b0 != null) {
                    int v2 = this.F0();
                    this.M.l0(context0, 0, b.e[this.I0()], v2, b0);
                    g j$g0 = (g)this.K0(v2);
                    j$g0.setSubText(b0.n(context0));
                    j$g0.setImageDrawable(this.D0(b0));
                    return;
                }
                break;
            }
        }
    }

    // 检测为 Lambda 实现
    private void J1(int v, CompoundButton compoundButton0, boolean z) [...]

    protected k K1(int v) {
        k j$k0;
        switch(b.e[v]) {
            case 0: 
            case 1: 
            case 2: 
            case 3: {
                j$k0 = new f(this, this.C0(b.f[v]), this.u(b.g[v]), this.u(0x7F06014C), this.M.o(b.h[v]));  // string:low_priority "Low priority"
                break;
            }
            default: {
                j$k0 = new f(this, this.C0(b.f[v]), this.u(b.g[v]), null, this.M.o(b.h[v]));
            }
        }
        ((j)j$k0).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> {
            Context context0 = compoundButton0.getContext();
            this.M.o0(context0, b.h[v], z);
            if(z) {
                s0.C(context0, 8, b.e[v]);
            }
        });
        return j$k0;
    }

    protected k L1(int v) {
        int v1 = b.e[this.I0()];
        Context context0 = this.M0();
        a.b b0 = this.M.i(v1, v);
        k j$k0 = new g(this, this.N[v], b0.n(context0), this.D0(b0));
        if(p.E(v1, v)) {
            q b$q0 = a.b.e();
            j$k0 = new g(this, this.N[v], b$q0.n(context0), this.D0(b$q0));
            j$k0.setOnTouchListener(e.j.L);
            j$k0.e();
        }
        return j$k0;
    }

    @Override  // e.j
    protected boolean N0(int v) {
        return true;
    }

    @Override  // e.j
    protected View h1(int v) {
        return this.K1(v);
    }

    @Override  // e.j
    protected View i1(int v) {
        return this.L1(v);
    }

    @Override  // e.j
    protected void k1(int v) {
        this.y1(v, this.N.length);
    }

    @Override  // e.j
    protected boolean l1(int v) {
        this.N(new e.z().v(new CharSequence[]{this.u(0x7F0600F4), this.u(0x7F0600D0)}, !this.M.o(b.h[v])), 1);  // string:enable "Enable"
        return true;
    }

    @Override  // e.j
    protected void o1(int v) {
        int v1 = this.I0();
        if(p.E(b.e[v1], v)) {
            return;
        }
        this.P(new d().I1((v == 0 ? 1 : 2), this.u(b.g[v1]), this.N[v]), 2);
    }

    @Override  // e.j
    protected boolean p1(int v) {
        this.F1(this.M.i(b.e[this.I0()], v), 2, (v == 0 ? 1 : 2));
        this.H1(this.u(b.g[this.I0()]) + "/" + this.N[v]);
        return true;
    }
}


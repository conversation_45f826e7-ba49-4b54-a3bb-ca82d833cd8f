package com.jozein.xedgepro.xposed;

import a.b.s0;
import android.content.Context;
import android.os.Build.VERSION;
import android.os.Handler;
import android.view.MotionEvent;
import android.view.View;
import android.view.accessibility.AccessibilityManager;
import de.robv.android.xposed.XC_MethodHook.MethodHookParam;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import f.g0;
import f.h0;
import f.l;
import f.v;

class l2 extends p2 {
    static final float E;

    static {
        l2.E = l.r ? 0.000001f : 0.000001f;
    }

    l2(ClassLoader classLoader0) {
        class a extends XC_MethodHook {
            final l2 a;

            View a(Object object0) {
                return (View)XposedHelpers.getObjectField(XposedHelpers.getSurroundingThis(object0), "mView");
            }

            protected void beforeHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                boolean z;
                Object[] arr_object = xC_MethodHook$MethodHookParam0.args;
                if(arr_object[0] instanceof MotionEvent) {
                    MotionEvent motionEvent0 = (MotionEvent)arr_object[0];
                    if(motionEvent0.getActionMasked() != 8 || motionEvent0.getAxisValue(9) != l2.E) {
                        z = false;
                    }
                    else {
                        try {
                            View view0 = this.a(xC_MethodHook$MethodHookParam0.thisObject);
                            if(view0 != null) {
                                l2.this.l(view0, motionEvent0);
                            }
                        }
                        catch(Throwable unused_ex) {
                        }
                        z = true;
                    }
                }
                else {
                    z = false;
                }
                if(z) {
                    XposedHelpers.callMethod(xC_MethodHook$MethodHookParam0.thisObject, "finishInputEvent", new Object[]{xC_MethodHook$MethodHookParam0.args[0], Boolean.TRUE});
                    xC_MethodHook$MethodHookParam0.setResult(null);
                }
            }
        }

        try {
            XposedBridge.hookAllMethods(XposedHelpers.findClass("android.view.ViewRootImpl$WindowInputEventReceiver", classLoader0), "onInputEvent", new a(this));
        }
        catch(Throwable throwable0) {
            p2.g(throwable0);
        }
    }

    private void l(View view0, MotionEvent motionEvent0) {
        view0.post(() -> {
            class b extends g0 {
                final com.jozein.xedgepro.xposed.q3.a D;
                final int E;
                final int F;
                final Context G;
                final l2 H;

                b(Handler handler0, int v, long v1, com.jozein.xedgepro.xposed.q3.a q3$a0, int v2, int v3, Context context0) {
                    this.D = q3$a0;
                    this.E = v2;
                    this.F = v3;
                    this.G = context0;
                    super(handler0, v, v1);
                }

                @Override  // f.g0
                protected boolean a(int v) {
                    if(!this.D.C.isAttachedToWindow()) {
                        return false;
                    }
                    com.jozein.xedgepro.xposed.q3.a q3$a0 = q3.g(this.D, this.E, this.F);
                    if(q3$a0 != null) {
                        if(q3$a0.C != null) {
                            return true;
                        }
                        l2.this.o(this.G, q3$a0.A);
                        return false;
                    }
                    l2.this.o(this.G, null);
                    return false;
                }

                @Override  // f.g0
                protected void b() {
                    l2.m(this.G, false);
                }
            }

            int[] arr_v = new int[2];
            view0.getLocationOnScreen(arr_v);
            int v2 = ((int)motionEvent0.getRawX()) - arr_v[0];
            int v3 = ((int)motionEvent0.getRawY()) - arr_v[1];
            Context context0 = view0.getContext();
            com.jozein.xedgepro.xposed.q3.a q3$a0 = q3.e(view0, v2, v3);
            if(q3$a0 != null) {
                CharSequence charSequence0 = q3$a0.A;
                if(charSequence0 != null) {
                    this.o(context0, charSequence0);
                    return;
                }
                if(q3$a0.C != null) {
                    AccessibilityManager accessibilityManager0 = (AccessibilityManager)context0.getSystemService("accessibility");
                    if(accessibilityManager0 == null || !accessibilityManager0.isEnabled()) {
                        goto label_18;
                    }
                    try {
                        com.jozein.xedgepro.xposed.q3.a q3$a1 = q3.g(q3$a0, ((int)motionEvent0.getRawX()), ((int)motionEvent0.getRawY()));
                        if(q3$a1 != null) {
                            this.o(context0, q3$a1.A);
                            return;
                        label_18:
                            l2.m(context0, true);
                            new b(this, h0.d(), 3, 150L, q3$a0, ((int)motionEvent0.getRawX()), ((int)motionEvent0.getRawY()), context0).e();
                            return;
                        }
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }
            this.o(context0, null);
        });
    }

    private static void m(Context context0, boolean z) {
        AccessibilityManager accessibilityManager0 = (AccessibilityManager)context0.getSystemService("accessibility");
        if(accessibilityManager0.isEnabled() == z) {
            return;
        }
        int v = Build.VERSION.SDK_INT;
        if(v >= 26) {
            XposedHelpers.setBooleanField(accessibilityManager0, "mIsEnabled", z);
            XposedHelpers.callMethod(accessibilityManager0, "notifyAccessibilityStateChanged", new Object[0]);
            return;
        }
        if(v >= 21) {
            XposedHelpers.setBooleanField(accessibilityManager0, "mIsEnabled", z);
            XposedHelpers.callMethod(accessibilityManager0, "handleNotifyAccessibilityStateChanged", new Object[0]);
            return;
        }
        synchronized(XposedHelpers.getObjectField(accessibilityManager0, "mHandler")) {
            XposedHelpers.setBooleanField(accessibilityManager0, "mIsEnabled", z);
            XposedHelpers.callMethod(accessibilityManager0, "notifyAccessibilityStateChangedLh", new Object[0]);
        }
    }

    // 检测为 Lambda 实现
    private void n(View view0, int v, int v1) [...]

    private void o(Context context0, CharSequence charSequence0) {
        s0.G(context0, 20, q3.c(charSequence0));
    }
}


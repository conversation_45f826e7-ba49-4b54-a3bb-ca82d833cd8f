package com.jozein.xedgepro.xposed;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.view.MotionEvent;
import android.view.View.OnClickListener;
import android.view.View.OnTouchListener;
import android.view.View;
import android.widget.FrameLayout.LayoutParams;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout.LayoutParams;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import f.v;
import g.a0;
import g.h;
import g.r;
import g.x;

abstract class r2 extends FrameLayout implements c {
    abstract class b extends FrameLayout implements View.OnClickListener {
        final r2 A;
        public final ImageView z;

        public b(Context context0, Drawable drawable0) {
            super(context0);
            ImageView imageView0 = new ImageView(context0);
            this.z = imageView0;
            imageView0.setImageDrawable(drawable0);
            this.addView(imageView0, r20.B);
            this.setBackground(a0.l());
            this.setOnClickListener(this);
        }
    }

    abstract class com.jozein.xedgepro.xposed.r2.c extends ScrollView {
        final r2 z;

        public com.jozein.xedgepro.xposed.r2.c(Context context0, Drawable[] arr_drawable) {
            class a extends b {
                final r2 B;
                final int C;
                final com.jozein.xedgepro.xposed.r2.c D;

                a(Context context0, Drawable drawable0, r2 r20, int v) {
                    this.B = r20;
                    this.C = v;
                    super(context0, drawable0);
                }

                @Override  // android.view.View$OnClickListener
                public void onClick(View view0) {
                    try {
                        com.jozein.xedgepro.xposed.r2.c.this.a(((b)view0), this.C);
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }

            super(context0);
            LinearLayout linearLayout0 = new LinearLayout(context0);
            linearLayout0.setOrientation(1);
            for(int v = 0; v < arr_drawable.length; ++v) {
                linearLayout0.addView(new a(this, context0, arr_drawable[v], r20, v), r20.A);
            }
            this.addView(linearLayout0);
            r20.w(this);
        }

        protected abstract void a(b arg1, int arg2);

        public void b() {
            this.setVisibility((this.getVisibility() == 0 ? 4 : 0));
        }
    }

    class d extends LinearLayout implements Runnable {
        private final b A;
        private final b B;
        private final b C;
        private final b D;
        private com.jozein.xedgepro.xposed.r2.c E;
        private com.jozein.xedgepro.xposed.r2.c F;
        private com.jozein.xedgepro.xposed.r2.c G;
        private boolean H;
        private boolean I;
        private int J;
        final r2 K;
        private final b z;

        public d(Context context0) {
            class com.jozein.xedgepro.xposed.r2.d.a extends b {
                final r2 B;
                final d C;

                com.jozein.xedgepro.xposed.r2.d.a(Context context0, Drawable drawable0, r2 r20) {
                    this.B = r20;
                    super(context0, drawable0);
                }

                @Override  // android.view.View$OnClickListener
                public void onClick(View view0) {
                    try {
                        r2.this.F.m();
                        d.this.m();
                        d.this.o();
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }


            class com.jozein.xedgepro.xposed.r2.d.b extends b {
                final r2 B;
                final d C;

                com.jozein.xedgepro.xposed.r2.d.b(Context context0, Drawable drawable0, r2 r20) {
                    this.B = r20;
                    super(context0, drawable0);
                }

                @Override  // android.view.View$OnClickListener
                public void onClick(View view0) {
                    try {
                        r2.this.F.k();
                        d.this.m();
                        d.this.o();
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }


            class com.jozein.xedgepro.xposed.r2.d.c extends b {
                final r2 B;
                final d C;

                com.jozein.xedgepro.xposed.r2.d.c(Context context0, Drawable drawable0, r2 r20) {
                    this.B = r20;
                    super(context0, drawable0);
                }

                @Override  // android.view.View$OnClickListener
                public void onClick(View view0) {
                    class com.jozein.xedgepro.xposed.r2.d.c.a extends com.jozein.xedgepro.xposed.r2.c {
                        final Drawable[] A;
                        final com.jozein.xedgepro.xposed.r2.d.c B;

                        com.jozein.xedgepro.xposed.r2.d.c.a(Context context0, Drawable[] arr_drawable, Drawable[] arr_drawable1) {
                            this.A = arr_drawable1;
                            super(context0, arr_drawable);
                        }

                        @Override  // com.jozein.xedgepro.xposed.r2$c
                        protected void a(b r2$b0, int v) {
                            r2.this.L = v;
                            d.this.B.z.setImageDrawable(this.A[r2.this.L]);
                            this.setVisibility(4);
                            r2.this.F.l(r2.N[r2.this.L], r2.this.M, r2.O[r2.this.L]);
                        }
                    }

                    try {
                        if(d.this.F != null) {
                            d.this.F.setVisibility(4);
                        }
                        if(d.this.G != null) {
                            d.this.G.setVisibility(4);
                        }
                        if(d.this.E == null) {
                            int[] arr_v = r2.N;
                            Drawable[] arr_drawable = new Drawable[arr_v.length];
                            for(int v = 0; v < arr_v.length; ++v) {
                                arr_drawable[v] = r2.this.u(r2.P[v]);
                            }
                            com.jozein.xedgepro.xposed.r2.d.c.a r2$d$c$a0 = new com.jozein.xedgepro.xposed.r2.d.c.a(this, view0.getContext(), arr_drawable, arr_drawable);
                            d.this.E = r2$d$c$a0;
                            r2.this.addView(d.this.E, r2.this.C);
                            return;
                        }
                        d.this.E.b();
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }


            class com.jozein.xedgepro.xposed.r2.d.d extends b {
                final r2 B;
                final d C;

                com.jozein.xedgepro.xposed.r2.d.d(Context context0, Drawable drawable0, r2 r20) {
                    this.B = r20;
                    super(context0, drawable0);
                }

                @Override  // android.view.View$OnClickListener
                public void onClick(View view0) {
                    class com.jozein.xedgepro.xposed.r2.d.d.a extends com.jozein.xedgepro.xposed.r2.c {
                        final com.jozein.xedgepro.xposed.r2.d.d A;

                        com.jozein.xedgepro.xposed.r2.d.d.a(Context context0, Drawable[] arr_drawable) {
                            super(context0, arr_drawable);
                        }

                        @Override  // com.jozein.xedgepro.xposed.r2$c
                        protected void a(b r2$b0, int v) {
                            r2.this.M = v;
                            Drawable drawable0 = d.this.n(r2.this.H[v]);
                            d.this.C.z.setImageDrawable(drawable0);
                            this.setVisibility(4);
                            r2.this.F.l(r2.N[r2.this.L], r2.this.M, r2.O[r2.this.L]);
                        }
                    }

                    try {
                        if(d.this.E != null) {
                            d.this.E.setVisibility(4);
                        }
                        if(d.this.G != null) {
                            d.this.G.setVisibility(4);
                        }
                        if(d.this.F == null) {
                            int[] arr_v = r2.this.H;
                            Drawable[] arr_drawable = new Drawable[arr_v.length];
                            for(int v = 0; v < arr_v.length; ++v) {
                                arr_drawable[v] = d.this.n(r2.this.H[v]);
                            }
                            com.jozein.xedgepro.xposed.r2.d.d.a r2$d$d$a0 = new com.jozein.xedgepro.xposed.r2.d.d.a(this, view0.getContext(), arr_drawable);
                            d.this.F = r2$d$d$a0;
                            r2.this.addView(d.this.F, r2.this.D);
                            return;
                        }
                        d.this.F.b();
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }


            class e extends b {
                final int[] B;
                boolean C;
                final r2 D;
                final d E;

                e(Context context0, Drawable drawable0, r2 r20) {
                    this.D = r20;
                    super(context0, drawable0);
                    this.B = Build.VERSION.SDK_INT < 24 ? new int[]{0x7F0400A0, 0x7F040076, 0x7F040075} : new int[]{0x7F0400A0, 0x7F0400A1, 0x7F040076, 0x7F040075};  // drawable:ic_screenshot
                    this.C = false;
                }

                @Override  // android.view.View$OnClickListener
                public void onClick(View view0) {
                    class com.jozein.xedgepro.xposed.r2.d.e.a extends com.jozein.xedgepro.xposed.r2.c {
                        final e A;

                        com.jozein.xedgepro.xposed.r2.d.e.a(Context context0, Drawable[] arr_drawable) {
                            super(context0, arr_drawable);
                        }

                        @Override  // com.jozein.xedgepro.xposed.r2$c
                        protected void a(b r2$b0, int v) {
                            d r2$d0;
                            try {
                                this.setVisibility(4);
                                e r2$d$e0 = e.this;
                                boolean z = true;
                                switch(r2$d$e0.B[v]) {
                                    case 0x7F040075: {  // drawable:ic_painter_close
                                        r2.this.r();
                                        return;
                                    }
                                    case 0x7F040076: {  // drawable:ic_painter_continue_gesture
                                        r2.this.x(r2$d$e0.C);
                                        e r2$d$e1 = e.this;
                                        if(r2$d$e1.C) {
                                            z = false;
                                        }
                                        r2$d$e1.C = z;
                                        Drawable drawable0 = r2.this.u((z ? 0x7F04007C : 0x7F040076));  // drawable:ic_painter_pause_gesture
                                        r2$b0.z.setImageDrawable(drawable0);
                                        return;
                                    }
                                    case 0x7F0400A0: {  // drawable:ic_screenshot
                                        d.this.l();
                                        this.removeCallbacks(d.this);
                                        d.this.J = 1;
                                        r2$d0 = d.this;
                                        break;
                                    }
                                    case 0x7F0400A1: {  // drawable:ic_screenshot_selected_region
                                        d.this.l();
                                        this.removeCallbacks(d.this);
                                        d.this.J = 2;
                                        r2$d0 = d.this;
                                        break;
                                    }
                                    default: {
                                        return;
                                    }
                                }
                                this.postDelayed(r2$d0, 500L);
                            }
                            catch(Throwable throwable0) {
                                v.d(throwable0);
                            }
                        }
                    }

                    try {
                        if(d.this.E != null) {
                            d.this.E.setVisibility(4);
                        }
                        if(d.this.F != null) {
                            d.this.F.setVisibility(4);
                        }
                        if(d.this.G == null) {
                            Drawable[] arr_drawable = new Drawable[this.B.length];
                            h h0 = h.a();
                            for(int v = 0; v < this.B.length; ++v) {
                                Drawable drawable0 = r2.this.u(this.B[v]);
                                h0.e(drawable0, r2.this.J);
                                arr_drawable[v] = drawable0;
                            }
                            com.jozein.xedgepro.xposed.r2.d.e.a r2$d$e$a0 = new com.jozein.xedgepro.xposed.r2.d.e.a(this, view0.getContext(), arr_drawable);
                            d.this.G = r2$d$e$a0;
                            r2.this.addView(d.this.G, r2.this.E);
                            return;
                        }
                        d.this.G.b();
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }


            class f extends b {
                final r2 B;
                final d C;

                f(Context context0, Drawable drawable0, r2 r20) {
                    this.B = r20;
                    super(context0, drawable0);
                }

                @Override  // android.view.View$OnClickListener
                public void onClick(View view0) {
                    try {
                        d.this.l();
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }

            super(context0);
            this.E = null;
            this.F = null;
            this.G = null;
            this.H = false;
            this.I = false;
            this.J = 0;
            this.setOrientation(0);
            com.jozein.xedgepro.xposed.r2.d.a r2$d$a0 = new com.jozein.xedgepro.xposed.r2.d.a(this, context0, r20.t(0x7F040080), r20);  // drawable:ic_painter_undo
            this.z = r2$d$a0;
            this.addView(r2$d$a0, r20.A);
            com.jozein.xedgepro.xposed.r2.d.b r2$d$b0 = new com.jozein.xedgepro.xposed.r2.d.b(this, context0, r20.t(0x7F04007F), r20);  // drawable:ic_painter_redo
            this.A = r2$d$b0;
            this.addView(r2$d$b0, r20.A);
            com.jozein.xedgepro.xposed.r2.d.c r2$d$c0 = new com.jozein.xedgepro.xposed.r2.d.c(this, context0, r20.u(r2.P[r20.L]), r20);
            this.B = r2$d$c0;
            this.addView(r2$d$c0, r20.A);
            com.jozein.xedgepro.xposed.r2.d.d r2$d$d0 = new com.jozein.xedgepro.xposed.r2.d.d(this, context0, this.n(r20.H[r20.M]), r20);
            this.C = r2$d$d0;
            this.addView(r2$d$d0, r20.A);
            e r2$d$e0 = new e(this, context0, r20.u(0x7F04007B), r20);  // drawable:ic_painter_more
            this.D = r2$d$e0;
            this.addView(r2$d$e0, r20.A);
            this.addView(new f(this, context0, r20.u(0x7F040079), r20), r20.A);  // drawable:ic_painter_hide
            r20.w(this);
        }

        public void l() {
            this.m();
            this.setVisibility(4);
        }

        private void m() {
            com.jozein.xedgepro.xposed.r2.c r2$c0 = this.E;
            if(r2$c0 != null) {
                r2$c0.setVisibility(4);
            }
            com.jozein.xedgepro.xposed.r2.c r2$c1 = this.F;
            if(r2$c1 != null) {
                r2$c1.setVisibility(4);
            }
            com.jozein.xedgepro.xposed.r2.c r2$c2 = this.G;
            if(r2$c2 != null) {
                r2$c2.setVisibility(4);
            }
        }

        private Drawable n(int v) {
            return v != 0 ? new ColorDrawable(v) : r2.this.u(0x7F040077);  // drawable:ic_painter_eraser
        }

        public void o() {
            h h0 = h.a();
            boolean z = r2.this.F.h();
            if(z != this.H) {
                this.H = z;
                if(z) {
                    h0.e(this.z.z.getDrawable(), r2.this.J);
                }
                else {
                    h0.g(this.z.z.getDrawable());
                }
            }
            boolean z1 = r2.this.F.g();
            if(z1 != this.I) {
                this.I = z1;
                if(z1) {
                    h0.e(this.A.z.getDrawable(), r2.this.J);
                    return;
                }
                h0.g(this.A.z.getDrawable());
            }
        }

        @Override
        public void run() {
            int v = this.J;
            this.J = 0;
            if(v == 1 || v == 2) {
                try {
                    r2.this.v(v);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }
    }

    private final LinearLayout.LayoutParams A;
    private final FrameLayout.LayoutParams B;
    private final FrameLayout.LayoutParams C;
    private final FrameLayout.LayoutParams D;
    private final FrameLayout.LayoutParams E;
    private final r F;
    private final d G;
    private final int[] H;
    private final int I;
    private final int J;
    private final h K;
    private int L;
    private int M;
    private static final int[] N;
    private static final boolean[] O;
    private static final int[] P;
    private final Resources z;

    static {
        r2.N = new int[]{2, 3, 4, 4, 5, 5};
        r2.O = new boolean[]{false, false, false, true, false, true};
        r2.P = new int[]{0x7F040078, 0x7F04007A, 0x7F04007D, 0x7F04007E, 0x7F040073, 0x7F040074};  // drawable:ic_painter_gesture
    }

    public r2(Context context0, Resources resources0, int[] arr_v, int v, boolean z) {
        class com.jozein.xedgepro.xposed.r2.a implements View.OnTouchListener {
            final r2 A;
            int z;

            com.jozein.xedgepro.xposed.r2.a() {
                this.z = 0;
            }

            @Override  // android.view.View$OnTouchListener
            public boolean onTouch(View view0, MotionEvent motionEvent0) {
                switch(motionEvent0.getActionMasked()) {
                    case 0: {
                        this.z = 0;
                        r2.this.G.l();
                        return false;
                    }
                    case 5: {
                        if(this.z < 2) {
                            int v = motionEvent0.getPointerCount();
                            if(v > this.z) {
                                this.z = v;
                            }
                            if(this.z == 2) {
                                r2.this.G.o();
                                r2.this.G.setVisibility(0);
                                return false;
                            }
                        }
                        return false;
                    }
                    default: {
                        return false;
                    }
                }
            }
        }

        super(context0);
        this.K = h.a();
        this.L = 0;
        this.M = 0;
        this.z = resources0;
        this.I = v;
        this.J = a0.r(v) ? 0 : 0xFF000000;
        this.H = arr_v == null || arr_v.length == 0 ? new int[]{0xFFFFA000} : arr_v;
        x x0 = x.b(context0);
        int v1 = (int)(((float)x0.a) * 0.7f);
        int v2 = (int)(((float)x0.b) * 0.7f);
        this.A = new LinearLayout.LayoutParams(v1, v1);
        this.B = new FrameLayout.LayoutParams(v2, v2, 17);
        r r0 = new r(context0, this.H);
        this.F = r0;
        r0.l(r2.N[0], 0, r2.O[0]);
        r0.setOnTouchListener(new com.jozein.xedgepro.xposed.r2.a(this));
        this.addView(r0);
        d r2$d0 = new d(this, context0);
        this.G = r2$d0;
        FrameLayout.LayoutParams frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(-2, -2, 81);
        frameLayout$LayoutParams0.bottomMargin = x0.g;
        this.addView(r2$d0, frameLayout$LayoutParams0);
        if(z) {
            r2$d0.l();
        }
        FrameLayout.LayoutParams frameLayout$LayoutParams1 = new FrameLayout.LayoutParams(-2, -2, 81);
        this.C = frameLayout$LayoutParams1;
        frameLayout$LayoutParams1.bottomMargin = frameLayout$LayoutParams0.bottomMargin + v1;
        frameLayout$LayoutParams1.rightMargin = v1 / 2;
        FrameLayout.LayoutParams frameLayout$LayoutParams2 = new FrameLayout.LayoutParams(-2, -2, 81);
        this.D = frameLayout$LayoutParams2;
        frameLayout$LayoutParams2.bottomMargin = frameLayout$LayoutParams0.bottomMargin + v1;
        frameLayout$LayoutParams2.leftMargin = v1 / 2;
        FrameLayout.LayoutParams frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(-2, -2, 81);
        this.E = frameLayout$LayoutParams3;
        frameLayout$LayoutParams3.bottomMargin = frameLayout$LayoutParams0.bottomMargin + v1;
        frameLayout$LayoutParams3.leftMargin = v1 + v1 / 2;
    }

    protected abstract void r();

    Drawable s(int v, int v1) {
        Drawable drawable0 = this.z.getDrawable(v);
        this.K.e(drawable0, v1);
        return drawable0;
    }

    Drawable t(int v) {
        return this.s(v, 0xFF888888);
    }

    Drawable u(int v) {
        return this.s(v, this.J);
    }

    protected abstract void v(int arg1);

    void w(View view0) {
        a0.w(view0, this.I, ((float)this.A.height) * 0.1f);
    }

    protected abstract void x(boolean arg1);
}


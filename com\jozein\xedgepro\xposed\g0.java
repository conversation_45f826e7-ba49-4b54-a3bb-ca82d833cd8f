package com.jozein.xedgepro.xposed;

import android.view.MotionEvent;
import com.jozein.xedgepro.service.BinderService;
import f.v;
import g.q;
import java.util.ArrayList;

class g0 {
    private final w1 a;
    private final f0 b;
    private boolean c;
    private boolean d;
    private long e;
    private boolean f;
    private final ArrayList g;

    g0(w1 w10) {
        this.c = false;
        this.d = false;
        this.f = false;
        this.g = new ArrayList(0x100);
        this.a = w10;
        this.b = new f0(w10.T1(), w10.U1(), true);
    }

    void a(MotionEvent motionEvent0) {
        int v = motionEvent0.getActionMasked();
        if(!this.c) {
            if(v != 0) {
                v.d(new IllegalStateException("Recording MotionEvent while not downed!"));
                return;
            }
            this.c = true;
            this.e = motionEvent0.getDownTime();
            this.f = this.a.V1().j0();
            boolean z = this.a.b2().o(6);
            this.d = z;
            if(z) {
                this.b.i();
            }
        }
        q q0 = new q(motionEvent0, this.e);
        this.g.add(q0);
        if(this.d) {
            this.b.d(q0);
        }
    }

    void b(boolean z) {
        if(!this.c) {
            return;
        }
        this.c = false;
        this.a.a3();
        if(this.d) {
            this.d = false;
            this.b.e();
        }
        if(!z) {
            try {
                BinderService.m(this.a.T1(), this.f, this.g);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        this.g.clear();
    }
}


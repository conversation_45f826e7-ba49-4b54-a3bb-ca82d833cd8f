package f;

import android.os.Build.VERSION;
import android.system.Os;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

public class r {
    private static Object a;
    private static Method b;
    private static Field c;
    private static Field d;

    static {
    }

    public static void a(File file0, File file1) {
        if(!file0.exists()) {
            return;
        }
        if(file0.isDirectory()) {
            r.d(file0, file1);
            return;
        }
        r.b(file0, file1);
    }

    private static void b(File file0, File file1) {
        FileOutputStream fileOutputStream0;
        FileInputStream fileInputStream0;
        try {
            fileInputStream0 = new FileInputStream(file0);
        }
        catch(Throwable throwable0) {
            fileInputStream0 = null;
            fileOutputStream0 = null;
            goto label_11;
        }
        try {
            fileOutputStream0 = null;
            fileOutputStream0 = new FileOutputStream(file1);
            r.c(fileInputStream0, fileOutputStream0);
            goto label_16;
        }
        catch(Throwable throwable0) {
        }
        try {
        label_11:
            if(fileInputStream0 != null) {
                fileInputStream0.close();
            }
            if(fileOutputStream0 != null) {
                fileOutputStream0.close();
            }
        }
        catch(IOException unused_ex) {
        }
        throw throwable0;
        try {
        label_16:
            fileInputStream0.close();
            fileOutputStream0.close();
        }
        catch(IOException unused_ex) {
        }
    }

    public static void c(InputStream inputStream0, OutputStream outputStream0) {
        byte[] arr_b = new byte[0x1000];
        int v;
        while((v = inputStream0.read(arr_b)) > 0) {
            outputStream0.write(arr_b, 0, v);
        }
    }

    private static void d(File file0, File file1) {
        if(!file1.exists() && !file1.mkdir()) {
            throw new IOException("Failed to make dir: " + file1.getPath());
        }
        String[] arr_s = file0.list();
        if(arr_s != null) {
            String s = file0.getPath();
            String s1 = file1.getPath();
            for(int v = 0; v < arr_s.length; ++v) {
                String s2 = arr_s[v];
                r.a(new File(s + '/' + s2), new File(s1 + '/' + s2));
            }
        }
    }

    // 去混淆评级： 低(40)
    public static boolean e(File file0) {
        return !file0.exists() || (!file0.isDirectory() || r.f(file0.listFiles())) && file0.delete();
    }

    public static boolean f(File[] arr_file) {
        boolean z = true;
        if(arr_file == null) {
            return true;
        }
        for(int v = 0; v < arr_file.length; ++v) {
            if(!r.e(arr_file[v])) {
                z = false;
            }
        }
        return z;
    }

    public static boolean g(String[] arr_s) {
        File[] arr_file = new File[arr_s.length];
        for(int v = 0; v < arr_s.length; ++v) {
            arr_file[v] = new File(arr_s[v]);
        }
        return r.f(arr_file);
    }

    public static boolean h(String s) {
        File file0 = new File(s);
        if(file0.exists()) {
            if(!file0.setExecutable(true, false)) {
                return false;
            }
            File[] arr_file = file0.listFiles();
            if(arr_file != null) {
                for(int v = 0; v < arr_file.length; ++v) {
                    if(!arr_file[v].setReadable(true, false)) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    public static void i(File file0, File file1) {
        try {
            boolean z = false;
            z = file0.renameTo(file1);
        }
        catch(Throwable unused_ex) {
        }
        if(!z) {
            r.a(file0, file1);
            r.e(file0);
        }
    }

    public static void j(String s, String s1) {
        r.i(new File(s), new File(s1));
    }

    public static int k(String s) {
        if(Build.VERSION.SDK_INT >= 21) {
            return Os.stat(s).st_mode;
        }
        if(r.c == null) {
            if(r.a == null) {
                r.a = Class.forName("libcore.io.Libcore").getField("os").get(null);
            }
            if(r.b == null) {
                r.b = Class.forName("libcore.io.Os").getMethod("stat", String.class);
            }
            r.c = Class.forName("libcore.io.StructStat").getField("st_mode");
        }
        Object object0 = r.b.invoke(r.a, s);
        return r.c.getInt(object0);
    }

    public static int l(String s) {
        if(Build.VERSION.SDK_INT >= 21) {
            return Os.stat(s).st_uid;
        }
        if(r.d == null) {
            if(r.a == null) {
                r.a = Class.forName("libcore.io.Libcore").getField("os").get(null);
            }
            if(r.b == null) {
                r.b = Class.forName("libcore.io.Os").getMethod("stat", String.class);
            }
            r.d = Class.forName("libcore.io.StructStat").getField("st_mode");
        }
        Object object0 = r.b.invoke(r.a, s);
        return r.d.getInt(object0);
    }
}


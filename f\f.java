package f;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

public class f extends e {
    protected final File C;
    private final boolean D;
    private boolean E;

    public f(File file0, boolean z) {
        this.E = false;
        this.C = file0;
        this.D = z;
    }

    public f(String s, boolean z) {
        this.E = false;
        this.C = new File(s);
        this.D = z;
    }

    public f A(boolean z) {
        this.E = this.C.exists() && z;
        super.j();
        return this;
    }

    public void B() {
        this.C(this.o(), this.p());
        this.l();
        if(!this.D) {
            this.C.setReadable(true, false);
        }
        this.E = false;
    }

    protected void C(byte[] arr_b, int v) {
        FileOutputStream fileOutputStream0;
        if(arr_b == null) {
            return;
        }
        File file0 = this.C.getParentFile();
        if(!file0.exists() && (!file0.mkdir() || !file0.setExecutable(true, this.D))) {
            v.c("Error make world readable dir!");
            return;
        }
        try {
            fileOutputStream0 = null;
            fileOutputStream0 = new FileOutputStream(this.C, this.E);
            fileOutputStream0.write(arr_b, 0, v);
            this.E = true;
        }
        catch(IOException iOException0) {
            v.d(iOException0);
            this.l();
        }
        if(fileOutputStream0 != null) {
            try {
                fileOutputStream0.close();
            }
            catch(IOException iOException1) {
                v.d(iOException1);
            }
        }
    }

    @Override  // f.e
    public void i(byte[] arr_b) {
        throw new UnsupportedOperationException("Use beginRead() instead!");
    }

    @Override  // f.e
    public e j() {
        return this.z();
    }

    @Override  // f.e
    public void m() {
        this.l();
    }

    @Override  // f.e
    protected byte[] r(int v, byte[] arr_b, int v1) {
        this.C(arr_b, v1);
        this.s(0);
        return v <= arr_b.length ? arr_b : new byte[(v + 0x4000) / 0x4000 * 0x4000];
    }

    public boolean y() {
        byte[] arr_b = this.D ? new b(this.C.getPath()).j() : q.c(this.C.getPath()).j();
        super.i(arr_b);
        return arr_b != null && arr_b.length != 0;
    }

    public f z() {
        return this.A(false);
    }
}


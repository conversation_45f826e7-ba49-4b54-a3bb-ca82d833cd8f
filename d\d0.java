package d;

import a.b.y0;
import android.content.Context;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout.LayoutParams;
import android.widget.FrameLayout;
import android.widget.TextView;
import e.r;
import g.q;
import g.x;
import java.util.ArrayList;

public class d0 extends r {
    private ArrayList E;
    private boolean F;
    private boolean G;

    public d0() {
        this.E = new ArrayList(0x100);
        this.F = false;
        this.G = false;
    }

    @Override  // e.j0$c
    protected boolean D(int v, KeyEvent keyEvent0) {
        return true;
    }

    @Override  // e.j0$c
    protected boolean E(int v, KeyEvent keyEvent0) {
        if(!this.G) {
            if(v != 4) {
                this.q0();
                return true;
            }
            this.r0();
            return true;
        }
        this.F = true;
        if(v == 4) {
            this.E = null;
        }
        return true;
    }

    @Override  // android.app.Fragment
    public View onCreateView(LayoutInflater layoutInflater0, ViewGroup viewGroup0, Bundle bundle0) {
        class a extends g.r {
            long L;
            final FrameLayout M;
            final TextView N;
            final d0 O;

            a(Context context0, int v, FrameLayout frameLayout0, TextView textView0) {
                this.M = frameLayout0;
                this.N = textView0;
                super(context0, v);
                this.L = -1L;
            }

            @Override  // g.r
            public boolean onTouchEvent(MotionEvent motionEvent0) {
                switch(motionEvent0.getButtonState()) {
                    case 0: 
                    case 1: {
                        if(this.L == -1L) {
                            this.L = motionEvent0.getDownTime();
                            this.M.removeView(this.N);
                        }
                        int v = motionEvent0.getActionMasked();
                        if(v == 0) {
                            if(d0.this.G) {
                                d0.this.r0();
                                return true;
                            }
                            d0.this.G = true;
                            if(d0.this.F) {
                                d0.this.q0();
                                return true;
                            }
                        }
                        q q0 = new q(motionEvent0, this.L);
                        if(d0.this.E != null) {
                            d0.this.E.add(q0);
                            this.e(q0);
                        }
                        if(v == 1) {
                            d0.this.G = false;
                            if(d0.this.F) {
                                d0.this.q0();
                                return true;
                            }
                        }
                        else if(v == 3) {
                            d0.this.r0();
                            return true;
                        }
                        this.invalidate();
                        return true;
                    }
                    default: {
                        d0.this.r0();
                        return true;
                    }
                }
            }
        }

        Context context0 = viewGroup0 == null ? this.f() : viewGroup0.getContext();
        x x0 = this.l();
        View view0 = new FrameLayout(context0);
        TextView textView0 = new TextView(context0);
        textView0.setText(0x7F060119);  // string:get_gesture_hint "Click back to cancel, other key to save gesture. \nYou 
                                        // can also record gesture via action: gesture record."
        FrameLayout.LayoutParams frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(-2, -2, 17);
        frameLayout$LayoutParams0.rightMargin = x0.f;
        frameLayout$LayoutParams0.leftMargin = x0.f;
        ((FrameLayout)view0).addView(textView0, frameLayout$LayoutParams0);
        a d0$a0 = new a(this, context0, 0xFF0097A7, ((FrameLayout)view0), textView0);
        d0$a0.l(0, 0, false);
        ((FrameLayout)view0).addView(d0$a0, -1, -1);
        return view0;
    }

    private void q0() {
        if(this.E != null && !this.E.isEmpty()) {
            try {
                this.W("result", new y0(this.f(), this.E, this.q().getConfiguration().orientation == 2));
            }
            catch(Throwable throwable0) {
                this.g0(throwable0);
            }
            this.E = null;
        }
        this.L();
    }

    private void r0() {
        this.G = false;
        this.L();
    }
}


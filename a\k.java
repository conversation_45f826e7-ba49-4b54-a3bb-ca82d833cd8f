package a;

import android.content.Context;
import android.content.Intent;
import f.l;
import f.m;
import f.p;
import f.v;
import java.util.HashSet;
import java.util.Set;

public class k extends r {
    private final p B;
    private Set C;
    private static final String D;
    private static final String E;
    private static k F;

    static {
        k.D = l.m + "hidden_apps";
        k.E = l.k + "HIDDEN_APPS";
        k.F = null;
    }

    private k() {
        super(k.E);
        this.B = new p(k.D);
        this.l();
        if(this.C == null) {
            this.C = new HashSet();
        }
    }

    @Override  // a.r
    protected void b(Intent intent0) {
        HashSet hashSet0;
        String s;
        int v;
        m m0 = new m(intent0);
        if(m0.i()) {
            v = m0.h();
            s = m0.a();
        }
        else {
            v = -1;
            s = null;
        }
        m0.k();
        switch(v) {
            case 1: {
                if(s != null && s.length() > 0) {
                    hashSet0 = new HashSet(this.C);
                    hashSet0.add(s);
                    break;
                }
                return;
            }
            case 2: {
                if(s != null && s.length() > 0) {
                    hashSet0 = new HashSet(this.C);
                    hashSet0.remove(s);
                    break;
                }
                return;
            }
            case 3: {
                hashSet0 = new HashSet();
                break;
            }
            default: {
                return;
            }
        }
        this.C = hashSet0;
    }

    public void e(Context context0, String s) {
        if(this.C.contains(s)) {
            return;
        }
        this.C.add(s);
        this.n(context0, 1, s);
    }

    public void f(Context context0) {
        if(this.C.isEmpty()) {
            return;
        }
        this.C.clear();
        this.n(context0, 3, null);
    }

    public boolean g(String s) {
        return this.C.contains(s);
    }

    public static k h() {
        synchronized(k.class) {
            if(k.F == null) {
                k.F = new k();
            }
            return k.F;
        }
    }

    public Set i() {
        return this.C;
    }

    public boolean j() {
        return this.C.isEmpty();
    }

    public static void k() {
        synchronized(k.class) {
            k.F = null;
        }
    }

    public void l() {
        HashSet hashSet0 = new HashSet();
        try {
            if(this.B.E()) {
                String s;
                while((s = this.B.s()) != null) {
                    if(s.length() > 0) {
                        hashSet0.add(s);
                    }
                }
            }
            this.B.m();
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        this.C = hashSet0;
    }

    public void m(Context context0, String s) {
        if(!this.C.remove(s)) {
            return;
        }
        this.n(context0, 2, s);
    }

    private void n(Context context0, int v, String s) {
        this.B.F();
        for(Object object0: this.C) {
            this.B.B(((String)object0));
        }
        this.B.n();
        Intent intent0 = new Intent();
        new m(intent0).j().o(v).q(s).l();
        this.c(context0, intent0);
    }
}


package f;

import android.os.Build.VERSION;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;

public class h0 extends Handler {
    private static volatile Handler a;
    private static volatile Handler b;

    static {
    }

    public h0(Looper looper0) {
        super(looper0);
    }

    public static Handler a() {
        if(h0.a == null) {
            Class class0 = h0.class;
            synchronized(class0) {
                if(h0.a == null) {
                    h0.a = new h0(Looper.getMainLooper());
                }
                return h0.a;
            }
        }
        return h0.a;
    }

    public static Handler b() {
        return h0.b == null ? h0.a() : h0.b;
    }

    public static boolean c(Handler handler0) {
        int v = Build.VERSION.SDK_INT;
        Looper looper0 = handler0.getLooper();
        return v < 23 ? looper0.getThread() == Thread.currentThread() : looper0.isCurrentThread();
    }

    public static Handler d() {
        Looper looper0 = Looper.myLooper();
        return looper0 != null && looper0 != Looper.getMainLooper() ? new h0(looper0) : h0.a();
    }

    @Override  // android.os.Handler
    public void dispatchMessage(Message message0) {
        try {
            super.dispatchMessage(message0);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    public static Handler e(String s) {
        HandlerThread handlerThread0 = new HandlerThread(s);
        handlerThread0.start();
        return new h0(handlerThread0.getLooper());
    }

    public static void f(Handler handler0, Runnable runnable0) {
        if(h0.c(handler0)) {
            runnable0.run();
            return;
        }
        handler0.post(runnable0);
    }

    public static void g(Handler handler0) {
        h0.b = handler0;
    }
}


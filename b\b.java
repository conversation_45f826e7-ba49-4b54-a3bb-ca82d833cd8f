package b;

import android.content.Context;
import android.os.Parcelable;
import com.jozein.xedgepro.service.BinderService;

public final class b implements Runnable {
    public final String A;
    public final Parcelable B;
    public final Context z;

    public b(Context context0, String s, Parcelable parcelable0) {
        this.z = context0;
        this.A = s;
        this.B = parcelable0;
    }

    @Override
    public final void run() {
        BinderService.o(this.z, this.A, this.B);
    }
}


package com.jozein.xedgepro.xposed;

import android.content.Context;
import android.os.Build.VERSION;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager.LayoutParams;
import android.view.WindowManager;
import android.view.animation.AlphaAnimation;
import android.widget.FrameLayout.LayoutParams;
import android.widget.FrameLayout;
import de.robv.android.xposed.XposedHelpers;
import f.l;
import f.v;
import java.lang.reflect.Field;

class t3 implements l {
    static class a extends FrameLayout {
        private final boolean z;

        a(Context context0, boolean z, boolean z1) {
            super(context0);
            WindowManager.LayoutParams windowManager$LayoutParams0;
            this.z = z;
            int v = Build.VERSION.SDK_INT;
            if(v >= 29) {
                this.setForceDarkAllowed(false);
            }
            WindowManager windowManager0 = (WindowManager)context0.getSystemService("window");
            if(z) {
                windowManager$LayoutParams0 = new WindowManager.LayoutParams((v <= 29 ? 2014 : 2017), 0x1080500, 1);
                windowManager$LayoutParams0.softInputMode = 51;
                this.setFocusableInTouchMode(true);
                if(t3.F != null) {
                    t3.F.a();
                }
            }
            else {
                windowManager$LayoutParams0 = new WindowManager.LayoutParams(0x7DF, 0x1080518, 1);
                if(v >= 28) {
                    windowManager$LayoutParams0.layoutInDisplayCutoutMode = 1;
                    if(v >= 30) {
                        windowManager$LayoutParams0.setFitInsetsTypes(0);
                    }
                }
                t3.i(windowManager$LayoutParams0, (z1 ? 0x100010 : 16));
            }
            windowManager0.addView(this, windowManager$LayoutParams0);
        }

        void b(View view0, FrameLayout.LayoutParams frameLayout$LayoutParams0, int v) {
            view0.setTag(v);
            if(!this.z || v == 273) {
                View view1 = this.d(v);
                if(view1 != null) {
                    this.i(view1);
                }
            }
            else {
                this.f();
            }
            this.addView(view0, ((v & 0x100) == 0 ? 0 : -1), frameLayout$LayoutParams0);
            if(this.z) {
                view0.requestFocus();
            }
        }

        void c() {
            this.f();
            this.g();
        }

        View d(int v) {
            for(int v1 = this.getChildCount() - 1; v1 >= 0; --v1) {
                View view0 = this.getChildAt(v1);
                Integer integer0 = (Integer)view0.getTag();
                if(integer0 != null && ((int)integer0) == v) {
                    return view0;
                }
            }
            return null;
        }

        @Override  // android.view.ViewGroup
        public boolean dispatchKeyEvent(KeyEvent keyEvent0) {
            if(keyEvent0.getKeyCode() == 4 && keyEvent0.getAction() == 0) {
                this.j();
                return true;
            }
            return super.dispatchKeyEvent(keyEvent0);
        }

        // 检测为 Lambda 实现
        private void e(View view0) [...]

        private void f() {
            for(int v = this.getChildCount() - 1; v >= 0; --v) {
                View view0 = this.getChildAt(v);
                if(view0 instanceof c) {
                    try {
                        ((c)view0).a();
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }
            this.removeAllViews();
        }

        private void g() {
            try {
                ((WindowManager)this.getContext().getSystemService("window")).removeView(this);
                t3 t30 = t3.n();
                if(t30.B == this) {
                    t30.B = null;
                }
                else if(t30.A == this) {
                    t30.A = null;
                }
                else if(t30.z == this) {
                    t30.z = null;
                }
                else {
                    v.c("Removing unknown container.");
                }
                if(this.z && t3.F != null) {
                    t3.F.b();
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        boolean h(int v, long v1) {
            View view0 = this.d(v);
            if(view0 == null) {
                return false;
            }
            if(v1 > 0L) {
                AlphaAnimation alphaAnimation0 = new AlphaAnimation(1.0f, 0.0f);
                alphaAnimation0.setDuration(v1);
                view0.startAnimation(alphaAnimation0);
                view0.setTag(null);
                this.postDelayed(() -> {
                    this.i(view0);
                    if(this.getChildCount() <= 0) {
                        this.g();
                    }
                }, v1);
                return true;
            }
            this.i(view0);
            if(this.getChildCount() <= 0) {
                this.g();
            }
            return true;
        }

        private void i(View view0) {
            this.removeView(view0);
            if(view0 instanceof c) {
                try {
                    ((c)view0).a();
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        boolean j() {
            boolean z = this.getChildCount() > 0;
            if(!this.h(273, 0L)) {
                this.c();
            }
            return z;
        }

        @Override  // android.view.View
        public boolean onGenericMotionEvent(MotionEvent motionEvent0) {
            View view0 = this.getChildAt(this.getChildCount() - 1);
            return view0 == null ? super.onGenericMotionEvent(motionEvent0) : view0.onGenericMotionEvent(motionEvent0);
        }

        @Override  // android.view.View
        public boolean onTouchEvent(MotionEvent motionEvent0) {
            if(motionEvent0.getActionMasked() == 0) {
                this.c();
            }
            return true;
        }
    }

    static abstract class b extends FrameLayout implements c {
        public b(Context context0) {
            super(context0);
        }
    }

    interface c {
        void a();
    }

    interface d {
        void a();

        void b();
    }

    private a A;
    private a B;
    private static Field C;
    static final boolean D;
    private static final t3 E;
    private static d F;
    private static boolean G;
    private a z;

    static {
        t3.D = Build.VERSION.SDK_INT >= 27;
        t3.E = new t3();
        t3.F = null;
        t3.G = true;
    }

    private t3() {
        this.z = null;
        this.A = null;
        this.B = null;
    }

    static boolean a() [...] // 潜在的解密器

    static void i(WindowManager.LayoutParams windowManager$LayoutParams0, int v) {
        try {
            if(t3.C == null) {
                t3.C = XposedHelpers.findField(WindowManager.LayoutParams.class, "privateFlags");
            }
            int v1 = t3.C.getInt(windowManager$LayoutParams0);
            t3.C.setInt(windowManager$LayoutParams0, v | v1);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    boolean j(View view0, int v) {
        return this.k(view0, v, null);
    }

    boolean k(View view0, int v, FrameLayout.LayoutParams frameLayout$LayoutParams0) {
        if(view0 == null) {
            return false;
        }
        if(frameLayout$LayoutParams0 == null) {
            frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(-1, -1);
        }
        if(v == 6) {
            this.l();
        }
        try {
            this.m(view0.getContext(), v).b(view0, frameLayout$LayoutParams0, v);
            return true;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return false;
        }
    }

    private void l() {
        a t3$a0 = this.B;
        if(t3$a0 != null) {
            try {
                t3$a0.c();
                this.B = null;
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    private a m(Context context0, int v) {
        if(v == 0x101 && t3.D) {
            if(this.z == null) {
                this.z = new a(context0, false, true);
            }
            return this.z;
        }
        if((v & 0xFF) >= 16) {
            if(this.B == null) {
                this.B = new a(context0, true, false);
            }
            return this.B;
        }
        if(this.A == null) {
            this.A = new a(context0, false, false);
        }
        return this.A;
    }

    static t3 n() {
        return t3.E;
    }

    // 去混淆评级： 低(20)
    static boolean o() {
        return t3.E == null ? false : t3.E.B != null && t3.E.B.j();
    }

    boolean p(int v) {
        a t3$a0 = this.r(v);
        return t3$a0 != null && t3$a0.d(v) != null;
    }

    static void q() {
        t3 t30 = t3.E;
        if(t30 != null) {
            t30.l();
        }
    }

    private a r(int v) {
        if(v == 0x101 && t3.D) {
            return this.z;
        }
        return (v & 0xFF) < 16 ? this.A : this.B;
    }

    static t3 s() {
        return t3.E;
    }

    void t(int v) {
        this.u(v, 0L);
    }

    void u(int v, long v1) {
        a t3$a0 = this.r(v);
        if(t3$a0 != null) {
            t3$a0.h(v, v1);
        }
    }

    static void v(boolean z) {
        t3.G = z;
    }

    static void w(d t3$d0) {
        t3.F = t3$d0;
    }
}


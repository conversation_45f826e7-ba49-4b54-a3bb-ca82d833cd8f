package com.jozein.xedgepro.xposed;

import android.content.ComponentName;
import android.os.Build.VERSION;
import f.l;
import f.v;

class k3 extends j2 {
    private final w1 H;

    k3(Object object0, w1 w10) {
        super(object0.getClass(), object0);
        this.H = w10;
    }

    void B(ComponentName componentName0) {
        if(Build.VERSION.SDK_INT >= 24) {
            this.r("addTile", new Object[]{componentName0});
            this.H.E4(0x7F060200);  // string:tile_added "Tile added."
        }
    }

    void C(String s) {
        this.B(new ComponentName(l.j, s));
    }

    boolean D() {
        try {
            this.r("expandNotificationsPanel", new Object[0]);
            return true;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return false;
        }
    }

    boolean E() {
        if(Build.VERSION.SDK_INT >= 24) {
            try {
                this.r("expandSettingsPanel", new Object[]{null});
                return true;
            }
            catch(Throwable unused_ex) {
            }
        }
        try {
            this.r("expandSettingsPanel", new Object[0]);
            return true;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return false;
        }
    }

    void F(ComponentName componentName0) {
        if(Build.VERSION.SDK_INT >= 24) {
            this.r("remTile", new Object[]{componentName0});
            this.H.E4(0x7F060201);  // string:tile_removed "Tile removed."
        }
    }

    void G(String s) {
        this.F(new ComponentName(l.j, s));
    }
}


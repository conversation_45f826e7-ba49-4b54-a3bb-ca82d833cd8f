package d;

import a.b.f1;
import a.b.g0;
import a.b.h3;
import a.b.n3;
import a.b.u0;
import a.b;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import c.j;
import c.m;
import c.s;
import e.j.g;
import e.j.k;
import e.y;
import f.e0;
import f.l;
import f.v;

public class d extends e implements e.j.d {
    interface a {
    }

    private b[] M;

    public d() {
        this.M = null;
    }

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F0601A2);  // string:select_action "Select action"
    }

    @Override  // e.j
    protected int B0() {
        int v = this.h().getInt("flag", -1);
        if(v == -1) {
            v = 1;
        }
        this.M = b.i(v);
        return !l.r || !(this.p() instanceof a) ? this.M.length - 1 : this.M.length;
    }

    @Override  // d.e
    protected void B1(Bundle bundle0, int v) {
        f1 b$f10;
        if(bundle0 == null) {
            return;
        }
        switch(v) {
            case 1: {
                this.U(bundle0);
                this.L();
                return;
            }
            case 2: {
                CharSequence charSequence0 = bundle0.getCharSequence("result");
                if(charSequence0 != null) {
                    try {
                        this.C1(new g0(Integer.parseInt(charSequence0.toString())));
                    }
                    catch(NumberFormatException numberFormatException0) {
                        v.d(numberFormatException0);
                    }
                    return;
                }
                this.L();
                return;
            }
            case 77: {
                String s = bundle0.getString("result");
                if(s != null) {
                    b$f10 = new f1(s);
                    this.C1(b$f10);
                    return;
                }
                break;
            }
            case 92: {
                int v1 = bundle0.getInt("result", 0);
                if(v1 * 10 > 0) {
                    b$f10 = new n3(v1 * 10);
                    this.C1(b$f10);
                    return;
                }
                break;
            }
        }
    }

    protected k E1(int v) {
        Context context0 = this.M0();
        b b0 = this.M[v];
        if(!l.r && b0 instanceof e0) {
            k j$k0 = new g(this, this.D0(b0), b0.p(context0), this.u(0x7F06017C));  // string:pro_only "Pro only"
            j$k0.setEnabled(false);
            return j$k0;
        }
        return new g(this, this.D0(b0), b0.p(context0), null);
    }

    protected k F1(int v) {
        return new k(this, ((u0)this.M[this.I0()]).a(this.M0(), v));
    }

    public d G1(int v) {
        this.h().putInt("flag", v);
        return this;
    }

    public d H1(int v, CharSequence charSequence0) {
        this.b0(charSequence0);
        return this.G1(v);
    }

    public d I1(int v, CharSequence charSequence0, CharSequence charSequence1) {
        return this.H1(v, charSequence0 + "/" + charSequence1);
    }

    public d J1(int v, CharSequence charSequence0, CharSequence charSequence1, CharSequence charSequence2) {
        return this.H1(v, charSequence0 + "/" + charSequence1 + "/" + charSequence2);
    }

    @Override  // e.j
    protected boolean N0(int v) {
        return this.M[v] instanceof u0;
    }

    @Override  // e.j$d
    public String[] a() {
        Context context0 = this.M0();
        int v = this.M.length;
        String[] arr_s = new String[v];
        for(int v1 = 0; v1 < v; ++v1) {
            arr_s[v1] = this.M[v1].p(context0).toString();
        }
        return arr_s;
    }

    @Override  // e.j
    protected View h1(int v) {
        return this.E1(v);
    }

    @Override  // e.j
    protected View i1(int v) {
        return this.F1(v);
    }

    @Override  // e.j
    protected void k1(int v) {
        b b0 = this.M[v];
        if(!l.r && b0 instanceof e0) {
            return;
        }
        if(b0 instanceof u0) {
            this.y1(v, ((u0)b0).b());
            return;
        }
        switch(b0.z) {
            case 0x30: {
                this.P(new f().b0(this.o()), 1);
                return;
            }
            case 53: {
                this.P(new a0().C1(true).b0(this.o()), 1);
                return;
            }
            case 54: {
                y y0 = new y();
                y0.J(this.u(0x7F0600F7), this.u(0x7F060204), "1000", 7, 9);  // string:enter_duration "Enter duration"
                this.N(y0, 2);
                return;
            }
            case 62: {
                this.P(new o().b0(this.o()), 1);
                return;
            }
            case 0x3F: {
                this.P(new s0().S1(new b[0], true).b0(this.o()), 1);
                return;
            }
            case 72: {
                this.P(new t().K1(new a.b.d(b.e()), this.h().getInt("flag")).b0(this.o()), 1);
                return;
            }
            case 75: {
                this.e0(0x7F060166);  // string:only_if_keyguard_not_secure "Unlock keyguard only if keyguard is not secure."
                break;
            }
            case 77: {
                this.P(new d.u0().b0(this.o()), 77);
                return;
            }
            case 78: {
                this.P(new v1().b0(this.o()), 1);
                return;
            }
            case 0x4F: {
                this.P(new d.b().T1(true).b0(this.o()), 1);
                return;
            }
            case 83: {
                this.P(new r().I1(true).b0(this.o()), 1);
                return;
            }
            case 84: {
                this.P(new r1().I1(true).b0(this.o()), 1);
                return;
            }
            case 86: {
                this.P(new a2().b0(this.o()), 1);
                return;
            }
            case 88: {
                this.P(new d2().b0(this.o()), 1);
                return;
            }
            case 89: {
                this.P(new d.g().b0(this.o()), 1);
                return;
            }
            case 92: {
                this.N(new e.g0().y(this.u(0x7F06021D), null, 1, 20, 100, 10), 92);  // string:vibrate_duration "Vibrate duration"
                return;
            }
            case 93: {
                this.P(new y1().b0(this.o()), 1);
                return;
            }
            case 94: {
                this.P(new x().b0(this.o()), 1);
                return;
            }
            case 100: {
                this.N(new j(), 1);
                return;
            }
            case 101: {
                this.P(new p1().M1(0, b.r()).b0(this.o()), 1);
                return;
            }
            case 105: {
                this.P(new i0().b0(this.o()), 1);
                return;
            }
            case 107: {
                this.P(new z1().L1(h3.K).b0(this.o()), 1);
                return;
            }
            case 108: {
                this.N(new c.o(), 1);
                return;
            }
            case 109: {
                this.N(new m(), 1);
                return;
            }
            case 110: {
                this.N(new c.r(), 1);
                return;
            }
            case 0x6F: {
                this.N(new s(), 1);
                return;
            }
            case 0x73: {
                this.P(new d.k().b0(this.o()), 1);
                return;
            }
        }
        this.C1(b0);
    }

    @Override  // e.j
    protected boolean l1(int v) {
        b b0 = this.M[v];
        if(!l.r && b0 instanceof e0) {
            return true;
        }
        if(b0 instanceof u0) {
            this.y1(v, ((u0)b0).b());
            return true;
        }
        int v1 = b0.z;
        if(v1 != 0 && v1 != 1) {
            switch(v1) {
                case 0x2F: 
                case 0x30: 
                case 51: 
                case 53: 
                case 54: 
                case 68: 
                case 72: 
                case 83: 
                case 84: 
                case 86: 
                case 100: 
                case 101: 
                case 105: {
                    break;
                }
                default: {
                    if(v1 != 61 && v1 != 62 && v1 != 0x3F && (v1 != 77 && v1 != 78 && v1 != 0x4F) && (v1 != 92 && v1 != 93 && v1 != 94) && (v1 != 108 && v1 != 109 && v1 != 110 && v1 != 0x6F)) {
                        this.D1(b0);
                    }
                }
            }
        }
        return true;
    }

    @Override  // e.j
    protected void o1(int v) {
        this.C1(((u0)this.M[this.I0()]).c(v));
    }

    @Override  // e.j
    protected boolean p1(int v) {
        if(this.M[this.I0()].z != 68) {
            this.D1(((u0)this.M[this.I0()]).c(v));
        }
        return true;
    }
}


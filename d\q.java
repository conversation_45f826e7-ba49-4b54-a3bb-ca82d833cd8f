package d;

import a.b;
import android.content.Context;
import android.os.Bundle;
import android.view.View.OnClickListener;
import android.view.View;
import e.j.g;
import e.j.k;
import e.m;
import f.v;

public class q extends w1 {
    private b[] N;
    private int O;

    @Override  // d.w1
    protected int B0() {
        b[] arr_b = (b[])this.h().getParcelableArray("actions");
        this.N = arr_b;
        int v = 4;
        if(arr_b == null) {
            this.N = new b[4];
        }
        for(int v1 = 0; true; ++v1) {
            b[] arr_b1 = this.N;
            if(v1 >= arr_b1.length) {
                break;
            }
            if(arr_b1[v1] == null) {
                arr_b1[v1] = b.r();
            }
        }
        if(arr_b1.length == 9) {
            v = 3;
        }
        else if(arr_b1.length != 16) {
            v = 2;
        }
        this.O = v;
        return v;
    }

    @Override  // d.w1
    protected void C1(Bundle bundle0, int v) {
        switch(v) {
            case 1: {
                Context context0 = this.f();
                b b0 = (b)bundle0.getParcelable("result");
                if(b0 != null) {
                    int v1 = this.E0();
                    int v2 = this.F0();
                    this.N[this.Q1(v1, v2)] = b0;
                    this.h().putBoolean("changed", true);
                    g j$g0 = (g)this.K0(v2);
                    j$g0.setSubText(b0.n(context0));
                    j$g0.setImageDrawable(this.D0(b0));
                }
                return;
            }
            case 2: {
                if(bundle0.getBoolean("result", false)) {
                    this.S1();
                }
                this.L();
                return;
            }
            default: {
                v.c(("Unknown code: " + v));
            }
        }
    }

    @Override  // d.w1
    protected k L1(int v) {
        Context context0 = this.M0();
        b b0 = this.N[this.Q1(this.I0(), v)];
        return new g(this, this.I1(v), b0.n(context0), this.D0(b0));
    }

    @Override  // d.w1
    protected void M1() {
        class a implements View.OnClickListener {
            final q z;

            @Override  // android.view.View$OnClickListener
            public void onClick(View view0) {
                if(q.this.h().getBoolean("changed", false)) {
                    q.this.S1();
                }
                q.this.L();
            }
        }

        this.c0(0x7F060019);  // string:action_custom_panel "Custom panel"
        if(this.h().getBoolean("editable", false)) {
            this.S(0x7F040071, new a(this));  // drawable:ic_ok
        }
    }

    private int Q1(int v, int v1) {
        return v * this.O + v1;
    }

    public q R1(CharSequence charSequence0, b[] arr_b, boolean z) {
        Bundle bundle0 = this.h();
        bundle0.putCharSequence("alias", charSequence0);
        bundle0.putParcelableArray("actions", arr_b);
        bundle0.putBoolean("editable", z);
        return this;
    }

    private void S1() {
        Bundle bundle0 = new Bundle(1);
        bundle0.putParcelableArray("result", this.N);
        this.U(bundle0);
    }

    @Override  // d.w1
    protected View i1(int v) {
        return this.L1(v);
    }

    @Override  // d.w1
    protected void k1(int v) {
        this.y1(v, this.O);
    }

    @Override  // d.w1
    protected void o1(int v) {
        CharSequence charSequence0 = this.h().getCharSequence("alias");
        if(charSequence0 == null) {
            charSequence0 = this.u(0x7F060019);  // string:action_custom_panel "Custom panel"
        }
        this.P(new d().J1(6, charSequence0, this.J1(this.I0()), this.I1(v)), 1);
    }

    @Override  // e.j
    public void onSaveInstanceState(Bundle bundle0) {
        this.h().putParcelableArray("actions", this.N);
        super.onSaveInstanceState(bundle0);
    }

    @Override  // d.w1
    protected boolean p1(int v) {
        this.G1(this.N[this.Q1(this.I0(), v)], 1, 6, this.h().getBoolean("editable"));
        return true;
    }

    @Override  // e.j0$c
    protected void y() {
        if(this.h().getBoolean("changed", false)) {
            this.N(new m().u(this.u(0x7F06008F)), 2);  // string:check_save_message "Do you want to save changes?"
            return;
        }
        super.y();
    }
}


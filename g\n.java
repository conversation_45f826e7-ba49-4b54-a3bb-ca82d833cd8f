package g;

import a.b;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;

public class n extends i {
    private final Drawable d;
    private final Drawable[] e;
    private final int f;

    public n(Context context0, b[] arr_b, Drawable drawable0) {
        int v1 = 4;
        if(arr_b.length == 0) {
            v1 = 0;
        }
        else if(arr_b.length <= 4) {
            v1 = 2;
        }
        else if(arr_b.length <= 9) {
            v1 = 3;
        }
        this.f = v1;
        this.d = drawable0;
        this.e = new Drawable[v1 * v1];
        for(int v = 0; true; ++v) {
            Drawable[] arr_drawable = this.e;
            if(v >= arr_drawable.length) {
                break;
            }
            b b0 = arr_b[v];
            if(b0 != null && (b0.z != 0 && b0.z != 1)) {
                arr_drawable[v] = b0.o(context0);
            }
        }
    }

    public n(Drawable drawable0, Drawable drawable1, Drawable drawable2, Drawable drawable3, Drawable drawable4) {
        this(new Drawable[]{drawable0, drawable1, drawable2, drawable3}, drawable4);
    }

    public n(Drawable[] arr_drawable, Drawable drawable0) {
        if(arr_drawable == null) {
            arr_drawable = a0.z;
        }
        this.e = arr_drawable;
        this.d = drawable0;
        int v = 4;
        if(arr_drawable.length <= 4) {
            v = 2;
        }
        else if(arr_drawable.length <= 9) {
            v = 3;
        }
        this.f = v;
    }

    @Override  // android.graphics.drawable.Drawable
    public void draw(Canvas canvas0) {
        this.a(canvas0, this.d);
        Drawable[] arr_drawable = this.e;
        for(int v = 0; v < arr_drawable.length; ++v) {
            this.a(canvas0, arr_drawable[v]);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public int getIntrinsicHeight() {
        Drawable drawable0 = this.d;
        if(drawable0 != null) {
            return drawable0.getIntrinsicHeight();
        }
        Drawable[] arr_drawable = this.e;
        for(int v = 0; v < arr_drawable.length; ++v) {
            Drawable drawable1 = arr_drawable[v];
            if(drawable1 != null) {
                return drawable1.getIntrinsicWidth() * this.f;
            }
        }
        return super.getIntrinsicHeight();
    }

    @Override  // android.graphics.drawable.Drawable
    public int getIntrinsicWidth() {
        Drawable drawable0 = this.d;
        if(drawable0 != null) {
            return drawable0.getIntrinsicWidth();
        }
        Drawable[] arr_drawable = this.e;
        for(int v = 0; v < arr_drawable.length; ++v) {
            Drawable drawable1 = arr_drawable[v];
            if(drawable1 != null) {
                return drawable1.getIntrinsicWidth() * this.f;
            }
        }
        return super.getIntrinsicWidth();
    }

    @Override  // android.graphics.drawable.Drawable
    public void setBounds(int v, int v1, int v2, int v3) {
        super.setBounds(v, v1, v2, v3);
        Drawable drawable0 = this.d;
        if(drawable0 != null) {
            drawable0.setBounds(v, v1, v2, v3);
        }
        int v4 = this.f;
        if(v4 == 0) {
            return;
        }
        int v5 = (v2 - v) / v4;
        int v6 = (v3 - v1) / v4;
        for(int v7 = 0; v7 < this.f; ++v7) {
            int v8 = v6 * v7 + v1;
            for(int v9 = 0; true; ++v9) {
                int v10 = this.f;
                if(v9 >= v10) {
                    break;
                }
                int v11 = v10 * v7 + v9;
                Drawable[] arr_drawable = this.e;
                if(v11 < arr_drawable.length) {
                    Drawable drawable1 = arr_drawable[v11];
                    if(drawable1 != null) {
                        int v12 = v5 * v9 + v;
                        drawable1.setBounds(v12, v8, v12 + v5, v8 + v6);
                    }
                }
            }
        }
    }
}


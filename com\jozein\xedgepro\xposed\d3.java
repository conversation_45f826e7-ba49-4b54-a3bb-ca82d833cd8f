package com.jozein.xedgepro.xposed;

import a.p.f;
import a.p;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint.Style;
import android.graphics.Paint;
import android.graphics.Point;
import android.os.Handler;
import android.provider.Settings.Global;
import android.util.Property;
import android.view.View;

class d3 implements a {
    class com.jozein.xedgepro.xposed.d3.a extends Property {
        com.jozein.xedgepro.xposed.d3.a(Class class0, String s) {
            super(class0, s);
        }

        public Float a(b d3$b0) {
            return d3$b0.R;
        }

        public void b(b d3$b0, Float float0) {
            d3$b0.h(((float)float0));
        }

        @Override  // android.util.Property
        public Object get(Object object0) {
            return this.a(((b)object0));
        }

        @Override  // android.util.Property
        public void set(Object object0, Object object1) {
            this.b(((b)object0), ((Float)object1));
        }
    }

    class b extends View implements f, com.jozein.xedgepro.xposed.b0.b {
        private final float A;
        private final float B;
        private final float C;
        private final float D;
        private final float E;
        private final float F;
        private final float G;
        private final float H;
        private final float I;
        private final float J;
        private float K;
        private float L;
        private float M;
        private final int[] N;
        private int O;
        private boolean P;
        private final float Q;
        private float R;
        final d3 S;
        private final float z;

        public b(int[] arr_v, float f, float f1, float f2, float f3, int v, boolean z) {
            super(d30.A);
            this.K = 0.0f;
            this.L = 0.0f;
            this.M = 0.0f;
            this.O = 0;
            this.P = false;
            this.R = 0.0f;
            float f4 = d30.C.g2().N();
            this.z = 0.3f * f4;
            float f5 = 0.01f * f4 / 2.0f;
            this.A = 0.025f * f4;
            this.B = f4 * 0.018f;
            this.Q = f4 * 0.072f;
            d30.D.setStrokeWidth(2.0f * f5);
            d30.E.setStrokeWidth(f5);
            d30.G.setStrokeWidth(0.025f * f4);
            d30.H.setStrokeWidth(2.0f * f5);
            this.N = arr_v;
            this.C = f - 0.1f;
            this.D = f1 - 0.1f;
            this.E = f2;
            this.F = f3;
            this.G = ((float)v) / 100.0f;
            this.H = 0.3f * f4 * (((float)v) / 100.0f);
            this.I = z ? -f5 : f5;
            this.J = f5;
            this.setKeepScreenOn(true);
        }

        @Override  // com.jozein.xedgepro.xposed.b0$b
        public void a(float f, float f1) {
            d3.this.B.post(() -> {
                this.K = f;
                this.L = f1;
                this.invalidate();
            });
        }

        private void f(Canvas canvas0, float f, float f1, float f2, float f3, Paint paint0) {
            float f4 = this.A;
            float f5 = f4 * 3.0f;
            canvas0.drawLine(f, f1, f2, f3, paint0);
            canvas0.drawCircle(f, f1, this.M, paint0);
            switch(this.N[this.O]) {
                case 1: {
                    canvas0.drawCircle(f2, f3, f4, paint0);
                    break;
                }
                case 2: {
                    f4 *= 2.0f;
                    canvas0.drawCircle(f2, f3, f4, paint0);
                    break;
                }
                case 3: {
                    canvas0.drawCircle(f2, f3, f4, paint0);
                    f4 *= 2.0f;
                    canvas0.drawCircle(f2, f3, f4, paint0);
                    break;
                }
                case 4: {
                    canvas0.drawCircle(f2, f3, 2.0f * f4, paint0);
                    break;
                }
                default: {
                    canvas0.drawCircle(f2, f3, f4, paint0);
                    return;
                }
            }
            canvas0.drawLine(f2 - f5, f3, f2 - f4, f3, paint0);
            canvas0.drawLine(f2 + f4, f3, f2 + f5, f3, paint0);
            canvas0.drawLine(f2, f3 - f5, f2, f3 - f4, paint0);
            canvas0.drawLine(f2, f3 + f4, f2, f3 + f5, paint0);
        }

        // 检测为 Lambda 实现
        private void g(float f, float f1) [...]

        void h(float f) {
            this.R = f;
            this.invalidate();
        }

        public void i() {
            long v;
            switch(this.N[this.O]) {
                case 1: {
                    d3.this.C.W1().h0(this.K, this.L);
                    v = 300L;
                    break;
                }
                case 2: {
                    d3.this.C.W1().d0(this.K, this.L);
                    v = 600L;
                    break;
                }
                case 3: {
                    d3.this.C.W1().X(this.K, this.L);
                    v = 450L;
                    break;
                }
                case 4: {
                    d3.this.C.W1().V().P(this.K, this.L, this);
                    this.R = 1.0f;
                    this.invalidate();
                    d3.this.C.w4(3000L);
                    return;
                }
                default: {
                    d3.x(0L);
                    return;
                }
            }
            d3.x(((long)(((float)v) * Settings.Global.getFloat(this.getContext().getContentResolver(), "animator_duration_scale", 1.0f))));
            ObjectAnimator objectAnimator0 = ObjectAnimator.ofFloat(this, d3.K, new float[]{0.0f, 1.0f});
            objectAnimator0.setDuration(v);
            objectAnimator0.start();
        }

        public void j(float f, float f1) {
            boolean z = this.M >= this.H;
            float f2 = (f - this.E) * this.G;
            float f3 = (f1 - this.F) * this.G;
            float f4 = (float)Math.sqrt(f2 * f2 + f3 * f3);
            this.M = f4;
            float f5 = this.E + f2;
            this.K = f5;
            float f6 = this.F + f3;
            this.L = f6;
            if(f5 < 0.0f) {
                this.K = 0.0f;
            }
            else {
                float f7 = this.C;
                if(f5 > f7) {
                    this.K = f7;
                }
            }
            if(f6 >= 0.0f) {
                float f8 = this.D;
                if(f6 > f8) {
                    this.L = f8;
                }
            }
            else {
                this.L = 0.0f;
            }
            if(!z && f4 >= this.H) {
                if(this.P) {
                    int v = this.O + 1;
                    this.O = v;
                    if(v >= this.N.length || this.N[v] <= 0) {
                        this.O = 0;
                    }
                }
                else {
                    this.P = true;
                }
            }
            this.invalidate();
        }

        @Override  // android.view.View
        protected void onDraw(Canvas canvas0) {
            Paint paint0;
            float f6;
            float f5;
            float f4;
            float f3;
            float f2;
            float f1;
            float f = this.R;
            if(f > 0.0f) {
                switch(this.N[this.O]) {
                    case 1: {
                        f1 = this.K;
                        f2 = this.L;
                        f3 = this.B * f * 4.0f;
                        canvas0.drawCircle(f1, f2, f3, d3.this.G);
                        return;
                    }
                    case 2: {
                        f1 = this.K;
                        f2 = this.L;
                        f3 = this.B * (f * 4.0f + 1.0f);
                        canvas0.drawCircle(f1, f2, f3, d3.this.G);
                        return;
                    }
                    case 3: {
                        canvas0.drawCircle(this.K, this.L, this.B * f * 4.0f / 2.0f, d3.this.G);
                        f4 = this.K;
                        f5 = this.L;
                        f6 = this.B * this.R * 4.0f;
                        paint0 = d3.this.H;
                        break;
                    }
                    case 4: {
                        canvas0.drawCircle(this.K, this.L, this.Q, d3.this.G);
                        f4 = this.K;
                        f5 = this.L;
                        f6 = this.Q;
                        paint0 = d3.this.D;
                        break;
                    }
                    default: {
                        return;
                    }
                }
                canvas0.drawCircle(f4, f5, f6, paint0);
                return;
            }
            canvas0.drawCircle(this.E, this.F, this.z, d3.this.F);
            canvas0.drawCircle(this.E, this.F, this.z, d3.this.D);
            if(this.M >= this.H) {
                this.f(canvas0, this.E + this.I, this.F + this.J, this.K + this.I, this.L + this.J, d3.this.E);
                this.f(canvas0, this.E, this.F, this.K, this.L, d3.this.D);
            }
        }
    }

    private final Context A;
    private final Handler B;
    private final w1 C;
    private final Paint D;
    private final Paint E;
    private final Paint F;
    private final Paint G;
    private final Paint H;
    private b I;
    private int J;
    private static final Property K;
    private final p z;

    static {
        d3.K = new com.jozein.xedgepro.xposed.d3.a(Float.class, "setAnimateProgress");
    }

    d3(w1 w10) {
        this.I = null;
        this.J = 0;
        this.z = w10.b2();
        this.A = w10.T1();
        this.B = w10.U1();
        this.C = w10;
        Paint paint0 = new Paint(1);
        this.D = paint0;
        paint0.setStyle(Paint.Style.STROKE);
        Paint paint1 = new Paint(1);
        this.E = paint1;
        paint1.setStyle(Paint.Style.STROKE);
        paint1.setColor(0x7FCCCCCC);
        Paint paint2 = new Paint(1);
        this.F = paint2;
        paint2.setStyle(Paint.Style.FILL);
        paint2.setColor(0x7FCCCCCC);
        this.G = paint2;
        Paint paint3 = new Paint(paint2);
        this.H = paint3;
        paint3.setStyle(Paint.Style.STROKE);
    }

    @Override  // com.jozein.xedgepro.xposed.b0$a
    public void b(float f, float f1, long v) {
        a3 a30 = () -> {
            b d3$b0 = this.I;
            this.I = null;
            if(d3$b0 != null) {
                d3$b0.j(f, f1);
                if(d3$b0.M >= d3$b0.H) {
                    d3$b0.i();
                    return;
                }
                d3.x(0L);
            }
        };
        this.B.post(a30);
    }

    @Override  // com.jozein.xedgepro.xposed.b0$a
    public void c() {
        z2 z20 = () -> {
            b d3$b0 = this.I;
            this.I = null;
            if(d3$b0 != null) {
                d3.x(0L);
            }
        };
        this.B.post(z20);
    }

    @Override  // com.jozein.xedgepro.xposed.b0$a
    public void d(float f, float f1, long v) {
        b3 b30 = () -> {
            b d3$b0 = this.I;
            if(d3$b0 != null) {
                d3$b0.j(f, f1);
            }
        };
        this.B.post(b30);
    }

    @Override  // com.jozein.xedgepro.xposed.b0$a
    public void e(int v, float f, float f1, long v1) {
        c3 c30 = () -> {
            int v1 = this.z.v(16, -14509620);
            this.D.setColor(v1);
            Point point0 = this.C.g2().I(new Point());
            this.I = new b(this, (this.J == 0 ? this.z.z() : new int[]{this.J}), ((float)point0.x), ((float)point0.y), f, f1, this.z.v(15, 0xFA), v == 1);
            t3.n().j(this.I, 7);
        };
        this.B.post(c30);
    }

    void s() {
        if(this.I != null) {
            this.c();
        }
    }

    // 检测为 Lambda 实现
    private void t() [...]

    // 检测为 Lambda 实现
    private void u(float f, float f1) [...]

    // 检测为 Lambda 实现
    private void v(float f, float f1, int v) [...]

    // 检测为 Lambda 实现
    private void w(float f, float f1) [...]

    private static void x(long v) {
        t3.n().u(7, v);
    }

    public d3 y(int v) {
        this.J = v;
        return this;
    }
}


package f;

import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class d {
    static class a extends Thread {
        private static final AtomicInteger z;

        static {
            a.z = new AtomicInteger(1);
        }

        a(Runnable runnable0) {
            super(runnable0, "Async #" + a.z.getAndIncrement());
        }

        a(Runnable runnable0, String s) {
            super(runnable0, s);
        }

        @Override
        public void run() {
            try {
                super.run();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    private static volatile ExecutorService a;

    static {
    }

    public static void b(Runnable runnable0) {
        d.c().execute(runnable0);
    }

    public static Executor c() {
        if(d.a == null) {
            Class class0 = d.class;
            synchronized(class0) {
                if(d.a == null) {
                    d.a = new ThreadPoolExecutor(0, 0x7FFFFFFF, 60L, TimeUnit.SECONDS, new SynchronousQueue(), (Runnable runnable0) -> new a(runnable0));
                }
                return d.a;
            }
        }
        return d.a;
    }

    // 检测为 Lambda 实现
    private static Thread d(String s, Runnable runnable0) [...]

    public static ExecutorService e(String s) {
        return Executors.newSingleThreadExecutor((Runnable runnable0) -> new a(runnable0, s));
    }

    public static void f(long v) {
        try {
            Thread.sleep(v);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }
}


package d;

import a.b.j2;
import android.os.Bundle;
import android.view.View;
import e.j.g;
import e.j;

public class q1 extends j {
    private j2 M;

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F0601A9);  // string:select_icon "Select icon"
    }

    @Override  // e.j
    protected int B0() {
        j2 b$j20 = new j2();
        this.M = b$j20;
        return b$j20.a();
    }

    @Override  // e.j
    protected View h1(int v) {
        return new g(this, this.M.b(v), this.M.d(v), null);
    }

    @Override  // e.j
    protected void k1(int v) {
        Bundle bundle0 = new Bundle(1);
        bundle0.putString("result", this.M.d(v));
        this.U(bundle0);
        this.L();
    }
}


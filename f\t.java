package f;

public final class t {
    public int a;

    public t(int v) {
        this.a = v;
    }

    @Override
    public boolean equals(Object object0) {
        if(this == object0) {
            return true;
        }
        if(object0 != null) {
            Class class0 = object0.getClass();
            return t.class == class0 && this.a == ((t)object0).a;
        }
        return false;
    }

    @Override
    public int hashCode() {
        return this.a;
    }

    @Override
    public String toString() {
        return Integer.toString(this.a);
    }
}


package d;

import a.b.y0;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Point;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import e.r;
import g.a0;
import g.q;
import java.util.List;

public class e2 extends r {
    static class a extends g.r implements Runnable {
        private final List L;
        private final int M;
        private final float N;
        private final float O;
        private int P;

        public a(Context context0, y0 b$y00) {
            super(context0, 0xFF0097A7);
            float f1;
            boolean z = false;
            this.P = 0;
            this.l(0, 0, false);
            List list0 = b$y00.D();
            this.L = list0;
            this.M = list0 == null ? 0 : list0.size();
            boolean z1 = b$y00.F();
            if(this.getResources().getConfiguration().orientation == 2) {
                z = true;
            }
            Point point0 = a0.n(context0, new Point());
            float f = 0.0f;
            if(z1 == z) {
                f1 = 0.0f;
            }
            else {
                f1 = z ? -90.0f : 90.0f;
            }
            this.N = f1;
            if(z1 != z) {
                f = ((float)(z ? point0.y : point0.x)) / 2.0f;
            }
            this.O = f;
            this.postDelayed(this, 1000L);
        }

        @Override  // g.r
        protected void onDraw(Canvas canvas0) {
            if(this.N == 0.0f) {
                super.onDraw(canvas0);
                return;
            }
            canvas0.save();
            canvas0.rotate(this.N, this.O, this.O);
            super.onDraw(canvas0);
            canvas0.restore();
        }

        @Override  // g.r
        public boolean onTouchEvent(MotionEvent motionEvent0) {
            if(motionEvent0.getActionMasked() == 0) {
                this.removeCallbacks(this);
                for(int v = this.P; v < this.M; ++v) {
                    this.e(((q)this.L.get(v)));
                }
                this.invalidate();
            }
            return true;
        }

        @Override
        public void run() {
            int v = this.P;
            if(v < this.M) {
                q q0 = (q)this.L.get(v);
                this.e(q0);
                int v1 = this.P + 1;
                this.P = v1;
                q q1 = v1 >= this.M ? null : ((q)this.L.get(v1));
                if(q1 != null) {
                    this.postDelayed(this, ((long)(q1.z - q0.z)));
                }
                this.invalidate();
            }
        }
    }

    public e2 k0(y0 b$y00) {
        this.h().putParcelable("data", b$y00);
        return this;
    }

    @Override  // android.app.Fragment
    public View onCreateView(LayoutInflater layoutInflater0, ViewGroup viewGroup0, Bundle bundle0) {
        Context context0 = viewGroup0.getContext();
        y0 b$y00 = (y0)this.h().getParcelable("data");
        return b$y00 != null ? new a(viewGroup0.getContext(), b$y00) : new View(context0);
    }
}


package c;

import a.b.s0;
import android.app.Activity;
import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.widget.TextView;
import com.jozein.xedgepro.ApplicationMain;
import e.j0.b;
import f.m;
import f.p0;
import g.x;

public class l extends b {
    private BroadcastReceiver C;
    private TextView D;

    public l() {
        this.C = null;
        this.D = null;
    }

    private void B() {
        s0.B(this.c(), 5);
        if(this.C != null) {
            this.getActivity().unregisterReceiver(this.C);
            this.C = null;
        }
    }

    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        class a implements DialogInterface.OnClickListener {
            final l z;

            @Override  // android.content.DialogInterface$OnClickListener
            public void onClick(DialogInterface dialogInterface0, int v) {
                Bundle bundle0 = l.this.e();
                l.this.o(bundle0);
            }
        }

        Activity activity0 = this.getActivity();
        TextView textView0 = new TextView(activity0);
        this.D = textView0;
        textView0.setText((ApplicationMain.isModuleActivated() ? 0x7F060092 : 0x7F060155));  // string:click_a_key_to_add "Click a key which you want to add."
        x x0 = this.g();
        this.D.setTextSize(0, ((float)x0.c));
        this.D.setPadding(x0.f, x0.f, x0.f, x0.f);
        return new AlertDialog.Builder(activity0).setTitle(0x7F060069).setView(this.D).setPositiveButton(0x104000A, new a(this)).setNegativeButton(0x1040000, b.B).create();  // string:add_key "Add a new key…"
    }

    @Override  // android.app.Fragment
    public void onPause() {
        super.onPause();
        if(ApplicationMain.isModuleActivated()) {
            this.B();
        }
    }

    @Override  // android.app.Fragment
    public void onResume() {
        class c.l.b extends BroadcastReceiver {
            final l a;

            @Override  // android.content.BroadcastReceiver
            public void onReceive(Context context0, Intent intent0) {
                m m0 = new m(intent0);
                if(m0.i()) {
                    int v = m0.h();
                    if(v > 0) {
                        String s = p0.k(l.this.getActivity(), v) + '\n' + l.this.k(0x7F060096) + v;  // string:code "Code: "
                        if(v == 26) {
                            s = s + "\n" + l.this.k(0x7F06015C);  // string:never_intercept_power_key "Never intercept power key!"
                            l.this.D.setTextColor(0xFFFF0000);
                        }
                        else {
                            l.this.e().putInt("result", v);
                        }
                        l.this.D.setText(s);
                    }
                }
                m0.k();
                l.this.B();
            }
        }

        super.onResume();
        if(ApplicationMain.isModuleActivated()) {
            this.C = new c.l.b(this);
            this.getActivity().registerReceiver(this.C, new IntentFilter(s0.L));
            s0.B(this.c(), 4);
        }
    }
}


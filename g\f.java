package g;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View.OnClickListener;
import android.view.View;
import android.view.ViewTreeObserver.OnDrawListener;
import android.widget.EditText;
import android.widget.FrameLayout.LayoutParams;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout.LayoutParams;
import android.widget.LinearLayout;
import android.widget.TextView;
import f.p0;
import f.v;

class f {
    class a extends LinearLayout {
        private ViewTreeObserver.OnDrawListener A;
        private final int B;
        final f C;
        private final FrameLayout.LayoutParams z;

        a(boolean z, int v, int v1) {
            super(f0.a);
            this.B = v1;
            FrameLayout.LayoutParams frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(-2, -2, 51);
            this.z = frameLayout$LayoutParams0;
            frameLayout$LayoutParams0.leftMargin = v;
            frameLayout$LayoutParams0.topMargin = -5000;
            this.setOrientation(!z);
            a0.w(this, f0.n, ((float)f0.f) / 2.0f);
            this.o();
            this.q();
            f0.b.addView(this, frameLayout$LayoutParams0);
            this.A = () -> try {
                if(Build.VERSION.SDK_INT < 23) {
                    f.this.d.invalidate();
                }
                this.r();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            };
            f0.c.getViewTreeObserver().addOnDrawListener(this.A);
            this.setOnClickListener((View view0) -> if(this.getOrientation() == 1) {
                this.n(true, -1);
            });
        }

        private View f(int v, View.OnClickListener view$OnClickListener0) {
            Context context0 = this.getContext();
            ImageView imageView0 = new ImageView(context0);
            Drawable drawable0 = context0.getResources().getDrawable(v);
            if(a0.r(f.this.e)) {
                h.a().d(drawable0);
            }
            else {
                h.a().b(drawable0);
            }
            imageView0.setImageDrawable(drawable0);
            View view0 = new FrameLayout(context0);
            int v1 = f.this.g;
            ((FrameLayout)view0).addView(imageView0, new FrameLayout.LayoutParams(v1 / 2, v1 / 2, 17));
            ((FrameLayout)view0).setBackground(a0.l());
            ((FrameLayout)view0).setOnClickListener(view$OnClickListener0);
            return view0;
        }

        private View g(MenuItem menuItem0) {
            View view0 = new TextView(this.getContext());
            ((TextView)view0).setTextSize(0, ((float)f.this.f));
            ((TextView)view0).setTextColor(f.this.e);
            ((TextView)view0).setTypeface(Typeface.defaultFromStyle(1));
            ((TextView)view0).setText(menuItem0.getTitle());
            ((TextView)view0).setGravity(16);
            ((TextView)view0).setPadding(f.this.h, 0, f.this.h, 0);
            ((TextView)view0).setBackground(a0.l());
            ((TextView)view0).setLayoutParams(f.this.i);
            ((TextView)view0).setOnClickListener((View view0) -> try {
                f.this.d.b().onActionItemClicked(f.this.d, menuItem0);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            });
            return view0;
        }

        void h() {
            try {
                f.this.c.getViewTreeObserver().removeOnDrawListener(this.A);
                this.A = null;
                f.this.b.removeView(this);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        // 检测为 Lambda 实现
        private void i(MenuItem menuItem0, View view0) [...]

        // 检测为 Lambda 实现
        private void j() [...]

        // 检测为 Lambda 实现
        private void k(View view0) [...]

        // 检测为 Lambda 实现
        private void l(View view0) [...]

        // 检测为 Lambda 实现
        private void m(View view0) [...]

        private void n(boolean z, int v) {
            this.h();
            a f$a0 = new a(f.this, z, this.z.leftMargin, v);
            f.this.q = f$a0;
        }

        void o() {
            try {
                this.removeAllViews();
                if(this.getOrientation() == 1) {
                    this.s();
                    return;
                }
                this.p();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        private void p() {
            Menu menu0 = f.this.d.getMenu();
            int v = menu0.size();
            int v2;
            for(v2 = 0; true; ++v2) {
                if(v2 >= v) {
                    v2 = v;
                    break;
                }
                if(menu0.getItem(v2).getItemId() >= 0x10000101) {
                    break;
                }
            }
            int v3 = Math.min(v2, f.this.m);
            for(int v1 = 0; v1 < v3; ++v1) {
                this.addView(this.g(menu0.getItem(v1)), f.this.i);
            }
            f.this.s = v3;
            if(v > v3) {
                this.addView(this.f(0x7F04007B, (View view0) -> this.n(false, (view0.getLeft() >= this.getLeft() / 2 ? this.getRight() : 0))), f.this.k);
            }
        }

        private boolean q() {
            int v12;
            if(f.this.r) {
                return false;
            }
            int v = this.z.topMargin;
            int v1 = this.z.leftMargin;
            int v2 = this.getHeight();
            int v3 = this.getWidth();
            Rect rect0 = f.this.o;
            int[] arr_v = f.this.p;
            f.this.c.getFocusedRect(rect0);
            f.this.c.getLocationInWindow(arr_v);
            int v4 = rect0.top - f.this.c.getScrollY();
            int v5 = rect0.bottom - f.this.c.getScrollY();
            if(v3 > 0 && v4 <= f.this.c.getHeight() - f.this.c.getCompoundPaddingBottom() && v5 >= f.this.c.getCompoundPaddingTop()) {
                if(!f.this.r) {
                    this.setVisibility(0);
                }
                if(v4 < 0) {
                    v4 = 0;
                }
                if(rect0.left < 0) {
                    rect0.left = 0;
                }
                int v6 = f.this.h;
                int v7 = f.this.c.getHeight() + arr_v[1];
                if(this.getOrientation() == 0) {
                    int v8 = arr_v[0];
                    int v9 = rect0.centerX();
                    int v10 = f.this.b.getWidth();
                    this.z.leftMargin = p0.e(v8 + v9 - v3 / 2, f.this.h, v10 - v3 - f.this.h);
                }
                else {
                    int v11 = this.B;
                    if(v11 > 0) {
                        this.z.leftMargin = v11 - v3;
                    }
                }
                if(this.z.leftMargin > rect0.right + arr_v[0] + f.this.h || this.z.leftMargin + v3 < rect0.left + arr_v[0] - f.this.h) {
                    v12 = v4 + arr_v[1] - f.this.g - v6 / 2;
                    if(v12 + v2 > v7 - v6 / 2) {
                        v12 = v7 - v2 - v6 / 2;
                    }
                    else if(v12 < f.this.l) {
                        v12 = f.this.l + v6 / 2;
                    }
                }
                else {
                    v12 = v4 + arr_v[1] - v2 - v6 / 2;
                    if(v12 < f.this.l) {
                        v12 = arr_v[1] + v5 + f.this.h;
                        if(v2 + v12 > v7 - v6 / 2) {
                            v12 = f.this.l + v6 / 2;
                        }
                    }
                }
                this.z.topMargin = v12;
                return v != v12 || v1 != this.z.leftMargin;
            }
            this.setVisibility(4);
            return false;
        }

        void r() {
            try {
                if(this.q()) {
                    this.setLayoutParams(this.z);
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        private void s() {
            Menu menu0 = f.this.d.getMenu();
            int v = menu0.size();
            boolean z = false;
            int v1 = f.this.g;
            if(v - f.this.s > 1) {
                z = true;
            }
            int v2 = f.this.s;
            View view0 = null;
            while(v2 < v) {
                View view1 = this.g(menu0.getItem(v2));
                if(z) {
                    view1.measure(0, 0);
                    int v3 = view1.getMeasuredWidth();
                    if(v3 > v1) {
                        view0 = view1;
                        v1 = v3;
                    }
                }
                this.addView(view1, f.this.i);
                ++v2;
            }
            if(z) {
                for(int v4 = this.getChildCount() - 1; v4 >= 0; --v4) {
                    View view2 = this.getChildAt(v4);
                    if(view2 != view0) {
                        view2.setLayoutParams(f.this.j);
                    }
                }
            }
            this.addView(this.f(0x7F04003F, (View view0) -> this.n(true, -1)), f.this.k);
        }
    }

    private final Context a;
    private final FrameLayout b;
    private final EditText c;
    private final g.v d;
    private final int e;
    private final int f;
    private final int g;
    private final int h;
    private final LinearLayout.LayoutParams i;
    private final LinearLayout.LayoutParams j;
    private final LinearLayout.LayoutParams k;
    private final int l;
    private final int m;
    private final int n;
    private final Rect o;
    private final int[] p;
    private a q;
    private boolean r;
    private int s;

    f(FrameLayout frameLayout0, EditText editText0, g.v v0, int v1) {
        this.o = new Rect();
        this.p = new int[2];
        this.r = false;
        int v2 = 4;
        this.s = 4;
        Context context0 = frameLayout0.getContext();
        this.a = context0;
        this.b = frameLayout0;
        this.c = editText0;
        this.d = v0;
        if(a0.r(v1)) {
            this.n = a0.c(v1, 0x20);
            this.e = -1;
        }
        else {
            this.n = a0.c(v1, 0xFFFFFFE0);
            this.e = 0xFF000000;
        }
        x x0 = x.c(context0);
        this.f = x0.e;
        this.h = x0.g;
        int v3 = x0.g * 2 + x0.e;
        this.g = v3;
        this.i = new LinearLayout.LayoutParams(-2, v3);
        this.j = new LinearLayout.LayoutParams(-1, v3);
        this.k = new LinearLayout.LayoutParams(v3, v3);
        this.l = a0.q();
        if(frameLayout0.getWidth() / x0.e >= 33) {
            v2 = 5;
        }
        this.m = v2;
        this.q = new a(this, true, 0, -1);
    }

    void u() {
        this.q.h();
    }

    void v(boolean z) {
        this.r = z;
        this.q.setVisibility((z ? 4 : 0));
    }

    void w(boolean z) {
        if(z) {
            this.q.o();
        }
    }
}


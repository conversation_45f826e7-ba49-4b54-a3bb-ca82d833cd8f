package com.jozein.xedgepro.xposed;

import android.view.View;

public final class k2 implements Runnable {
    public final View A;
    public final int B;
    public final int C;
    public final l2 z;

    public k2(l2 l20, View view0, int v, int v1) {
        this.z = l20;
        this.A = view0;
        this.B = v;
        this.C = v1;
    }

    @Override
    public final void run() {
        this.z.n(this.A, this.B, this.C);
    }
}


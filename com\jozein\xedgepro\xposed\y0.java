package com.jozein.xedgepro.xposed;

import a.p;
import android.content.Context;
import android.hardware.input.InputManager;
import android.os.Build.VERSION;
import android.os.Handler;
import android.os.SystemClock;
import android.view.InputEvent;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.MotionEvent.PointerCoords;
import android.view.MotionEvent.PointerProperties;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import android.view.accessibility.AccessibilityManager.AccessibilityStateChangeListener;
import android.view.accessibility.AccessibilityManager;
import de.robv.android.xposed.XC_MethodHook.MethodHookParam;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedHelpers;
import f.h0;
import f.l;
import f.v;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.concurrent.ExecutorService;

class y0 extends j2 {
    static class c {
        Object a;
        Method b;

        c(Object object0) {
            Class class0 = XposedHelpers.findClass("com.android.server.input.InputManagerService$InputFilterHost", object0.getClass().getClassLoader());
            Constructor constructor0 = class0.getDeclaredConstructors()[0];
            constructor0.setAccessible(true);
            this.a = constructor0.newInstance(object0);
            this.b = class0.getMethod("sendInputEvent", InputEvent.class, Integer.TYPE);
        }

        void a(InputEvent inputEvent0, int v) {
            this.b.invoke(this.a, inputEvent0, v);
        }
    }

    abstract class d extends XC_MethodHook {
        final y0 a;

        private d() {
        }

        d(a y0$a0) {
        }

        d a(ClassLoader classLoader0) {
            class com.jozein.xedgepro.xposed.y0.d.a extends XC_MethodHook {
                final d a;

                protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                    try {
                        boolean z = xC_MethodHook$MethodHookParam0.args[0] != null;
                        if(!y0.this.Q) {
                            y0.this.N = z;
                            if(!z) {
                                y0.this.P();
                            }
                        }
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }

            if(Build.VERSION.SDK_INT >= 26) {
                try {
                    com.jozein.xedgepro.xposed.y0.d.a y0$d$a0 = new com.jozein.xedgepro.xposed.y0.d.a(this);
                    y0.this.o("setInputFilter", y0$d$a0);
                    return this;
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
            return this;
        }

        public abstract int b(InputEvent arg1, int arg2, int arg3);

        protected void beforeHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
            boolean z = true;
            y0.this.Q = true;
            try {
                int v = xC_MethodHook$MethodHookParam0.args.length - 1;
                boolean z1 = ((Boolean)xC_MethodHook$MethodHookParam0.args[v]).booleanValue();
                if(!this.c()) {
                    y0.this.N = z1;
                }
                if(y0.this.O) {
                    if(!z1 && !y0.this.B0()) {
                        z = false;
                    }
                    xC_MethodHook$MethodHookParam0.args[v] = Boolean.valueOf(z);
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        boolean c() {
            StackTraceElement[] arr_stackTraceElement = new Throwable().getStackTrace();
            for(int v = 2; v < arr_stackTraceElement.length; ++v) {
                if(arr_stackTraceElement[v].getClassName().startsWith(l.v)) {
                    return true;
                }
            }
            return false;
        }

        public abstract void d(boolean arg1);
    }

    class e extends d {
        private Method b;
        private Object c;
        final y0 d;

        private e() {
            super(null);
        }

        e(a y0$a0) {
        }

        @Override  // com.jozein.xedgepro.xposed.y0$d
        d a(ClassLoader classLoader0) {
            this.b = y0.this.i("nativeInjectInputEvent");
            try {
                y0.this.o("nativeSetInputFilterEnabled", this);
                return super.a(classLoader0);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return super.a(classLoader0);
            }
        }

        @Override  // com.jozein.xedgepro.xposed.y0$d
        public int b(InputEvent inputEvent0, int v, int v1) {
            int v2 = Build.VERSION.SDK_INT;
            if(v2 >= 29) {
                return (int)(((Integer)this.b.invoke(null, this.e(), inputEvent0, 0, 0, v, 5000, v1)));
            }
            return v2 < 21 ? ((int)(((Integer)this.b.invoke(null, this.e(), inputEvent0, 0, 0, v, 5000, v1)))) : ((int)(((Integer)this.b.invoke(null, this.e(), inputEvent0, 0, 0, 0, v, 5000, v1))));
        }

        @Override  // com.jozein.xedgepro.xposed.y0$d
        public void d(boolean z) {
            try {
                Object[] arr_object = {this.e(), Boolean.valueOf(z)};
                y0.this.h("nativeSetInputFilterEnabled", arr_object);
            }
            catch(Throwable throwable0) {
                p2.g(throwable0);
            }
        }

        private Object e() {
            if(this.c == null) {
                this.c = y0.this.u("mPtr");
            }
            return this.c;
        }
    }

    class f extends d {
        private Object b;
        private Method c;
        final y0 d;

        private f() {
            super(null);
        }

        f(a y0$a0) {
        }

        @Override  // com.jozein.xedgepro.xposed.y0$d
        d a(ClassLoader classLoader0) {
            Class class0 = this.e(classLoader0);
            Class[] arr_class = new Class[6];
            arr_class[0] = InputEvent.class;
            Class class1 = Boolean.TYPE;
            arr_class[1] = class1;
            arr_class[2] = Integer.TYPE;
            arr_class[3] = Integer.TYPE;
            arr_class[4] = Integer.TYPE;
            arr_class[5] = Integer.TYPE;
            this.c = XposedHelpers.findMethodExact(class0, "injectInputEvent", arr_class);
            try {
                XposedHelpers.findAndHookMethod(class0, "setInputFilterEnabled", new Object[]{class1, this});
                return super.a(classLoader0);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return super.a(classLoader0);
            }
        }

        @Override  // com.jozein.xedgepro.xposed.y0$d
        public int b(InputEvent inputEvent0, int v, int v1) {
            return (int)(((Integer)this.c.invoke(this.f(), inputEvent0, Boolean.FALSE, -1, v, 5000, v1)));
        }

        @Override  // com.jozein.xedgepro.xposed.y0$d
        public void d(boolean z) {
            try {
                XposedHelpers.callMethod(this.f(), "setInputFilterEnabled", new Object[]{Boolean.valueOf(z)});
            }
            catch(Throwable throwable0) {
                p2.g(throwable0);
            }
        }

        private Class e(ClassLoader classLoader0) {
            try {
                return XposedHelpers.findClass((XposedHelpers.findField(y0.this.m(), "mNative").getType().getName() + "$NativeImpl"), classLoader0);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return XposedHelpers.findClass("com.android.server.input.NativeInputManagerService$NativeImpl", classLoader0);
            }
        }

        private Object f() {
            if(this.b == null) {
                this.b = y0.this.u("mNative");
            }
            return this.b;
        }
    }

    private final w1 H;
    private final p I;
    private Handler J;
    private a0 K;
    private b0 L;
    private d M;
    private volatile boolean N;
    private boolean O;
    private c P;
    private volatile boolean Q;
    private boolean R;
    private Method S;
    private boolean T;
    private volatile boolean U;
    private g0 V;
    private g0 W;
    private Method X;
    private boolean Y;
    private int Z;
    private ExecutorService a0;
    private final MotionEvent.PointerProperties[] b0;
    private final MotionEvent.PointerCoords[] c0;
    private volatile Handler d0;
    private final MotionEvent.PointerProperties[] e0;
    private final MotionEvent.PointerCoords[] f0;
    private static final boolean g0;
    private static final boolean h0;

    static {
        y0.g0 = Build.VERSION.SDK_INT >= 29;
        y0.h0 = Build.VERSION.SDK_INT >= 29;
    }

    y0(w1 w10, p p0, ClassLoader classLoader0) {
        class a extends XC_MethodHook {
            final y0 a;

            a(int v) {
                super(v);
            }

            protected void beforeHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                if(!y0.this.O) {
                    y0.this.A(xC_MethodHook$MethodHookParam0.thisObject);
                    return;
                }
                try {
                    Object object0 = xC_MethodHook$MethodHookParam0.args[0];
                    if(object0 instanceof KeyEvent) {
                        y0.this.T(xC_MethodHook$MethodHookParam0, ((KeyEvent)object0));
                        return;
                    }
                    if(object0 instanceof MotionEvent) {
                        y0.this.U(xC_MethodHook$MethodHookParam0, ((MotionEvent)object0));
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        super("com.android.server.input.InputManagerService", classLoader0);
        this.K = null;
        this.L = null;
        this.M = null;
        this.N = false;
        this.O = false;
        this.P = null;
        this.Q = false;
        this.R = false;
        this.S = null;
        this.T = true;
        this.U = false;
        this.V = null;
        this.W = null;
        this.X = null;
        this.Y = true;
        this.Z = -1;
        this.a0 = null;
        this.b0 = new MotionEvent.PointerProperties[]{new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties()};
        this.c0 = new MotionEvent.PointerCoords[]{new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords()};
        this.d0 = null;
        this.e0 = new MotionEvent.PointerProperties[]{new MotionEvent.PointerProperties()};
        this.f0 = new MotionEvent.PointerCoords[]{new MotionEvent.PointerCoords()};
        this.H = w10;
        this.I = p0;
        try {
            this.p("filterInputEvent", new Object[]{InputEvent.class, Integer.TYPE, new a(this, (l.r ? 10000 : 50))});
            this.M = Build.VERSION.SDK_INT < 33 ? new e(this, null).a(classLoader0) : new f(this, null).a(classLoader0);
        }
        catch(Throwable throwable0) {
            p2.g(throwable0);
        }
    }

    void A0(boolean z) {
        this.U = z;
    }

    // 去混淆评级： 低(40)
    private boolean B0() {
        return this.N || !this.R || this.I.p(1) || this.I.p(0);
    }

    void C0() {
        if(this.R) {
            this.z0(this.B0());
        }
    }

    private void P() {
        if(this.B0()) {
            this.z0(true);
        }
    }

    void Q(Runnable runnable0) {
        if(this.a0 == null) {
            synchronized(this) {
                if(this.a0 == null) {
                    this.a0 = f.d.e("Async #key");
                }
            }
        }
        this.a0.execute(runnable0);
    }

    void R(Runnable runnable0) {
        this.W().post(runnable0);
    }

    private void S(boolean z) {
        this.U = false;
        g0 g00 = this.W;
        if(g00 != null) {
            g00.b(z);
            this.W = null;
        }
    }

    void T(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, KeyEvent keyEvent0) {
        a0 a00 = this.K;
        if(a00 == null) {
            return;
        }
        if(a00.j(xC_MethodHook$MethodHookParam0, keyEvent0)) {
            xC_MethodHook$MethodHookParam0.setResult(Boolean.FALSE);
            this.H.p5();
        }
    }

    void U(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, MotionEvent motionEvent0) {
        g0 g00;
        if(this.L == null) {
            return;
        }
        if(motionEvent0.getDownTime() == 0L) {
            return;
        }
        if(y0.h0) {
            try {
                if(this.Y) {
                    this.Y = false;
                    this.X = XposedHelpers.findMethodExact(MotionEvent.class, "getDisplayId", new Class[0]);
                }
                Method method0 = this.X;
                if(method0 != null) {
                    int v = (int)(((Integer)method0.invoke(motionEvent0)));
                    goto label_15;
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            goto label_17;
        label_15:
            if(v != -1 && v != 0) {
                return;
            }
        }
    label_17:
        switch(motionEvent0.getActionMasked()) {
            case 0: {
                int v1 = motionEvent0.getDeviceId();
                int v2 = motionEvent0.getButtonState();
                if(this.Z >= 0 && v1 != this.Z || v2 != 0 && v2 != 1) {
                    this.Z = -1;
                    this.L.p();
                    this.S(false);
                    return;
                }
                this.Z = v1;
                if(this.L.k(xC_MethodHook$MethodHookParam0, motionEvent0)) {
                    xC_MethodHook$MethodHookParam0.setResult(Boolean.FALSE);
                    this.H.p5();
                }
                if(!this.U) {
                    this.S(false);
                    return;
                }
                if(this.V == null) {
                    this.V = new g0(this.H);
                }
                g00 = this.V;
                this.W = g00;
                g00.a(motionEvent0);
                return;
            }
            case 1: {
                if(this.Z == motionEvent0.getDeviceId()) {
                    this.Z = -1;
                    if(this.L.k(xC_MethodHook$MethodHookParam0, motionEvent0)) {
                        xC_MethodHook$MethodHookParam0.setResult(Boolean.FALSE);
                        this.H.p5();
                    }
                    g0 g01 = this.W;
                    if(g01 != null) {
                        g01.a(motionEvent0);
                        if(!this.U) {
                            this.S(false);
                            return;
                        }
                    }
                }
                break;
            }
            case 3: {
                if(this.Z == motionEvent0.getDeviceId()) {
                    this.Z = -1;
                    if(this.L.k(xC_MethodHook$MethodHookParam0, motionEvent0)) {
                        xC_MethodHook$MethodHookParam0.setResult(Boolean.FALSE);
                        this.H.p5();
                    }
                    this.S(true);
                    return;
                }
                break;
            }
            case 2: 
            case 5: 
            case 6: {
                if(this.Z == motionEvent0.getDeviceId()) {
                    if(this.L.k(xC_MethodHook$MethodHookParam0, motionEvent0)) {
                        xC_MethodHook$MethodHookParam0.setResult(Boolean.FALSE);
                        this.H.p5();
                    }
                    g00 = this.W;
                    if(g00 != null) {
                        g00.a(motionEvent0);
                        return;
                    }
                }
                break;
            }
        }
    }

    b0 V() {
        return this.L;
    }

    Handler W() {
        if(this.d0 == null) {
            synchronized(this) {
                if(this.d0 == null) {
                    this.d0 = h0.e("Async #gesture");
                }
                return this.d0;
            }
        }
        return this.d0;
    }

    void X(float f, float f1) {
        this.R(() -> try {
            long v = SystemClock.uptimeMillis();
            this.f0(this.t0(v, v, 0, f, f1));
            this.f0(this.t0(v, v + 5L, 1, f, f1));
            f.d.f(60L);
            long v1 = SystemClock.uptimeMillis();
            this.f0(this.t0(v1, v1, 0, f, f1));
            this.f0(this.t0(v1, v1 + 5L, 1, f, f1));
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        });
    }

    void Y(int v) {
        this.Q(() -> try {
            long v1 = SystemClock.uptimeMillis();
            this.b0(new KeyEvent(v1, v1, 0, v, 0, 0, -1, 0, 6));
            this.b0(new KeyEvent(v1, v1 + 5L, 1, v, 0, 0, -1, 0, 6));
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        });
    }

    void Z(int v, int v1) {
        this.Q(() -> try {
            long v2 = SystemClock.uptimeMillis();
            this.b0(new KeyEvent(v2, v2, 0, v, 0, v1));
            this.b0(new KeyEvent(v2, v2 + 5L, 1, v, 0, v1));
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        });
    }

    void a0(int v, int v1) {
        this.Q(() -> try {
            long v2 = SystemClock.uptimeMillis();
            this.b0(new KeyEvent(v2, v2, 0, v, 0, v1));
            this.b0(new KeyEvent(v2, v2 + 5L, 1, v, 0, v1));
            f.d.f(60L);
            long v3 = SystemClock.uptimeMillis();
            this.b0(new KeyEvent(v3, v3, 0, v, 0, v1));
            this.b0(new KeyEvent(v3, v3 + 5L, 1, v, 0, v1));
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        });
    }

    private void b0(KeyEvent keyEvent0) {
        this.u0(keyEvent0, 2, 0x62000000);
    }

    void c0(int v, int v1) {
        this.Q(() -> try {
            long v2 = SystemClock.uptimeMillis();
            this.b0(new KeyEvent(v2, v2, 0, v, 0, v1));
            int v3 = ViewConfiguration.getLongPressTimeout();
            f.d.f(v3 + 300);
            this.b0(new KeyEvent(v2, v2 + ((long)(v3 + 300)), 1, v, 0, v1));
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        });
    }

    void d0(float f, float f1) {
        this.R(() -> try {
            long v = SystemClock.uptimeMillis();
            this.f0(this.t0(v, v, 0, f, f1));
            int v1 = ViewConfiguration.getLongPressTimeout();
            for(long v2 = 100L; v2 < ((long)(v1 + 300)); v2 += 100L) {
                this.f0(this.t0(v, v + v2, 2, f, f1));
            }
            this.f0(this.t0(v, v + ((long)(v1 + 300)), 1, f, f1));
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        });
    }

    void e0(MotionEvent motionEvent0) {
        this.u0(motionEvent0, 0, 0x66000000);
    }

    void f0(MotionEvent motionEvent0) {
        this.u0(motionEvent0, 2, 0x66000000);
    }

    void g0(float f, float f1, float f2) {
        long v = SystemClock.uptimeMillis();
        this.e0[0].id = 0;
        this.e0[0].toolType = 3;
        this.f0[0].x = f1;
        this.f0[0].y = f2;
        this.f0[0].setAxisValue(9, f);
        this.e0(MotionEvent.obtain(v, v, 8, 1, this.e0, this.f0, 0, 0, 0.0f, 0.0f, 0, 0, 0x2002, 0));
    }

    void h0(float f, float f1) {
        this.R(() -> try {
            long v = SystemClock.uptimeMillis();
            this.f0(this.t0(v, v, 0, f, f1));
            this.f0(this.t0(v, v + 5L, 1, f, f1));
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        });
    }

    boolean i0(String s) {
        if(s != null && s.length() != 0) {
            KeyEvent[] arr_keyEvent = KeyCharacterMap.load(-1).getEvents(s.toCharArray());
            if(arr_keyEvent != null) {
                this.Q(() -> try {
                label_1:
                    for(int v = 0; v < arr_keyEvent.length; ++v) {
                        this.b0(KeyEvent.changeTimeRepeat(arr_keyEvent[v], SystemClock.uptimeMillis(), 0));
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                    if(true) {
                        return;
                    }
                    goto label_1;
                });
                return true;
            }
            return false;
        }
        return true;
    }

    void j0(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
        if(this.N) {
            this.w(xC_MethodHook$MethodHookParam0.method, xC_MethodHook$MethodHookParam0.args);
            return;
        }
        Object[] arr_object = xC_MethodHook$MethodHookParam0.args;
        InputEvent inputEvent0 = (InputEvent)arr_object[0];
        int v = (int)(((Integer)arr_object[1]));
        c y0$c0 = this.P;
        if(y0$c0 != null) {
            y0$c0.a(inputEvent0, v);
            return;
        }
        this.u0(inputEvent0, 0, v | 0x4000000);
    }

    boolean k0() {
        return this.U;
    }

    // 检测为 Lambda 实现
    private void l0(float f, float f1) [...]

    // 检测为 Lambda 实现
    private void m0(int v) [...]

    // 检测为 Lambda 实现
    private void n0(int v, int v1) [...]

    // 检测为 Lambda 实现
    private void o0(int v, int v1) [...]

    // 检测为 Lambda 实现
    private void p0(int v, int v1) [...]

    // 检测为 Lambda 实现
    private void q0(float f, float f1) [...]

    // 检测为 Lambda 实现
    private void r0(float f, float f1) [...]

    // 检测为 Lambda 实现
    private void s0(KeyEvent[] arr_keyEvent) [...]

    private MotionEvent t0(long v, long v1, int v2, float f, float f1) {
        this.b0[0].id = 0;
        this.b0[0].toolType = 1;
        this.c0[0].x = f;
        this.c0[0].y = f1;
        return MotionEvent.obtain(v, v1, v2, 1, this.b0, this.c0, 0, 0, 1.0f, 1.0f, 0, 0, 0x1002, 0);
    }

    private void u0(InputEvent inputEvent0, int v, int v1) {
        switch(this.M.b(inputEvent0, v, v1)) {
            case 0: {
                return;
            }
            case 1: {
                throw new SecurityException("Input event injection permission denied!");
            }
            case 3: {
                throw new RuntimeException("Input event injection timed out!");
            }
            default: {
                throw new RuntimeException("Input event injection failed!");
            }
        }
    }

    void v0(Context context0) {
        class b implements AccessibilityManager.AccessibilityStateChangeListener {
            final AccessibilityManager a;
            final y0 b;

            b(AccessibilityManager accessibilityManager0) {
                this.a = accessibilityManager0;
                super();
            }

            @Override  // android.view.accessibility.AccessibilityManager$AccessibilityStateChangeListener
            public void onAccessibilityStateChanged(boolean z) {
                try {
                    if(y0.this.Q) {
                        this.a.removeAccessibilityStateChangeListener(this);
                        return;
                    }
                    if(!z) {
                        y0.this.P();
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        this.O = true;
        this.P();
        if(Build.VERSION.SDK_INT >= 26) {
            AccessibilityManager accessibilityManager0 = (AccessibilityManager)context0.getSystemService("accessibility");
            if(accessibilityManager0 != null) {
                accessibilityManager0.addAccessibilityStateChangeListener(new b(this, accessibilityManager0));
            }
        }
    }

    void w0() {
        this.Z = -1;
    }

    @Override  // com.jozein.xedgepro.xposed.j2
    protected Object x() {
        Context context0 = this.H.T1();
        if(context0 != null) {
            InputManager inputManager0 = (InputManager)context0.getSystemService("input");
            if(inputManager0 != null) {
                try {
                    Object object0 = XposedHelpers.getObjectField(inputManager0, "mIm");
                    return object0 == null ? g3.l() : object0;
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                    return g3.l();
                }
            }
        }
        return g3.l();
    }

    void x0() {
        y2 y20 = this.H.r3();
        if(y20 != null) {
            y20.p();
        }
        d3 d30 = this.H.s3();
        if(d30 != null) {
            d30.s();
        }
    }

    void y0(w1 w10) {
        try {
            Handler handler0 = (Handler)this.u("mHandler");
            if(handler0 != null) {
                this.J = new h0(handler0.getLooper());
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        if(this.J == null) {
            this.J = w10.U1();
        }
        this.K = new a0(w10, this, this.J);
        this.L = new b0(w10, this, this.J);
        try {
            this.P = new c(this.v());
        }
        catch(Throwable throwable1) {
            v.d(throwable1);
        }
    }

    private void z0(boolean z) {
        Object object0;
        try {
            object0 = this.u("mInputFilterLock");
            goto label_5;
        }
        catch(Throwable throwable0) {
            try {
                p2.g(throwable0);
                object0 = null;
            label_5:
                if(object0 != null) {
                    synchronized(object0) {
                        this.M.d(z);
                    }
                    return;
                }
                this.M.d(z);
                return;
            }
            catch(Throwable throwable1) {
            }
        }
        v.d(throwable1);
    }
}


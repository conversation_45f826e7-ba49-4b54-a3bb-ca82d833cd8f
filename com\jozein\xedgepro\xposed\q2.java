package com.jozein.xedgepro.xposed;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.os.Build.VERSION;
import android.os.Bundle;
import de.robv.android.xposed.XC_MethodHook.MethodHookParam;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedHelpers;
import f.c0;
import f.l;
import f.o0;
import f.v;
import f.z;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

class q2 extends p2 {
    private boolean E;
    private Context F;
    private PackageManager G;
    private Object H;
    private Method I;
    private String J;
    private static final String K;

    static {
        q2.K = Build.VERSION.SDK_INT < 28 ? "com.android.launcher2" : "com.android.launcher3";
    }

    q2(ClassLoader classLoader0) {
        class a extends XC_MethodHook {
            int a;
            boolean b;
            final q2 c;

            a() {
                this.a = -1;
                this.b = false;
            }

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                if(!this.b && q2.this.E) {
                    try {
                        String s = (String)xC_MethodHook$MethodHookParam0.args[0];
                        int v = -1;
                        switch(s) {
                            case "android.intent.action.PACKAGE_ADDED": {
                                v = 0;
                                break;
                            }
                            case "android.intent.action.PACKAGE_CHANGED": {
                                v = 2;
                                break;
                            }
                            case "android.intent.action.PACKAGE_REMOVED": {
                                v = 1;
                            }
                        }
                        if(v == 0 || v == 1 || v == 2) {
                            if(this.a < 0) {
                                for(int v1 = 0; true; ++v1) {
                                    Object[] arr_object = xC_MethodHook$MethodHookParam0.args;
                                    if(v1 >= arr_object.length) {
                                        break;
                                    }
                                    if(arr_object[v1] instanceof int[]) {
                                        this.a = v1;
                                        break;
                                    }
                                }
                            }
                            Object[] arr_object1 = xC_MethodHook$MethodHookParam0.args;
                            String s1 = (String)arr_object1[1];
                            int v2 = this.a;
                            if(v2 >= 0 && arr_object1[v2] != null) {
                                z.l0(s, s1, ((int[])arr_object1[v2]));
                                return;
                            }
                            z.k0(s, s1, o0.h(((Bundle)arr_object1[2]).getInt("android.intent.extra.UID", 0)));
                            return;
                        }
                        return;
                    }
                    catch(ClassCastException classCastException0) {
                        this.b = true;
                        z.m0(false);
                    }
                    catch(Throwable classCastException0) {
                    }
                    v.d(classCastException0);
                }
            }
        }

        this.E = false;
        this.I = null;
        this.J = null;
        if(Build.VERSION.SDK_INT >= 21 && Build.VERSION.SDK_INT <= 29) {
            a q2$a0 = new a(this);
            try {
                new j2("com.android.server.pm.PackageManagerService", classLoader0).o("sendPackageBroadcast", q2$a0);
                z.m0(true);
            }
            catch(Throwable throwable0) {
                p2.g(throwable0);
            }
        }
    }

    boolean i(String s, int v) {
        if((v != 0 || !l.j.equals(s)) && !"android".equals(s) && !"com.android.systemui".equals(s)) {
            if(Build.VERSION.SDK_INT >= 28) {
                String s1 = this.o();
                return s1 == null || !s1.equals(s) ? !this.n().equals(s) : false;
            }
            return !this.n().equals(s);
        }
        return false;
    }

    // 去混淆评级： 低(30)
    boolean j(String s) {
        return !"android".equals(s) && !"com.android.systemui".equals(s) && !this.n().equals(s);
    }

    String k(String s, boolean z) {
        try {
            ResolveInfo resolveInfo0 = c0.x(new Intent("android.intent.action.MAIN").addCategory(s), (!z || Build.VERSION.SDK_INT < 24 ? 0x10000 : 0x100000), 0);
            return resolveInfo0 == null ? null : resolveInfo0.activityInfo.packageName;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return null;
        }
    }

    private Object l() {
        Object object0 = this.H;
        if(object0 == null) {
            object0 = g3.k();
            this.H = object0;
        }
        return object0;
    }

    List m() {
        List list0 = new ArrayList();
        String s = this.n();
        list0.add(s);
        if(Build.VERSION.SDK_INT >= 28) {
            String s1 = this.o();
            if(s1 != null && !s1.equals(s) && !s1.equals("android")) {
                list0.add(s1);
            }
        }
        list0.add("android");
        list0.add("com.android.systemui");
        return list0;
    }

    String n() {
        String s = this.k("android.intent.category.HOME", false);
        return s == null ? q2.K : s;
    }

    private String o() {
        if(this.J == null) {
            this.J = this.k("android.intent.category.HOME", true);
        }
        return this.J;
    }

    void p(Context context0) {
        this.F = context0;
        this.G = context0.getPackageManager();
    }

    void q(String s, int v) {
        PackageManager packageManager0 = Build.VERSION.SDK_INT >= 24 ? this.G : this.l();
        XposedHelpers.callMethod(packageManager0, "installExistingPackageAsUser", new Object[]{s, v});
    }

    boolean r(String s, int v) {
        return c0.c(s, v).enabled;
    }

    // 去混淆评级： 低(20)
    static boolean s(Intent intent0) {
        return intent0 != null && "android.intent.action.MAIN".equals(intent0.getAction()) && intent0.hasCategory("android.intent.category.HOME") && intent0.getCategories().size() == 1 && intent0.getData() == null && intent0.getType() == null;
    }

    boolean t(String s, int v) {
        return z.K(this.F).V(s, v);
    }

    public void u(String s, int v, boolean z) {
        Class class0 = String.class;
        if(v != 0) {
            Object object0 = this.l();
            if(this.I == null) {
                this.I = XposedHelpers.findMethodBestMatch(object0.getClass(), "setApplicationEnabledSetting", new Class[]{class0, Integer.TYPE, Integer.TYPE, Integer.TYPE, class0});
            }
            this.I.invoke(object0, s, ((int)(z ? 1 : 2)), 0, v, "android");
            return;
        }
        this.G.setApplicationEnabledSetting(s, (z ? 1 : 2), 0);
    }

    public void v(String s, int v, boolean z) {
        try {
            this.u(s, v, z);
        }
        catch(Throwable throwable0) {
            v.c(throwable0.toString());
        }
    }

    void w() {
        this.E = true;
        z.K(this.F);
        if(Build.VERSION.SDK_INT < 24) {
            try {
                c0.y(this.l());
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }
}


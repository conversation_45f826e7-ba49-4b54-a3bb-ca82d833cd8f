package d;

import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.CompoundButton;

public final class y0 implements CompoundButton.OnCheckedChangeListener {
    public final o1 a;

    public y0(o1 o10) {
        this.a = o10;
    }

    @Override  // android.widget.CompoundButton$OnCheckedChangeListener
    public final void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
        this.a.l2(compoundButton0, z);
    }
}


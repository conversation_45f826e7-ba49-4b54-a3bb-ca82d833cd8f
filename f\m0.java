package f;

import android.content.Context;
import android.os.Build.VERSION;
import android.os.SystemClock;
import android.provider.Settings.System;
import android.speech.tts.TextToSpeech.OnInitListener;
import android.speech.tts.TextToSpeech;
import android.speech.tts.UtteranceProgressListener;
import java.text.DateFormat;
import java.text.DateFormatSymbols;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;

public class m0 implements l {
    private TextToSpeech A;
    private boolean B;
    private Locale C;
    private int D;
    private String E;
    private long F;
    private final Context z;

    public m0(Context context0) {
        this.A = null;
        this.B = false;
        this.C = null;
        this.D = 0;
        this.E = null;
        this.F = 0L;
        this.z = context0;
    }

    // 去混淆评级： 低(20)
    private static String h(String s, Locale locale0, Context context0) {
        return !locale0.getLanguage().startsWith("zh") || !m0.l(context0) ? m0.j(s, locale0, false) : m0.j(s, locale0, true);
    }

    public static String i(String s, Context context0) {
        return m0.j(s, Locale.getDefault(), m0.l(context0));
    }

    private static String j(String s, Locale locale0, boolean z) {
        String s3;
        String s2;
        StringBuilder stringBuilder0;
        if(s == null) {
            return null;
        }
        if(s.length() < 2) {
            return s;
        }
        Calendar calendar0 = Calendar.getInstance();
        Date date0 = new Date(calendar0.getTimeInMillis());
        if(s.contains("%t")) {
            if(z) {
                int v = calendar0.get(11);
                int v1 = calendar0.get(12);
                String s1 = v <= 9 ? "0" + v : Integer.toString(v);
                if(v1 > 9) {
                    stringBuilder0 = new StringBuilder();
                    stringBuilder0.append(s1);
                    s2 = ":";
                }
                else {
                    stringBuilder0 = new StringBuilder();
                    stringBuilder0.append(s1);
                    s2 = ":0";
                }
                stringBuilder0.append(s2);
                stringBuilder0.append(v1);
                s3 = stringBuilder0.toString();
            }
            else {
                s3 = DateFormat.getTimeInstance(3, locale0).format(date0);
            }
            s = s.replace("%t", s3);
        }
        if(s.contains("%d")) {
            s = s.replace("%d", DateFormat.getDateInstance(1, locale0).format(date0));
        }
        if(s.contains("%w")) {
            String[] arr_s = new DateFormatSymbols(locale0).getWeekdays();
            return s.replace("%w", arr_s[calendar0.get(7)]);
        }
        return s;
    }

    private void k(String s, boolean z) {
        class a implements TextToSpeech.OnInitListener {
            final String a;
            final boolean b;
            final m0 c;

            a(String s, boolean z) {
                this.a = s;
                this.b = z;
                super();
            }

            @Override  // android.speech.tts.TextToSpeech$OnInitListener
            public void onInit(int v) {
                class f.m0.a.a extends UtteranceProgressListener {
                    final a a;

                    @Override  // android.speech.tts.UtteranceProgressListener
                    public void onDone(String s) {
                        long v;
                        m0 m00;
                        if(m0.this.E == null || !m0.this.E.equals(s)) {
                            m00 = m0.this;
                            v = 0L;
                        }
                        else {
                            m00 = m0.this;
                            v = SystemClock.uptimeMillis();
                        }
                        m00.F = v;
                    }

                    @Override  // android.speech.tts.UtteranceProgressListener
                    public void onError(String s) {
                    }

                    @Override  // android.speech.tts.UtteranceProgressListener
                    public void onStart(String s) {
                    }
                }

                if(v == 0) {
                    Locale locale0 = m0.this.A.getDefaultLanguage();
                    m0.this.C = locale0;
                    switch(m0.this.A.setLanguage(m0.this.C)) {
                        case -2: 
                        case -1: {
                            m0.this.C = Locale.ENGLISH;
                            switch(m0.this.A.setLanguage(m0.this.C)) {
                                case -2: 
                                case -1: {
                                    v.c("Failed to set locale of text to speech.");
                                    return;
                                }
                                default: {
                                label_7:
                                    m0.this.B = true;
                                    m0.this.F = 0L;
                                    m0.this.A.setOnUtteranceProgressListener(new f.m0.a.a(this));
                                    try {
                                        m0.this.p(this.a, this.b, true);
                                    }
                                    catch(Throwable throwable0) {
                                        v.d(throwable0);
                                    }
                                    return;
                                }
                            }
                        }
                        default: {
                            goto label_7;
                        }
                    }
                }
                v.c("Failed to init TextToSpeech.");
            }
        }

        a m0$a0 = new a(this, s, z);
        this.A = new TextToSpeech(this.z, m0$a0);
    }

    private static boolean l(Context context0) {
        return "24".equals(Settings.System.getString(context0.getContentResolver(), "time_12_24"));
    }

    public boolean m() {
        return this.A != null && this.A.isSpeaking();
    }

    public void n() {
        TextToSpeech textToSpeech0 = this.A;
        if(textToSpeech0 != null) {
            try {
                textToSpeech0.shutdown();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            this.A = null;
        }
    }

    public void o(String s, boolean z, boolean z1) {
        if(this.A != null) {
            if(this.F != 0L && SystemClock.uptimeMillis() - this.F >= 30000L) {
                this.n();
                this.k(s, z);
                return;
            }
            this.p(s, z, z1);
            return;
        }
        this.k(s, z);
    }

    private void p(String s, boolean z, boolean z1) {
        if(this.B && s != null && s.length() != 0) {
            if(z) {
                s = m0.h(s, this.C, this.z);
            }
            int v = this.D + 1;
            this.D = v;
            String s1 = Integer.toString(v);
            if(Build.VERSION.SDK_INT >= 21) {
                this.A.speak(s, ((int)z1), null, s1);
            }
            else {
                HashMap hashMap0 = new HashMap();
                hashMap0.put("utteranceId", s1);
                this.A.speak(s, ((int)z1), hashMap0);
            }
            this.E = s1;
        }
    }
}


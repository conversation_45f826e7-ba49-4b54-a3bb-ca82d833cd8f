package com.jozein.xedgepro.xposed;

import android.content.Intent;
import android.os.Build.VERSION;
import de.robv.android.xposed.XposedHelpers;
import f.c0;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

class b {
    private static Field a;
    private static Method b;
    private static Field c;
    private static Field d;
    private static Field e;
    private static Field f;

    static {
    }

    static Object a(Object object0) {
        if(b.f == null) {
            String s = Build.VERSION.SDK_INT < 33 ? "appToken" : "token";
            b.f = XposedHelpers.findField(object0.getClass(), s);
        }
        return b.f.get(object0);
    }

    static int b(Object object0) {
        if(Build.VERSION.SDK_INT >= 26 && v.f) {
            try {
                if(b.b == null) {
                    b.b = p2.b(object0.getClass(), "getDisplayId", new Class[0]);
                }
                int v = (int)(((Integer)b.b.invoke(object0)));
                return v == -1 ? 0 : v;
            }
            catch(Throwable throwable0) {
                f.v.d(throwable0);
            }
        }
        return 0;
    }

    static Intent c(Object object0) {
        if(b.d == null) {
            b.d = XposedHelpers.findField(object0.getClass(), "intent");
        }
        return (Intent)b.d.get(object0);
    }

    static String d(Object object0) {
        try {
            if(b.e == null) {
                b.e = XposedHelpers.findField(object0.getClass(), "packageName");
            }
            return (String)b.e.get(object0);
        }
        catch(Throwable throwable0) {
            f.v.d(throwable0);
            try {
                return c0.f(b.c(object0));
            }
            catch(Throwable throwable1) {
                f.v.d(throwable1);
                return null;
            }
        }
    }

    private static Object e(Object object0) {
        try {
            if(b.c == null) {
                b.c = XposedHelpers.findField(object0.getClass(), "task");
            }
            return b.c.get(object0);
        }
        catch(Throwable throwable0) {
            f.v.d(throwable0);
            return null;
        }
    }

    static int f(Object object0) {
        return p3.d(b.e(object0));
    }

    static int g(Object object0) {
        try {
            if(b.a == null) {
                b.a = XposedHelpers.findField(object0.getClass(), (Build.VERSION.SDK_INT <= 28 ? "userId" : "mUserId"));
            }
            return b.a.getInt(object0);
        }
        catch(Throwable throwable0) {
            f.v.d(throwable0);
            return 0;
        }
    }
}


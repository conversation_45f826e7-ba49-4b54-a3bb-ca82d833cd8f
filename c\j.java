package c;

import a.b.a0;
import a.z;
import android.app.Activity;
import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter.LengthFilter;
import android.text.InputFilter;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.LinearLayout;
import com.jozein.xedgepro.ApplicationMain;
import e.j0.b;
import e.y;
import f.h0;
import f.i0;
import g.p;
import g.x;
import java.util.StringTokenizer;

public class j extends b {
    private CheckBox C;
    private CheckBox D;
    private EditText E;
    private z F;

    public j() {
        this.F = null;
    }

    // 检测为 Lambda 实现
    private void A(View view0) [...]

    // 检测为 Lambda 实现
    private void B(CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void C(CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void D(DialogInterface dialogInterface0, int v) [...]

    public j E(CharSequence charSequence0, boolean z, boolean z1) {
        Bundle bundle0 = this.e();
        bundle0.putCharSequence("text", charSequence0);
        bundle0.putBoolean("system", z);
        bundle0.putBoolean("root", z1);
        return this;
    }

    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        Bundle bundle1 = this.e();
        Activity activity0 = this.getActivity();
        LinearLayout linearLayout0 = new LinearLayout(activity0);
        linearLayout0.setOrientation(1);
        x x0 = this.g();
        linearLayout0.setPadding(x0.f, x0.f, x0.f, x0.f);
        p p0 = this.m(this.f(0x7F040083));  // drawable:ic_play
        p0.setOnClickListener((View view0) -> {
            Editable editable0 = this.E.getText();
            if(editable0.length() == 0) {
                return;
            }
            try {
                String s = editable0.toString();
                if(this.F == null) {
                    this.F = this.d().h();
                }
                if(this.F != null && this.F.f0(this.c(), s)) {
                    this.t(s);
                    return;
                }
                a0 b$a00 = this.y(s);
                if(!b$a00.I && !ApplicationMain.isModuleActivated()) {
                    boolean z = this.F.o(25);
                    boolean z1 = this.F.o(36);
                    i0.c(b$a00, this.c(), h0.a(), z, z1);
                    return;
                }
                a.b.t(this.E.getContext(), b$a00);
            }
            catch(Throwable throwable0) {
                this.t(throwable0.getMessage());
            }
        });
        x x1 = this.g();
        linearLayout0.addView(p0, x1.b, x1.b);
        EditText editText0 = new EditText(activity0);
        this.E = editText0;
        this.r(editText0);
        y.L(x1, null, this.E);
        this.E.setHint(0x7F06009B);  // string:command_hint "Shell command for developers only"
        this.E.setInputType(0x20001);
        CharSequence charSequence0 = bundle1.getCharSequence("text");
        if(charSequence0 == null) {
            charSequence0 = "";
        }
        this.E.setText(charSequence0);
        this.E.setSelection(charSequence0.length());
        this.E.setFilters(new InputFilter[]{new InputFilter.LengthFilter(0x4000)});
        linearLayout0.addView(this.E);
        CheckBox checkBox0 = new CheckBox(activity0);
        this.C = checkBox0;
        checkBox0.setChecked(bundle1.getBoolean("system", false));
        this.C.setText(0x7F06009C);  // string:command_on_system_process "Runs on system process"
        linearLayout0.addView(this.C);
        this.C.setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> if(z) {
            this.D.setChecked(false);
            Editable editable0 = this.E.getText();
            if(editable0.length() == 0) {
                return;
            }
            if(j.z(editable0.toString())) {
                this.s(0x7F0601EA);  // string:su_not_supported_on_system "su not supported run on system process."
                compoundButton0.setChecked(false);
            }
        });
        CheckBox checkBox1 = new CheckBox(activity0);
        this.D = checkBox1;
        checkBox1.setChecked(bundle1.getBoolean("root", false));
        this.D.setText(0x7F06009D);  // string:command_root_access "Root access"
        linearLayout0.addView(this.D);
        this.D.setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> if(z) {
            this.C.setChecked(false);
            Editable editable0 = this.E.getText();
            if(editable0.length() == 0) {
                return;
            }
            if(j.z(editable0.toString())) {
                this.s(0x7F06015F);  // string:not_need_to_exec_su_explicitly "Not need to call su explicitly if root access 
                                     // checked."
                compoundButton0.setChecked(false);
            }
        });
        return new AlertDialog.Builder(activity0).setTitle(0x7F060016).setView(linearLayout0).setPositiveButton(0x104000A, (DialogInterface dialogInterface0, int v) -> {
            Editable editable0 = this.E.getText();
            if(editable0.length() == 0) {
                return;
            }
            Bundle bundle0 = new Bundle(1);
            bundle0.putParcelable("result", this.y(editable0.toString()));
            this.o(bundle0);
        }).setNegativeButton(0x1040000, b.B).create();
    }

    @Override  // e.j0$b
    public void onSaveInstanceState(Bundle bundle0) {
        super.onSaveInstanceState(bundle0);
        Bundle bundle1 = this.e();
        bundle1.putBoolean("system", this.C.isChecked());
        bundle1.putBoolean("root", this.D.isChecked());
        bundle1.putCharSequence("text", this.E.getText());
    }

    private a0 y(String s) {
        boolean z = this.D.isChecked();
        if(z) {
            this.C.setChecked(false);
        }
        boolean z1 = this.C.isChecked();
        boolean z2 = j.z(s);
        if(z2 && z1) {
            this.C.setChecked(false);
            this.s(0x7F0601EA);  // string:su_not_supported_on_system "su not supported run on system process."
            z1 = false;
        }
        if(z2 && z) {
            this.D.setChecked(false);
            this.s(0x7F06015F);  // string:not_need_to_exec_su_explicitly "Not need to call su explicitly if root access 
                                 // checked."
            z = false;
        }
        return new a0(z1, z, s);
    }

    private static boolean z(String s) {
        StringTokenizer stringTokenizer0 = new StringTokenizer(s, "\r\n");
        int v = stringTokenizer0.countTokens();
        int v1 = 0;
        while(v1 < v) {
            String s1 = stringTokenizer0.nextToken().trim();
            if(!"su".equals(s1) && !s1.startsWith("su ")) {
                ++v1;
                continue;
            }
            return true;
        }
        return false;
    }
}


package com.jozein.xedgepro.xposed;

import android.app.ActivityManager.RunningTaskInfo;
import java.util.Comparator;

public final class j0 implements Comparator {
    public static final j0 z;

    static {
        j0.z = new j0();
    }

    @Override
    public final int compare(Object object0, Object object1) {
        return l0.M0(((ActivityManager.RunningTaskInfo)object0), ((ActivityManager.RunningTaskInfo)object1));
    }
}


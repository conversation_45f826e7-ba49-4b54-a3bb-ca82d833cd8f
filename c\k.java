package c;

import a.b.z0;
import android.app.Activity;
import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.KeyEvent;
import android.widget.CheckBox;
import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.CompoundButton;
import android.widget.LinearLayout.LayoutParams;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup.LayoutParams;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import e.j0.b;
import g.x;

public class k extends b {
    private int C;
    private int D;
    private int E;

    private CheckBox B(Context context0, CharSequence charSequence0, int v) {
        class c implements CompoundButton.OnCheckedChangeListener {
            final int a;
            final k b;

            c(int v) {
                this.a = v;
                super();
            }

            @Override  // android.widget.CompoundButton$OnCheckedChangeListener
            public void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
                k k0 = k.this;
                if(z) {
                    k.z(k0, this.a);
                    return;
                }
                k.y(k0, ~this.a);
            }
        }

        CheckBox checkBox0 = new CheckBox(context0);
        checkBox0.setText(charSequence0);
        if((this.E & v) != 0) {
            checkBox0.setChecked(true);
        }
        checkBox0.setOnCheckedChangeListener(new c(this, v));
        return checkBox0;
    }

    private RadioButton C(Context context0, int v, int v1) {
        class c.k.b implements CompoundButton.OnCheckedChangeListener {
            final int a;
            final k b;

            c.k.b(int v) {
                this.a = v;
                super();
            }

            @Override  // android.widget.CompoundButton$OnCheckedChangeListener
            public void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
                if(z) {
                    k.this.D = this.a;
                }
            }
        }

        RadioButton radioButton0 = new RadioButton(context0);
        radioButton0.setText(v);
        radioButton0.setOnCheckedChangeListener(new c.k.b(this, v1));
        return radioButton0;
    }

    public k D(int v, int v1, int v2) {
        Bundle bundle0 = this.e();
        bundle0.putInt("keycode", v);
        bundle0.putInt("key_action", v1);
        bundle0.putInt("modifiers", v2);
        return this;
    }

    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        class a implements DialogInterface.OnClickListener {
            final k z;

            @Override  // android.content.DialogInterface$OnClickListener
            public void onClick(DialogInterface dialogInterface0, int v) {
                Bundle bundle0 = new Bundle(1);
                bundle0.putParcelable("result", new z0(k.this.C, k.this.D, k.this.E));
                k.this.o(bundle0);
            }
        }

        Activity activity0 = this.getActivity();
        Bundle bundle1 = this.e();
        this.C = bundle1.getInt("keycode", 0);
        this.D = bundle1.getInt("key_action", 0);
        this.E = bundle1.getInt("modifiers", 0);
        x x0 = this.g();
        ScrollView scrollView0 = new ScrollView(activity0);
        scrollView0.setPadding(x0.f, x0.f, x0.f, x0.f);
        LinearLayout linearLayout0 = new LinearLayout(activity0);
        linearLayout0.setOrientation(1);
        scrollView0.addView(linearLayout0);
        RadioGroup radioGroup0 = new RadioGroup(activity0);
        radioGroup0.setOrientation(0);
        radioGroup0.setPadding(0, x0.f, 0, x0.f * 2);
        linearLayout0.addView(radioGroup0);
        RadioGroup.LayoutParams radioGroup$LayoutParams0 = new RadioGroup.LayoutParams(-2, -2, 1.0f);
        RadioButton radioButton0 = this.C(activity0, 0x7F060133, 0);  // string:key_click "Click"
        radioGroup0.addView(radioButton0, radioGroup$LayoutParams0);
        RadioButton radioButton1 = this.C(activity0, 0x7F060135, 1);  // string:key_double_click "Double click"
        radioGroup0.addView(radioButton1, radioGroup$LayoutParams0);
        RadioButton radioButton2 = this.C(activity0, 0x7F06013B, 2);  // string:key_long_press "Long press"
        radioGroup0.addView(radioButton2, radioGroup$LayoutParams0);
        switch(this.D) {
            case 0: {
                radioButton0.setChecked(true);
                break;
            }
            case 1: {
                radioButton1.setChecked(true);
                break;
            }
            case 2: {
                radioButton2.setChecked(true);
            }
        }
        LinearLayout linearLayout1 = new LinearLayout(activity0);
        linearLayout1.setOrientation(0);
        linearLayout0.addView(linearLayout1);
        LinearLayout.LayoutParams linearLayout$LayoutParams0 = new LinearLayout.LayoutParams(-1, -2, 1.0f);
        LinearLayout linearLayout2 = new LinearLayout(activity0);
        linearLayout2.setOrientation(1);
        linearLayout1.addView(linearLayout2, linearLayout$LayoutParams0);
        linearLayout2.addView(this.B(activity0, "Shift", 1), linearLayout$LayoutParams0);
        linearLayout2.addView(this.B(activity0, "Alt", 2), linearLayout$LayoutParams0);
        linearLayout2.addView(this.B(activity0, "Ctrl", 0x1000), linearLayout$LayoutParams0);
        LinearLayout linearLayout3 = new LinearLayout(activity0);
        linearLayout3.setOrientation(1);
        linearLayout1.addView(linearLayout3, linearLayout$LayoutParams0);
        linearLayout3.addView(this.B(activity0, "Meta", 0x10000), linearLayout$LayoutParams0);
        linearLayout3.addView(this.B(activity0, "Fn", 8), linearLayout$LayoutParams0);
        linearLayout3.addView(this.B(activity0, "Sym", 4), linearLayout$LayoutParams0);
        return new AlertDialog.Builder(activity0).setTitle(KeyEvent.keyCodeToString(this.C)).setView(scrollView0).setPositiveButton(0x104000A, new a(this)).setNegativeButton(0x1040000, b.B).create();
    }

    @Override  // e.j0$b
    public void onSaveInstanceState(Bundle bundle0) {
        this.D(this.C, this.D, this.E);
        super.onSaveInstanceState(bundle0);
    }

    static int y(k k0, int v) {
        int v1 = v & k0.E;
        k0.E = v1;
        return v1;
    }

    static int z(k k0, int v) {
        int v1 = v | k0.E;
        k0.E = v1;
        return v1;
    }
}


package com.jozein.xedgepro.xposed;

import a.b.g;
import a.b.h;
import a.p;
import android.app.ActivityManager.MemoryInfo;
import android.app.ActivityManager.RunningAppProcessInfo;
import android.app.ActivityManager.RunningServiceInfo;
import android.app.ActivityManager.RunningTaskInfo;
import android.app.ActivityManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager.NameNotFoundException;
import android.hardware.display.DisplayManager;
import android.net.Uri;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.Handler;
import android.os.UserHandle;
import android.util.EventLog;
import android.util.SparseArray;
import android.util.SparseIntArray;
import com.jozein.xedgepro.ui.ActivityPerformAction;
import de.robv.android.xposed.XC_MethodHook.MethodHookParam;
import de.robv.android.xposed.XC_MethodHook.Unhook;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedHelpers;
import f.c0.b;
import f.c0.c;
import f.c0;
import f.l;
import f.o0;
import f.p0;
import f.t;
import f.v;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

class l0 extends j2 {
    static final class d implements Comparable {
        final int A;
        final String z;

        d(String s, int v) {
            this.z = s;
            this.A = v;
        }

        public int a(d l0$d0) {
            int v = this.z.compareTo(l0$d0.z);
            return v == 0 ? this.A - l0$d0.A : v;
        }

        public boolean b(String s, int v) {
            return v == this.A && Objects.equals(s, this.z);
        }

        @Override
        public int compareTo(Object object0) {
            return this.a(((d)object0));
        }

        @Override
        public boolean equals(Object object0) {
            if(this == object0) {
                return true;
            }
            if(object0 != null) {
                Class class0 = object0.getClass();
                return d.class == class0 && (this.A == ((d)object0).A && Objects.equals(this.z, ((d)object0).z));
            }
            return false;
        }

        @Override
        public int hashCode() {
            return Objects.hash(new Object[]{this.z, this.A});
        }
    }

    static final class e {
        private final Map a;
        private final SparseArray b;

        private e() {
            this.a = new HashMap();
            this.b = new SparseArray();
        }

        e(a l0$a0) {
        }

        void a(String s, int v) {
            synchronized(this) {
                this.d(v).remove(s);
            }
        }

        int b(String s, int v) {
            synchronized(this) {
                Map map0 = this.d(v);
                t t0 = (t)map0.get(s);
                if(t0 == null) {
                    v.c(("Removing package not added! " + s + "(" + v + ")."));
                    return 0;
                }
                int v2 = t0.a - 1;
                t0.a = v2;
                if(v2 <= 0) {
                    map0.remove(s);
                    return 0;
                }
                return v2;
            }
        }

        int c(String s, int v) {
            synchronized(this) {
                t t0 = (t)this.d(v).get(s);
                return t0 == null ? 0 : t0.a;
            }
        }

        private Map d(int v) {
            if(v == 0) {
                return this.a;
            }
            Map map0 = (Map)this.b.get(v);
            if(map0 == null) {
                map0 = new HashMap();
                this.b.put(v, map0);
            }
            return map0;
        }

        int e(String s, int v) {
            synchronized(this) {
                Map map0 = this.d(v);
                t t0 = (t)map0.get(s);
                if(t0 == null) {
                    map0.put(s, new t(1));
                    return 1;
                }
                int v2 = t0.a + 1;
                t0.a = v2;
                return v2;
            }
        }
    }

    static class f extends b {
        private final l0 g;
        private final ActivityManager.RunningTaskInfo h;
        private final Object i;
        private ComponentName j;
        private Intent k;

        f(l0 l00, ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0, ApplicationInfo applicationInfo0) {
            super(applicationInfo0);
            this.g = l00;
            this.h = activityManager$RunningTaskInfo0;
            this.j = activityManager$RunningTaskInfo0.topActivity;
            this.k = Build.VERSION.SDK_INT < 29 || activityManager$RunningTaskInfo0.numActivities != 1 ? null : activityManager$RunningTaskInfo0.baseIntent;
            this.i = null;
        }

        f(l0 l00, Object object0, ApplicationInfo applicationInfo0, Intent intent0) {
            super(applicationInfo0);
            this.g = l00;
            this.i = object0;
            this.k = intent0;
            this.h = null;
        }

        f(l0 l00, String s, int v) {
            super(c0.c(s, v));
            this.g = l00;
            this.i = null;
            this.h = null;
        }

        void A() {
            int[] arr_v = o0.e(this.g.L);
            String s = this.i();
            int v = this.k();
            for(int v1 = 0; v1 < arr_v.length; ++v1) {
                int v2 = arr_v[v1];
                if(v2 != v && !this.g.K.t(s, v2)) {
                    this.g.K.q(s, v2);
                    this.g.I.G4(s + c0.z + v2 + ") installed");
                    return;
                }
            }
        }

        boolean B() {
            try {
                if(this.h != null) {
                    return this.g.p0().id == this.h.id;
                }
                return this.i == null ? this.g.o0().equals(this.i()) : this.i == this.g.n0();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return false;
            }
        }

        // 去混淆评级： 低(20)
        boolean C() {
            return !this.m() || this.g.I0(this.i(), this.k());
        }

        static f D(l0 l00, Object object0) {
            int v = com.jozein.xedgepro.xposed.b.g(object0);
            Intent intent0 = com.jozein.xedgepro.xposed.b.c(object0);
            return new f(l00, object0, c0.c(c0.f(intent0), v), intent0);
        }

        void E(Bundle bundle0) {
            ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0 = this.h;
            if(activityManager$RunningTaskInfo0 != null) {
                this.g.X0(activityManager$RunningTaskInfo0, bundle0);
                return;
            }
            Object object0 = this.i;
            if(object0 != null) {
                int v = com.jozein.xedgepro.xposed.b.f(object0);
                if(v >= 0) {
                    this.g.W0(v, bundle0);
                    return;
                }
            }
            this.g.s1(this.i(), bundle0, this.k());
        }

        void F(boolean z) {
            this.g.l1(this.i(), this.k(), z);
        }

        void G() {
            this.g.t1(this.i(), this.k());
        }

        void H() {
            String s = this.i();
            Intent intent0 = new Intent("android.intent.action.VIEW", Uri.parse(("market://details?id=" + s)));
            if(l.w.equals(s)) {
                intent0.setPackage("com.android.vending");
            }
            try {
                this.g.q1(intent0, false, null, 0);
            }
            catch(Throwable throwable0) {
                v.c(throwable0.toString());
                try {
                    Intent intent1 = new Intent("android.intent.action.VIEW", Uri.parse(("http://play.google.com/store/apps/details?id=" + s)));
                    this.g.q1(intent1, false, null, 0);
                }
                catch(Throwable throwable1) {
                    v.d(throwable1);
                }
            }
        }

        a.b q() {
            ComponentName componentName0 = this.x();
            if(componentName0 == null) {
                return null;
            }
            return this.l() ? new a.b.f(componentName0.getPackageName(), componentName0.getClassName(), this.k()) : new a.b.e(componentName0.getPackageName(), componentName0.getClassName());
        }

        a.b r() {
            Intent intent0 = this.y();
            if(intent0 == null) {
                return null;
            }
            return this.l() ? new h(intent0, this.k()) : new g(intent0);
        }

        boolean s() {
            return this.g.K.i(this.i(), this.k());
        }

        boolean t() {
            return this.i != null || this.h != null ? !q2.s(this.y()) : false;
        }

        // 去混淆评级： 中等(50)
        boolean u() {
            return this.m() && this.g.K.j(this.i()) && (this.i != null || this.h != null || this.g.J0(this.a().uid) && this.g.H0(this.i()));
        }

        void v() {
            Object object0 = this.i;
            if(object0 != null) {
                this.g.d0(object0);
                return;
            }
            ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0 = this.h;
            if(activityManager$RunningTaskInfo0 != null) {
                this.g.f0(activityManager$RunningTaskInfo0);
                return;
            }
            v.d(new RuntimeException("Finish activity with out an activity info."));
        }

        void w() {
            this.g.i0(this.i(), this.k());
        }

        ComponentName x() {
            if(this.j == null) {
                Intent intent0 = this.y();
                if(intent0 != null) {
                    this.j = intent0.getComponent();
                }
            }
            return this.j;
        }

        Intent y() {
            if(this.k == null) {
                Object object0 = this.i;
                if(object0 != null) {
                    try {
                        this.k = com.jozein.xedgepro.xposed.b.c(object0);
                        return this.k;
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }
            return this.k;
        }

        CharSequence z() {
            try {
                ComponentName componentName0 = this.x();
                if(componentName0 != null) {
                    return componentName0.flattenToShortString();
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            return null;
        }
    }

    static class com.jozein.xedgepro.xposed.l0.g {
        private final List a;

        private com.jozein.xedgepro.xposed.l0.g() {
            this.a = new ArrayList();
        }

        com.jozein.xedgepro.xposed.l0.g(a l0$a0) {
        }

        void a(String s, int v) {
            synchronized(this) {
                if(this.h(s, v) < 0) {
                    d l0$d0 = new d(s, v);
                    this.a.add(l0$d0);
                }
            }
        }

        boolean b(String s, int v) {
            synchronized(this) {
                return this.h(s, v) >= 0;
            }
        }

        List c() {
            synchronized(this) {
                List list0 = new ArrayList(this.a);
                this.a.clear();
                return list0;
            }
        }

        void d(c c0$c0, int v) {
            synchronized(this) {
                for(int v2 = this.a.size() - 1; v2 >= 0; --v2) {
                    d l0$d0 = (d)this.a.get(v2);
                    if(l0$d0.A == v) {
                        c0$c0.a(l0$d0.z, v);
                    }
                }
            }
        }

        void e(List list0, int v) {
            synchronized(this) {
                for(int v2 = this.a.size() - 1; v2 >= 0; --v2) {
                    d l0$d0 = (d)this.a.get(v2);
                    if(l0$d0.A == v) {
                        list0.add(l0$d0);
                    }
                }
            }
        }

        void f(c c0$c0, int[] arr_v) {
            synchronized(this) {
                for(int v1 = this.a.size() - 1; v1 >= 0; --v1) {
                    d l0$d0 = (d)this.a.get(v1);
                    if(p0.a(arr_v, l0$d0.A) >= 0) {
                        c0$c0.a(l0$d0.z, l0$d0.A);
                    }
                }
            }
        }

        void g(List list0, int[] arr_v) {
            synchronized(this) {
                for(int v1 = this.a.size() - 1; v1 >= 0; --v1) {
                    d l0$d0 = (d)this.a.get(v1);
                    if(p0.a(arr_v, l0$d0.A) >= 0) {
                        list0.add(l0$d0);
                    }
                }
            }
        }

        private int h(String s, int v) {
            for(int v1 = this.a.size() - 1; v1 >= 0; --v1) {
                if(((d)this.a.get(v1)).b(s, v)) {
                    return v1;
                }
            }
            return -1;
        }

        boolean i(String s, int v) {
            boolean z;
            synchronized(this) {
                int v2 = this.h(s, v);
                if(v2 >= 0) {
                    this.a.remove(v2);
                    z = true;
                }
                else {
                    z = false;
                }
                return z;
            }
        }
    }

    class com.jozein.xedgepro.xposed.l0.h extends com.jozein.xedgepro.xposed.g {
        final l0 G;

        com.jozein.xedgepro.xposed.l0.h(ClassLoader classLoader0) {
            super("com.android.server.am.ActivityRecord", classLoader0);
        }

        boolean r() {
            class com.jozein.xedgepro.xposed.l0.h.a extends XC_MethodHook {
                final com.jozein.xedgepro.xposed.l0.h a;

                protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                    l0.this.c1(xC_MethodHook$MethodHookParam0.thisObject);
                }
            }


            class com.jozein.xedgepro.xposed.l0.h.b extends XC_MethodHook {
                final com.jozein.xedgepro.xposed.l0.h a;

                protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                    if(!l0.this.l0) {
                        l0.this.l0 = true;
                    }
                    l0.this.e1(xC_MethodHook$MethodHookParam0.thisObject);
                }
            }

            try {
                XC_MethodHook.Unhook xC_MethodHook$Unhook0 = this.p("takeFromHistory", new Object[]{new com.jozein.xedgepro.xposed.l0.h.a(this)});
                try {
                    this.p((Build.VERSION.SDK_INT < 26 ? "putInHistory" : "createWindowContainer"), new Object[]{new com.jozein.xedgepro.xposed.l0.h.b(this)});
                    return true;
                }
                catch(Throwable throwable1) {
                    v.d(throwable1);
                    xC_MethodHook$Unhook0.unhook();
                    return false;
                }
            }
            catch(Throwable throwable0) {
                p2.g(throwable0);
                return false;
            }
        }
    }

    class i extends com.jozein.xedgepro.xposed.g {
        final l0 G;

        i(ClassLoader classLoader0) {
            super("com.android.server.am.ActivityStack", classLoader0);
        }

        boolean r() {
            class com.jozein.xedgepro.xposed.l0.i.a extends XC_MethodHook {
                final i a;

                protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                    if(!l0.this.l0) {
                        l0.this.c1(xC_MethodHook$MethodHookParam0.args[0]);
                    }
                }
            }


            class com.jozein.xedgepro.xposed.l0.i.b extends XC_MethodHook {
                final i a;

                protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                    if(!l0.this.l0) {
                        l0.this.e1(xC_MethodHook$MethodHookParam0.args[0]);
                    }
                }
            }

            try {
                XC_MethodHook.Unhook xC_MethodHook$Unhook0 = this.o("removeActivityFromHistoryLocked", new com.jozein.xedgepro.xposed.l0.i.a(this));
                com.jozein.xedgepro.xposed.l0.i.b l0$i$b0 = new com.jozein.xedgepro.xposed.l0.i.b(this);
                if(Build.VERSION.SDK_INT >= 24 && Build.VERSION.SDK_INT < 26) {
                    try {
                        this.o("addConfigOverride", l0$i$b0);
                        return true;
                    }
                    catch(Throwable unused_ex) {
                    }
                }
                try {
                    this.o("startActivityLocked", l0$i$b0);
                    return true;
                }
                catch(Throwable throwable1) {
                    v.d(throwable1);
                    xC_MethodHook$Unhook0.unhook();
                    return false;
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return false;
            }
        }
    }

    static class j extends f.c0.f {
        private final l0 i;

        j(l0 l00, ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0, int v) {
            super(activityManager$RunningTaskInfo0, v);
            this.i = l00;
        }

        void s(Bundle bundle0) {
            this.i.X0(this.r(), bundle0);
        }

        f t() {
            return new f(this.i, this.r(), this.a());
        }
    }

    private final p H;
    private final w1 I;
    private final com.jozein.xedgepro.xposed.v J;
    final q2 K;
    private Context L;
    private Handler M;
    private final e N;
    private final com.jozein.xedgepro.xposed.l0.g O;
    private final SparseIntArray P;
    private int Q;
    private ActivityManager R;
    private boolean S;
    private boolean T;
    private Method U;
    private Method V;
    private Method W;
    private Method X;
    private final Comparator Y;
    private final Comparator Z;
    private Field a0;
    private int b0;
    private Method c0;
    private Method d0;
    private Method e0;
    private Object f0;
    private Object g0;
    private Method h0;
    private Method i0;
    private Object j0;
    private Object k0;
    private boolean l0;

    l0(ClassLoader classLoader0, p p0, w1 w10, com.jozein.xedgepro.xposed.v v0, boolean z) {
        class a extends XC_MethodHook {
            final l0 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                if(!l0.this.T) {
                    l0.this.A(xC_MethodHook$MethodHookParam0.thisObject);
                    return;
                }
                try {
                    Object object0 = xC_MethodHook$MethodHookParam0.args[0];
                    ApplicationInfo applicationInfo0 = (ApplicationInfo)XposedHelpers.getObjectField(object0, "info");
                    int v = XposedHelpers.getIntField(object0, "userId");
                    l0.this.Z0(applicationInfo0.packageName, v);
                }
                catch(Throwable unused_ex) {
                }
            }
        }


        class com.jozein.xedgepro.xposed.l0.b extends XC_MethodHook {
            final ComponentName a;
            int b;
            final l0 c;

            com.jozein.xedgepro.xposed.l0.b(int v) {
                super(v);
                this.a = new ComponentName(l.j, ActivityPerformAction.class.getName());
                this.b = -1;
            }

            protected void beforeHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                if(!l0.this.T) {
                    if(Build.VERSION.SDK_INT < 29) {
                        l0.this.A(xC_MethodHook$MethodHookParam0.thisObject);
                        return;
                    }
                    l0.this.j0 = xC_MethodHook$MethodHookParam0.thisObject;
                    return;
                }
                try {
                    if(this.b < 0) {
                        for(int v = 0; true; ++v) {
                            Object[] arr_object = xC_MethodHook$MethodHookParam0.args;
                            if(v >= arr_object.length) {
                                break;
                            }
                            if(arr_object[v] instanceof Intent) {
                                this.b = v;
                                break;
                            }
                        }
                        if(this.b < 0) {
                            return;
                        }
                    }
                    Intent intent0 = (Intent)xC_MethodHook$MethodHookParam0.args[this.b];
                    ComponentName componentName0 = intent0.getComponent();
                    if(this.a.equals(componentName0)) {
                        a.b b0 = f.l0.j(intent0);
                        if(b0.z != 0 && b0.z != 1) {
                            l0.this.I.u3(b0);
                            xC_MethodHook$MethodHookParam0.setResult(4);
                        }
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        super("com.android.server.am.ActivityManagerService", classLoader0);
        this.L = null;
        this.M = null;
        this.N = new e(null);
        this.O = new com.jozein.xedgepro.xposed.l0.g(null);
        this.P = new SparseIntArray();
        boolean z1 = false;
        this.Q = 0;
        this.R = null;
        this.T = false;
        this.W = null;
        this.X = null;
        this.Y = (ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0, ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo1) -> activityManager$RunningTaskInfo0.id - activityManager$RunningTaskInfo1.id;
        this.Z = (ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0, ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo1) -> activityManager$RunningTaskInfo1.id - activityManager$RunningTaskInfo0.id;
        this.a0 = null;
        this.b0 = -1;
        this.c0 = null;
        this.d0 = null;
        this.e0 = null;
        this.f0 = null;
        this.g0 = null;
        this.h0 = null;
        this.i0 = null;
        this.j0 = null;
        this.k0 = null;
        this.l0 = false;
        this.H = p0;
        this.I = w10;
        this.J = v0;
        this.S = z;
        this.K = new q2(classLoader0);
        try {
            if(Build.VERSION.SDK_INT < 26) {
                z1 = new com.jozein.xedgepro.xposed.l0.h(this, classLoader0).r();
                if(new i(this, classLoader0).r()) {
                    z1 = true;
                }
            }
            if(!z1) {
                this.B0();
            }
        }
        catch(Throwable throwable0) {
            p2.g(throwable0);
        }
        try {
            this.o("handleAppDiedLocked", new a(this));
        }
        catch(Throwable throwable1) {
            p2.g(throwable1);
        }
        try {
            com.jozein.xedgepro.xposed.l0.b l0$b0 = new com.jozein.xedgepro.xposed.l0.b(this, 10000);
            if(Build.VERSION.SDK_INT >= 29) {
                new com.jozein.xedgepro.xposed.g("com.android.server.wm.ActivityTaskManagerService", classLoader0).q("startActivityAsUser", l0$b0);
                return;
            }
            this.q("startActivityAsUser", l0$b0);
        }
        catch(Throwable throwable2) {
            p2.g(throwable2);
        }
    }

    int A0(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0) {
        int v2;
        int v1;
        int v = this.x0(activityManager$RunningTaskInfo0.id);
        if(v >= 0) {
            return v;
        }
        if(Build.VERSION.SDK_INT >= 29) {
            v1 = f3.c(activityManager$RunningTaskInfo0);
            if(v1 >= 0) {
                v2 = activityManager$RunningTaskInfo0.taskId;
                this.h1(v2, v1);
                return v1;
            }
        }
        v1 = this.c0(activityManager$RunningTaskInfo0.id);
        if(v1 >= 0) {
            v2 = activityManager$RunningTaskInfo0.id;
            this.h1(v2, v1);
            return v1;
        }
        return this.Q;
    }

    private void B0() {
        class com.jozein.xedgepro.xposed.l0.c extends XC_MethodHook {
            final l0 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                try {
                    int v = (int)(((Integer)xC_MethodHook$MethodHookParam0.args[0]));
                    Object[] arr_object = (Object[])xC_MethodHook$MethodHookParam0.args[1];
                    if(v == 30001 || v == 30005) {
                        int v1 = (int)(((Integer)arr_object[0]));
                        ComponentName componentName0 = ComponentName.unflattenFromString(((String)arr_object[3]));
                        if(componentName0 != null) {
                            int v2 = (int)(((Integer)arr_object[2]));
                            if(!l0.this.S && v1 > 1000) {
                                l0.this.S = true;
                                v.c(("Invalid user id: " + v1));
                            }
                            if(l0.this.S) {
                                v1 = l0.this.z0(v2);
                            }
                            if(v == 30005) {
                                String s = componentName0.getPackageName();
                                l0.this.f1(s, v1);
                                return;
                            }
                            String s1 = componentName0.getPackageName();
                            l0.this.d1(s1, v1);
                        }
                    }
                    else if(v == 31003) {
                        l0.this.k1(((int)(((Integer)arr_object[0]))));
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        com.jozein.xedgepro.xposed.l0.c l0$c0 = new com.jozein.xedgepro.xposed.l0.c(this);
        XposedHelpers.findAndHookMethod(EventLog.class, "writeEvent", new Object[]{Integer.TYPE, Object[].class, l0$c0});
    }

    void C0(Context context0, Handler handler0) {
        this.L = context0;
        this.M = handler0;
        this.K.p(context0);
    }

    Intent D0(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0) {
        if(Build.VERSION.SDK_INT >= 29) {
            return new Intent(activityManager$RunningTaskInfo0.baseIntent);
        }
        try {
            return new Intent(p3.a(this.V(activityManager$RunningTaskInfo0.id)));
        }
        catch(Throwable throwable0) {
            v.c(throwable0.toString());
            return new Intent().setComponent(activityManager$RunningTaskInfo0.baseActivity);
        }
    }

    // 去混淆评级： 低(20)
    private boolean E0(f.o0.a o0$a0, ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0) {
        return o0$a0.b() || o0$a0.a(this.A0(activityManager$RunningTaskInfo0));
    }

    boolean F0(String s) {
        for(Object object0: this.j0().getRunningServices(100)) {
            ActivityManager.RunningServiceInfo activityManager$RunningServiceInfo0 = (ActivityManager.RunningServiceInfo)object0;
            if(s == null) {
                String s1 = activityManager$RunningServiceInfo0.service.getPackageName();
                if(!l.j.equals(s1) || !activityManager$RunningServiceInfo0.started) {
                    continue;
                }
                return true;
            }
            if(s.equals(activityManager$RunningServiceInfo0.service.getClassName())) {
                return activityManager$RunningServiceInfo0.started;
            }
            if(false) {
                break;
            }
        }
        return false;
    }

    boolean G0() {
        try {
            Object object0 = this.n0();
            if(object0 != null) {
                return q2.s(com.jozein.xedgepro.xposed.b.c(object0));
            }
            ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0 = this.p0();
            if(Build.VERSION.SDK_INT >= 29) {
                return q2.s(activityManager$RunningTaskInfo0.baseIntent);
            }
            if(activityManager$RunningTaskInfo0.numActivities == 1 && this.K.n().equals(activityManager$RunningTaskInfo0.baseActivity.getPackageName())) {
                return true;
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        return false;
    }

    boolean H0(String s) {
        Class class0 = String.class;
        if(Build.VERSION.SDK_INT >= 23) {
            try {
                if(this.c0 == null) {
                    this.c0 = XposedHelpers.findMethodExact(this.m(), "getPackageProcessState", new Class[]{class0, class0});
                }
                int v = (int)(((Integer)this.c0.invoke(this.v(), s, "android")));
                return v >= 0 && v <= this.s0();
            }
            catch(Throwable unused_ex) {
                try {
                    if(((int)(((Integer)XposedHelpers.callMethod(this.j0(), "getPackageImportance", new Object[]{s})))) > 400) {
                        return false;
                    }
                }
                catch(Throwable unused_ex) {
                }
            }
        }
        return true;
    }

    boolean I0(String s, int v) {
        return this.O.b(s, v);
    }

    boolean J0(int v) {
        if(Build.VERSION.SDK_INT >= 26) {
            try {
                if(this.d0 == null) {
                    this.d0 = XposedHelpers.findMethodExact(this.m(), "getUidProcessState", new Class[]{Integer.TYPE, String.class});
                }
                int v1 = (int)(((Integer)this.d0.invoke(this.v(), v, "android")));
                return v1 >= 0 && v1 <= this.s0();
            }
            catch(Throwable unused_ex) {
                try {
                    if(((int)(((Integer)XposedHelpers.callMethod(this.j0(), "getUidImportance", new Object[]{v})))) > 400) {
                        return false;
                    }
                }
                catch(Throwable unused_ex) {
                }
            }
        }
        return true;
    }

    private void K0(String s, int v) {
        this.Q0(s, v);
        if(v != 0) {
            try {
                if(this.W == null) {
                    this.W = this.k("killBackgroundProcesses", new Class[]{String.class, Integer.TYPE});
                }
                this.W.invoke(this.v(), s, v);
                return;
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        this.j0().killBackgroundProcesses(s);
    }

    // 检测为 Lambda 实现
    private static int L0(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0, ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo1) [...]

    // 检测为 Lambda 实现
    private static int M0(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0, ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo1) [...]

    // 检测为 Lambda 实现
    private void N0(String s, int v) [...]

    // 检测为 Lambda 实现
    private void O0(Intent intent0, Bundle bundle0, int v, String s) [...]

    void P0(int v) {
        List list0 = this.w0();
        List list1 = this.K.m();
        int v1 = list0.size();
        if(v1 <= 1) {
            return;
        }
        if(v < 0 || v > 3) {
            v = 0;
        }
        Bundle bundle0 = com.jozein.xedgepro.xposed.a.d(this, v);
        f.o0.a o0$a0 = o0.f(this.L);
        for(int v2 = 1; v2 < v1; ++v2) {
            ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0 = (ActivityManager.RunningTaskInfo)list0.get(v2);
            if(!list1.contains(activityManager$RunningTaskInfo0.baseActivity.getPackageName()) && this.E0(o0$a0, activityManager$RunningTaskInfo0) && this.X0(activityManager$RunningTaskInfo0, bundle0)) {
                return;
            }
        }
    }

    private void Q0(String s, int v) {
        if(this.O.i(s, v)) {
            this.K.v(s, v, false);
        }
    }

    private void R0(boolean z, Bundle bundle0) {
        List list0 = this.w0();
        int v = list0.size();
        if(v <= 1) {
            return;
        }
        List list1 = this.K.m();
        ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0 = (ActivityManager.RunningTaskInfo)list0.get(0);
        f.o0.a o0$a0 = o0.f(this.L);
        if(list1.contains(activityManager$RunningTaskInfo0.baseActivity.getPackageName())) {
            for(int v1 = 1; v1 < v; ++v1) {
                ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo1 = (ActivityManager.RunningTaskInfo)list0.get(v1);
                if(!list1.contains(activityManager$RunningTaskInfo1.baseActivity.getPackageName()) && this.E0(o0$a0, activityManager$RunningTaskInfo1) && this.X0(activityManager$RunningTaskInfo1, bundle0)) {
                    return;
                }
            }
            return;
        }
        Collections.sort(list0, (z ? this.Z : this.Y));
        int v3 = list0.indexOf(activityManager$RunningTaskInfo0);
        for(int v4 = v3 + 1; v4 < v; ++v4) {
            ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo2 = (ActivityManager.RunningTaskInfo)list0.get(v4);
            if(!list1.contains(activityManager$RunningTaskInfo2.baseActivity.getPackageName()) && this.E0(o0$a0, activityManager$RunningTaskInfo2) && this.X0(activityManager$RunningTaskInfo2, bundle0)) {
                return;
            }
        }
        for(int v2 = 0; v2 < v3; ++v2) {
            ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo3 = (ActivityManager.RunningTaskInfo)list0.get(v2);
            if(!list1.contains(activityManager$RunningTaskInfo3.baseActivity.getPackageName()) && this.E0(o0$a0, activityManager$RunningTaskInfo3) && this.X0(activityManager$RunningTaskInfo3, bundle0)) {
                return;
            }
        }
    }

    boolean S0(int v) {
        try {
            return ((Boolean)this.r("moveActivityTaskToBack", new Object[]{com.jozein.xedgepro.xposed.b.a(this.v1(v)), Boolean.TRUE})).booleanValue();
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return false;
        }
    }

    private void T0(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0, Bundle bundle0, int v) {
        if(Build.VERSION.SDK_INT >= 0x1F) {
            this.V0(activityManager$RunningTaskInfo0.taskId, v);
            return;
        }
        int v1 = this.A0(activityManager$RunningTaskInfo0);
        this.p1(this.D0(activityManager$RunningTaskInfo0), bundle0, v1, v);
    }

    void U(c c0$c0) {
        int[] arr_v = o0.e(this.L);
        if(o0.k(arr_v)) {
            this.O.d(c0$c0, this.Q);
            return;
        }
        this.O.f(c0$c0, arr_v);
    }

    private void U0(Object object0, Bundle bundle0, int v) {
        if(Build.VERSION.SDK_INT >= 0x1F) {
            this.V0(p3.d(object0), v);
            return;
        }
        int v1 = p3.e(object0);
        this.p1(new Intent(p3.a(object0)), bundle0, v1, v);
    }

    Object V(int v) {
        int v1 = Build.VERSION.SDK_INT;
        if(v1 >= 29) {
            Object object0 = this.t0();
            if(this.h0 == null) {
                this.h0 = XposedHelpers.findMethodExact(object0.getClass(), "anyTaskForId", new Class[]{Integer.TYPE, Integer.TYPE});
            }
            Object object1 = this.y0();
            return this.h0.invoke(object0, v, 0);
        }
        if(v1 >= 21) {
            if(this.f0 == null) {
                this.f0 = XposedHelpers.getObjectField(this.v(), "mStackSupervisor");
            }
            if(this.h0 == null) {
                this.h0 = p2.b(this.f0.getClass(), "anyTaskForIdLocked", new Class[]{Integer.TYPE});
            }
            Object object2 = this.y0();
            return this.h0.invoke(this.f0, v);
        }
        if(this.e0 == null) {
            this.e0 = XposedHelpers.findMethodExact(this.m(), "recentTaskForIdLocked", new Class[]{Integer.TYPE});
        }
        Object object3 = this.y0();
        return this.e0.invoke(this.v(), v);
    }

    private void V0(int v, int v1) {
        XposedHelpers.callMethod(this.k0(), "moveRootTaskToDisplay", new Object[]{v, v1});
    }

    void W(Intent intent0, int v) {
        this.X(intent0, false, false, v);
    }

    void W0(int v, Bundle bundle0) {
        if(this.J.d()) {
            try {
                Object object0 = this.V(v);
                int v1 = p3.b(object0);
                if(v1 != 0) {
                    this.U0(object0, bundle0, v1);
                    return;
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        try {
            this.j0().moveTaskToFront(v, 2, bundle0);
        }
        catch(Throwable throwable1) {
            v.d(throwable1);
        }
    }

    void X(Intent intent0, boolean z, boolean z1, int v) {
        try {
            this.Z(intent0, z, z1, v);
        }
        catch(InvocationTargetException invocationTargetException0) {
            throw invocationTargetException0.getTargetException();
        }
        catch(Throwable throwable0) {
            v.c(throwable0.toString());
            if(z) {
                Context context0 = this.L;
                UserHandle userHandle0 = o0.q(v);
                if(z1) {
                    context0.sendStickyOrderedBroadcastAsUser(intent0, userHandle0, null, null, -1, null, null);
                    return;
                }
                context0.sendOrderedBroadcastAsUser(intent0, userHandle0, null, null, null, -1, null, null);
                return;
            }
            Context context1 = this.L;
            UserHandle userHandle1 = o0.q(v);
            if(z1) {
                context1.sendStickyBroadcastAsUser(intent0, userHandle1);
                return;
            }
            context1.sendBroadcastAsUser(intent0, userHandle1);
        }
    }

    boolean X0(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0, Bundle bundle0) {
        if(f3.e(activityManager$RunningTaskInfo0)) {
            return false;
        }
        if(this.J.d()) {
            try {
                if(Build.VERSION.SDK_INT >= 29) {
                    int v = f3.a(activityManager$RunningTaskInfo0);
                    if(v != 0) {
                        this.T0(activityManager$RunningTaskInfo0, bundle0, v);
                        return true;
                    }
                }
                else {
                    Object object0 = this.V(activityManager$RunningTaskInfo0.id);
                    int v1 = p3.b(object0);
                    if(v1 != 0) {
                        this.U0(object0, bundle0, v1);
                        return true;
                    }
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        try {
            this.j0().moveTaskToFront(activityManager$RunningTaskInfo0.id, 2, bundle0);
            return true;
        }
        catch(Throwable throwable1) {
            v.d(throwable1);
            return false;
        }
    }

    void Y(Intent intent0) {
        this.X(intent0, false, false, this.Q);
    }

    void Y0(int v) {
        if(v < 0 || v > 3) {
            v = 1;
        }
        this.R0(false, com.jozein.xedgepro.xposed.a.d(this, v));
    }

    private boolean Z(Intent intent0, boolean z, boolean z1, int v) {
        Integer integer0;
        int v1 = Build.VERSION.SDK_INT;
        if(v1 >= 29) {
            intent0.addFlags(0x400000);
        }
        synchronized(this.v()) {
            if(v1 >= 33) {
                integer0 = (Integer)this.r("broadcastIntentLocked", new Object[]{null, null, null, intent0, null, null, 0, null, null, null, null, null, -1, null, Boolean.valueOf(z), Boolean.valueOf(z1), 0, 0, 0, 0, v});
            }
            else if(v1 >= 0x1F) {
                integer0 = (Integer)this.r("broadcastIntentLocked", new Object[]{null, null, null, intent0, null, null, 0, null, null, null, null, -1, null, Boolean.valueOf(z), Boolean.valueOf(z1), 0, 0, 0, 0, v});
            }
            else if(v1 >= 30) {
                integer0 = (Integer)this.r("broadcastIntentLocked", new Object[]{null, null, null, intent0, null, null, 0, null, null, null, -1, null, Boolean.valueOf(z), Boolean.valueOf(z1), 0, 0, 0, 0, v});
            }
            else if(v1 >= 29) {
                integer0 = (Integer)this.r("broadcastIntentLocked", new Object[]{null, null, intent0, null, null, 0, null, null, null, -1, null, Boolean.valueOf(z), Boolean.valueOf(z1), 0, 0, 0, 0, v});
            }
            else {
                integer0 = v1 < 23 ? ((Integer)this.r("broadcastIntentLocked", new Object[]{null, null, intent0, null, null, 0, null, null, null, -1, Boolean.valueOf(z), Boolean.valueOf(z1), 0, 0, v})) : ((Integer)this.r("broadcastIntentLocked", new Object[]{null, null, intent0, null, null, 0, null, null, null, -1, null, Boolean.valueOf(z), Boolean.valueOf(z1), 0, 0, v}));
            }
        }
        return ((int)integer0) == 0;
    }

    private void Z0(String s, int v) {
        if(this.T && this.N.c(s, v) <= 0) {
            this.Q0(s, v);
        }
    }

    int a0() {
        boolean z;
        ActivityManager activityManager0 = this.j0();
        ActivityManager.MemoryInfo activityManager$MemoryInfo0 = new ActivityManager.MemoryInfo();
        activityManager0.getMemoryInfo(activityManager$MemoryInfo0);
        try {
            this.r("killAllBackgroundProcesses", new Object[0]);
            z = true;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            z = false;
        }
        if(!z) {
            for(Object object0: activityManager0.getRunningAppProcesses()) {
                ActivityManager.RunningAppProcessInfo activityManager$RunningAppProcessInfo0 = (ActivityManager.RunningAppProcessInfo)object0;
                if(activityManager$RunningAppProcessInfo0.uid >= 10000 && activityManager$RunningAppProcessInfo0.uid <= 0x4E1F && activityManager$RunningAppProcessInfo0.importance > 200 && !activityManager$RunningAppProcessInfo0.processName.equals("com.android.systemui") && !activityManager$RunningAppProcessInfo0.processName.equals("android") && activityManager$RunningAppProcessInfo0.pkgList != null) {
                    int v = activityManager$RunningAppProcessInfo0.uid / 100000;
                    String[] arr_s = activityManager$RunningAppProcessInfo0.pkgList;
                    for(int v1 = 0; v1 < arr_s.length; ++v1) {
                        this.K0(arr_s[v1], v);
                    }
                }
            }
        }
        long v2 = activityManager$MemoryInfo0.availMem;
        activityManager0.getMemoryInfo(activityManager$MemoryInfo0);
        long v3 = activityManager$MemoryInfo0.availMem - v2;
        return (int)(v3 <= 0L ? 0L : v3 / 0x100000L);
    }

    private void a1(String s, int v) {
        this.I.b3(s);
    }

    private Object b0() {
        int v = Build.VERSION.SDK_INT;
        if(v >= 29) {
            try {
                Object object0 = this.k0();
                if(this.a0 == null) {
                    this.a0 = XposedHelpers.findField(object0.getClass(), "mLastResumedActivity");
                }
                Object object1 = this.a0.get(object0);
                if(object1 != null) {
                    return object1;
                }
                Object object2 = this.y0();
                synchronized(object2) {
                    Object object3 = XposedHelpers.callMethod(object0, "getTopDisplayFocusedStack", new Object[0]);
                    return object3 != null ? XposedHelpers.callMethod(object3, (v <= 29 ? "getTopActivity" : "topRunningActivity"), new Object[0]) : null;
                }
                try {
                label_19:
                    if(this.a0 == null) {
                        this.a0 = XposedHelpers.findField(this.m(), (v < 26 ? "mFocusedActivity" : "mLastResumedActivity"));
                    }
                    Object object4 = this.a0.get(this.v());
                    if(object4 != null) {
                        return object4;
                    }
                }
                catch(Throwable unused_ex) {
                }
                Object object5 = this.y0();
                synchronized(object5) {
                    Object object6 = this.r("getFocusedStack", new Object[0]);
                    if(object6 != null) {
                        String s = Build.VERSION.SDK_INT < 28 ? "topActivity" : "getTopActivity";
                        return XposedHelpers.callMethod(object6, s, new Object[0]);
                    }
                    return null;
                }
            }
            catch(Throwable unused_ex) {
            }
        }
        else {
            goto label_19;
        }
        return null;
    }

    private void b1(String s, int v) {
        this.I.c3(s);
        if(this.T && !this.H.o(29) && this.I0(s, v)) {
            this.M.postDelayed(() -> if(this.N.c(s, v) <= 0) {
                this.Q0(s, v);
            }, 1000L);
        }
    }

    private int c0(int v) {
        try {
            return p3.e(this.V(v));
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return -1;
        }
    }

    private void c1(Object object0) {
        String s = com.jozein.xedgepro.xposed.b.d(object0);
        if(s != null) {
            this.d1(s, com.jozein.xedgepro.xposed.b.g(object0));
        }
    }

    void d0(Object object0) {
        if(object0 == null) {
            return;
        }
        if(q2.s(com.jozein.xedgepro.xposed.b.c(object0))) {
            return;
        }
        Object object1 = com.jozein.xedgepro.xposed.b.a(object0);
        int v = Build.VERSION.SDK_INT;
        if(v >= 24) {
            this.r("finishActivity", new Object[]{object1, 0, new Intent(), 0});
            return;
        }
        if(v >= 21) {
            this.r("finishActivity", new Object[]{object1, 0, new Intent(), Boolean.FALSE});
            return;
        }
        this.r("finishActivity", new Object[]{object1, 0, new Intent()});
    }

    private void d1(String s, int v) {
        if(s != null && this.N.b(s, v) <= 0) {
            this.b1(s, v);
        }
    }

    void e0() {
        Object object0 = this.n0();
        if(object0 != null) {
            this.d0(object0);
            return;
        }
        this.f0(this.p0());
    }

    private void e1(Object object0) {
        String s = com.jozein.xedgepro.xposed.b.d(object0);
        if(s != null) {
            this.f1(s, com.jozein.xedgepro.xposed.b.g(object0));
        }
    }

    void f0(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0) {
        if(activityManager$RunningTaskInfo0.numActivities != 1) {
            try {
                Object object0 = this.v1(activityManager$RunningTaskInfo0.id);
                if(object0 != null) {
                    this.d0(object0);
                    return;
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        this.j1(activityManager$RunningTaskInfo0.id);
    }

    private void f1(String s, int v) {
        if(s != null && this.N.e(s, v) == 1) {
            this.a1(s, v);
        }
    }

    void g0() {
        Object object0 = this.n0();
        if(object0 != null) {
            this.h0(object0);
            return;
        }
        ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0 = this.p0();
        this.i0(activityManager$RunningTaskInfo0.topActivity.getPackageName(), this.A0(activityManager$RunningTaskInfo0));
    }

    void g1(int v) {
        if(v < 0 || v > 3) {
            v = 0;
        }
        this.R0(true, com.jozein.xedgepro.xposed.a.d(this, v));
    }

    void h0(Object object0) {
        String s = com.jozein.xedgepro.xposed.b.d(object0);
        if(s != null) {
            this.i0(s, com.jozein.xedgepro.xposed.b.g(object0));
        }
    }

    private void h1(int v, int v1) {
        synchronized(this.P) {
            this.P.put(v, v1);
        }
    }

    void i0(String s, int v) {
        if(!this.K.j(s)) {
            return;
        }
        ActivityManager activityManager0 = this.j0();
        if(v == 0) {
            XposedHelpers.callMethod(activityManager0, "forceStopPackage", new Object[]{s});
        }
        else {
            XposedHelpers.callMethod(activityManager0, "forceStopPackageAsUser", new Object[]{s, v});
        }
        this.Q0(s, v);
        this.N.a(s, v);
    }

    boolean i1() {
        List list0 = this.O.c();
        for(Object object0: list0) {
            this.K.v(((d)object0).z, ((d)object0).A, false);
        }
        return list0.size() > 0;
    }

    ActivityManager j0() {
        if(this.R == null) {
            this.R = (ActivityManager)this.L.getSystemService("activity");
        }
        return this.R;
    }

    void j1(int v) {
        if(this.X == null) {
            this.X = this.i("removeTask");
        }
        if(Build.VERSION.SDK_INT >= 21) {
            this.X.invoke(this.v(), v);
            return;
        }
        this.X.invoke(this.v(), v, 0);
    }

    private Object k0() {
        if(this.j0 == null) {
            try {
                this.j0 = this.u("mActivityTaskManager");
                return this.j0;
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                this.j0 = g3.j();
            }
        }
        return this.j0;
    }

    private void k1(int v) {
        synchronized(this.P) {
            this.P.delete(v);
        }
    }

    List l0() {
        List list0 = this.w0();
        List list1 = this.K.m();
        int v = list0.size();
        List list2 = new ArrayList(v);
        f.o0.a o0$a0 = o0.f(this.L);
        for(int v1 = 1; v1 < v; ++v1) {
            ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0 = (ActivityManager.RunningTaskInfo)list0.get(v1);
            if(!list1.contains(activityManager$RunningTaskInfo0.baseActivity.getPackageName())) {
                int v2 = this.A0(activityManager$RunningTaskInfo0);
                if(o0$a0.a(v2)) {
                    try {
                        list2.add(new j(this, activityManager$RunningTaskInfo0, v2));
                    }
                    catch(Throwable throwable0) {
                        v.c(throwable0.toString());
                    }
                }
            }
        }
        return list2;
    }

    boolean l1(String s, int v, boolean z) {
        try {
            if(!z) {
                this.O.i(s, v);
                if(this.K.r(s, v)) {
                    return true;
                }
                this.K.u(s, v, true);
                return true;
            }
            if(this.I0(s, v) || !this.K.r(s, v)) {
                return true;
            }
            if(this.K.i(s, v)) {
                this.K.u(s, v, false);
                if(this.K.r(s, v)) {
                    return false;
                }
                this.O.i(s, v);
                return true;
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        return false;
    }

    Context m0() {
        return this.L;
    }

    void m1(int v) {
        if(v != this.Q) {
            this.Q = v;
        }
    }

    private Object n0() {
        Object object0 = this.b0();
        if(object0 != null && com.jozein.xedgepro.xposed.b.b(object0) != 0) {
            object0 = null;
        }
        return object0 == null ? this.I.g2().K() : object0;
    }

    void n1() {
        this.T = true;
        this.K.w();
    }

    String o0() {
        try {
            Object object0 = this.n0();
            if(object0 != null) {
                String s = com.jozein.xedgepro.xposed.b.d(object0);
                if(s != null) {
                    return s;
                }
            }
        }
        catch(Throwable unused_ex) {
        }
        return this.p0().topActivity.getPackageName();
    }

    void o1(Intent intent0, Bundle bundle0, int v, boolean z) {
        String s = c0.f(intent0);
        if(s != null && !this.K.r(s, v)) {
            if(c0.x(intent0, 0, v) != null) {
                try {
                    this.q1(intent0, z, bundle0, v);
                    return;
                }
                catch(Throwable unused_ex) {
                }
            }
            this.u1(s, intent0, bundle0, v);
            return;
        }
        this.q1(intent0, z, bundle0, v);
    }

    ActivityManager.RunningTaskInfo p0() {
        if(!this.J.d()) {
            return (ActivityManager.RunningTaskInfo)this.j0().getRunningTasks(1).get(0);
        }
        List list0 = this.v0();
        List list1 = this.r0(list0);
        return list1.isEmpty() ? ((ActivityManager.RunningTaskInfo)list0.get(0)) : ((ActivityManager.RunningTaskInfo)list1.get(0));
    }

    void p1(Intent intent0, Bundle bundle0, int v, int v1) {
        intent0.addFlags(0x30000000);
        if(v1 != -1 && v1 != 0) {
            Bundle bundle1 = com.jozein.xedgepro.xposed.a.f(bundle0, v1);
            DisplayManager displayManager0 = (DisplayManager)this.L.getSystemService("display");
            Context context0 = this.L.createDisplayContext(displayManager0.getDisplay(v1));
            if(v == 0) {
                context0.startActivity(intent0, bundle1);
                return;
            }
            XposedHelpers.callMethod(context0, "startActivityAsUser", new Object[]{intent0, bundle1, o0.q(v)});
            return;
        }
        this.q1(intent0, true, bundle0, v);
    }

    f q0() {
        Object object0 = this.n0();
        if(object0 != null) {
            return f.D(this, object0);
        }
        ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0 = this.p0();
        if(Build.VERSION.SDK_INT >= 29 && activityManager$RunningTaskInfo0.numActivities == 1) {
            return new j(this, activityManager$RunningTaskInfo0, this.A0(activityManager$RunningTaskInfo0)).t();
        }
        Object object1 = this.v1(activityManager$RunningTaskInfo0.id);
        return object1 == null ? new j(this, activityManager$RunningTaskInfo0, this.A0(activityManager$RunningTaskInfo0)).t() : f.D(this, object1);
    }

    void q1(Intent intent0, boolean z, Bundle bundle0, int v) {
        Objects.requireNonNull(intent0, "intent == null");
        if(this.J.d()) {
            bundle0 = com.jozein.xedgepro.xposed.a.e(bundle0);
        }
        intent0.addFlags((z ? 0x30000000 : 0x10000000));
        try {
            this.r1(intent0, (z ? 1 : 0), bundle0, v);
        }
        catch(NoSuchMethodError unused_ex) {
            Context context0 = this.L;
            if(v == 0) {
                context0.startActivity(intent0, bundle0);
                return;
            }
            XposedHelpers.callMethod(context0, "startActivityAsUser", new Object[]{intent0, bundle0, o0.q(v)});
        }
    }

    private List r0(List list0) {
        if(Build.VERSION.SDK_INT >= 29 && this.J.d()) {
            int v = list0.size();
            List list1 = new ArrayList(v);
            for(int v1 = 0; v1 < v; ++v1) {
                try {
                    ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0 = (ActivityManager.RunningTaskInfo)list0.get(v1);
                    switch(f3.a(activityManager$RunningTaskInfo0)) {
                        case -1: 
                        case 0: {
                            list1.add(activityManager$RunningTaskInfo0);
                        }
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                    return list1;
                }
            }
            return list1;
        }
        return list0;
    }

    private void r1(Intent intent0, int v, Bundle bundle0, int v1) {
        String s1;
        Object object0;
        String s = intent0.getType();
        if(s == null && intent0.getData() != null && "content".equals(intent0.getData().getScheme())) {
            try {
                if(this.U == null) {
                    this.U = XposedHelpers.findMethodExact(this.m(), "getProviderMimeType", new Class[]{Uri.class, Integer.TYPE});
                }
                s = (String)this.U.invoke(this.v(), intent0.getData(), v1);
            }
            catch(Throwable unused_ex) {
            }
        }
        if(Build.VERSION.SDK_INT >= 21) {
            if(this.V == null) {
                this.V = XposedHelpers.findMethodBestMatch(this.m(), "startActivityAsUser", new Object[]{null, null, intent0, s, null, null, 0, v, null, bundle0, v1});
            }
            object0 = this.V.invoke(this.v(), null, null, intent0, s, null, null, 0, v, null, bundle0, v1);
        }
        else {
            object0 = this.r("startActivityAsUser", new Object[]{null, null, intent0, s, null, null, 0, v, null, null, bundle0, v1});
        }
        switch(((int)(((Integer)object0)))) {
            case 0: 
            case 1: 
            case 2: 
            case 3: 
            case 4: {
                return;
            }
            default: {
                if(intent0.getComponent() != null) {
                    s1 = intent0.getComponent().flattenToShortString();
                }
                else if(intent0.getPackage() != null) {
                    s1 = intent0.getPackage();
                }
                else {
                    s1 = intent0.toString();
                }
                throw new RuntimeException("Failed to start: " + s1);
            }
        }
    }

    private int s0() {
        if(this.b0 < 0) {
            this.b0 = XposedHelpers.getStaticIntField(ActivityManager.class, "PROCESS_STATE_CACHED_EMPTY");
        }
        return this.b0;
    }

    void s1(String s, Bundle bundle0, int v) {
        if(this.K.r(s, v)) {
            this.q1(c0.g(s, v), true, bundle0, v);
            return;
        }
        try {
            this.q1(c0.g(s, v), true, bundle0, v);
        }
        catch(Throwable unused_ex) {
            this.u1(s, null, bundle0, v);
        }
    }

    private Object t0() {
        if(this.g0 == null) {
            this.g0 = XposedHelpers.getObjectField(this.k0(), (Build.VERSION.SDK_INT <= 29 ? "mRootActivityContainer" : "mRootWindowContainer"));
        }
        return this.g0;
    }

    void t1(String s, int v) {
        Intent intent0 = new Intent("android.settings.APPLICATION_DETAILS_SETTINGS", Uri.fromParts("package", s, null)).putExtra("userId", v);
        try {
            this.q1(intent0, false, null, v);
        }
        catch(Throwable throwable0) {
            v.c(throwable0.toString());
            if(v != this.Q) {
                Intent intent1 = new Intent("miui.intent.action.APP_MANAGER_APPLICATION_DETAIL").putExtra("package_name", s).putExtra("miui.intent.extra.USER_ID", v);
                try {
                    this.q1(intent1, false, null, this.Q);
                }
                catch(Throwable throwable1) {
                    v.c(throwable1.toString());
                }
            }
        }
    }

    List u0() {
        int[] arr_v = o0.e(this.L);
        ArrayList arrayList0 = new ArrayList();
        if(o0.k(arr_v)) {
            this.O.e(arrayList0, this.Q);
        }
        else {
            this.O.g(arrayList0, arr_v);
        }
        int v = arrayList0.size();
        List list0 = new ArrayList(v);
        for(int v1 = 0; v1 < v; ++v1) {
            d l0$d0 = (d)arrayList0.get(v1);
            try {
                list0.add(new f(this, l0$d0.z, l0$d0.A));
            }
            catch(Throwable unused_ex) {
            }
        }
        return list0;
    }

    private void u1(String s, Intent intent0, Bundle bundle0, int v) {
        this.K.v(s, v, true);
        try {
            if(intent0 == null) {
                intent0 = c0.g(s, v);
            }
            this.M.postDelayed(() -> try {
                this.q1(intent0, true, bundle0, v);
                this.O.a(s, v);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                this.K.v(s, v, false);
            }, 0xFAL);
        }
        catch(PackageManager.NameNotFoundException packageManager$NameNotFoundException0) {
            this.K.v(s, v, false);
            throw packageManager$NameNotFoundException0;
        }
    }

    List v0() {
        return this.j0().getRunningTasks(100);
    }

    private Object v1(int v) {
        try {
            Object object0 = this.V(v);
            if(object0 == null) {
                return null;
            }
            if(this.i0 == null) {
                Method method0 = p2.b(object0.getClass(), "topRunningActivityLocked", new Class[0]);
                this.i0 = method0;
                if(method0 == null) {
                    this.i0 = Build.VERSION.SDK_INT < 30 ? p2.b(object0.getClass(), "getTopActivity", new Class[0]) : p2.b(object0.getClass(), "getTopNonFinishingActivity", new Class[0]);
                }
            }
            if(this.i0 != null) {
                Object object1 = this.y0();
                return this.i0.invoke(object0);
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        return null;
    }

    List w0() {
        return this.r0(this.v0());
    }

    @Override  // com.jozein.xedgepro.xposed.j2
    protected Object x() {
        return g3.i();
    }

    private int x0(int v) {
        synchronized(this.P) {
        }
        return this.P.get(v, -1);
    }

    private Object y0() {
        if(this.k0 == null) {
            this.k0 = Build.VERSION.SDK_INT >= 29 ? XposedHelpers.getObjectField(this.k0(), "mGlobalLock") : this.v();
        }
        return this.k0;
    }

    int z0(int v) {
        int v1 = this.x0(v);
        if(v1 >= 0) {
            return v1;
        }
        int v2 = this.c0(v);
        if(v2 >= 0) {
            this.h1(v, v2);
            return v2;
        }
        return this.Q;
    }
}


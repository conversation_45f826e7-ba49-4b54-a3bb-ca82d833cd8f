package d;

import a.b.s0;
import a.v.a;
import android.os.Bundle;
import android.view.View;
import c.v;
import e.j.k;
import e.j;
import e.y;
import e.z;

public class d2 extends j {
    private a M;

    @Override  // e.j
    protected int B0() {
        this.c0(0x7F060216);  // string:variables "Variables"
        a v$a0 = a.g();
        this.M = v$a0;
        return v$a0.k() + 1;
    }

    @Override  // e.j0$c
    protected void J(Bundle bundle0, int v) {
        if(bundle0 == null) {
            return;
        }
        switch(v) {
            case 1: {
                CharSequence charSequence0 = bundle0.getCharSequence("result");
                if(charSequence0 != null) {
                    try {
                        if(this.M.e(this.f(), charSequence0.toString())) {
                            this.x0(this.M.k() - 1);
                            return;
                        }
                    }
                    catch(Throwable throwable0) {
                        this.g0(throwable0);
                    }
                }
                return;
            }
            case 2: {
                int v1 = bundle0.getInt("result", -1);
                int v2 = this.E0();
                switch(v1) {
                    case 0: {
                        s0.D(this.f(), 14, this.M.f(v2));
                        return;
                    }
                    case 1: {
                        try {
                            this.M.i(this.f(), this.M.f(v2));
                            this.u1(v2);
                        }
                        catch(Throwable throwable0) {
                            this.g0(throwable0);
                        }
                        return;
                    }
                    default: {
                        return;
                    }
                }
            }
            case 3: {
                this.U(bundle0);
                this.L();
            }
        }
    }

    @Override  // e.j
    protected View h1(int v) {
        return v < this.M.k() ? new k(this, this.M.f(v)) : this.b1();
    }

    @Override  // e.j
    protected void k1(int v) {
        int v2;
        v v1;
        if(v < this.M.k()) {
            v1 = new v().z(this.M.f(v));
            v2 = 3;
        }
        else {
            v1 = new y().J(this.u(0x7F0600F8), this.u(0x7F060215), null, 6, 0x20);  // string:enter_name "Enter name"
            v2 = 1;
        }
        this.N(v1, v2);
    }

    @Override  // e.j
    protected boolean l1(int v) {
        if(v < this.M.k()) {
            this.N(new z().u(new CharSequence[]{this.u(0x7F0601B9), this.u(0x7F0600CD)}), 2);  // string:show_value "Show value"
            return true;
        }
        return super.l1(v);
    }

    @Override  // android.app.Fragment
    public void onResume() {
        super.onResume();
        if(this.M != null && this.H0() != this.M.k()) {
            this.w1();
        }
    }
}


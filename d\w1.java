package d;

import a.b;
import a.z;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import e.j.g;
import e.j.k;
import f.v;

public class w1 extends c implements a {
    private z M;

    @Override  // e.j0$c
    protected final void B() {
        super.B();
        this.M1();
    }

    @Override  // e.j
    protected int B0() {
        this.M = this.g().h();
        return 4;
    }

    @Override  // d.c
    protected void C1(Bundle bundle0, int v) {
        if(v == 1) {
            Context context0 = this.f();
            b b0 = (b)bundle0.getParcelable("result");
            if(b0 != null) {
                int v1 = this.E0();
                int v2 = this.F0();
                this.M.l0(context0, 3, v1, v2, b0);
                g j$g0 = (g)this.K0(v2);
                j$g0.setSubText(b0.n(context0));
                j$g0.setImageDrawable(this.D0(b0));
            }
        }
        else {
            v.c(("Unknown code: " + v));
        }
    }

    protected String I1(int v) {
        return this.getString(0x7F06009A, new Object[]{((int)(v + 1))});  // string:column_index_f "Column %1$d"
    }

    protected String J1(int v) {
        return this.getString(0x7F060191, new Object[]{((int)(v + 1))});  // string:row_index_f "Row %1$d"
    }

    protected k K1(int v) {
        return new k(this, this.J1(v));
    }

    protected k L1(int v) {
        Context context0 = this.M0();
        b b0 = this.M.m(this.I0(), v);
        return new g(this, this.I1(v), b0.n(context0), this.D0(b0));
    }

    protected void M1() {
        this.c0(0x7F0601B2);  // string:shortcuts_panel "Shortcuts panel"
    }

    @Override  // e.j
    protected boolean N0(int v) {
        return true;
    }

    @Override  // e.j
    protected View h1(int v) {
        return this.K1(v);
    }

    @Override  // e.j
    protected View i1(int v) {
        return this.L1(v);
    }

    @Override  // e.j
    protected void k1(int v) {
        this.y1(v, 4);
    }

    @Override  // e.j
    protected void o1(int v) {
        this.P(new d().J1(6, this.u(0x7F0601B2), this.J1(this.I0()), this.I1(v)), 1);  // string:shortcuts_panel "Shortcuts panel"
    }

    @Override  // e.j
    protected boolean p1(int v) {
        this.F1(this.M.m(this.I0(), v), 1, 6);
        return true;
    }
}


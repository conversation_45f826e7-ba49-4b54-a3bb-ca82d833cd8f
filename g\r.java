package g;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint.Cap;
import android.graphics.Paint.Join;
import android.graphics.Paint.Style;
import android.graphics.Paint;
import android.graphics.Path.Direction;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.PorterDuff.Mode;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.view.MotionEvent;
import android.view.View;
import f.v;
import java.util.ArrayList;

public class r extends View {
    static class a {
    }

    static class b implements e {
        private final float a;
        private final float b;
        private final float c;
        private final Paint d;
        private final float[] e;
        private final PointF f;
        private float g;
        private float h;
        private float i;
        private float j;
        private Path k;
        private boolean l;

        b(Context context0, Paint paint0) {
            this.e = new float[14];
            this.f = new PointF();
            this.g = -1.0f;
            this.h = 0.0f;
            this.i = 0.0f;
            this.j = 0.0f;
            this.k = null;
            this.l = false;
            this.d = paint0;
            float f = a0.m(context0);
            this.a = 0.03f * f;
            this.b = 0.075f * f;
            this.c = f * 0.08f;
        }

        @Override  // g.r$e
        public void a(d r$d0) {
            float f = r$d0.e();
            this.i = f;
            this.e[0] = f;
            float f1 = r$d0.g();
            this.j = f1;
            this.e[1] = f1;
            this.g();
        }

        @Override  // g.r$e
        public Path b(d r$d0) {
            this.a(r$d0);
            Path path0 = this.l ? this.k : null;
            this.k = null;
            return path0;
        }

        @Override  // g.r$e
        public void c(Canvas canvas0) {
            if(this.l) {
                Path path0 = this.k;
                if(path0 != null) {
                    canvas0.drawPath(path0, this.d);
                }
            }
        }

        @Override  // g.r$e
        public void d(d r$d0) {
        }

        @Override  // g.r$e
        public boolean e(d r$d0) {
            return false;
        }

        @Override  // g.r$e
        public void f(d r$d0) {
            float f = r$d0.e();
            this.g = f;
            this.i = f;
            float f1 = r$d0.g();
            this.h = f1;
            this.j = f1;
        }

        private void g() {
            if(r.a(this.g, this.h, this.i, this.j) < this.c * this.c) {
                this.l = false;
                return;
            }
            this.l = true;
            float f = r.f(this.g, this.h, this.i, this.j);
            PointF pointF0 = r.j(this.g, this.h, f + 1.570796f, this.a, this.f);
            this.e[6] = pointF0.x;
            this.e[7] = pointF0.y;
            PointF pointF1 = r.j(this.g, this.h, f - 1.570796f, this.a, this.f);
            this.e[8] = pointF1.x;
            this.e[9] = pointF1.y;
            PointF pointF2 = r.j(this.i, this.j, ((float)(((double)f) + 3.141593)), this.c, this.f);
            float f1 = pointF2.x;
            float f2 = pointF2.y;
            PointF pointF3 = r.j(f1, f2, f + 1.570796f, this.a, this.f);
            this.e[4] = pointF3.x;
            this.e[5] = pointF3.y;
            PointF pointF4 = r.j(f1, f2, f - 1.570796f, this.a, this.f);
            this.e[10] = pointF4.x;
            this.e[11] = pointF4.y;
            PointF pointF5 = r.j(f1, f2, f + 1.570796f, this.b, this.f);
            this.e[2] = pointF5.x;
            this.e[3] = pointF5.y;
            PointF pointF6 = r.j(f1, f2, f - 1.570796f, this.b, this.f);
            this.e[12] = pointF6.x;
            this.e[13] = pointF6.y;
            Path path0 = this.k;
            if(path0 == null) {
                this.k = new Path();
            }
            else {
                path0.reset();
            }
            this.k.moveTo(this.e[0], this.e[1]);
            for(int v = 1; v < 7; ++v) {
                this.k.lineTo(this.e[v * 2], this.e[v * 2 + 1]);
            }
            this.k.close();
        }
    }

    static class c implements e {
        private final Paint a;
        private final Path[] b;
        private final Path[] c;
        private final boolean d;
        private final Path e;
        private boolean f;

        c(Paint paint0, boolean z) {
            this.b = new Path[10];
            this.c = new Path[10];
            this.f = false;
            this.a = paint0;
            this.d = z;
            this.e = z ? null : new Path();
            for(int v = 0; v < 10; ++v) {
                this.b[v] = new Path();
            }
        }

        @Override  // g.r$e
        public void a(d r$d0) {
            this.f = true;
            int v = r$d0.b();
            for(int v1 = 0; v1 < v; ++v1) {
                int v2 = r$d0.f(v1);
                Path[] arr_path = this.c;
                if(arr_path[v2] == null) {
                    this.g(v2, r$d0.c(v1), r$d0.a(v1));
                }
                else {
                    arr_path[v2].lineTo(r$d0.c(v1), r$d0.a(v1));
                }
            }
        }

        @Override  // g.r$e
        public Path b(d r$d0) {
            this.a(r$d0);
            int v = 0;
            this.f = false;
            if(this.d) {
                while(v < 10) {
                    Path[] arr_path = this.c;
                    if(arr_path[v] != null) {
                        arr_path[v].reset();
                        this.c[v] = null;
                    }
                    ++v;
                }
                return null;
            }
            this.e.reset();
            while(v < 10) {
                Path[] arr_path1 = this.c;
                if(arr_path1[v] != null) {
                    this.e.addPath(arr_path1[v]);
                    this.c[v].reset();
                    this.c[v] = null;
                }
                ++v;
            }
            return this.e;
        }

        @Override  // g.r$e
        public void c(Canvas canvas0) {
            if(!this.f) {
                return;
            }
            for(int v = 0; v < 10; ++v) {
                Path[] arr_path = this.c;
                if(arr_path[v] != null) {
                    canvas0.drawPath(arr_path[v], this.a);
                }
            }
        }

        @Override  // g.r$e
        public void d(d r$d0) {
            this.a(r$d0);
            if(this.d) {
                int v = r$d0.h();
                Path[] arr_path = this.c;
                if(arr_path[v] != null) {
                    arr_path[v].reset();
                    this.c[v] = null;
                }
            }
        }

        @Override  // g.r$e
        public boolean e(d r$d0) {
            Path path0;
            this.f = true;
            int v = r$d0.h();
            int v1 = r$d0.b();
            for(int v2 = 0; v2 < v1; ++v2) {
                int v3 = r$d0.f(v2);
                float f = r$d0.c(v2);
                float f1 = r$d0.a(v2);
                Path[] arr_path = this.c;
                if(arr_path[v3] == null) {
                    this.g(v3, f, f1);
                }
                else {
                    if(v2 == v) {
                        if(this.d) {
                            arr_path[v3].reset();
                        }
                        this.c[v3].moveTo(f, f1);
                        path0 = this.c[v3];
                        f1 += 0.1f;
                    }
                    else {
                        path0 = arr_path[v3];
                    }
                    path0.lineTo(f, f1);
                }
            }
            return true;
        }

        @Override  // g.r$e
        public void f(d r$d0) {
            this.f = true;
            this.g(0, r$d0.e(), r$d0.g());
        }

        private void g(int v, float f, float f1) {
            this.c[v] = this.b[v];
            this.c[v].moveTo(f, f1);
            this.c[v].lineTo(f, f1 + 0.1f);
        }
    }

    interface d {
        float a(int arg1);

        int b();

        float c(int arg1);

        int d();

        float e();

        int f(int arg1);

        float g();

        int h();
    }

    interface e {
        void a(d arg1);

        Path b(d arg1);

        void c(Canvas arg1);

        void d(d arg1);

        boolean e(d arg1);

        void f(d arg1);
    }

    static class f implements e {
        private final Paint a;
        private float b;
        private float c;
        private float d;
        private float e;
        private boolean f;

        f(Paint paint0) {
            this.b = -1.0f;
            this.c = 0.0f;
            this.d = 0.0f;
            this.e = 0.0f;
            this.f = false;
            this.a = paint0;
        }

        @Override  // g.r$e
        public void a(d r$d0) {
            this.f = true;
            this.d = r$d0.e();
            this.e = r$d0.g();
        }

        @Override  // g.r$e
        public Path b(d r$d0) {
            this.a(r$d0);
            this.f = false;
            Path path0 = new Path();
            path0.moveTo(this.b, this.c);
            path0.lineTo(this.d, this.e);
            return path0;
        }

        @Override  // g.r$e
        public void c(Canvas canvas0) {
            if(this.f) {
                canvas0.drawLine(this.b, this.c, this.d, this.e, this.a);
            }
        }

        @Override  // g.r$e
        public void d(d r$d0) {
        }

        @Override  // g.r$e
        public boolean e(d r$d0) {
            return false;
        }

        @Override  // g.r$e
        public void f(d r$d0) {
            this.f = false;
            this.b = r$d0.e();
            this.c = r$d0.g();
        }
    }

    static class g implements d {
        public q a;

        private g() {
            this.a = null;
        }

        g(a r$a0) {
        }

        @Override  // g.r$d
        public float a(int v) {
            return (float)this.a.B[v].c;
        }

        @Override  // g.r$d
        public int b() {
            return this.a.B.length;
        }

        @Override  // g.r$d
        public float c(int v) {
            return (float)this.a.B[v].b;
        }

        @Override  // g.r$d
        public int d() {
            return this.a.A & 0xFF;
        }

        @Override  // g.r$d
        public float e() {
            return (float)this.a.B[this.h()].b;
        }

        @Override  // g.r$d
        public int f(int v) {
            return this.a.B[v].a;
        }

        @Override  // g.r$d
        public float g() {
            return (float)this.a.B[this.h()].c;
        }

        @Override  // g.r$d
        public int h() {
            return (this.a.A & 0xFF00) >> 8;
        }
    }

    static class h implements d {
        public MotionEvent a;

        private h() {
            this.a = null;
        }

        h(a r$a0) {
        }

        @Override  // g.r$d
        public float a(int v) {
            return this.a.getY(v);
        }

        @Override  // g.r$d
        public int b() {
            return this.a.getPointerCount();
        }

        @Override  // g.r$d
        public float c(int v) {
            return this.a.getX(v);
        }

        @Override  // g.r$d
        public int d() {
            return this.a.getActionMasked();
        }

        @Override  // g.r$d
        public float e() {
            return this.a.getX();
        }

        @Override  // g.r$d
        public int f(int v) {
            return this.a.getPointerId(v);
        }

        @Override  // g.r$d
        public float g() {
            return this.a.getY();
        }

        @Override  // g.r$d
        public int h() {
            return this.a.getActionIndex();
        }
    }

    static class i {
        final Path a;
        final Paint b;

        i(Path path0, Paint paint0) {
            this.a = path0;
            this.b = paint0;
        }
    }

    static class j implements e {
        private final Paint a;
        private final float b;
        private final RectF c;
        private float d;
        private float e;
        private float f;
        private float g;
        private boolean h;

        j(Context context0, Paint paint0) {
            this.c = new RectF();
            this.d = -1.0f;
            this.e = 0.0f;
            this.f = 0.0f;
            this.g = 0.0f;
            this.h = false;
            this.a = paint0;
            this.b = a0.m(context0) * 0.01f;
        }

        @Override  // g.r$e
        public void a(d r$d0) {
            this.h = true;
            this.f = r$d0.e();
            float f = r$d0.g();
            this.g = f;
            float f1 = this.d;
            float f2 = this.f;
            if(f1 < f2) {
                this.c.left = f1;
                this.c.right = f2;
            }
            else {
                this.c.left = f2;
                this.c.right = f1;
            }
            float f3 = this.e;
            if(f3 < f) {
                this.c.top = f3;
                this.c.bottom = f;
                return;
            }
            this.c.top = f;
            this.c.bottom = f3;
        }

        @Override  // g.r$e
        public Path b(d r$d0) {
            this.a(r$d0);
            this.h = false;
            Path path0 = new Path();
            if(this.g()) {
                path0.addRoundRect(this.c, this.b, this.b, Path.Direction.CW);
                return path0;
            }
            path0.addRect(this.c, Path.Direction.CW);
            return path0;
        }

        @Override  // g.r$e
        public void c(Canvas canvas0) {
            if(!this.h) {
                return;
            }
            if(this.g()) {
                canvas0.drawRoundRect(this.c, this.b, this.b, this.a);
                return;
            }
            canvas0.drawRect(this.c, this.a);
        }

        @Override  // g.r$e
        public void d(d r$d0) {
        }

        @Override  // g.r$e
        public boolean e(d r$d0) {
            return false;
        }

        @Override  // g.r$e
        public void f(d r$d0) {
            this.h = false;
            float f = r$d0.e();
            this.f = f;
            this.d = f;
            float f1 = r$d0.g();
            this.g = f1;
            this.e = f1;
        }

        private boolean g() {
            float f = this.b + this.b;
            return (this.f - this.d > f || this.d - this.f > f) && (this.g - this.e > f || this.e - this.g > f);
        }
    }

    static class k implements e {
        private final Paint a;
        private Path b;
        private boolean c;

        k(Paint paint0) {
            this.b = null;
            this.c = false;
            this.a = paint0;
        }

        @Override  // g.r$e
        public void a(d r$d0) {
            Path path0 = this.b;
            if(path0 != null) {
                path0.lineTo(r$d0.e(), r$d0.g());
                this.c = true;
            }
        }

        @Override  // g.r$e
        public Path b(d r$d0) {
            this.a(r$d0);
            this.c = false;
            Path path0 = this.b;
            this.b = null;
            return path0;
        }

        @Override  // g.r$e
        public void c(Canvas canvas0) {
            if(this.c) {
                Path path0 = this.b;
                if(path0 != null) {
                    canvas0.drawPath(path0, this.a);
                }
            }
        }

        @Override  // g.r$e
        public void d(d r$d0) {
        }

        @Override  // g.r$e
        public boolean e(d r$d0) {
            return false;
        }

        @Override  // g.r$e
        public void f(d r$d0) {
            this.c = false;
            this.b = new Path();
            float f = r$d0.e();
            float f1 = r$d0.g();
            this.b.moveTo(f, f1);
            this.b.lineTo(f, f1 + 0.1f);
        }
    }

    private final Paint[] A;
    private final boolean B;
    private Path C;
    private ArrayList D;
    private int E;
    private final h F;
    private final g G;
    private int H;
    private boolean I;
    private e J;
    private boolean K;
    private final int[] z;

    public r(Context context0, int v) {
        this(context0, new int[]{v}, false);
    }

    public r(Context context0, int[] arr_v) {
        this(context0, arr_v, true);
    }

    private r(Context context0, int[] arr_v, boolean z) {
        super(context0);
        this.C = null;
        this.D = null;
        this.E = 0;
        this.F = new h(null);
        this.G = new g(null);
        this.H = 0;
        this.I = false;
        this.J = null;
        this.K = true;
        this.z = arr_v;
        this.B = z;
        this.A = new Paint[arr_v.length * 2];
    }

    static float a(float f, float f1, float f2, float f3) {
        return (f2 - f) * (f2 - f) + (f3 - f1) * (f3 - f1);
    }

    private void d(d r$d0) {
        switch(r$d0.d()) {
            case 0: {
                this.K = true;
                e r$e0 = this.J;
                if(r$e0 != null) {
                    r$e0.f(r$d0);
                }
                return;
            label_7:
                if(this.K) {
                    this.K = false;
                    this.invalidate();
                    return;
                }
                break;
            }
            case 1: {
                if(this.K) {
                    e r$e4 = this.J;
                    if(r$e4 != null) {
                        Path path0 = r$e4.b(r$d0);
                        if(path0 == null) {
                            this.K = false;
                        }
                        else if(this.B) {
                            if(this.D == null) {
                                this.D = new ArrayList();
                            }
                            int v = this.I ? this.H + this.z.length : this.H;
                            int v1 = this.D.size();
                            if(v1 > this.E) {
                                for(int v2 = v1 - 1; v2 >= this.E; --v2) {
                                    this.D.remove(v2);
                                }
                            }
                            this.D.add(new i(path0, this.A[v]));
                            ++this.E;
                        }
                        else {
                            if(this.C == null) {
                                this.C = new Path();
                            }
                            this.C.addPath(path0);
                        }
                        this.invalidate();
                        return;
                    }
                }
                break;
            }
            case 2: {
                if(this.K) {
                    e r$e3 = this.J;
                    if(r$e3 != null) {
                        r$e3.a(r$d0);
                        this.invalidate();
                        return;
                    }
                }
                break;
            }
            case 5: {
                if(this.K) {
                    e r$e2 = this.J;
                    if(r$e2 != null) {
                        this.K = r$e2.e(r$d0);
                        this.invalidate();
                        return;
                    }
                }
                break;
            }
            case 6: {
                if(this.K) {
                    e r$e1 = this.J;
                    if(r$e1 != null) {
                        r$e1.d(r$d0);
                    }
                }
                break;
            }
            default: {
                goto label_7;
            }
        }
        if(this.K) {
            this.invalidate();
        }
    }

    public void e(q q0) {
        this.G.a = q0;
        this.d(this.G);
    }

    private static float f(float f, float f1, float f2, float f3) {
        return (float)Math.atan2(f3 - f1, f2 - f);
    }

    public boolean g() {
        return this.B && (this.D != null && this.E < this.D.size());
    }

    public boolean h() {
        return this.B && this.D != null && this.E > 0;
    }

    private static float i(float f, float f1, float f2, float f3) [...] // Inlined contents

    private static PointF j(float f, float f1, float f2, float f3, PointF pointF0) {
        pointF0.x = (float)(((double)f) + Math.cos(f2) * ((double)f3));
        pointF0.y = (float)(((double)f1) + Math.sin(f2) * ((double)f3));
        return pointF0;
    }

    public void k() {
        if(this.B) {
            ArrayList arrayList0 = this.D;
            if(arrayList0 != null) {
                int v = this.E + 1;
                int v1 = arrayList0.size();
                if(v > v1) {
                    v = v1;
                }
                if(v != this.E) {
                    this.E = v;
                    this.invalidate();
                }
            }
        }
    }

    public void l(int v, int v1, boolean z) {
        c r$c0;
        if(v1 >= this.z.length) {
            v.d(new Throwable("Index out of range: " + v1 + ", size: " + this.z.length));
            return;
        }
        Context context0 = this.getContext();
        this.H = v1;
        this.I = z;
        int v2 = z ? this.z.length + v1 : v1;
        Paint paint0 = this.A[v2];
        if(paint0 == null) {
            paint0 = new Paint(1);
            paint0.setStrokeWidth(a0.m(context0) * 0.015f);
            paint0.setStrokeJoin(Paint.Join.ROUND);
            paint0.setStrokeCap(Paint.Cap.ROUND);
            paint0.setStyle((z ? Paint.Style.FILL_AND_STROKE : Paint.Style.STROKE));
            int[] arr_v = this.z;
            if(arr_v[v1] == 0) {
                paint0.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.DST_OUT));
            }
            else {
                paint0.setColor(arr_v[v1]);
            }
            this.A[v2] = paint0;
        }
        switch(v) {
            case 0: {
                paint0.setStyle(Paint.Style.STROKE);
                r$c0 = new c(paint0, false);
                break;
            }
            case 1: {
                paint0.setStyle(Paint.Style.STROKE);
                r$c0 = new c(paint0, true);
                break;
            }
            case 2: {
                paint0.setStyle(Paint.Style.STROKE);
                r$c0 = new k(paint0);
                break;
            }
            case 3: {
                r$c0 = new f(paint0);
                break;
            }
            case 4: {
                r$c0 = new j(context0, paint0);
                break;
            }
            case 5: {
                r$c0 = new b(context0, paint0);
                break;
            }
            default: {
                v.c(("Unknown style: " + v));
                return;
            }
        }
        this.J = r$c0;
    }

    public void m() {
        if(this.B && this.D != null) {
            int v = this.E - 1 >= 0 ? this.E - 1 : 0;
            if(v != this.E) {
                this.E = v;
                this.invalidate();
            }
        }
    }

    @Override  // android.view.View
    protected void onDraw(Canvas canvas0) {
        if(!this.B) {
            Path path0 = this.C;
            if(path0 != null) {
                canvas0.drawPath(path0, this.A[(this.I ? this.H + this.z.length : this.H)]);
            }
        }
        else if(this.D != null) {
            for(int v = 0; v < this.E; ++v) {
                i r$i0 = (i)this.D.get(v);
                canvas0.drawPath(r$i0.a, r$i0.b);
            }
        }
        if(this.K) {
            e r$e0 = this.J;
            if(r$e0 != null) {
                r$e0.c(canvas0);
            }
        }
    }

    @Override  // android.view.View
    public boolean onTouchEvent(MotionEvent motionEvent0) {
        this.F.a = motionEvent0;
        this.d(this.F);
        return true;
    }
}


package d;

import a.b.y0;
import android.os.Bundle;
import android.os.Parcelable;
import android.view.View;
import e.j.k;
import e.j;
import e.y;
import e.z;
import java.util.List;

public class a0 extends j {
    private List M;
    private CharSequence[] N;
    private int[] O;

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F060110);  // string:gesture_records "Gesture records"
    }

    @Override  // e.j
    protected int B0() {
        this.M = y0.E();
        y0.H();
        int[] arr_v = this.h().getBoolean("selectable", false) ? new int[]{0x7F0601A1, 0x7F060222} : new int[]{0x7F060222, 0x7F060159, 0x7F060158, 0x7F060182, 0x7F0600CD};  // string:select "Select"
        this.O = arr_v;
        this.N = new CharSequence[arr_v.length];
        for(int v = 0; true; ++v) {
            int[] arr_v1 = this.O;
            if(v >= arr_v1.length) {
                break;
            }
            CharSequence[] arr_charSequence = this.N;
            arr_charSequence[v] = this.u(arr_v1[v]);
        }
        return this.M.size() + 1;
    }

    protected k B1(int v) {
        if(v < this.M.size()) {
            y0 b$y00 = (y0)this.M.get(v);
            return new k(this, b$y00.A(), this.u((b$y00.F() ? 0x7F060168 : 0x7F060169)) + ", " + b$y00.C() + "ms");  // string:orientation_landscape "Landscape"
        }
        return this.b1();
    }

    public a0 C1(boolean z) {
        this.h().putBoolean("selectable", z);
        return this;
    }

    @Override  // e.j0$c
    protected void G() {
        super.G();
        y0.Q(this.f());
    }

    @Override  // e.j0$c
    protected void J(Bundle bundle0, int v) {
        if(bundle0 == null) {
            return;
        }
        if(v == 1) {
            int v2 = this.E0();
            switch(this.O[bundle0.getInt("result", -2)]) {
                case 0x7F0600CD: {  // string:delete "Delete"
                    try {
                        y0.M(((y0)this.M.get(v2)));
                        this.M.remove(v2);
                        this.u1(v2);
                    }
                    catch(Throwable throwable0) {
                        this.g0(throwable0);
                    }
                    return;
                }
                case 0x7F060158: {  // string:move_down "Move down"
                    if(v2 < this.M.size() - 1) {
                        try {
                            y0.I(((y0)this.M.get(v2)));
                            this.M.add(v2 + 1, ((y0)this.M.remove(v2)));
                            this.a1(v2, v2 + 1);
                            return;
                        label_39:
                            if(v2 > 0) {
                                y0.J(((y0)this.M.get(v2)));
                                this.M.add(v2 - 1, ((y0)this.M.remove(v2)));
                                this.a1(v2, v2 - 1);
                                return;
                            }
                        }
                        catch(Throwable throwable0) {
                            this.g0(throwable0);
                            return;
                        }
                    }
                    break;
                }
                case 0x7F060159: {  // string:move_up "Move up"
                    goto label_39;
                }
                case 0x7F060182: {  // string:rename "Rename"
                    y y0 = new y();
                    y0.K(this.u(0x7F0600F8), null, ((y0)this.M.get(v2)).A());  // string:enter_name "Enter name"
                    this.N(y0, 3);
                    return;
                }
                case 0x7F0601A1: {  // string:select "Select"
                    this.W("result", ((Parcelable)this.M.get(v2)));
                    this.L();
                    return;
                }
                case 0x7F060222: {  // string:view "View"
                    e2 e20 = new e2();
                    e20.k0(((y0)this.M.get(v2)));
                    this.P(e20, 4);
                }
            }
        }
        else {
            switch(v) {
                case 2: {
                    y0 b$y00 = (y0)bundle0.getParcelable("result");
                    if(b$y00 != null) {
                        this.M.add(b$y00);
                        this.x0(this.M.size() - 1);
                        return;
                    }
                    break;
                }
                case 3: {
                    CharSequence charSequence0 = bundle0.getCharSequence("result");
                    if(charSequence0 != null) {
                        int v1 = this.E0();
                        try {
                            ((y0)this.M.get(v1)).P(charSequence0.toString());
                            ((k)this.L0(v1)).setText(charSequence0);
                        }
                        catch(Throwable throwable0) {
                            this.g0(throwable0);
                        }
                        return;
                    }
                    break;
                }
            }
        }
    }

    @Override  // e.j
    protected View h1(int v) {
        return this.B1(v);
    }

    @Override  // e.j
    protected void k1(int v) {
        if(v < this.M.size()) {
            this.N(new z().u(this.N), 1);
            return;
        }
        this.P(new d0(), 2);
    }
}


package e;

import android.text.InputFilter;
import android.text.Spanned;

public final class w implements InputFilter {
    public static final w a;

    static {
        w.a = new w();
    }

    @Override  // android.text.InputFilter
    public final CharSequence filter(CharSequence charSequence0, int v, int v1, Spanned spanned0, int v2, int v3) {
        return y.D(charSequence0, v, v1, spanned0, v2, v3);
    }
}


package d;

import a.x;
import android.appwidget.AppWidgetHost;
import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProviderInfo;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.view.View;
import e.j.g;
import e.j.i.c;
import e.j.i;
import e.j;
import f.u;
import f.v;
import f.z;
import java.lang.reflect.Field;
import java.util.ArrayList;

public class f2 extends j {
    private AppWidgetManager M;
    private AppWidgetHost N;
    private u O;
    private static Field P;

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F060232);  // string:widgets "Widgets"
    }

    @Override  // e.j
    protected int B0() {
        Context context0 = this.f();
        this.M = AppWidgetManager.getInstance(context0);
        this.N = new AppWidgetHost(context0, 1);
        if(this.O == null) {
            this.O = z.K(context0).B();
        }
        return this.O.size();
    }

    private void B1(AppWidgetProviderInfo appWidgetProviderInfo0) {
        if(appWidgetProviderInfo0.configure == null) {
            int v = x.h().e(appWidgetProviderInfo0.provider.flattenToShortString());
            if(v != 0) {
                this.F1(v, appWidgetProviderInfo0);
                return;
            }
        }
        int v1 = this.N.allocateAppWidgetId();
        try {
            if(this.M.bindAppWidgetIdIfAllowed(v1, appWidgetProviderInfo0.provider)) {
                this.C1(v1, appWidgetProviderInfo0);
                return;
            }
            Intent intent0 = new Intent("android.appwidget.action.APPWIDGET_BIND");
            intent0.putExtra("appWidgetId", v1);
            intent0.putExtra("appWidgetProvider", appWidgetProviderInfo0.provider);
            this.startActivityForResult(intent0, 1);
        }
        catch(Throwable throwable0) {
            this.g0(throwable0);
            this.N.deleteAppWidgetId(v1);
        }
    }

    private void C1(int v, AppWidgetProviderInfo appWidgetProviderInfo0) {
        if(appWidgetProviderInfo0 == null) {
            return;
        }
        ComponentName componentName0 = appWidgetProviderInfo0.configure;
        if(componentName0 != null) {
            this.D1(v, componentName0);
            return;
        }
        this.F1(v, appWidgetProviderInfo0);
    }

    private void D1(int v, ComponentName componentName0) {
        Intent intent0 = new Intent("android.appwidget.action.APPWIDGET_CONFIGURE");
        intent0.setComponent(componentName0);
        intent0.putExtra("appWidgetId", v);
        this.startActivityForResult(intent0, 2);
    }

    private ApplicationInfo E1(int v) {
        int v1 = Build.VERSION.SDK_INT;
        if(v1 >= 0x1F) {
            return ((AppWidgetProviderInfo)((ArrayList)this.O.k(v)).get(0)).getActivityInfo().applicationInfo;
        }
        if(v1 >= 21) {
            try {
                if(f2.P == null) {
                    Field field0 = AppWidgetProviderInfo.class.getDeclaredField("providerInfo");
                    f2.P = field0;
                    field0.setAccessible(true);
                }
                AppWidgetProviderInfo appWidgetProviderInfo0 = (AppWidgetProviderInfo)((ArrayList)this.O.k(v)).get(0);
                return ((ActivityInfo)f2.P.get(appWidgetProviderInfo0)).applicationInfo;
            }
            catch(Throwable throwable0) {
                v.c(throwable0.toString());
            }
        }
        try {
            return z.K(this.f()).E(((String)this.O.e(v)));
        }
        catch(Throwable throwable1) {
            v.c(throwable1.toString());
            return null;
        }
    }

    private void F1(int v, AppWidgetProviderInfo appWidgetProviderInfo0) {
        if(appWidgetProviderInfo0 != null) {
            this.V("result", v);
        }
        this.L();
    }

    @Override  // e.j
    protected boolean N0(int v) {
        return true;
    }

    @Override  // e.j
    protected void g1(Object object0) {
        this.O = (u)object0;
    }

    @Override  // e.j
    protected View h1(int v) {
        CharSequence charSequence0;
        Drawable drawable0;
        PackageManager packageManager0 = this.f().getPackageManager();
        String s = (String)this.O.e(v);
        z z0 = z.K(this.f());
        try {
            drawable0 = null;
            drawable0 = z0.C(s);
            charSequence0 = z0.G(s);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            charSequence0 = null;
        }
        if(charSequence0 == null) {
            ApplicationInfo applicationInfo0 = this.E1(v);
            if(applicationInfo0 != null) {
                return new g(this, applicationInfo0.loadIcon(packageManager0), applicationInfo0.loadLabel(packageManager0), null);
            }
            drawable0 = this.j(0x7F040043);  // drawable:ic_floating_widget
            charSequence0 = (CharSequence)this.O.e(v);
        }
        return new g(this, drawable0, charSequence0, null);
    }

    @Override  // e.j
    protected View i1(int v) {
        return this.f1(((AppWidgetProviderInfo)((ArrayList)this.O.k(this.I0())).get(v)));
    }

    @Override  // e.j
    protected void k1(int v) {
        this.y1(v, ((ArrayList)this.O.k(v)).size());
    }

    @Override  // e.j
    protected i m1() {
        Object object0 = this.s();
        if(object0 instanceof u) {
            this.O = (u)object0;
        }
        return this.O != null ? null : new c();
    }

    @Override  // e.j
    protected void o1(int v) {
        this.B1(((AppWidgetProviderInfo)((ArrayList)this.O.k(this.I0())).get(v)));
    }

    @Override  // android.app.Fragment
    public void onActivityResult(int v, int v1, Intent intent0) {
        if(intent0 != null) {
            try {
                int v2 = intent0.getIntExtra("appWidgetId", 0);
                if(v2 == 0) {
                    return;
                }
                switch(v) {
                    case 1: {
                        if(v1 == -1) {
                            this.C1(v2, this.M.getAppWidgetInfo(v2));
                            return;
                        }
                        this.N.deleteAppWidgetId(v2);
                        return;
                    }
                    case 2: {
                        if(v1 == -1) {
                            this.F1(v2, this.M.getAppWidgetInfo(v2));
                            return;
                        }
                        break;
                    }
                }
            }
            catch(Throwable throwable0) {
                this.g0(throwable0);
            }
        }
    }

    @Override  // e.j
    public void onSaveInstanceState(Bundle bundle0) {
        super.onSaveInstanceState(bundle0);
        this.Z(this.O);
    }
}


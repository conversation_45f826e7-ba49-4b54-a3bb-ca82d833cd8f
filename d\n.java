package d;

import a.b;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import e.j.d;
import e.j.g;
import e.j;

public class n extends j implements d {
    private b[] M;

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F0601A9);  // string:select_icon "Select icon"
    }

    @Override  // e.j
    protected int B0() {
        b[] arr_b = b.i(0);
        this.M = arr_b;
        return arr_b.length;
    }

    @Override  // e.j$d
    public String[] a() {
        Context context0 = this.M0();
        int v = this.M.length;
        String[] arr_s = new String[v];
        for(int v1 = 0; v1 < v; ++v1) {
            arr_s[v1] = this.M[v1].p(context0).toString();
        }
        return arr_s;
    }

    @Override  // e.j
    protected View h1(int v) {
        b b0 = this.M[v];
        Context context0 = this.M0();
        return new g(this, this.D0(b0), b0.p(context0), null);
    }

    @Override  // e.j
    protected void k1(int v) {
        String s = this.q().getResourceName(this.M[v].B);
        if(s.indexOf(0x2F) != -1) {
            s = s.substring(s.indexOf(0x2F) + 1);
        }
        Bundle bundle0 = new Bundle(1);
        bundle0.putString("result", s);
        this.U(bundle0);
        this.L();
    }
}


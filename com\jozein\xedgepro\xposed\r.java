package com.jozein.xedgepro.xposed;

import a.p;
import android.content.ClipData.Item;
import android.content.ClipData;
import android.content.ClipboardManager.OnPrimaryClipChangedListener;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.os.Handler;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.AnimationSet;
import android.view.animation.TranslateAnimation;
import android.widget.FrameLayout.LayoutParams;
import android.widget.FrameLayout;
import android.widget.ImageView;
import de.robv.android.xposed.XC_MethodHook.MethodHookParam;
import de.robv.android.xposed.XC_MethodHook;
import f.v;
import g.a0;
import g.h;
import g.l.e;
import g.l;
import g.t;
import g.x;
import g.y;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

class r extends g {
    class f extends y {
        final r N;

        f(Context context0, Drawable drawable0, Drawable drawable1, Drawable drawable2, int v, int v1) {
            super(context0, drawable0, drawable1, drawable2, v, v1);
        }

        // 检测为 Lambda 实现
        private void A(String s) [...]

        @Override  // g.y
        protected int q(Context context0) {
            return r.this.g0() > 0 ? r.this.G.size() : 0;
        }

        @Override  // g.y
        protected CharSequence r(int v) {
            return (CharSequence)r.this.G.get(v);
        }

        @Override  // g.y
        protected boolean s(int v) {
            return v < r.this.O;
        }

        @Override  // g.y
        protected void u(int v) {
            String s = (String)r.this.G.get(v);
            if(s != null && s.length() > 0) {
                r.this.M.postDelayed(() -> r.this.I.o2(s, false), 200L);
            }
            t3.n().t(20);
        }

        @Override  // g.y
        protected void v(int v) {
            if(v < r.this.O) {
                int v1 = r.L(r.this);
                r.this.G.add(v1, ((String)r.this.G.remove(v)));
            }
            else {
                r.this.G.remove(v);
                if(r.this.G.size() <= 0) {
                    t3.n().t(20);
                }
            }
            r.this.P = true;
        }

        @Override  // g.y
        protected void w(int v) {
            class a implements e {
                final String a;
                final int b;
                final f c;

                a(String s, int v) {
                    this.a = s;
                    this.b = v;
                    super();
                }

                @Override  // g.l$e
                public void a(Intent intent0) {
                    r.this.I.n4(intent0);
                }

                @Override  // g.l$e
                public boolean b(CharSequence charSequence0) {
                    if(charSequence0 != null && charSequence0.length() > 0) {
                        String s = charSequence0.toString();
                        if(s.equals(this.a)) {
                            return true;
                        }
                        int v = r.this.G.indexOf(s);
                        if(v < 0) {
                            r.this.G.set(this.b, s);
                        }
                        else if(v < this.b) {
                            r.this.G.remove(this.b);
                        }
                        else {
                            r.this.G.remove(v);
                            r.this.G.set(this.b, s);
                        }
                        r.this.i0();
                    }
                    return true;
                }
            }

            t3.n().t(20);
            String s = (String)this.r(v);
            r.this.T();
            try {
                a r$f$a0 = new a(this, s, v);
                s s1 = new s(r.this);
                r.this.S(s, null, r$f$a0, s1);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                r.this.Q();
            }
        }

        @Override  // g.y
        protected void x(int v) {
            if(v == 0) {
                if(r.this.O > 0) {
                    return;
                }
                r.this.O = 1;
            }
            else {
                r.this.G.add(0, ((String)r.this.G.remove(v)));
                if(v >= r.this.O) {
                    r.K(r.this);
                }
            }
            r.this.P = true;
            this.t();
        }
    }

    private final List G;
    private final List H;
    private final w1 I;
    private final l0 J;
    private final p K;
    private Context L;
    private Handler M;
    private final Runnable N;
    private int O;
    private boolean P;
    private int Q;
    private String R;
    private boolean S;
    private ClipboardManager.OnPrimaryClipChangedListener T;
    private ClipData.Item U;
    private final Runnable V;
    private l W;
    private static final String X;
    private static final String Y;

    static {
        r.X = p2.z + f.l.x + "_clipboard";
        r.Y = p2.A + (f.l.r ? "clipboard_pro" : "clipboard");
    }

    r(ClassLoader classLoader0, w1 w10, l0 l00, p p0) {
        class com.jozein.xedgepro.xposed.r.a extends XC_MethodHook {
            private int a;
            final r b;

            com.jozein.xedgepro.xposed.r.a() {
                this.a = -1;
            }

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                try {
                    if(this.a < 0) {
                        if(!(xC_MethodHook$MethodHookParam0.args[0] instanceof ClipData) && !(xC_MethodHook$MethodHookParam0.args[1] instanceof ClipData)) {
                            return;
                        }
                        this.a = 0;
                    }
                    r.this.j0(((ClipData)xC_MethodHook$MethodHookParam0.args[this.a]));
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }


        class b extends XC_MethodHook {
            final r a;

            protected void beforeHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                if("android".equals(xC_MethodHook$MethodHookParam0.args[1])) {
                    xC_MethodHook$MethodHookParam0.setResult(Boolean.TRUE);
                }
            }
        }

        int v = Build.VERSION.SDK_INT;
        super((v < 21 ? "com.android.server.ClipboardService" : "com.android.server.clipboard.ClipboardService"), classLoader0);
        this.G = new ArrayList(16);
        this.H = new ArrayList();
        this.N = () -> {
            this.h0();
            if(!this.P) {
                return false;
            }
            this.P = false;
            if(this.g0() > 0 && this.G.size() > 0) {
                try {
                    f.f f0 = new f.f(this.R, true);
                    f0.z();
                    f0.t(this.G.size()).t(this.O);
                    for(Object object0: this.G) {
                        f0.v(((String)object0));
                    }
                    f0.B();
                    return true;
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                    return false;
                }
            }
            new File(this.R).delete();
            return false;
        };
        this.O = 0;
        this.P = false;
        this.Q = 0;
        this.R = r.Y;
        this.T = null;
        this.U = null;
        this.V = () -> {
            this.O = 0;
            this.G.clear();
            this.l0();
            this.m0();
        };
        this.W = null;
        this.I = w10;
        this.J = l00;
        this.K = p0;
        try {
            com.jozein.xedgepro.xposed.r.a r$a0 = new com.jozein.xedgepro.xposed.r.a(this);
            if(v >= 33) {
                this.o("setPrimaryClipInternalNoClassifyLocked", r$a0);
            }
            else if(v >= 0x1F) {
                this.q("setPrimaryClipInternalLocked", r$a0);
            }
            else if(v >= 28) {
                this.q("setPrimaryClipInternal", r$a0);
            }
            else if(v < 21) {
                this.o("setPrimaryClip", r$a0);
            }
            else {
                this.o("setPrimaryClipInternal", r$a0);
            }
            this.S = true;
        }
        catch(Throwable throwable0) {
            p2.g(throwable0);
            this.S = false;
        }
        if(!this.S && Build.VERSION.SDK_INT >= 29) {
            try {
                this.n("clipboardAccessAllowed", new b(this));
            }
            catch(Throwable throwable1) {
                p2.g(throwable1);
            }
        }
    }

    static int K(r r0) {
        int v = r0.O + 1;
        r0.O = v;
        return v;
    }

    static int L(r r0) {
        int v = r0.O - 1;
        r0.O = v;
        return v;
    }

    void N(CharSequence charSequence0) {
        if(this.g0() <= 0) {
            return;
        }
        if(charSequence0.length() > 0x10000) {
            charSequence0 = charSequence0.subSequence(0, 0x10000);
        }
        String s = charSequence0.toString();
        if(this.Q > 0) {
            this.H.add(0, s);
            return;
        }
        this.O(s);
        this.i0();
    }

    private void O(String s) {
        int v = this.G.indexOf(s);
        if(v < 0) {
            this.G.add(this.O, s);
            int v1 = this.G.size();
            if(v1 > this.K.r()) {
                this.G.remove(v1 - 1);
            }
        }
        else if(v > this.O) {
            this.G.remove(v);
            this.G.add(this.O, s);
        }
    }

    void P(int v) {
        l l0 = this.W;
        if(l0 != null) {
            l0.j(v);
        }
    }

    private void Q() {
        int v = this.Q - 1;
        this.Q = v;
        if(v <= 0) {
            this.Q = 0;
            this.h0();
            if(this.P) {
                this.i0();
            }
        }
    }

    void R() {
        l l0 = this.W;
        if(l0 != null) {
            l0.o(true);
            this.W = null;
        }
    }

    void S(CharSequence charSequence0, CharSequence charSequence1, e l$e0, g.l.f l$f0) {
        l l0 = this.W;
        if(l0 != null) {
            l0.o(false);
        }
        l l1 = new l(new u(this.L, this.J), this.K.v(25, 0xFF003037), l$e0, () -> {
            this.W = null;
            if(l$f0 != null) {
                l$f0.a();
            }
        });
        this.W = l1;
        l1.u(charSequence0, charSequence1, p2.d(this.L).getResources().getDrawable(0x7F040040));  // drawable:ic_edit_done
        if(Build.VERSION.SDK_INT < 30) {
            this.W.j(this.I.X1());
        }
    }

    private void T() {
        int v = this.Q + 1;
        this.Q = v;
        if(v > 2) {
            this.Q = 2;
        }
    }

    boolean U(CharSequence charSequence0) {
        return this.W != null && this.W.m(charSequence0);
    }

    boolean V() {
        return this.W != null;
    }

    // 检测为 Lambda 实现
    private void W(g.l.f l$f0) [...]

    // 检测为 Lambda 实现
    private void X() [...]

    // 检测为 Lambda 实现
    private void Y(CharSequence charSequence0) [...]

    // 检测为 Lambda 实现
    private void Z() [...]

    private static void a0(View view0) {
    }

    // 检测为 Lambda 实现
    private void b0(View view0) [...]

    // 检测为 Lambda 实现
    private void c0(FrameLayout frameLayout0, FrameLayout frameLayout1, f r$f0, View view0) [...]

    // 检测为 Lambda 实现
    private void d0(int v, FrameLayout frameLayout0, Resources resources0, f r$f0, int v1, View view0) [...]

    private void e0() {
        if(this.g0() <= 0) {
            return;
        }
        String s = r.Y;
        File file0 = new File(s);
        if(file0.exists()) {
            this.f0(file0);
            File file1 = new File(r.X);
            if(file1.exists()) {
                file1.delete();
            }
        }
        else {
            File file2 = file0.getParentFile();
            if((!file2.exists() || !file2.canExecute()) && !file2.mkdir()) {
                s = r.X;
            }
            this.R = s;
            File file3 = new File(r.X);
            if(file3.exists()) {
                this.f0(file3);
            }
        }
    }

    private void f0(File file0) {
        try {
            f.f f0 = new f.f(file0, true);
            if(f0.y()) {
                int v = f0.h();
                this.O = f0.h();
                while(v > 0) {
                    String s = f0.a();
                    if(s != null && s.length() > 0) {
                        this.G.add(s);
                    }
                    --v;
                }
            }
            f0.m();
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    // 去混淆评级： 低(20)
    private int g0() {
        return this.K.o(0x1F) ? 0 : this.K.r();
    }

    private void h0() {
        if(this.H.size() > 0) {
            for(Object object0: this.H) {
                this.O(((String)object0));
            }
            this.P = true;
            this.H.clear();
        }
    }

    private void i0() {
        this.P = true;
        this.M.removeCallbacks(this.N);
        this.M.postDelayed(this.N, 50000L);
    }

    private void j0(ClipData clipData0) {
        if(clipData0 != null && clipData0.getItemCount() > 0) {
            ClipData.Item clipData$Item0 = clipData0.getItemAt(0);
            if(this.U != null && this.U.getText() == clipData$Item0.getText() && this.U.getHtmlText() == clipData$Item0.getHtmlText() && this.U.getIntent() == clipData$Item0.getIntent() && this.U.getUri() == clipData$Item0.getUri() && (Build.VERSION.SDK_INT < 0x1F || this.U.getTextLinks() == clipData$Item0.getTextLinks())) {
                return;
            }
            this.U = clipData$Item0;
            CharSequence charSequence0 = clipData$Item0.coerceToText(this.L);
            if(charSequence0 != null && charSequence0.length() > 0) {
                this.M.post(() -> this.N(charSequence0.toString()));
                return;
            }
        }
        this.U = null;
    }

    void k0(Context context0, Handler handler0) {
        try {
            this.L = context0;
            this.M = handler0;
            this.e0();
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        this.o0();
    }

    private void l0() {
        if(this.T == null) {
            return;
        }
        ((ClipboardManager)this.L.getSystemService("clipboard")).removePrimaryClipChangedListener(this.T);
        this.T = null;
    }

    boolean m0() {
        Handler handler0 = this.M;
        if(handler0 != null) {
            handler0.removeCallbacks(this.N);
        }
        return this.n0();
    }

    // 检测为 Lambda 实现
    private boolean n0() [...]

    private void o0() {
        if(!this.S && this.T == null && this.g0() > 0) {
            this.T = () -> try {
                ClipboardManager clipboardManager0 = (ClipboardManager)this.L.getSystemService("clipboard");
                if(clipboardManager0 != null) {
                    this.j0(clipboardManager0.getPrimaryClip());
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            };
            ClipboardManager clipboardManager0 = (ClipboardManager)this.L.getSystemService("clipboard");
            if(clipboardManager0 != null) {
                clipboardManager0.addPrimaryClipChangedListener(this.T);
            }
        }
    }

    void p0() {
        class d extends com.jozein.xedgepro.xposed.t3.b {
            final r z;

            d(Context context0) {
                super(context0);
            }

            @Override  // com.jozein.xedgepro.xposed.t3$c
            public void a() {
                r.this.Q();
            }
        }


        class com.jozein.xedgepro.xposed.r.e extends f {
            final o2 O;
            final r P;

            com.jozein.xedgepro.xposed.r.e(Context context0, Drawable drawable0, Drawable drawable1, Drawable drawable2, int v, int v1, o2 o20) {
                this.O = o20;
                super(context0, drawable0, drawable1, drawable2, v, v1);
            }

            @Override  // android.widget.AbsListView
            public boolean onTouchEvent(MotionEvent motionEvent0) {
                if(this.O.c()) {
                    return true;
                }
                try {
                    return super.onTouchEvent(motionEvent0);
                }
                catch(Throwable throwable0) {
                    this.O.b(throwable0);
                    return true;
                }
            }
        }

        if(!this.I.w2()) {
            return;
        }
        if(this.L == null) {
            this.k0(this.I.T1(), this.I.U1());
        }
        t3 t30 = t3.n();
        if(t30.p(20)) {
            t30.t(20);
            return;
        }
        x x0 = x.c(this.L);
        int v = (int)(((float)x0.a) * 0.85f);
        int v1 = (int)(((float)v) * 0.75f);
        int v2 = this.K.v(25, 0xFF003037);
        int v3 = a0.c(v2 | 0xFF000000, (a0.r(v2 | 0xFF000000) ? 16 : -16));
        Resources resources0 = p2.d(this.L).getResources();
        Drawable drawable0 = resources0.getDrawable(0x7F040019);  // drawable:ic_clips_add
        Drawable drawable1 = resources0.getDrawable(0x7F04001A);  // drawable:ic_clips_clear_all
        h h0 = h.a();
        if(a0.r(v2 | 0xFF000000)) {
            h0.b(drawable0);
            h0.b(drawable1);
        }
        else {
            h0.d(drawable0);
            h0.d(drawable1);
        }
        FrameLayout frameLayout0 = new FrameLayout(this.L);
        frameLayout0.setOnClickListener(m.z);
        frameLayout0.setBackgroundColor(v3);
        g.p p0 = new g.p(this.L);
        p0.setImageDrawable(drawable0);
        p0.setOnClickListener((View view0) -> {
            class c implements e {
                final r a;

                @Override  // g.l$e
                public void a(Intent intent0) {
                    r.this.I.n4(intent0);
                }

                @Override  // g.l$e
                public boolean b(CharSequence charSequence0) {
                    r.this.N(charSequence0);
                    return true;
                }
            }

            t3.n().t(20);
            this.S("", null, new c(this), null);
        });
        FrameLayout.LayoutParams frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(v1, v1, 0x800003);
        frameLayout$LayoutParams0.rightMargin = x0.g;
        frameLayout$LayoutParams0.leftMargin = x0.g;
        frameLayout0.addView(p0, frameLayout$LayoutParams0);
        g.p p1 = new g.p(this.L);
        p1.setImageDrawable(drawable1);
        FrameLayout.LayoutParams frameLayout$LayoutParams1 = new FrameLayout.LayoutParams(v1, v1, 0x800005);
        frameLayout$LayoutParams1.rightMargin = x0.g;
        frameLayout$LayoutParams1.leftMargin = x0.g;
        frameLayout0.addView(p1, frameLayout$LayoutParams1);
        d r$d0 = new d(this, this.L);
        r$d0.addView(frameLayout0, -1, -2);
        o2 o20 = new o2(this.L, 20);
        com.jozein.xedgepro.xposed.r.e r$e0 = new com.jozein.xedgepro.xposed.r.e(this, this.L, resources0.getDrawable(0x7F04001B), resources0.getDrawable(0x7F04001C), resources0.getDrawable(0x7F04001D), v, v2 | 0xFF000000, o20);  // drawable:ic_clips_edit
        o20.a(r$e0);
        r$d0.setBackgroundColor(r$e0.getItemColor());
        FrameLayout.LayoutParams frameLayout$LayoutParams2 = new FrameLayout.LayoutParams(-1, -2, 0x30);
        frameLayout$LayoutParams2.topMargin = v1;
        frameLayout$LayoutParams2.bottomMargin = x0.g;
        r$d0.addView(r$e0, frameLayout$LayoutParams2);
        p1.setOnClickListener((View view0) -> {
            FrameLayout frameLayout1 = new FrameLayout(this.L);
            frameLayout1.setBackgroundColor(v3);
            Objects.requireNonNull(r$d0);
            frameLayout1.setOnClickListener(new i(r$d0));
            ImageView imageView0 = new ImageView(this.L);
            imageView0.setImageDrawable(new t(resources0.getDrawable(0x7F04001A), null, 0.7f));  // drawable:ic_clips_clear_all
            imageView0.setBackground(a0.o(r$e0.getItemColor(), ((float)v) * 0.5f));
            imageView0.setOnClickListener((View view0) -> {
                int v = this.O;
                if(v == 0) {
                    this.G.clear();
                    t3.n().u(20, 100L);
                    return;
                }
                if(v == this.G.size()) {
                    frameLayout0.removeView(frameLayout1);
                    return;
                }
                int v1 = this.O;
                if(this.G.size() > v1) {
                    this.G.subList(v1, this.G.size()).clear();
                }
                frameLayout0.removeView(frameLayout1);
                r$f0.t();
            });
            frameLayout1.addView(imageView0, new FrameLayout.LayoutParams(v, v, 17));
            r$d0.addView(frameLayout1, -1, -1);
        });
        int v4 = r$e0.q(this.L);
        if(v4 > 5) {
            v4 = 5;
        }
        int v5 = v1 + v * v4 + x0.g;
        t30.k(r$d0, 20, new FrameLayout.LayoutParams(-1, v5, 80));
        this.T();
        AnimationSet animationSet0 = new AnimationSet(true);
        animationSet0.addAnimation(new TranslateAnimation(0.0f, 0.0f, ((float)v5) / 2.0f, 0.0f));
        animationSet0.addAnimation(new AlphaAnimation(0.0f, 1.0f));
        a0.z(r$d0, animationSet0, ((long)(((float)(((long)(v4 + 1)) * 66L)) * 0.6375f)));
    }

    void q0() {
        this.P = true;
        int v = this.g0();
        if(v <= 0) {
            this.M.postDelayed(this.V, 10000L);
            return;
        }
        this.M.removeCallbacks(this.V);
        int v1 = this.G.size();
        if(v < v1) {
            this.G.subList(v, v1).clear();
            if(this.O > v) {
                this.O = v;
            }
            this.i0();
        }
        this.o0();
    }

    public static void t(View view0) {
    }
}


package d;

import a.b;
import a.p.g;
import a.z;
import android.content.Context;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.view.View;
import e.j.d;
import e.j.k;

public class q0 extends c implements a, d {
    private z M;
    private static final int N;

    static {
        q0.N = g.b;
    }

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F060157);  // string:more_triggers "More triggers"
    }

    @Override  // e.j
    protected int B0() {
        this.M = this.g().h();
        return q0.N;
    }

    @Override  // d.c
    protected void C1(Bundle bundle0, int v) {
        if(v != 1) {
            return;
        }
        b b0 = (b)bundle0.getParcelable("result");
        if(b0 != null) {
            int v1 = this.E0();
            Context context0 = this.M0();
            this.M.m0(context0, this.I1(v1), b0);
            e.j.g j$g0 = (e.j.g)this.L0(v1);
            j$g0.setSubText(b0.n(context0));
            j$g0.setImageDrawable(this.D0(b0));
        }
    }

    private int I1(int v) {
        return g.a[v * 2];
    }

    private CharSequence J1(int v) {
        return this.u(g.a[v * 2 + 1]);
    }

    @Override  // e.j$d
    public String[] a() {
        String[] arr_s = new String[q0.N];
        for(int v = 0; v < q0.N; ++v) {
            arr_s[v] = this.J1(v).toString();
        }
        return arr_s;
    }

    @Override  // e.j
    protected View h1(int v) {
        int v1 = this.I1(v);
        b b0 = this.M.k(v1);
        View view0 = new e.j.g(this, this.J1(v), b0.n(this.M0()), this.D0(b0));
        if(Build.VERSION.SDK_INT < 21 && v1 == 27) {
            ((k)view0).setEnabled(false);
        }
        return view0;
    }

    @Override  // e.j
    protected void k1(int v) {
        this.P(new d.d().H1(6, this.J1(v)), 1);
    }

    @Override  // e.j
    protected boolean l1(int v) {
        this.F1(this.M.k(this.I1(v)), 1, 6);
        return true;
    }
}


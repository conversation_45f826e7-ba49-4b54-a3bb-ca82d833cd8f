package a;

import f.s;

public class n extends l {
    public n(String s, String s1) {
        super(s, s1);
    }

    @Override  // a.l
    protected Object g(s s0) {
        return this.y(s0);
    }

    @Override  // a.l
    protected void x(s s0, Object object0) {
        this.z(s0, ((o)object0));
    }

    protected o y(s s0) {
        return new o(s0);
    }

    protected void z(s s0, o o0) {
        o0.a(s0);
    }
}


package e;

import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface.OnMultiChoiceClickListener;
import android.content.DialogInterface;
import android.os.Bundle;

public class c0 extends b {
    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        class a implements DialogInterface.OnClickListener {
            final c0 A;
            final Bundle z;

            a(Bundle bundle0) {
                this.z = bundle0;
                super();
            }

            @Override  // android.content.DialogInterface$OnClickListener
            public void onClick(DialogInterface dialogInterface0, int v) {
                boolean[] arr_z = this.z.getBooleanArray("result");
                boolean[] arr_z1 = this.z.getBooleanArray("ori");
                if(arr_z != null && arr_z1 != null) {
                    for(int v1 = 0; v1 < arr_z1.length; ++v1) {
                        if(arr_z[v1] != arr_z1[v1]) {
                            c0.this.o(this.z);
                            return;
                        }
                    }
                }
            }
        }


        class e.c0.b implements DialogInterface.OnMultiChoiceClickListener {
            final c0 a;

            @Override  // android.content.DialogInterface$OnMultiChoiceClickListener
            public void onClick(DialogInterface dialogInterface0, int v, boolean z) {
            }
        }

        Bundle bundle1 = this.e();
        return new AlertDialog.Builder(this.getActivity()).setMultiChoiceItems(bundle1.getCharSequenceArray("items"), bundle1.getBooleanArray("result"), new e.c0.b(this)).setNegativeButton(0x1040000, b.B).setPositiveButton(0x104000A, new a(this, bundle1)).create();
    }

    public c0 u(CharSequence[] arr_charSequence, boolean[] arr_z) {
        Bundle bundle0 = this.e();
        bundle0.putBooleanArray("result", arr_z);
        bundle0.putCharSequenceArray("items", arr_charSequence);
        boolean[] arr_z1 = new boolean[arr_z.length];
        System.arraycopy(arr_z, 0, arr_z1, 0, arr_z.length);
        bundle0.putBooleanArray("ori", arr_z1);
        return this;
    }
}


package f;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

public class r0 {
    private final byte[] a;
    private final File b;

    public r0(File file0) {
        this.a = new byte[0xC800];
        this.b = file0;
    }

    public r0(String s) {
        this.a = new byte[0xC800];
        this.b = new File(s);
    }

    public File a() {
        return this.b;
    }

    public boolean b(String s, String s1) {
        try {
            Enumeration enumeration0 = new ZipFile(this.b).entries();
            while(true) {
            label_1:
                if(!enumeration0.hasMoreElements()) {
                    return false;
                }
                String s2 = ((ZipEntry)enumeration0.nextElement()).getName();
                if(s2.equals(s) || s2.equals(s1)) {
                    return true;
                }
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return false;
        }
        goto label_1;
    }

    private void c(File file0) {
        File file1 = file0.getParentFile();
        if(file1 != null && !file1.exists()) {
            this.c(file1);
            file1.mkdir();
        }
        file0.mkdir();
    }

    public void d(String s) {
        ZipFile zipFile0 = new ZipFile(this.b);
        Enumeration enumeration0 = zipFile0.entries();
        while(enumeration0.hasMoreElements()) {
            ZipEntry zipEntry0 = (ZipEntry)enumeration0.nextElement();
            File file0 = new File(s + zipEntry0.getName());
            if(!file0.exists()) {
                File file1 = file0.getParentFile();
                if(file1 != null && !file1.exists()) {
                    this.c(file1);
                }
                if(zipEntry0.getName().endsWith("/")) {
                    file0.mkdir();
                    continue;
                }
                else {
                    file0.createNewFile();
                }
            }
            try(InputStream inputStream0 = zipFile0.getInputStream(zipEntry0); FileOutputStream fileOutputStream0 = new FileOutputStream(file0)) {
                int v;
                while((v = inputStream0.read(this.a)) > 0) {
                    fileOutputStream0.write(this.a, 0, v);
                }
            }
        }
    }

    public void e(File[] arr_file) {
        if(!this.b.exists() && !this.b.createNewFile()) {
            throw new IOException("File creation failed!");
        }
        try(ZipOutputStream zipOutputStream0 = new ZipOutputStream(new FileOutputStream(this.b))) {
            for(int v = 0; true; ++v) {
                if(v >= arr_file.length) {
                    break;
                }
                File file0 = arr_file[v];
                if(file0.exists()) {
                    if(file0.isDirectory()) {
                        this.g(file0, zipOutputStream0, "");
                    }
                    else {
                        this.h(file0, zipOutputStream0, "");
                    }
                }
            }
        }
    }

    public void f(String[] arr_s) {
        File[] arr_file = new File[arr_s.length];
        for(int v = 0; v < arr_s.length; ++v) {
            arr_file[v] = new File(arr_s[v]);
        }
        this.e(arr_file);
    }

    private void g(File file0, ZipOutputStream zipOutputStream0, String s) {
        File[] arr_file = file0.listFiles();
        String s1 = s + file0.getName() + '/';
        if(arr_file != null && arr_file.length != 0) {
            for(int v = 0; v < arr_file.length; ++v) {
                File file1 = arr_file[v];
                if(file1.isDirectory()) {
                    this.g(file1, zipOutputStream0, s1);
                }
                else {
                    this.h(file1, zipOutputStream0, s1);
                }
            }
            return;
        }
        try {
            zipOutputStream0.putNextEntry(new ZipEntry(s1));
        }
        finally {
            zipOutputStream0.closeEntry();
        }
    }

    private void h(File file0, ZipOutputStream zipOutputStream0, String s) {
        FileInputStream fileInputStream0 = null;
        try {
            fileInputStream0 = new FileInputStream(file0);
            zipOutputStream0.putNextEntry(new ZipEntry(s + file0.getName()));
            int v;
            while((v = fileInputStream0.read(this.a)) > 0) {
                zipOutputStream0.write(this.a, 0, v);
            }
        }
        catch(Throwable throwable0) {
            zipOutputStream0.closeEntry();
            if(fileInputStream0 != null) {
                fileInputStream0.close();
            }
            throw throwable0;
        }
        zipOutputStream0.closeEntry();
        fileInputStream0.close();
    }
}


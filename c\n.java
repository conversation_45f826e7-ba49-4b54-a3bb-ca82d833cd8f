package c;

import android.os.Bundle;
import android.widget.SeekBar;
import e.g0;
import java.util.Locale;

public class n extends g0 {
    private CharSequence A(float f) {
        return f <= 0.0f ? this.k(0x7F060081) + "\n" : String.format(Locale.ENGLISH, "%.2f inch.\n%.2f mm.", f, ((float)(f * 25.4f)));  // string:as_default "As default"
    }

    public n B(CharSequence charSequence0, float f, float f1) {
        this.w(charSequence0, this.A(f), ((int)(f * 200.0f)), ((int)(f1 * 200.0f)));
        return this;
    }

    @Override  // e.j0$b
    protected void o(Bundle bundle0) {
        if(bundle0 != null) {
            bundle0.putFloat("result_inch", ((float)bundle0.getInt("result", 0)) / 200.0f);
        }
        super.o(bundle0);
    }

    @Override  // e.g0
    public void onProgressChanged(SeekBar seekBar0, int v, boolean z) {
        this.z(this.A(((float)v) / 200.0f));
    }
}


package f;

import a.b.r2;
import a.b;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent.ShortcutIconResource;
import android.content.Intent;
import android.content.pm.LauncherApps.PinItemRequest;
import android.content.pm.LauncherApps;
import android.content.pm.ShortcutInfo.Builder;
import android.content.pm.ShortcutInfo;
import android.content.pm.ShortcutManager;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.Icon;
import android.os.Build.VERSION;
import com.jozein.xedgepro.ui.ActivityPerformAction;
import g.a0;

public class l0 implements l {
    private static ShortcutInfo a(Context context0, Intent intent0, Bitmap bitmap0, String s) {
        return new ShortcutInfo.Builder(context0, Long.toHexString(System.currentTimeMillis())).setIntent(intent0).setIcon(Icon.createWithBitmap(bitmap0)).setShortLabel(s).build();
    }

    public static Intent b(Context context0, b b0) {
        return l0.c(context0, l0.k(b0), a0.a(b0, context0), b0.n(context0).toString());
    }

    private static Intent c(Context context0, Intent intent0, Bitmap bitmap0, String s) {
        Intent intent1 = new Intent();
        intent1.putExtra("android.intent.extra.shortcut.INTENT", intent0);
        intent1.putExtra("android.intent.extra.shortcut.ICON", bitmap0);
        intent1.putExtra("android.intent.extra.shortcut.NAME", s);
        return intent1;
    }

    private static b d(Intent intent0) {
        if(intent0 != null) {
            ComponentName componentName0 = intent0.getComponent();
            if(componentName0 != null) {
                String s = componentName0.getPackageName();
                if(l.j.equals(s)) {
                    String s1 = componentName0.getClassName();
                    if(ActivityPerformAction.class.getName().equals(s1)) {
                        b b0 = l0.j(intent0);
                        return b0.z == 0 ? null : b0;
                    }
                }
            }
        }
        return null;
    }

    public static b e(Intent intent0, Context context0, String s) {
        b b0 = l0.f(intent0, context0, s);
        return b0 == null ? l0.g(intent0, context0) : b0;
    }

    private static b f(Intent intent0, Context context0, String s) {
        Intent intent1 = (Intent)intent0.getParcelableExtra("android.intent.extra.shortcut.INTENT");
        if(intent1 == null) {
            v.c("No result data got!");
            return null;
        }
        b b0 = l0.d(intent1);
        if(b0 != null) {
            return b0;
        }
        String s1 = intent0.getStringExtra("android.intent.extra.shortcut.NAME");
        Intent.ShortcutIconResource intent$ShortcutIconResource0 = (Intent.ShortcutIconResource)intent0.getParcelableExtra("android.intent.extra.shortcut.ICON_RESOURCE");
        if(intent$ShortcutIconResource0 != null) {
            return new r2(intent1, s1, intent$ShortcutIconResource0);
        }
        Bitmap bitmap0 = (Bitmap)intent0.getParcelableExtra("android.intent.extra.shortcut.ICON");
        return bitmap0 == null ? new r2(intent1, s1, s) : new r2(intent1, s1, bitmap0, context0);
    }

    private static b g(Intent intent0, Context context0) {
        String s = null;
        if(Build.VERSION.SDK_INT < 26) {
            return null;
        }
        LauncherApps launcherApps0 = (LauncherApps)context0.getSystemService("launcherapps");
        if(launcherApps0 == null) {
            return null;
        }
        LauncherApps.PinItemRequest launcherApps$PinItemRequest0 = launcherApps0.getPinItemRequest(intent0);
        if(launcherApps$PinItemRequest0 == null) {
            return null;
        }
        ShortcutInfo shortcutInfo0 = launcherApps$PinItemRequest0.getShortcutInfo();
        if(shortcutInfo0 == null) {
            return null;
        }
        Intent intent1 = shortcutInfo0.getIntent();
        if(intent1 == null) {
            return null;
        }
        b b0 = l0.d(intent1);
        if(b0 != null) {
            return b0;
        }
        Drawable drawable0 = launcherApps0.getShortcutIconDrawable(shortcutInfo0, 0);
        CharSequence charSequence0 = shortcutInfo0.getShortLabel();
        if(charSequence0 == null) {
            charSequence0 = shortcutInfo0.getLongLabel();
        }
        if(charSequence0 != null) {
            s = charSequence0.toString();
        }
        return new r2(intent1, s, a0.h(drawable0), context0);
    }

    private static void h(Intent intent0, b b0) {
        new k0(intent0).j().n(b0).l();
    }

    public static void i(Context context0, b b0) {
        if(Build.VERSION.SDK_INT >= 26) {
            ShortcutManager shortcutManager0 = (ShortcutManager)context0.getSystemService("shortcut");
            if(shortcutManager0 != null && shortcutManager0.isRequestPinShortcutSupported()) {
                shortcutManager0.requestPinShortcut(l0.a(context0, l0.k(b0), a0.a(b0, context0), b0.n(context0).toString()), null);
                return;
            }
        }
        context0.sendBroadcast(l0.b(context0, b0).setAction("com.android.launcher.action.INSTALL_SHORTCUT"));
    }

    public static b j(Intent intent0) {
        String s = intent0.getStringExtra("data");
        if(s != null) {
            return b.f(s);
        }
        k0 k00 = new k0(intent0);
        return k00.i() ? k00.g() : b.e();
    }

    public static Intent k(b b0) {
        Intent intent0 = new Intent("android.intent.action.VIEW");
        l0.h(intent0, b0);
        intent0.setComponent(new ComponentName(l.j, ActivityPerformAction.class.getName()));
        intent0.setFlags(0x18000000);
        return intent0;
    }
}


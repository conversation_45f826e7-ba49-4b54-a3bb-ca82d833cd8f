package com.jozein.xedgepro.service;

import a.b.d;
import a.p;
import a.t;
import a.u.b;
import a.u;
import android.content.ComponentName;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.drawable.Icon;
import android.service.quicksettings.Tile;
import android.service.quicksettings.TileService;
import com.jozein.xedgepro.ui.ActivityMain;
import f.l;
import f.v;
import g.a0;

public abstract class SuperTileService extends TileService {
    public static final class Tile10 extends SuperTileService {
    }

    public static final class Tile11 extends SuperTileService {
    }

    public static final class Tile12 extends SuperTileService {
    }

    public static final class Tile1 extends SuperTileService {
    }

    public static final class Tile2 extends SuperTileService {
    }

    public static final class Tile3 extends SuperTileService {
    }

    public static final class Tile4 extends SuperTileService {
    }

    public static final class Tile5 extends SuperTileService {
    }

    public static final class Tile6 extends SuperTileService {
    }

    public static final class Tile7 extends SuperTileService {
    }

    public static final class Tile8 extends SuperTileService {
    }

    public static final class Tile9 extends SuperTileService {
    }

    private b A;
    private d B;
    private boolean C;
    private static int D;
    private static t E;
    public static String[] F;
    private final String z;

    static {
        SuperTileService.F = new String[]{Tile1.class.getName(), Tile2.class.getName(), Tile3.class.getName(), Tile4.class.getName(), Tile5.class.getName(), Tile6.class.getName(), Tile7.class.getName(), Tile8.class.getName(), Tile9.class.getName(), Tile10.class.getName(), Tile11.class.getName(), Tile12.class.getName()};
    }

    public SuperTileService() {
        this.z = this.getClass().getName();
        this.C = false;
    }

    private Icon b(d b$d0) {
        return b$d0.B() || b$d0.K.z != 0 && b$d0.K.z != 1 ? Icon.createWithBitmap(a0.a(b$d0, this)) : Icon.createWithResource(l.j, 0x7F04005D);  // drawable:ic_launcher
    }

    private CharSequence c(d b$d0) {
        if(!b$d0.C() && (b$d0.K.z == 0 || b$d0.K.z == 1)) {
            PackageManager packageManager0 = this.getPackageManager();
            try {
                String s = this.getClass().getName();
                return packageManager0.getServiceInfo(new ComponentName(l.j, s), 0xC0000).loadLabel(packageManager0);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        return b$d0.n(this);
    }

    private boolean d() {
        int v = a.b.x(this.B).z;
        switch(v) {
            case 0: 
            case 1: {
                return false;
            }
            case 34: {
                return SuperTileService.E == null ? p.q(this, 1) : SuperTileService.E.h(34, true);
            }
            case 35: {
                return SuperTileService.E == null ? p.q(this, 0) : SuperTileService.E.h(35, true);
            }
            case 29: 
            case 30: 
            case 0x1F: 
            case 0x20: 
            case 33: 
            case 73: 
            case 97: {
                return SuperTileService.E == null ? true : SuperTileService.E.h(v, true);
            }
            default: {
                return true;
            }
        }
    }

    // 检测为 Lambda 实现
    private void e(boolean z) [...]

    public final void f() {
        this.B = this.A.b(this.z);
        this.i();
    }

    private void g() {
        this.A.l(this);
        this.B = this.A.b(this.z);
        this.i();
    }

    private void h() {
        this.A.o(this);
    }

    private void i() {
        this.k(this.d(), this.c(this.B), this.b(this.B));
    }

    private void j() {
        this.k(this.d(), this.c(this.B), null);
    }

    protected void k(boolean z, CharSequence charSequence0, Icon icon0) {
        Tile tile0 = this.getQsTile();
        if(tile0 == null) {
            return;
        }
        int v = 2;
        int v1 = 0;
        int v2 = 1;
        if(z != (tile0.getState() == 2)) {
            if(!z) {
                v = 1;
            }
            tile0.setState(v);
            v1 = 1;
        }
        if(charSequence0 != null && !charSequence0.equals(tile0.getLabel())) {
            tile0.setLabel(charSequence0);
            v1 = 1;
        }
        if(icon0 == null) {
            v2 = v1;
        }
        else {
            tile0.setIcon(icon0);
        }
        if(v2 != 0) {
            tile0.updateTile();
        }
    }

    @Override  // android.service.quicksettings.TileService
    public void onClick() {
        try {
            if(this.B.K.z != 0 && this.B.K.z != 1) {
                this.j();
                a.b.t(this, this.B);
                return;
            }
            Intent intent0 = new Intent("android.intent.action.MAIN");
            intent0.setComponent(new ComponentName(l.j, ActivityMain.class.getName()));
            intent0.setFlags(0x10000000);
            intent0.putExtra("clicked", 7);
            try {
                this.startActivityAndCollapse(intent0);
            }
            catch(Throwable throwable1) {
                v.d(throwable1);
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    @Override  // android.app.Service
    public void onCreate() {
        super.onCreate();
        try {
            b u$b0 = u.d();
            this.A = u$b0;
            this.B = u$b0.b(this.z);
            int v = SuperTileService.D;
            SuperTileService.D = v + 1;
            if(v == 0) {
                t t0 = new t(this.getApplicationContext());
                SuperTileService.E = t0;
                t0.f();
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    @Override  // android.service.quicksettings.TileService
    public void onDestroy() {
        super.onDestroy();
        try {
            if(this.C) {
                this.C = false;
                this.h();
            }
            int v = SuperTileService.D - 1;
            SuperTileService.D = v;
            if(v == 0) {
                t t0 = SuperTileService.E;
                if(t0 != null) {
                    t0.e();
                    SuperTileService.E = null;
                }
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    @Override  // android.service.quicksettings.TileService
    public void onStartListening() {
        try {
            if(this.C) {
                this.j();
            }
            else {
                this.C = true;
                this.g();
            }
            a.b b0 = a.b.x(this.B);
            if(SuperTileService.E != null && t.i(b0.z)) {
                t t0 = SuperTileService.E;
                b.d d0 = (boolean z) -> try {
                    this.k(z, null, null);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                };
                t0.j(this.z, b0.z, d0);
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    @Override  // android.service.quicksettings.TileService
    public void onStopListening() {
        t t0 = SuperTileService.E;
        if(t0 != null) {
            try {
                t0.n(this.z);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    @Override  // android.service.quicksettings.TileService
    public void onTileRemoved() {
        if(this.C) {
            try {
                this.C = false;
                this.h();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }
}


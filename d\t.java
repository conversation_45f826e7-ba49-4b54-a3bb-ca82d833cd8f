package d;

import a.b.d;
import a.b.j2;
import a.b;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import e.j.g;
import e.j.k;
import e.y;
import f.z;
import g.a0;
import java.io.IOException;
import java.io.InputStream;

public class t extends c {
    private d M;

    public t() {
        this.M = null;
    }

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F06001C);  // string:action_editable_action "Select and edit"
        this.S(0x7F040071, (View view0) -> {
            b b0 = this.M == null ? ((d)this.h().getParcelable("action")) : this.M;
            if(b0 != null) {
                if(!((d)b0).B() && !((d)b0).C()) {
                    b0 = b0.K;
                }
                this.W("result", b0);
            }
            this.L();
        });
    }

    @Override  // e.j
    protected int B0() {
        if(this.M == null) {
            this.M = (d)this.h().getParcelable("action");
        }
        if(this.M == null) {
            this.M = new d(b.e());
        }
        return 3;
    }

    @Override  // d.c
    protected void C1(Bundle bundle0, int v) {
        g j$g0;
        Drawable drawable0;
        CharSequence charSequence1;
        k j$k0;
        switch(v) {
            case 0: {
                b b0 = (b)bundle0.getParcelable("result");
                if(b0 != null) {
                    this.M = this.M.D(b0);
                    this.h().putParcelable("action", this.M);
                    j$k0 = (g)this.L0(0);
                    ((g)j$k0).setImageDrawable(this.D0(b0));
                    charSequence1 = b0.n(this.M0());
                    j$k0.setSubText(charSequence1);
                    return;
                }
                break;
            }
            case 1: {
                CharSequence charSequence0 = bundle0.getCharSequence("result");
                if(charSequence0 != null) {
                    j$k0 = (k)this.L0(1);
                    this.M = charSequence0.length() == 0 ? this.M.I(null) : this.M.I(charSequence0.toString());
                    this.h().putParcelable("action", this.M);
                    if(this.M.C()) {
                        charSequence1 = this.M.n(this.M0());
                        j$k0.setSubText(charSequence1);
                        return;
                    }
                    charSequence1 = "-";
                    j$k0.setSubText(charSequence1);
                    return;
                }
                break;
            }
            case 3: {
                String s2 = bundle0.getString("result");
                if(s2 != null) {
                    this.M = this.M.H(s2);
                    this.h().putParcelable("action", this.M);
                    j$g0 = (g)this.L0(2);
                    drawable0 = this.D0(this.M);
                    j$g0.setImageDrawable(drawable0);
                    ((k)this.K0(0)).c();
                    return;
                }
                break;
            }
            case 5: {
                String s1 = bundle0.getString("result");
                if(s1 != null) {
                    try {
                        Context context0 = this.f();
                        Drawable drawable1 = z.K(context0).C(s1);
                        drawable1.clearColorFilter();
                        this.M = this.M.F(a0.h(drawable1), context0);
                        this.h().putParcelable("action", this.M);
                        ((g)this.L0(2)).setImageDrawable(this.D0(this.M));
                        ((k)this.K0(0)).c();
                    }
                    catch(Throwable throwable0) {
                        this.g0(throwable0);
                    }
                    return;
                }
                break;
            }
            case 6: {
                String s = bundle0.getString("result");
                if(s != null) {
                    drawable0 = j2.c(s);
                    this.M = this.M.G(s);
                    this.h().putParcelable("action", this.M);
                    j$g0 = (g)this.L0(2);
                    j$g0.setImageDrawable(drawable0);
                    ((k)this.K0(0)).c();
                    return;
                }
                break;
            }
        }
    }

    // 检测为 Lambda 实现
    private void J1(View view0) [...]

    public t K1(d b$d0, int v) {
        Bundle bundle0 = this.h();
        bundle0.putParcelable("action", b$d0);
        bundle0.putInt("flag", v);
        return this;
    }

    @Override  // e.j
    protected boolean N0(int v) {
        return v == 2;
    }

    @Override  // e.j
    protected View h1(int v) {
        switch(v) {
            case 0: {
                b b0 = this.M.K;
                return new g(this, this.u(0x7F060002), b0.n(this.M0()), this.D0(b0));  // string:action "Action:"
            }
            case 1: {
                CharSequence charSequence0 = this.u(0x7F060143);  // string:label "Label:"
                return this.M.C() ? new k(this, charSequence0, this.M.n(this.M0())) : new k(this, charSequence0, "-");
            }
            case 2: {
                CharSequence charSequence1 = this.u(0x7F060126);  // string:icon "Icon:"
                return this.M.B() ? new g(this, charSequence1, null, this.D0(this.M)) : new g(this, charSequence1, null, null);
            }
            default: {
                return null;
            }
        }
    }

    @Override  // e.j
    protected View i1(int v) {
        switch(v) {
            case 0: {
                View view0 = new k(this, this.u(0x7F0600CD), null);  // string:delete "Delete"
                if(!this.M.B()) {
                    ((k)view0).e();
                }
                return view0;
            }
            case 1: {
                return new k(this, this.u(0x7F060089), null);  // string:built_in "Built-in icons"
            }
            case 2: {
                return new k(this, this.u(0x7F060103), null);  // string:gallery_apps "Gallery apps"
            }
            case 3: {
                return new k(this, this.u(0x7F060077), null);  // string:app_icons "App icons"
            }
            case 4: {
                return new k(this, this.u(0x7F060196), null);  // string:saved_icons "Saved icons"
            }
            default: {
                return null;
            }
        }
    }

    @Override  // e.j
    protected void k1(int v) {
        switch(v) {
            case 0: {
                this.P(new d.d().H1(this.h().getInt("flag", 1), this.o()), 0);
                break;
            }
            case 1: {
                y y0 = new y();
                CharSequence charSequence0 = this.u(0x7F0600F8);  // string:enter_name "Enter name"
                CharSequence charSequence1 = this.u(0x7F0600F3);  // string:empty_as_default "Empty as default"
                CharSequence charSequence2 = this.M.C() ? this.M.n(this.M0()) : "";
                y0.K(charSequence0, charSequence1, charSequence2);
                this.N(y0, 1);
                return;
            label_12:
                if(v == 2) {
                    this.y1(2, 5);
                    return;
                }
                break;
            }
            default: {
                goto label_12;
            }
        }
    }

    @Override  // e.j
    protected boolean l1(int v) {
        if(v == 0) {
            this.F1(this.M.K, 0, 1);
            return true;
        }
        return super.l1(v);
    }

    @Override  // e.j
    protected void o1(int v) {
        int v1;
        h h0;
        switch(v) {
            case 0: {
                this.M = this.M.E();
                this.h().putParcelable("action", this.M);
                ((g)this.L0(2)).setImageDrawable(null);
                ((k)this.K0(0)).e();
                return;
            }
            case 1: {
                this.P(new n(), 3);
                return;
            }
            case 2: {
                Intent intent0 = new Intent("android.intent.action.GET_CONTENT");
                intent0.setType("image/*");
                try {
                    this.startActivityForResult(intent0, 4);
                }
                catch(Throwable throwable0) {
                    this.g0(throwable0);
                }
                return;
            }
            case 3: {
                h0 = new h();
                v1 = 5;
                break;
            }
            case 4: {
                h0 = new q1();
                v1 = 6;
                break;
            }
            default: {
                return;
            }
        }
        this.P(h0, v1);
    }

    @Override  // android.app.Fragment
    public void onActivityResult(int v, int v1, Intent intent0) {
        if(v == 4 && v1 == -1) {
            Uri uri0 = intent0.getData();
            if(uri0 == null) {
                return;
            }
            try {
                InputStream inputStream0 = this.getActivity().getContentResolver().openInputStream(uri0);
                if(inputStream0 == null) {
                    throw new IOException("Failed to open input stream!");
                }
                Bitmap bitmap0 = BitmapFactory.decodeStream(inputStream0);
                inputStream0.close();
                if(bitmap0 != null && bitmap0.getByteCount() != 0) {
                    int v2 = a0.g(72);
                    if(v2 > 0xC0) {
                        v2 = 0xC0;
                    }
                    if(bitmap0.getWidth() > 0xC0 || bitmap0.getHeight() > 0xC0) {
                        bitmap0 = a0.u(bitmap0, v2, v2);
                    }
                    this.M = this.M.F(bitmap0, this.f());
                    this.h().putParcelable("action", this.M);
                    ((g)this.L0(2)).setImageDrawable(this.D0(this.M));
                    ((k)this.K0(0)).c();
                    return;
                }
                this.e0(0x7F060130);  // string:invalid_file "Invalid file"
            }
            catch(Throwable throwable0) {
                this.g0(throwable0);
            }
        }
    }
}


package g;

import android.os.Build.VERSION;
import android.view.ActionMode.Callback;
import android.view.ActionMode;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.EditText;

class v extends ActionMode {
    class a implements Runnable {
        final v z;

        @Override
        public void run() {
            if(v.this.d != null) {
                try {
                    v.this.invalidate();
                    v.this.d.v(false);
                }
                catch(Throwable throwable0) {
                    f.v.d(throwable0);
                }
            }
        }
    }

    private final ActionMode.Callback a;
    private final EditText b;
    private final u c;
    private f d;
    private final Runnable e;

    v(EditText editText0, ActionMode.Callback actionMode$Callback0) {
        this.d = null;
        this.e = new a(this);
        this.a = actionMode$Callback0;
        this.b = editText0;
        this.c = new u(editText0.getContext());
    }

    ActionMode.Callback b() {
        return this.a;
    }

    void c(f f0) {
        this.d = f0;
    }

    @Override  // android.view.ActionMode
    public void finish() {
        try {
            this.a.onDestroyActionMode(this);
        }
        catch(Throwable throwable0) {
            f.v.d(throwable0);
        }
    }

    @Override  // android.view.ActionMode
    public View getCustomView() {
        return null;
    }

    @Override  // android.view.ActionMode
    public Menu getMenu() {
        return this.c;
    }

    @Override  // android.view.ActionMode
    public MenuInflater getMenuInflater() {
        return new MenuInflater(this.b.getContext());
    }

    @Override  // android.view.ActionMode
    public CharSequence getSubtitle() {
        return null;
    }

    @Override  // android.view.ActionMode
    public CharSequence getTitle() {
        return null;
    }

    @Override  // android.view.ActionMode
    public void hide(long v) {
        try {
            if(v == -1L) {
                v = Build.VERSION.SDK_INT < 23 ? 2000L : ViewConfiguration.getDefaultActionModeHideDuration();
            }
            if(v > 3000L) {
                v = 3000L;
            }
            this.b.removeCallbacks(this.e);
            if(v <= 0L) {
                this.e.run();
                return;
            }
            f f0 = this.d;
            if(f0 != null) {
                f0.v(true);
            }
            this.b.postDelayed(this.e, v);
            return;
        }
        catch(Throwable throwable0) {
        }
        f.v.d(throwable0);
    }

    @Override  // android.view.ActionMode
    public void invalidate() {
        this.c.clear();
        this.a.onPrepareActionMode(this, this.c);
    }

    @Override  // android.view.ActionMode
    public void onWindowFocusChanged(boolean z) {
        if(!z) {
            this.finish();
        }
    }

    @Override  // android.view.ActionMode
    public void setCustomView(View view0) {
    }

    @Override  // android.view.ActionMode
    public void setSubtitle(int v) {
    }

    @Override  // android.view.ActionMode
    public void setSubtitle(CharSequence charSequence0) {
    }

    @Override  // android.view.ActionMode
    public void setTitle(int v) {
    }

    @Override  // android.view.ActionMode
    public void setTitle(CharSequence charSequence0) {
    }
}


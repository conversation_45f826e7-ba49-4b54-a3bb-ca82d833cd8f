package com.jozein.xedgepro.service;

import android.app.Notification.Builder;
import android.app.PendingIntent;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Icon;
import android.os.Build.VERSION;
import android.os.IBinder;
import f.l;
import f.v;
import f.w;

public abstract class a extends Service implements l {
    class com.jozein.xedgepro.service.a.a extends BroadcastReceiver {
        final a a;

        @Override  // android.content.BroadcastReceiver
        public void onReceive(Context context0, Intent intent0) {
            try {
                a.this.stopSelf();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    private final BroadcastReceiver A;
    private static final String B;
    public static final String C;
    private static w D;
    private static int E;
    private boolean z;

    static {
        a.B = l.k + "STOP_SELF";
        a.C = l.k + "STOP_SERVICE";
        a.D = null;
        a.E = 0x3FFFFFFF;
    }

    public a() {
        this.z = false;
        this.A = new com.jozein.xedgepro.service.a.a(this);
    }

    protected void a(int v, CharSequence charSequence0) {
        if(this.z) {
            return;
        }
        try {
            this.z = true;
            Notification.Builder notification$Builder0 = this.b().c(0).d(w.A).b(this);
            notification$Builder0.setContentTitle(this.getText(l.y)).setContentText(charSequence0).setSmallIcon((v <= 0 ? 0x7F04005D : v));  // drawable:ic_launcher
            if(v > 0) {
                if(Build.VERSION.SDK_INT >= 23) {
                    notification$Builder0.setLargeIcon(Icon.createWithResource(l.j, v));
                }
                else {
                    notification$Builder0.setLargeIcon(BitmapFactory.decodeResource(this.getResources(), v));
                }
            }
            Intent intent0 = new Intent(this, this.getClass());
            intent0.setAction(a.B);
            notification$Builder0.setContentIntent(PendingIntent.getService(this, 0, intent0, (Build.VERSION.SDK_INT < 0x1F ? 0 : 0x2000000)));
            int v1 = a.E + 1;
            a.E = v1;
            this.startForeground(v1, notification$Builder0.build());
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private w b() {
        if(a.D == null) {
            String s = this.getString(l.y);
            a.D = w.a(s, s);
        }
        return a.D;
    }

    protected void c() {
        this.a(0, null);
    }

    protected abstract boolean d(Intent arg1);

    public static boolean e(Context context0, Intent intent0) {
        intent0.setPackage(l.j);
        intent0.addFlags(0x20);
        return (Build.VERSION.SDK_INT < 26 ? context0.startService(intent0) : context0.startForegroundService(intent0)) != null;
    }

    @Override  // android.app.Service
    public IBinder onBind(Intent intent0) {
        return null;
    }

    @Override  // android.app.Service
    public void onCreate() {
        super.onCreate();
        IntentFilter intentFilter0 = new IntentFilter(a.C);
        this.registerReceiver(this.A, intentFilter0);
        if(!this.z && Build.VERSION.SDK_INT >= 26) {
            this.c();
        }
    }

    @Override  // android.app.Service
    public void onDestroy() {
        super.onDestroy();
        this.unregisterReceiver(this.A);
    }

    @Override  // android.app.Service
    public final int onStartCommand(Intent intent0, int v, int v1) {
        String s = intent0.getAction();
        if(!a.B.equals(s)) {
            try {
                if(this.d(intent0)) {
                    return 2;
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        this.stopSelf();
        return 2;
    }
}


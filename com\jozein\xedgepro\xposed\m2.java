package com.jozein.xedgepro.xposed;

import android.content.res.Resources;
import android.database.ContentObserver;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.Region;
import android.net.Uri;
import android.os.Build.VERSION;
import android.os.Handler;
import android.provider.Settings.Global;
import android.view.WindowManager;
import de.robv.android.xposed.XposedHelpers;
import f.f0.d;
import f.v;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

class m2 extends j2 {
    class c {
        private Object a;
        private Object b;
        private Method c;
        private Field d;
        private Method e;
        final m2 f;

        c() {
            this.b = null;
            this.c = null;
            this.d = null;
            this.e = null;
            synchronized(m20.P()) {
                try {
                    this.a = m20.r("getDefaultDisplayContentLocked", new Object[0]);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        Object a() {
            return this.a;
        }

        Object b() {
            if(this.b == null) {
                this.b = XposedHelpers.callMethod(this.a, "getDisplayPolicy", new Object[0]);
            }
            return this.b;
        }

        Object c() {
            Object object0;
            if(Build.VERSION.SDK_INT >= 29) {
                try {
                    if(this.d == null) {
                        this.d = XposedHelpers.findField(this.a.getClass(), "mCurrentFocus");
                    }
                    object0 = this.d.get(this.a);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                    goto label_10;
                }
                if(object0 != null) {
                    return object0;
                }
            }
        label_10:
            if(this.e == null) {
                this.e = p2.b(this.a.getClass(), "findFocusedWindow", new Class[0]);
            }
            Object object1 = m2.this.P();
            return this.e.invoke(this.a);
        }

        Object d(float f, float f1) {
            try {
                if(this.c == null) {
                    this.c = XposedHelpers.findMethodBestMatch(this.a.getClass(), "getTouchableWinAtPointLocked", new Class[]{Float.TYPE, Float.TYPE});
                }
                Object object0 = m2.this.P();
                return this.c.invoke(this.a, f, f1);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return null;
            }
        }
    }

    private final w1 H;
    private WindowManager I;
    private Object J;
    private String K;
    private boolean L;
    private Field M;
    private Method N;
    private Method O;
    private c P;
    private Object Q;
    private Point R;
    private Method S;
    private int T;

    m2(ClassLoader classLoader0, w1 w10) {
        super("com.android.server.wm.WindowManagerService", classLoader0);
        this.J = null;
        this.K = null;
        this.L = false;
        this.M = null;
        this.N = null;
        this.O = null;
        this.P = null;
        this.Q = null;
        this.R = null;
        this.S = null;
        this.T = 0;
        this.H = w10;
    }

    void D(int v) {
        this.r("freezeRotation", new Object[]{v});
    }

    Rect E() {
        Region region0 = this.F();
        if(region0 != null) {
            Rect rect0 = new Rect();
            return region0.getBounds(rect0) ? rect0 : null;
        }
        return null;
    }

    Region F() {
        if(Build.VERSION.SDK_INT >= 28) {
            if(this.O == null) {
                this.O = this.i("getCurrentImeTouchRegion");
            }
            return (Region)this.O.invoke(this.v());
        }
        return null;
    }

    Object G() {
        return this.J().a();
    }

    Object H() {
        return this.J().b();
    }

    Point I(Point point0) {
        if(point0 == null) {
            point0 = new Point();
        }
        this.R().getDefaultDisplay().getRealSize(point0);
        return point0;
    }

    private c J() {
        if(this.P == null) {
            synchronized(this) {
                if(this.P == null) {
                    this.P = new c(this);
                }
                return this.P;
            }
        }
        return this.P;
    }

    Object K() {
        Object object1;
        if(Build.VERSION.SDK_INT >= 30) {
            try {
                Object object0 = this.G();
                if(this.M == null) {
                    this.M = XposedHelpers.findField(object0.getClass(), "mFocusedApp");
                }
                object1 = this.M.get(object0);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                goto label_11;
            }
            if(object1 != null) {
                return object1;
            }
        }
        try {
        label_11:
            Object object2 = this.M();
            if(object2 != null) {
                return u3.a(object2);
            }
        }
        catch(Throwable throwable1) {
            v.d(throwable1);
        }
        return null;
    }

    String L() {
        return this.K;
    }

    Object M() {
        try {
            Object object0 = this.J;
            if(object0 != null) {
                return object0;
            }
            if(Build.VERSION.SDK_INT >= 26) {
                return this.J().c();
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        return null;
    }

    float N() {
        return Resources.getSystem().getDisplayMetrics().xdpi;
    }

    int O() {
        if(Build.VERSION.SDK_INT >= 26) {
            if(this.N == null) {
                this.N = this.k("getDefaultDisplayRotation", new Class[0]);
            }
            return (int)(((Integer)this.N.invoke(this.v())));
        }
        return this.t("mRotation");
    }

    private Object P() {
        if(this.Q == null) {
            this.Q = this.u((Build.VERSION.SDK_INT < 29 ? "mWindowMap" : "mGlobalLock"));
        }
        return this.Q;
    }

    Object Q(float f, float f1) {
        return this.J().d(f, f1);
    }

    WindowManager R() {
        if(this.I == null) {
            this.I = (WindowManager)this.H.T1().getSystemService("window");
        }
        return this.I;
    }

    void S() {
        this.H.V1().o0();
    }

    void T(Object object0) {
        Object object1 = this.J;
        if(object1 == object0) {
            return;
        }
        if(object0 == null) {
            try {
                if(!u3.i(object1)) {
                    this.J = null;
                    String s = this.K;
                    if(s != null) {
                        this.K = null;
                        this.H.f3(s, null);
                        return;
                    }
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return;
            }
        }
        else {
            this.J = object0;
            String s1 = u3.b(object0);
            if(s1 != null && !s1.equals(this.K)) {
                String s2 = this.K;
                this.K = s1;
                this.H.f3(s2, s1);
            }
        }
        try {
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    void U() {
        class a extends ContentObserver {
            final m2 a;

            a(Handler handler0) {
                super(handler0);
            }

            @Override  // android.database.ContentObserver
            public void onChange(boolean z) {
                try {
                    m2.this.S();
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        try {
            a m2$a0 = new a(this, this.H.U1());
            Uri uri0 = Settings.Global.getUriFor("display_size_forced");
            this.H.T1().getContentResolver().registerContentObserver(uri0, true, m2$a0);
            this.S();
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    void V() {
        try {
            this.r("reboot", new Object[]{Boolean.FALSE});
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    void W() {
        class b extends d {
            private Method a;
            final m2 b;

            b() {
                this.a = null;
            }

            @Override  // f.f0$d
            protected Object a(String s, Object[] arr_object) {
                Object object0;
                if("focusChanged".equals(s)) {
                    try {
                        object0 = m2.this.J().c();
                        goto label_14;
                    }
                    catch(Throwable throwable0) {
                        try {
                            v.d(throwable0);
                            object0 = this.b();
                            if(object0 != null && com.jozein.xedgepro.xposed.v.f) {
                                try {
                                    int v = u3.d(object0);
                                }
                                catch(Throwable throwable2) {
                                    v.d(throwable2);
                                    goto label_14;
                                }
                                if(v != -1 && v != 0) {
                                    return null;
                                }
                            }
                        label_14:
                            m2.this.T(object0);
                            return null;
                        }
                        catch(Throwable throwable1) {
                        }
                    }
                    v.d(throwable1);
                }
                return null;
            }

            private Object b() {
                if(this.a == null) {
                    this.a = m2.this.k("getFocusedWindow", new Class[0]);
                }
                return this.a.invoke(m2.this.v());
            }
        }

        try {
            if(this.L) {
                return;
            }
            this.L = true;
            this.z("addWindowChangeListener", new b(this), new Object[0]);
            if(Build.VERSION.SDK_INT < 0x1F) {
                v.c("Setup WindowChangeListener");
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    void X() {
        try {
            this.r("shutdown", new Object[]{Boolean.FALSE});
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    @Override  // com.jozein.xedgepro.xposed.j2
    protected Object x() {
        if(this.H.y() != null) {
            try {
                Class class0 = this.m();
                Object object0 = this.H.u("mWindowManager");
                if(class0.isInstance(object0)) {
                    return object0;
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            try {
                Object object1 = this.H.u("mWindowManagerFuncs");
                if(class0.isInstance(object1)) {
                    return object1;
                }
            }
            catch(Throwable throwable1) {
                v.d(throwable1);
            }
        }
        return g3.u();
    }
}


package com.jozein.xedgepro.xposed;

import a.p;
import android.content.Context;
import android.media.AudioAttributes.Builder;
import android.media.AudioAttributes;
import android.os.Build.VERSION;
import android.os.VibrationEffect;
import android.os.Vibrator;
import de.robv.android.xposed.XposedHelpers;
import f.l;
import f.v;
import java.lang.reflect.Method;

class r3 extends g {
    private final Object G;
    private final p H;
    private final Vibrator I;
    private final AudioAttributes J;
    private Method K;
    private Method L;

    r3(Object object0, Class class0, Context context0, p p0) {
        super(class0);
        this.K = null;
        this.L = null;
        this.G = object0;
        this.H = p0;
        Vibrator vibrator0 = (Vibrator)context0.getSystemService("vibrator");
        if(vibrator0 != null && vibrator0.hasVibrator()) {
            this.I = vibrator0;
            if(Build.VERSION.SDK_INT >= 21) {
                this.J = new AudioAttributes.Builder().setContentType(4).setUsage(13).build();
                return;
            }
            this.J = null;
            return;
        }
        this.I = null;
        this.J = null;
        v.c("No vibrator.");
    }

    private boolean r(int v) {
        int v1 = 4;
        switch(v) {
            case 3: {
                v1 = Build.VERSION.SDK_INT >= 28 ? 17 : 3;
                break;
            }
            case 4: {
                if(Build.VERSION.SDK_INT < 21) {
                    v1 = 3;
                }
                break;
            }
            default: {
                v1 = v == 5 ? 0 : 3;
            }
        }
        try {
            if(Build.VERSION.SDK_INT >= 29) {
                if(this.K == null) {
                    this.K = this.j("performHapticFeedback", new Class[]{Integer.TYPE, Boolean.TYPE, String.class});
                }
                return ((Boolean)this.K.invoke(this.G, v1, Boolean.TRUE, l.x)).booleanValue();
            }
            if(this.K == null) {
                this.K = this.i("performHapticFeedbackLw");
            }
            return ((Boolean)this.K.invoke(this.G, null, v1, Boolean.TRUE)).booleanValue();
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return false;
        }
    }

    void s() {
        if(this.I == null) {
            return;
        }
        int v = this.H.v(34, 0);
        if(v == 0) {
            return;
        }
        if(v == 1) {
            this.v();
        }
        else if(!this.r(v)) {
            if(Build.VERSION.SDK_INT >= 26) {
                try {
                    this.w(v);
                    return;
                }
                catch(Throwable unused_ex) {
                }
            }
            this.u(v);
        }
    }

    void t(int v) {
        Vibrator vibrator0 = this.I;
        if(vibrator0 == null) {
            return;
        }
        if(Build.VERSION.SDK_INT >= 26) {
            vibrator0.vibrate(VibrationEffect.createOneShot(v, -1), this.J);
            return;
        }
        vibrator0.vibrate(((long)v));
    }

    private void u(int v) {
        this.t((v == 5 ? 50 : 20));
    }

    private void v() {
        int v = this.H.v(6, 50);
        if(v > 100) {
            v = 100;
        }
        this.t(v);
    }

    private void w(int v) {
        VibrationEffect vibrationEffect0;
        int v1 = 5;
        if(v == 3) {
            v1 = 1;
        }
        else {
            switch(v) {
                case 4: {
                    v1 = Build.VERSION.SDK_INT >= 27 ? 2 : 0;
                    break;
                }
                case 5: {
                    if(Build.VERSION.SDK_INT < 28) {
                        v1 = 0;
                    }
                    break;
                }
                default: {
                    v1 = 0;
                }
            }
        }
        if(Build.VERSION.SDK_INT >= 29) {
            vibrationEffect0 = VibrationEffect.createPredefined(v1);
        }
        else {
            if(this.L == null) {
                this.L = XposedHelpers.findMethodExact(VibrationEffect.class, "get", new Class[]{Integer.TYPE});
            }
            vibrationEffect0 = (VibrationEffect)this.L.invoke(null, v1);
        }
        this.I.vibrate(vibrationEffect0, this.J);
    }
}


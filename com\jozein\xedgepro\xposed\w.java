package com.jozein.xedgepro.xposed;

import android.content.Context;
import android.content.Intent;
import f.o;
import f.q;
import java.util.ArrayList;
import java.util.Hashtable;

class w {
    static class a extends f.q.a implements Runnable {
        private final f.q.a A;
        private final Context B;
        private final ArrayList z;

        a(f.q.a q$a0, Context context0) {
            this.z = new ArrayList();
            this.A = q$a0;
            this.B = context0;
        }

        @Override  // f.q$a
        public q a(String s) {
            q q0 = w.f(s);
            if(q0 != null) {
                return q0;
            }
            q q1 = (o)a.g.a.f(this.B).g(s);
            if(q1 != null) {
                w.e(s, ((o)q1));
                return q1;
            }
            this.z.add(s);
            this.b(this);
            return this.A == null ? super.a(s) : this.A.a(s);
        }

        void b(Runnable runnable0) {
            a.g.a.f(this.B).h(runnable0);
        }

        @Override
        public void run() {
            for(Object object0: this.z) {
                String s = (String)object0;
                o o0 = (o)a.g.a.f(this.B).g(s);
                if(o0 != null) {
                    w.e(s, o0);
                }
            }
            this.z.clear();
        }
    }

    private static final Hashtable a;
    private static boolean b;
    private static a c;

    static {
        w.a = new Hashtable();
        w.b = false;
        w.c = null;
    }

    static void c() {
        w.a.clear();
        q.b();
    }

    static void d(Intent intent0) {
        String s = intent0.getStringExtra("name");
        if(s != null) {
            w.e(s, new o(s, intent0.getLongExtra("last_modified", 0L), intent0.getByteArrayExtra("buffer")));
            w.b = true;
        }
    }

    private static void e(String s, o o0) {
        w.a.put(s, o0);
    }

    private static o f(String s) {
        return (o)w.a.get(s);
    }

    static boolean g() [...] // 潜在的解密器

    static void h(Context context0) {
        if(w.c == null) {
            a w$a0 = new a(q.e(), context0);
            w.c = w$a0;
            q.k(w$a0);
        }
    }
}


package g;

import a.b.u2;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.Intent;
import android.content.pm.ResolveInfo;
import android.content.res.Resources;
import android.graphics.Insets;
import android.graphics.Point;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build.VERSION;
import android.util.Patterns;
import android.view.ActionMode.Callback;
import android.view.ActionMode;
import android.view.Display;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.WindowInsets;
import android.view.WindowManager.LayoutParams;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.view.textclassifier.TextClassifier;
import android.widget.EditText;
import android.widget.FrameLayout.LayoutParams;
import android.widget.FrameLayout;
import android.widget.Toast;
import f.o0;
import f.p0;
import java.util.ArrayList;
import java.util.List;

public class l extends FrameLayout {
    final class c implements ActionMode.Callback {
        private final ActionMode.Callback a;
        final l b;

        c(ActionMode.Callback actionMode$Callback0) {
            this.a = actionMode$Callback0;
        }

        @Override  // android.view.ActionMode$Callback
        public boolean onActionItemClicked(ActionMode actionMode0, MenuItem menuItem0) {
            int v = menuItem0.getItemId();
            return l.this.s(v);
        }

        @Override  // android.view.ActionMode$Callback
        public boolean onCreateActionMode(ActionMode actionMode0, Menu menu0) {
            this.a.onCreateActionMode(actionMode0, menu0);
            menu0.clear();
            l.this.p();
            l.this.t(menu0);
            return true;
        }

        @Override  // android.view.ActionMode$Callback
        public void onDestroyActionMode(ActionMode actionMode0) {
            this.a.onDestroyActionMode(actionMode0);
            if(l.this.I != null) {
                l.this.I.u();
                l.this.I = null;
            }
            l.this.H = null;
            l.this.K = null;
        }

        @Override  // android.view.ActionMode$Callback
        public boolean onPrepareActionMode(ActionMode actionMode0, Menu menu0) {
            if(l.this.I != null) {
                boolean z = l.this.t(menu0);
                l.this.I.w(z);
            }
            return true;
        }
    }

    static final class d extends ContextWrapper {
        public d(Context context0) {
            super(context0);
        }

        @Override  // android.content.ContextWrapper
        public Context getApplicationContext() {
            Context context0 = super.getApplicationContext();
            return context0 == null ? this.getBaseContext() : context0;
        }

        @Override  // android.content.ContextWrapper
        public Object getSystemService(String s) {
            return "window".equals(s) ? new g(((WindowManager)super.getSystemService(s))) : super.getSystemService(s);
        }
    }

    public interface e {
        void a(Intent arg1);

        boolean b(CharSequence arg1);
    }

    public interface f {
        void a();
    }

    static final class g implements WindowManager {
        private final WindowManager z;

        g(WindowManager windowManager0) {
            this.z = windowManager0;
        }

        private static boolean a(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
            if(viewGroup$LayoutParams0 instanceof WindowManager.LayoutParams && ((WindowManager.LayoutParams)viewGroup$LayoutParams0).type == 1002) {
                ((WindowManager.LayoutParams)viewGroup$LayoutParams0).type = 2002;
                ((WindowManager.LayoutParams)viewGroup$LayoutParams0).flags |= 0x100;
            }
            return true;
        }

        @Override  // android.view.ViewManager
        public void addView(View view0, ViewGroup.LayoutParams viewGroup$LayoutParams0) {
            if(g.a(viewGroup$LayoutParams0)) {
                this.z.addView(view0, viewGroup$LayoutParams0);
            }
        }

        @Override  // android.view.WindowManager
        public Display getDefaultDisplay() {
            return this.z.getDefaultDisplay();
        }

        @Override  // android.view.ViewManager
        public void removeView(View view0) {
            this.z.removeView(view0);
        }

        @Override  // android.view.WindowManager
        public void removeViewImmediate(View view0) {
            this.z.removeViewImmediate(view0);
        }

        @Override  // android.view.ViewManager
        public void updateViewLayout(View view0, ViewGroup.LayoutParams viewGroup$LayoutParams0) {
            if(g.a(viewGroup$LayoutParams0)) {
                this.z.updateViewLayout(view0, viewGroup$LayoutParams0);
            }
        }
    }

    private final e A;
    private final f B;
    private int C;
    private boolean D;
    private FrameLayout E;
    private FrameLayout.LayoutParams F;
    private EditText G;
    private v H;
    private g.f I;
    private int J;
    private List K;
    private static CharSequence L;
    private final int z;

    static {
    }

    public l(Context context0, int v, e l$e0, f l$f0) {
        if(Build.VERSION.SDK_INT >= 30) {
            context0 = new d(context0);
        }
        super(context0);
        this.C = 0;
        this.D = false;
        this.E = null;
        this.F = null;
        this.G = null;
        this.H = null;
        this.I = null;
        this.J = 0;
        this.K = null;
        this.z = v;
        this.A = l$e0;
        this.B = l$f0;
    }

    private ActionMode.Callback A(ActionMode.Callback actionMode$Callback0) {
        return new c(this, actionMode$Callback0);
    }

    @Override  // android.view.ViewGroup
    public boolean dispatchKeyEvent(KeyEvent keyEvent0) {
        if(!keyEvent0.isCanceled() && keyEvent0.getKeyCode() == 4 && keyEvent0.getAction() == 1) {
            this.k();
            return true;
        }
        return super.dispatchKeyEvent(keyEvent0);
    }

    public String getSelectedText() {
        String s = this.G.getText().toString();
        int v = this.G.getSelectionStart();
        int v1 = this.G.getSelectionEnd();
        return s.substring(Math.max(0, Math.min(v, v1)), Math.max(0, Math.max(v, v1)));
    }

    private CharSequence getShareText() {
        CharSequence charSequence0 = p0.n("share");
        return charSequence0 == null ? this.getContext().getText(0x7F0601AF) : charSequence0;  // string:share "Share"
    }

    public void j(int v) {
        if(this.D && Build.VERSION.SDK_INT < 30) {
            if(v <= 0) {
                this.F.height = -1;
            }
            else {
                int v1 = a0.n(this.getContext(), new Point()).y / 4;
                if(v < v1) {
                    v = v1;
                }
                this.F.height = v + this.C;
            }
            this.E.setLayoutParams(this.F);
        }
    }

    private void k() {
        try {
            v v0 = this.H;
            if(v0 != null) {
                v0.finish();
                this.H = null;
                return;
            }
            InputMethodManager inputMethodManager0 = (InputMethodManager)this.getContext().getSystemService("input_method");
            if(inputMethodManager0 == null || !inputMethodManager0.hideSoftInputFromWindow(this.getWindowToken(), 0)) {
                this.o(false);
            }
        }
        catch(Throwable throwable0) {
            f.v.d(throwable0);
        }
    }

    private boolean l() {
        ClipboardManager clipboardManager0 = (ClipboardManager)this.getContext().getSystemService("clipboard");
        return clipboardManager0 != null && clipboardManager0.hasPrimaryClip();
    }

    public boolean m(CharSequence charSequence0) {
        return a0.e(this.G, charSequence0);
    }

    private ActionMode n(ActionMode.Callback actionMode$Callback0) {
        if(actionMode$Callback0 == null) {
            return null;
        }
        try {
            v v0 = this.H;
            if(v0 != null) {
                v0.finish();
            }
            ActionMode.Callback actionMode$Callback1 = this.A(actionMode$Callback0);
            v v1 = new v(this.G, actionMode$Callback1);
            if(actionMode$Callback1.onCreateActionMode(v1, v1.getMenu())) {
                g.f f0 = new g.f(this, this.G, v1, this.z);
                this.I = f0;
                v1.c(f0);
                this.H = v1;
                return this.H;
            }
            this.H = null;
        }
        catch(Throwable unused_ex) {
        }
        return null;
    }

    public void o(boolean z) {
        if(this.D) {
            try {
                if(z) {
                    l.L = this.G.getText();
                }
                this.G.clearFocus();
                Context context0 = this.getContext();
                InputMethodManager inputMethodManager0 = (InputMethodManager)context0.getSystemService("input_method");
                if(inputMethodManager0 != null) {
                    inputMethodManager0.hideSoftInputFromWindow(this.getWindowToken(), 0);
                }
                v v0 = this.H;
                if(v0 != null) {
                    v0.finish();
                    this.H = null;
                }
                ((WindowManager)context0.getSystemService("window")).removeView(this);
                f l$f0 = this.B;
                if(l$f0 != null) {
                    l$f0.a();
                }
            }
            catch(Throwable throwable0) {
                f.v.d(throwable0);
            }
            this.D = false;
        }
    }

    @Override  // android.view.View
    public WindowInsets onApplyWindowInsets(WindowInsets windowInsets0) {
        if(Build.VERSION.SDK_INT >= 30) {
            try {
                Insets insets0 = windowInsets0.getInsets(0x20F);
                this.F.leftMargin = insets0.left;
                this.F.rightMargin = insets0.right;
                this.F.topMargin = Math.max(insets0.top - this.C, 0);
                this.F.bottomMargin = Math.max(insets0.bottom - this.C, 0);
                this.E.setLayoutParams(this.F);
                return WindowInsets.CONSUMED;
            }
            catch(Throwable throwable0) {
                f.v.d(throwable0);
            }
        }
        return super.onApplyWindowInsets(windowInsets0);
    }

    @Override  // android.widget.FrameLayout
    protected void onLayout(boolean z, int v, int v1, int v2, int v3) {
        try {
            super.onLayout(z, v, v1, v2, v3);
        }
        catch(Throwable unused_ex) {
        }
    }

    @Override  // android.view.View
    public boolean onTouchEvent(MotionEvent motionEvent0) {
        if(motionEvent0.getActionMasked() == 0) {
            this.k();
        }
        return true;
    }

    @Override  // android.view.View
    public void onWindowFocusChanged(boolean z) {
        super.onWindowFocusChanged(z);
        v v0 = this.H;
        if(v0 != null) {
            v0.onWindowFocusChanged(z);
        }
    }

    private void p() {
        Context context0 = this.getContext();
        List list0 = context0.getPackageManager().queryIntentActivities(new Intent("android.intent.action.PROCESS_TEXT").setType("text/plain"), 0);
        if(list0.size() > 0) {
            this.K = new ArrayList(4);
            for(Object object0: list0) {
                ResolveInfo resolveInfo0 = (ResolveInfo)object0;
                if(f.l.j.equals(resolveInfo0.activityInfo.packageName) || resolveInfo0.activityInfo.exported && (resolveInfo0.activityInfo.permission == null || context0.checkCallingOrSelfPermission(resolveInfo0.activityInfo.permission) == 0)) {
                    this.K.add(resolveInfo0);
                    if(this.K.size() >= 4) {
                        break;
                    }
                }
            }
        }
    }

    // 去混淆评级： 低(30)
    private static boolean q(String s) {
        return (s.startsWith("http://") || s.startsWith("https://")) && Patterns.WEB_URL.matcher(s).matches();
    }

    // 检测为 Lambda 实现
    private void r(View view0) [...]

    private boolean s(int v) {
        try {
            switch(v) {
                case 0x10000101: {
                    this.y();
                    return true;
                }
                case 0x10000102: {
                    this.x();
                    return true;
                }
                default: {
                    if(v >= 0x10000103 && v < 0x10000107) {
                        this.w(((ResolveInfo)this.K.get(v - 0x10000103)));
                        return true;
                    }
                    return this.G.onTextContextMenuItem(v);
                }
            }
        }
        catch(Throwable throwable0) {
        }
        this.z(throwable0.toString());
        return false;
    }

    @Override  // android.view.ViewGroup
    public ActionMode startActionModeForChild(View view0, ActionMode.Callback actionMode$Callback0) {
        return this.n(actionMode$Callback0);
    }

    @Override  // android.view.ViewGroup
    public ActionMode startActionModeForChild(View view0, ActionMode.Callback actionMode$Callback0, int v) {
        return this.n(actionMode$Callback0);
    }

    private boolean t(Menu menu0) {
        int v3;
        EditText editText0 = this.G;
        if(editText0 == null) {
            return false;
        }
        int v = editText0.getSelectionStart();
        int v1 = this.G.getSelectionEnd();
        int v2 = this.G.length();
        boolean z = this.G.hasSelection();
        Resources resources0 = Resources.getSystem();
        if(z) {
            menu0.add(0, 0x1020020, 1, resources0.getText(0x1040003));
            menu0.add(0, 0x1020021, 2, resources0.getText(0x1040001));
            v3 = 3;
        }
        else {
            v3 = 0;
        }
        if(this.l()) {
            menu0.add(0, 0x1020022, 4, resources0.getText(0x104000B));
            v3 |= 4;
        }
        if(v2 > 0 && (v != 0 || v1 != v2)) {
            menu0.add(0, 0x102001F, 8, resources0.getText(0x104000D));
            v3 |= 8;
        }
        if(z) {
            menu0.add(0, 0x10000101, 16, this.getShareText());
            menu0.add(0, 0x10000102, 0x20, resources0.getText(0x104000C));
            v3 |= 0x30;
            if(this.K != null && this.K.size() > 0) {
                int v4 = Math.min(this.K.size(), 4);
                for(int v5 = 0; v5 < v4; ++v5) {
                    try {
                        menu0.add(0, v5 + 0x10000103, 0x40, ((ResolveInfo)this.K.get(v5)).loadLabel(this.getContext().getPackageManager()));
                        v3 |= 0x40;
                    }
                    catch(Throwable throwable0) {
                        f.v.d(throwable0);
                    }
                }
            }
        }
        if(this.J == v3) {
            return false;
        }
        this.J = v3;
        return true;
    }

    public void u(CharSequence charSequence0, CharSequence charSequence1, Drawable drawable0) {
        class a extends FrameLayout {
            final l z;

            a(Context context0) {
                super(context0);
            }

            @Override  // android.view.View
            public boolean onTouchEvent(MotionEvent motionEvent0) {
                super.onTouchEvent(motionEvent0);
                return true;
            }
        }


        class b extends EditText {
            final l z;

            b(Context context0) {
                super(context0);
            }

            @Override  // android.widget.TextView
            public TextClassifier getTextClassifier() {
                return Build.VERSION.SDK_INT < 26 ? super.getTextClassifier() : TextClassifier.NO_OP;
            }

            @Override  // android.view.View
            public ActionMode startActionMode(ActionMode.Callback actionMode$Callback0) {
                return l.this.n(actionMode$Callback0);
            }

            @Override  // android.view.View
            public ActionMode startActionMode(ActionMode.Callback actionMode$Callback0, int v) {
                return l.this.n(actionMode$Callback0);
            }
        }

        CharSequence charSequence2;
        if(this.D) {
            f.v.c("showing");
            this.o(false);
        }
        if(charSequence0 != null && charSequence0.length() != 0) {
            charSequence2 = charSequence0;
        }
        else if(l.L == null || l.L.length() <= 0) {
            charSequence2 = "";
        }
        else {
            charSequence2 = l.L;
        }
        try {
            l.L = null;
            Context context0 = this.getContext();
            int v = a0.r(this.z) ? -1 : 0xFF000000;
            x x0 = x.c(context0);
            int v1 = x0.b;
            int v2 = x0.e;
            this.C = x0.f * 2;
            a l$a0 = new a(this, context0);
            l$a0.setBackgroundColor(this.z);
            b l$b0 = new b(this, context0);
            int v3 = Build.VERSION.SDK_INT;
            if(v3 >= 29) {
                l$b0.setForceDarkAllowed(false);
            }
            l$b0.setBackground(new j(l$b0, v2 / 8));
            l$b0.setTextColor(v);
            l$b0.setTextSize(0, ((float)v2));
            l$b0.setInputType(0x20001);
            l$b0.setText(charSequence2);
            l$b0.setHint(charSequence1);
            l$b0.setSelection(charSequence2.length());
            FrameLayout.LayoutParams frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(-1, -2);
            frameLayout$LayoutParams0.setMarginStart(this.C);
            frameLayout$LayoutParams0.setMarginEnd(this.C + v1);
            frameLayout$LayoutParams0.bottomMargin = this.C;
            frameLayout$LayoutParams0.topMargin = this.C;
            l$a0.addView(l$b0, frameLayout$LayoutParams0);
            p p0 = new p(context0);
            t t0 = new t(drawable0, null, 0.75f);
            h.a().e(t0, 0xFF000000 | l$b0.getHighlightColor());
            p0.setImageDrawable(t0);
            p0.setBackground(a0.l());
            p0.setOnClickListener((View view0) -> {
                e l$e0 = this.A;
                if(l$e0 != null) {
                    try {
                        if(!l$e0.b(this.G.getText())) {
                            return;
                        }
                    }
                    catch(Throwable throwable0) {
                        f.v.d(throwable0);
                    }
                }
                this.o(false);
            });
            FrameLayout.LayoutParams frameLayout$LayoutParams1 = new FrameLayout.LayoutParams(v1, v1, 0x800015);
            frameLayout$LayoutParams1.setMarginEnd(this.C);
            l$a0.addView(p0, frameLayout$LayoutParams1);
            FrameLayout frameLayout0 = new FrameLayout(context0);
            this.E = frameLayout0;
            frameLayout0.setPadding(0, a0.q() - frameLayout$LayoutParams0.topMargin, 0, 0);
            this.E.addView(l$a0, new FrameLayout.LayoutParams(-1, -2, 80));
            FrameLayout.LayoutParams frameLayout$LayoutParams2 = new FrameLayout.LayoutParams(-1, -1, 0x30);
            this.F = frameLayout$LayoutParams2;
            frameLayout$LayoutParams2.bottomMargin = this.C;
            this.addView(this.E, frameLayout$LayoutParams2);
            WindowManager.LayoutParams windowManager$LayoutParams0 = new WindowManager.LayoutParams(-1, -1, 2002, (v3 < 30 ? 0x1000500 : 0x1000100), 1);
            windowManager$LayoutParams0.softInputMode = 0x30;
            if(v3 >= 28) {
                windowManager$LayoutParams0.layoutInDisplayCutoutMode = 1;
            }
            this.G = l$b0;
            ((WindowManager)context0.getSystemService("window")).addView(this, windowManager$LayoutParams0);
            this.D = true;
        }
        catch(Throwable throwable0) {
            f.v.d(throwable0);
        }
    }

    private void v(Intent intent0) {
        intent0.addFlags(0x10000000);
        e l$e0 = this.A;
        if(l$e0 == null) {
            this.getContext().startActivity(intent0);
        }
        else {
            l$e0.a(intent0);
        }
        this.o(true);
    }

    private void w(ResolveInfo resolveInfo0) {
        this.v(new Intent("android.intent.action.PROCESS_TEXT").setType("text/plain").putExtra("android.intent.extra.PROCESS_TEXT", this.getSelectedText().trim()).putExtra("android.intent.extra.PROCESS_TEXT_READONLY", true).setClassName(resolveInfo0.activityInfo.packageName, resolveInfo0.activityInfo.name));
    }

    private void x() {
        String s = this.getSelectedText().trim();
        this.v((l.q(s) ? new Intent("android.intent.action.VIEW", Uri.parse(s)) : new Intent("android.intent.action.WEB_SEARCH").putExtra("query", s)));
    }

    private void y() {
        this.v(Intent.createChooser(new Intent("android.intent.action.SEND").setType("text/plain").putExtra("android.intent.extra.TEXT", this.getSelectedText()).addFlags(0x10000000), null));
    }

    private void z(String s) {
        try {
            if(o0.d() == 0) {
                Toast.makeText(this.getContext(), s, 1).show();
                return;
            }
            a.b.t(this.getContext(), new u2(s));
        }
        catch(Throwable unused_ex) {
        }
    }
}


package com.jozein.xedgepro.xposed;

import android.graphics.Rect;
import android.graphics.Region;
import android.os.Build.VERSION;
import android.view.WindowManager.LayoutParams;
import de.robv.android.xposed.XposedHelpers;
import f.v;
import java.lang.ref.WeakReference;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

class u3 {
    private static Field a;
    private static Field b;
    private static Field c;
    private static Method d;
    private static Method e;
    private static Method f;
    private static Method g;
    private static Method h;
    private static Method i;
    private static Method j;
    private static Class k;

    static {
    }

    static Object a(Object object0) {
        try {
            if(Build.VERSION.SDK_INT >= 30) {
                if(u3.b == null) {
                    u3.b = XposedHelpers.findField(u3.f(object0), "mActivityRecord");
                }
                return u3.b.get(object0);
            }
            WindowManager.LayoutParams windowManager$LayoutParams0 = u3.c(object0);
            if(windowManager$LayoutParams0 != null && (windowManager$LayoutParams0.token != null && windowManager$LayoutParams0.token.getClass().getName().endsWith("ActivityRecord$Token"))) {
                if(u3.c == null) {
                    u3.c = XposedHelpers.findField(windowManager$LayoutParams0.token.getClass(), "weakActivity");
                }
                return ((WeakReference)u3.c.get(windowManager$LayoutParams0.token)).get();
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        return null;
    }

    static String b(Object object0) {
        try {
            String s = u3.c(object0).packageName;
            if(s != null) {
                return s;
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        try {
            if(Build.VERSION.SDK_INT >= 30) {
                Object object1 = XposedHelpers.getObjectField(object0, "mActivityRecord");
                if(object1 != null) {
                    return b.c(object1).getPackage();
                }
            }
            else {
                Object object2 = XposedHelpers.callMethod(object0, "getAppToken", new Object[0]);
                if(object2 != null) {
                    return (String)XposedHelpers.getObjectField(((WeakReference)XposedHelpers.getObjectField(object2, "weakActivity")).get(), "packageName");
                }
            }
        }
        catch(Throwable throwable1) {
            v.d(throwable1);
        }
        return null;
    }

    static WindowManager.LayoutParams c(Object object0) {
        if(u3.a == null) {
            u3.a = XposedHelpers.findField(u3.f(object0), "mAttrs");
        }
        return (WindowManager.LayoutParams)u3.a.get(object0);
    }

    static int d(Object object0) {
        if(u3.f == null) {
            u3.f = XposedHelpers.findMethodExact(u3.f(object0), "getDisplayId", new Class[0]);
        }
        return (int)(((Integer)u3.f.invoke(object0)));
    }

    static Region e(Object object0, Region region0) {
        if(u3.j == null) {
            u3.j = XposedHelpers.findMethodExact(u3.f(object0), "getTouchableRegion", new Class[]{Region.class});
        }
        u3.j.invoke(object0, region0);
        return region0;
    }

    private static Class f(Object object0) {
        if(u3.k == null) {
            Class class0 = object0.getClass();
            if(!"com.android.server.wm.WindowState".equals(class0.getName())) {
                class0 = XposedHelpers.findClass("com.android.server.wm.WindowState", class0.getClassLoader());
            }
            u3.k = class0;
        }
        return u3.k;
    }

    static Rect g(Object object0, Rect rect0) {
        if(Build.VERSION.SDK_INT >= 0x1F) {
            if(u3.g == null) {
                u3.g = XposedHelpers.findMethodExact(u3.f(object0), "getFrame", new Class[0]);
            }
            return (Rect)u3.g.invoke(object0);
        }
        if(u3.h == null) {
            Class class0 = u3.f(object0);
            u3.h = XposedHelpers.findMethodExact(class0, "getContentFrameLw", new Class[0]);
            u3.i = XposedHelpers.findMethodExact(class0, "getGivenContentInsetsLw", new Class[0]);
        }
        Rect rect1 = (Rect)u3.h.invoke(object0);
        Rect rect2 = (Rect)u3.i.invoke(object0);
        rect0.left = rect1.left + rect2.left;
        rect0.top = rect1.top + rect2.top;
        rect0.right = rect1.right + rect2.right;
        rect0.bottom = rect1.bottom + rect2.bottom;
        return rect0;
    }

    static boolean h(Object object0) {
        if(u3.d == null) {
            u3.d = XposedHelpers.findMethodExact(u3.f(object0), "isFocused", new Class[0]);
        }
        return ((Boolean)u3.d.invoke(object0)).booleanValue();
    }

    static boolean i(Object object0) {
        if(u3.e == null) {
            u3.e = XposedHelpers.findMethodExact(u3.f(object0), "isVisibleNow", new Class[0]);
        }
        return ((Boolean)u3.e.invoke(object0)).booleanValue();
    }
}


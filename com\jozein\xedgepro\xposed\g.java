package com.jozein.xedgepro.xposed;

import de.robv.android.xposed.XC_MethodHook.Unhook;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import java.lang.reflect.Method;
import java.util.Set;

class g extends p2 {
    private Class E;
    private Method[] F;

    g(Class class0) {
        this.F = null;
        this.E = class0;
    }

    g(String s, ClassLoader classLoader0) {
        this.E = null;
        this.F = null;
        try {
            this.E = XposedHelpers.findClass(s, classLoader0);
        }
        catch(Throwable throwable0) {
            p2.g(throwable0);
        }
    }

    final Object h(String s, Object[] arr_object) {
        return XposedHelpers.callStaticMethod(this.E, s, arr_object);
    }

    final Method i(String s) {
        if(this.F == null) {
            this.F = this.E.getDeclaredMethods();
        }
        Method[] arr_method = this.F;
        for(int v = 0; v < arr_method.length; ++v) {
            Method method0 = arr_method[v];
            if(method0.getName().equals(s)) {
                method0.setAccessible(true);
                return method0;
            }
        }
        throw new NoSuchMethodError(this.E.getName() + '.' + s);
    }

    final Method j(String s, Class[] arr_class) {
        return XposedHelpers.findMethodBestMatch(this.E, s, arr_class);
    }

    final Method k(String s, Class[] arr_class) {
        return XposedHelpers.findMethodExact(this.E, s, arr_class);
    }

    final Method l(String s) {
        if(this.F == null) {
            this.F = this.E.getDeclaredMethods();
        }
        Method method0 = null;
        int v = -1;
        Method[] arr_method = this.F;
        for(int v1 = 0; v1 < arr_method.length; ++v1) {
            Method method1 = arr_method[v1];
            if(method1.getName().equals(s)) {
                int v2 = method1.getParameterTypes().length;
                if(v2 > v) {
                    method0 = method1;
                    v = v2;
                }
            }
        }
        if(method0 == null) {
            throw new NoSuchMethodError(this.E.getName() + '.' + s);
        }
        method0.setAccessible(true);
        return method0;
    }

    final Class m() {
        return this.E;
    }

    final Set n(String s, XC_MethodHook xC_MethodHook0) {
        return XposedBridge.hookAllMethods(this.E, s, xC_MethodHook0);
    }

    final XC_MethodHook.Unhook o(String s, XC_MethodHook xC_MethodHook0) {
        return XposedBridge.hookMethod(this.i(s), xC_MethodHook0);
    }

    final XC_MethodHook.Unhook p(String s, Object[] arr_object) {
        return XposedHelpers.findAndHookMethod(this.E, s, arr_object);
    }

    final XC_MethodHook.Unhook q(String s, XC_MethodHook xC_MethodHook0) {
        return XposedBridge.hookMethod(this.l(s), xC_MethodHook0);
    }
}


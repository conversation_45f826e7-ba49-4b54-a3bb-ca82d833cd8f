package g;

import a.m;
import a.p;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;

public abstract class o {
    public static class a extends o {
        private final int b;
        private int c;
        private final int[] d;

        public a(Resources resources0) {
            super(resources0);
            switch(m.b().a(4)) {
                case 0: {
                    this.b = -1;
                    this.c = 0;
                    this.d = resources0.getIntArray(0x7F020000);  // array:colors
                    return;
                }
                case 1: {
                    this.b = -1;
                    this.c = resources0.getColor(0x7F030003);  // color:colorPrimary
                    break;
                }
                default: {
                    this.b = resources0.getColor(0x7F030003);  // color:colorPrimary
                    this.c = 0;
                    break;
                }
            }
            this.d = null;
        }

        @Override  // g.o
        public boolean c() {
            return this.d != null || this.c != 0;
        }

        @Override  // g.o
        Drawable f(Drawable drawable0, boolean z) {
            int[] arr_v = this.d;
            if(arr_v == null) {
                return new z(drawable0, this.b, this.c);
            }
            if(z) {
                int v = this.c;
                int v1 = arr_v[v];
                this.c = v + 1;
                if(v + 1 >= arr_v.length) {
                    this.c = 0;
                }
                return new z(drawable0, this.b, v1);
            }
            return new z(drawable0, this.b, 0xFF888888);
        }
    }

    public static class b extends o {
        private final int b;
        private final int c;
        private final int d;
        private final int e;

        public b(p p0, Resources resources0) {
            super(resources0);
            this.b = p0.v(0x1F, -1);
            boolean z = p0.o(42);
            this.c = 0xFF888888;
            if(z) {
                this.d = p0.v(0x20, 0xFF167C80);
                this.e = a0.r(p0.v(2, 0xFF003037)) ? -12303292 : 0xFFCCCCCC;
                return;
            }
            this.e = 0;
            this.d = 0;
        }

        @Override  // g.o
        public boolean c() {
            return this.d != 0;
        }

        // 去混淆评级： 低(20)
        @Override  // g.o
        Drawable f(Drawable drawable0, boolean z) {
            return z ? new z(drawable0, this.b, this.d) : new z(drawable0, this.c, this.e);
        }
    }

    private final Resources a;

    o(Resources resources0) {
        this.a = resources0;
    }

    public final Drawable a(int v) {
        return this.f(this.a.getDrawable(v), true);
    }

    public final Drawable b(int v, boolean z) {
        return this.f(this.a.getDrawable(v), z);
    }

    public abstract boolean c();

    // 去混淆评级： 低(20)
    public final Drawable d(Drawable drawable0) {
        return drawable0 instanceof g ? drawable0 : this.f(drawable0, true);
    }

    // 去混淆评级： 低(20)
    public final Drawable e(Drawable drawable0, boolean z) {
        return drawable0 instanceof g ? drawable0 : this.f(drawable0, z);
    }

    abstract Drawable f(Drawable arg1, boolean arg2);
}


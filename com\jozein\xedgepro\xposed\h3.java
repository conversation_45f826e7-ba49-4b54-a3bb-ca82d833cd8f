package com.jozein.xedgepro.xposed;

import android.content.ContentResolver;
import android.database.ContentObserver;
import android.net.Uri;
import android.provider.Settings.Global;
import android.provider.Settings.Secure;
import android.provider.Settings.System;
import de.robv.android.xposed.XposedHelpers;
import f.o0;
import f.v;

class h3 {
    static class a {
        static int a(ContentResolver contentResolver0, String s, int v) {
            int v1 = o0.d();
            if(v1 != 0) {
                try {
                    return (int)(((Integer)XposedHelpers.callStaticMethod(Settings.Global.class, "getIntForUser", new Object[]{contentResolver0, s, v, v1})));
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
            return Settings.Global.getInt(contentResolver0, s, v);
        }

        static boolean b(ContentResolver contentResolver0, String s, int v) {
            int v1 = o0.d();
            if(v1 != 0) {
                try {
                    return ((Boolean)XposedHelpers.callStaticMethod(Settings.Global.class, "putIntForUser", new Object[]{contentResolver0, s, v, v1})).booleanValue();
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
            return Settings.Global.putInt(contentResolver0, s, v);
        }
    }

    static class b {
        static int a(ContentResolver contentResolver0, String s, int v) {
            int v1 = o0.d();
            if(v1 != 0) {
                try {
                    return (int)(((Integer)XposedHelpers.callStaticMethod(Settings.Secure.class, "getIntForUser", new Object[]{contentResolver0, s, v, v1})));
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
            return Settings.Secure.getInt(contentResolver0, s, v);
        }

        static boolean b(ContentResolver contentResolver0, String s, int v) {
            int v1 = o0.d();
            if(v1 != 0) {
                try {
                    return ((Boolean)XposedHelpers.callStaticMethod(Settings.Secure.class, "putIntForUser", new Object[]{contentResolver0, s, v, v1})).booleanValue();
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
            return Settings.Secure.putInt(contentResolver0, s, v);
        }
    }

    static class c {
        static int a(ContentResolver contentResolver0, String s, int v) {
            int v1 = o0.d();
            if(v1 != 0) {
                try {
                    return (int)(((Integer)XposedHelpers.callStaticMethod(Settings.System.class, "getIntForUser", new Object[]{contentResolver0, s, v, v1})));
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
            return Settings.System.getInt(contentResolver0, s, v);
        }

        static boolean b(ContentResolver contentResolver0, String s, int v) {
            int v1 = o0.d();
            if(v1 != 0) {
                try {
                    return ((Boolean)XposedHelpers.callStaticMethod(Settings.System.class, "putIntForUser", new Object[]{contentResolver0, s, v, v1})).booleanValue();
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
            return Settings.System.putInt(contentResolver0, s, v);
        }
    }

    static void a(ContentResolver contentResolver0, Uri uri0, ContentObserver contentObserver0) {
        contentResolver0.notifyChange(uri0, contentObserver0);
    }
}


package com.jozein.xedgepro.xposed;

import android.bluetooth.BluetoothAdapter;
import android.content.ContentResolver;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.wifi.WifiManager;
import android.nfc.NfcAdapter;
import android.nfc.NfcManager;
import android.os.Build.VERSION;
import android.telephony.TelephonyManager;
import de.robv.android.xposed.XposedHelpers;
import f.v;

class j3 {
    static boolean a(Context context0) {
        return c.a(context0.getContentResolver(), "screen_brightness_mode", 0) != 0;
    }

    static boolean b(Context context0) {
        return c.a(context0.getContentResolver(), "accelerometer_rotation", 0) != 0;
    }

    static boolean c() {
        BluetoothAdapter bluetoothAdapter0 = BluetoothAdapter.getDefaultAdapter();
        if(bluetoothAdapter0 == null) {
            return false;
        }
        switch(bluetoothAdapter0.getState()) {
            case 11: 
            case 12: {
                return true;
            }
            default: {
                return false;
            }
        }
    }

    static boolean d(Context context0) {
        return b.a(context0.getContentResolver(), "location_mode", 0) != 0;
    }

    static boolean e(Context context0) {
        try {
            if(Build.VERSION.SDK_INT < 21) {
                ConnectivityManager connectivityManager0 = (ConnectivityManager)context0.getSystemService("connectivity");
                if(connectivityManager0 != null) {
                    return ((Boolean)XposedHelpers.callMethod(connectivityManager0, "getMobileDataEnabled", new Object[0])).booleanValue();
                }
            }
            else {
                TelephonyManager telephonyManager0 = (TelephonyManager)context0.getSystemService("phone");
                if(telephonyManager0 != null) {
                    return ((Boolean)XposedHelpers.callMethod(telephonyManager0, "getDataEnabled", new Object[0])).booleanValue();
                }
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        return false;
    }

    static boolean f(Context context0) {
        NfcManager nfcManager0 = (NfcManager)context0.getSystemService("nfc");
        if(nfcManager0 == null) {
            return false;
        }
        NfcAdapter nfcAdapter0 = nfcManager0.getDefaultAdapter();
        if(nfcAdapter0 == null) {
            return false;
        }
        switch(((int)(((Integer)XposedHelpers.callMethod(nfcAdapter0, "getAdapterState", new Object[0]))))) {
            case 2: 
            case 3: {
                return true;
            }
            default: {
                return false;
            }
        }
    }

    static boolean g() {
        return ContentResolver.getMasterSyncAutomatically();
    }

    static boolean h(Context context0) {
        switch(((int)(((Integer)XposedHelpers.callMethod(((WifiManager)context0.getSystemService("wifi")), "getWifiApState", new Object[0]))))) {
            case 12: 
            case 13: {
                return true;
            }
            default: {
                return false;
            }
        }
    }

    static boolean i(Context context0) {
        WifiManager wifiManager0 = (WifiManager)context0.getSystemService("wifi");
        if(wifiManager0 == null) {
            return false;
        }
        switch(wifiManager0.getWifiState()) {
            case 2: 
            case 3: {
                return true;
            }
            default: {
                return false;
            }
        }
    }
}


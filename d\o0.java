package d;

import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.CompoundButton;

public final class o0 implements CompoundButton.OnCheckedChangeListener {
    public final p0 a;

    public o0(p0 p00) {
        this.a = p00;
    }

    @Override  // android.widget.CompoundButton$OnCheckedChangeListener
    public final void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
        this.a.R1(compoundButton0, z);
    }
}


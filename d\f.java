package d;

import a.b;
import a.m;
import android.os.Bundle;
import android.view.View;
import e.j.d;
import e.j.g;
import e.j.i;
import e.j.k;
import f.c0.a;
import f.z;
import java.util.List;

public class f extends e implements d {
    private List M;

    public f() {
        this.M = null;
    }

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F0601A3);  // string:select_activity "Select activity"
    }

    @Override  // e.j
    protected int B0() {
        if(this.M == null) {
            this.M = z.K(this.f()).R();
        }
        return this.M.size();
    }

    @Override  // d.e
    protected void B1(Bundle bundle0, int v) {
        if(v == 1) {
            int v1 = bundle0.getInt("result", 0);
            if(v1 == 0 || v1 == 1) {
                try {
                    m.b().f(3, v1);
                    this.w1();
                }
                catch(Throwable throwable0) {
                    this.g0(throwable0);
                }
            }
        }
    }

    private b E1(int v) {
        return ((f.c0.d)this.M.get(this.I0())).q()[v].q();
    }

    protected k F1(int v) {
        f.c0.d c0$d0 = (f.c0.d)this.M.get(v);
        k j$k0 = new g(this, c0$d0.d(), c0$d0.f(), c0$d0.i());
        if(!c0$d0.m()) {
            j$k0.e();
        }
        return j$k0;
    }

    protected k G1(int v) {
        a c0$a0 = ((f.c0.d)this.M.get(this.I0())).q()[v];
        k j$k0 = new g(this, c0$a0.j(), c0$a0.f(), c0$a0.t());
        if(!c0$a0.u()) {
            j$k0.e();
        }
        return j$k0;
    }

    @Override  // e.j
    protected boolean N0(int v) {
        return true;
    }

    @Override  // e.j$d
    public String[] a() {
        return z.L(this.M);
    }

    @Override  // e.j
    protected void g1(Object object0) {
        this.M = (List)object0;
    }

    @Override  // e.j
    protected View h1(int v) {
        return this.F1(v);
    }

    @Override  // e.j
    protected View i1(int v) {
        return this.G1(v);
    }

    @Override  // e.j
    protected void k1(int v) {
        this.y1(v, ((f.c0.d)this.M.get(v)).q().length);
    }

    @Override  // e.j
    protected i m1() {
        List list0 = this.m(f.c0.d.class);
        this.M = list0;
        return list0 != null ? null : new e.j.i.a();
    }

    @Override  // e.j
    protected void n1(int v, int v1) {
        if(v >= 0 && v < this.M.size() && ((f.c0.d)this.M.get(v)).q().length == v1) {
            super.n1(v, v1);
        }
    }

    @Override  // e.j
    protected void o1(int v) {
        this.C1(this.E1(v));
    }

    @Override  // e.j
    public void onSaveInstanceState(Bundle bundle0) {
        super.onSaveInstanceState(bundle0);
        this.Z(this.M);
    }

    @Override  // e.j
    protected boolean p1(int v) {
        this.D1(this.E1(v));
        return true;
    }
}


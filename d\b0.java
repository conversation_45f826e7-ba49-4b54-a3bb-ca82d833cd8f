package d;

import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.CompoundButton;

public final class b0 implements CompoundButton.OnCheckedChangeListener {
    public final c0 a;
    public final int b;

    public b0(c0 c00, int v) {
        this.a = c00;
        this.b = v;
    }

    @Override  // android.widget.CompoundButton$OnCheckedChangeListener
    public final void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
        this.a.J1(this.b, compoundButton0, z);
    }
}


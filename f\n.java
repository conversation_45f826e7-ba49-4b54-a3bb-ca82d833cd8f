package f;

import android.net.Uri;
import java.io.File;
import java.util.Set;

public class n {
    private final String a;
    private final p b;
    private final u c;
    private long d;

    public n(String s) {
        this.d = 0L;
        this.a = s;
        this.c = new u();
        this.b = new p(s + "_list");
    }

    public String a(String s) {
        String s2;
        String s1 = Uri.encode(s);
        this.f();
        do {
            s2 = Integer.toHexString(p0.r());
        }
        while(this.c.containsKey(s2));
        this.b.G(true).y(s2).y(s1).C().n();
        this.c.put(s2, s1);
        this.d = this.b.I();
        return s2;
    }

    public String b(String s) {
        if(s != null) {
            this.f();
            String s1 = (String)this.c.get(s);
            return s1 == null ? null : Uri.decode(s1);
        }
        return null;
    }

    public Set c() {
        this.f();
        return this.c.keySet();
    }

    public void d(String s) {
        if(s == null) {
            return;
        }
        this.f();
        int v = this.c.d(s);
        if(v == -1) {
            return;
        }
        this.c.g(v);
        this.h();
    }

    public void e(String s) {
        if(s == null) {
            return;
        }
        this.f();
        int v = this.c.d(s);
        if(v == -1) {
            return;
        }
        this.c.h(v);
        this.h();
    }

    public void f() {
        long v = this.b.I();
        if(v > 0L && v != this.d) {
            this.c.clear();
            if(this.b.E()) {
                while(true) {
                    String s = this.b.a();
                    String s1 = this.b.a();
                    this.c.put(s, s1);
                    if(!this.b.t()) {
                        break;
                    }
                }
            }
            this.b.m();
            this.d = v;
        }
    }

    public void g(String s) {
        this.f();
        if(this.c.containsKey(s)) {
            this.c.remove(s);
            new File(this.a + s).delete();
            this.h();
            return;
        }
        v.c(("not contains: " + s));
    }

    private void h() {
        this.b.F();
        int v = this.c.size();
        for(int v1 = 0; v1 < v; ++v1) {
            String s = (String)this.c.e(v1);
            this.b.y(s).y(((String)this.c.k(v1))).C();
        }
        this.b.n();
        this.d = this.b.I();
    }

    public void i(String s, String s1) {
        if(s == null) {
            return;
        }
        this.f();
        if(this.c.containsKey(s)) {
            String s2 = Uri.encode(s1);
            this.c.put(s, s2);
            this.h();
        }
    }
}


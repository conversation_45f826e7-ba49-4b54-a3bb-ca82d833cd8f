package d;

import a.b;
import a.z;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.CompoundButton;
import c.l;
import e.j.g;
import e.j.j;
import e.j.k;
import f.p0;
import java.util.ArrayList;

public class j0 extends c implements a {
    private z M;
    private ArrayList N;

    public j0() {
        this.N = null;
    }

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F060142);  // string:keys "Keys"
    }

    @Override  // e.j
    protected int B0() {
        z z0 = this.g().h();
        this.M = z0;
        ArrayList arrayList0 = z0.x();
        this.N = arrayList0;
        return arrayList0.size() + 1;
    }

    @Override  // d.c
    protected void C1(Bundle bundle0, int v) {
        boolean z = true;
        Context context0 = this.f();
        switch(v) {
            case 0: {
                b b0 = (b)bundle0.getParcelable("result");
                if(b0 != null) {
                    int v1 = (int)(((Integer)this.N.get(this.E0())));
                    int v2 = this.F0();
                    this.M.l0(context0, 1, v1, v2, b0);
                    g j$g0 = (g)this.K0(v2);
                    j$g0.setSubText(b0.n(context0));
                    j$g0.setImageDrawable(this.D0(b0));
                    return;
                }
                break;
            }
            case 1: {
                int v3 = bundle0.getInt("result", -1);
                if(v3 != -1 && !this.N.contains(v3)) {
                    this.M.d0(context0, v3);
                    this.N.add(v3);
                    this.x0(this.N.size() - 1);
                    return;
                }
                break;
            }
            case 2: {
                int v4 = bundle0.getInt("result");
                if(v4 <= 1) {
                    j j$j0 = (j)this.L0(this.E0());
                    if(v4 != 0) {
                        z = false;
                    }
                    j$j0.setChecked(z);
                    return;
                }
                if(v4 == 2) {
                    int v5 = this.E0();
                    int v6 = (int)(((Integer)this.N.get(v5)));
                    this.M.i0(context0, v6);
                    this.N.remove(v5);
                    this.u1(v5);
                    return;
                }
                break;
            }
        }
    }

    protected k J1(int v) {
        class d.j0.a implements CompoundButton.OnCheckedChangeListener {
            final int a;
            final j0 b;

            d.j0.a(int v) {
                this.a = v;
                super();
            }

            @Override  // android.widget.CompoundButton$OnCheckedChangeListener
            public void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
                j0.this.M.t0(compoundButton0.getContext(), this.a, z);
            }
        }

        Context context0 = this.M0();
        if(v != this.N.size()) {
            int v1 = (int)(((Integer)this.N.get(v)));
            k j$k0 = new j(this, p0.k(context0, v1), null, this.M.w(v1));
            ((j)j$k0).setOnCheckedChangeListener(new d.j0.a(this, v1));
            return j$k0;
        }
        return this.b1();
    }

    protected k K1(int v) {
        Context context0 = this.M0();
        b b0 = this.M.j(((int)(((Integer)this.N.get(this.I0())))), v);
        return new g(this, p0.j(context0, v), b0.n(context0), this.D0(b0));
    }

    @Override  // e.j
    protected boolean N0(int v) {
        return v != this.N.size();
    }

    @Override  // e.j
    protected View h1(int v) {
        return this.J1(v);
    }

    @Override  // e.j
    protected View i1(int v) {
        return this.K1(v);
    }

    @Override  // e.j
    protected void k1(int v) {
        if(v != this.N.size()) {
            this.y1(v, 3);
            return;
        }
        this.N(new l(), 1);
    }

    @Override  // e.j
    protected boolean l1(int v) {
        if(v != this.N.size()) {
            this.N(new e.z().v(new CharSequence[]{this.u(0x7F0600F4), this.u(0x7F0600D0), this.u(0x7F0600CD)}, !this.M.w(((int)(((Integer)this.N.get(v)))))), 2);  // string:enable "Enable"
            return true;
        }
        return super.l1(v);
    }

    @Override  // e.j
    protected void o1(int v) {
        Context context0 = this.M0();
        this.P(new d().I1(1, p0.k(context0, ((int)(((Integer)this.N.get(this.I0()))))), p0.j(context0, v)), 0);
    }

    @Override  // e.j
    protected boolean p1(int v) {
        this.F1(this.M.j(((int)(((Integer)this.N.get(this.I0())))), v), 0, 1);
        return true;
    }
}


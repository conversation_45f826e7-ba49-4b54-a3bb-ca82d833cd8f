package g;

import a.b;
import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.graphics.Bitmap.Config;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Matrix;
import android.graphics.Point;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.RippleDrawable;
import android.graphics.drawable.StateListDrawable;
import android.os.Build.VERSION;
import android.provider.Settings.Global;
import android.text.Editable;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.WindowManager.LayoutParams;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.widget.EditText;
import f.l;
import f.p0;
import f.v;

public class a0 implements l {
    static final ColorMatrixColorFilter A;
    static final Drawable[] z;

    static {
        a0.z = new Drawable[0];
        ColorMatrix colorMatrix0 = new ColorMatrix();
        colorMatrix0.setSaturation(0.0f);
        a0.A = new ColorMatrixColorFilter(colorMatrix0);
    }

    public static Bitmap a(b b0, Context context0) {
        Drawable drawable0 = b0.m(context0);
        return a0.i(drawable0, Math.min(drawable0.getIntrinsicWidth(), 0xC0), Math.min(drawable0.getIntrinsicHeight(), 0xC0));
    }

    public static void b(WindowManager.LayoutParams windowManager$LayoutParams0, String s) {
    }

    public static int c(int v, int v1) {
        return a0.d(Color.alpha(v), Color.red(v), Color.green(v), Color.blue(v), v1);
    }

    public static int d(int v, int v1, int v2, int v3, int v4) {
        int v5 = v1 + v4;
        int v6 = v2 + v4;
        int v7 = v3 + v4;
        if(v4 > 0) {
            if(v5 > 0xFF) {
                v5 = 0xFF;
            }
            if(v6 > 0xFF) {
                v6 = 0xFF;
            }
            return v7 <= 0xFF ? Color.argb(v, v5, v6, v7) : Color.argb(v, v5, v6, 0xFF);
        }
        if(v5 < 0) {
            v5 = 0;
        }
        if(v6 < 0) {
            v6 = 0;
        }
        if(v7 < 0) {
            v7 = 0;
        }
        return Color.argb(v, v5, v6, v7);
    }

    public static boolean e(EditText editText0, CharSequence charSequence0) {
        if(editText0 != null && charSequence0 != null && editText0.isFocused()) {
            int v = editText0.getSelectionStart();
            int v1 = editText0.getSelectionEnd();
            if(v >= 0 && v1 >= 0) {
                Editable editable0 = editText0.getEditableText();
                if(editable0 != null) {
                    editable0.replace(v, v1, charSequence0);
                    return true;
                }
            }
        }
        return false;
    }

    public static int f(int v, int v1) {
        int v2 = v >> 24 & 0xFF;
        if(v2 == 0xFF) {
            return v;
        }
        if(v2 == 0) {
            return v1;
        }
        int v3 = v1 >> 24 & 0xFF;
        int v4 = 0xFF - (0xFF - v3) * (0xFF - v2) / 0xFF;
        return v4 == 0 ? 0 : ((v & 0xFF) * 0xFF * v2 + (v1 & 0xFF) * v3 * (0xFF - v2)) / (v4 * 0xFF) | (v4 << 24 | ((v >> 16 & 0xFF) * 0xFF * v2 + (v1 >> 16 & 0xFF) * v3 * (0xFF - v2)) / (v4 * 0xFF) << 16 | ((v >> 8 & 0xFF) * 0xFF * v2 + (v1 >> 8 & 0xFF) * v3 * (0xFF - v2)) / (v4 * 0xFF) << 8);
    }

    public static int g(int v) {
        return (int)(((float)v) * Resources.getSystem().getDisplayMetrics().density + 0.5f);
    }

    // 去混淆评级： 低(20)
    public static Bitmap h(Drawable drawable0) {
        return drawable0 instanceof BitmapDrawable ? ((BitmapDrawable)drawable0).getBitmap() : a0.i(drawable0, drawable0.getIntrinsicWidth(), drawable0.getIntrinsicHeight());
    }

    public static Bitmap i(Drawable drawable0, int v, int v1) {
        Bitmap bitmap0 = Bitmap.createBitmap(v, v1, (drawable0.getOpacity() == -1 ? Bitmap.Config.RGB_565 : Bitmap.Config.ARGB_8888));
        Canvas canvas0 = new Canvas(bitmap0);
        drawable0.setBounds(0, 0, v, v1);
        drawable0.draw(canvas0);
        return bitmap0;
    }

    public static float j() {
        return Resources.getSystem().getDisplayMetrics().xdpi;
    }

    public static Point k(Context context0, Point point0) {
        if(context0 != null) {
            try {
                WindowManager windowManager0 = (WindowManager)context0.getSystemService("window");
                if(windowManager0 != null) {
                    windowManager0.getDefaultDisplay().getRealSize(point0);
                    return point0;
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        DisplayMetrics displayMetrics0 = Resources.getSystem().getDisplayMetrics();
        point0.x = displayMetrics0.widthPixels;
        point0.y = displayMetrics0.heightPixels;
        return point0;
    }

    public static Drawable l() {
        if(Build.VERSION.SDK_INT >= 21) {
            return new RippleDrawable(ColorStateList.valueOf(0x3F7F7F7F), null, new ColorDrawable(-1));
        }
        Drawable drawable0 = new StateListDrawable();
        ColorDrawable colorDrawable0 = new ColorDrawable(0x3F7F7F7F);
        ColorDrawable colorDrawable1 = new ColorDrawable(0);
        ((StateListDrawable)drawable0).addState(new int[]{0x10102FE}, colorDrawable0);
        ((StateListDrawable)drawable0).addState(new int[]{0x10100A7}, colorDrawable0);
        ((StateListDrawable)drawable0).addState(new int[]{0x101009C}, colorDrawable0);
        ((StateListDrawable)drawable0).addState(new int[0], colorDrawable1);
        return drawable0;
    }

    public static float m(Context context0) {
        return context0.getResources().getDisplayMetrics().xdpi;
    }

    public static Point n(Context context0, Point point0) {
        DisplayMetrics displayMetrics0 = context0.getResources().getDisplayMetrics();
        point0.x = displayMetrics0.widthPixels;
        point0.y = displayMetrics0.heightPixels;
        return point0;
    }

    public static Drawable o(int v, float f) {
        int v1 = a0.f(0x3F7F7F7F, v);
        if(Build.VERSION.SDK_INT >= 21) {
            return new RippleDrawable(ColorStateList.valueOf(v1), new s(v, f), new s(-1, f));
        }
        Drawable drawable0 = new StateListDrawable();
        s s0 = new s(v1, f);
        s s1 = new s(v, f);
        ((StateListDrawable)drawable0).addState(new int[]{0x10102FE}, s0);
        ((StateListDrawable)drawable0).addState(new int[]{0x10100A7}, s0);
        ((StateListDrawable)drawable0).addState(new int[]{0x101009C}, s0);
        ((StateListDrawable)drawable0).addState(new int[0], s1);
        return drawable0;
    }

    public static int p() {
        return p0.l("status_bar_height");
    }

    public static int q() {
        int v = a0.p();
        if(v > 0) {
            return v;
        }
        DisplayMetrics displayMetrics0 = Resources.getSystem().getDisplayMetrics();
        return (int)(((float)(displayMetrics0.widthPixels + displayMetrics0.heightPixels)) * 0.025f);
    }

    public static boolean r(int v) {
        return (v >> 16 & 0xFF) + (v >> 8 & 0xFF) + (v & 0xFF) < 0x180;
    }

    public static boolean s(Drawable drawable0) {
        if(drawable0 instanceof ColorDrawable) {
            return a0.r(((ColorDrawable)drawable0).getColor());
        }
        Bitmap bitmap0 = Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888);
        drawable0.setBounds(0, 0, 1, 1);
        drawable0.draw(new Canvas(bitmap0));
        return a0.r(bitmap0.getPixel(0, 0));
    }

    public static int t(int v) {
        return (int)(((float)v) / Resources.getSystem().getDisplayMetrics().density + 0.5f);
    }

    public static Bitmap u(Bitmap bitmap0, int v, int v1) {
        int v2 = bitmap0.getWidth();
        int v3 = bitmap0.getHeight();
        float f = ((float)v) / ((float)v2);
        float f1 = ((float)v1) / ((float)v3);
        Matrix matrix0 = new Matrix();
        if(f > f1) {
            matrix0.postScale(f, f);
            return Bitmap.createBitmap(bitmap0, 0, (v3 - v2) / 2, v2, v2, matrix0, false);
        }
        matrix0.postScale(f1, f1);
        return Bitmap.createBitmap(bitmap0, (v2 - v3) / 2, 0, v3, v3, matrix0, false);
    }

    public static void v(Drawable drawable0, boolean z) {
        if(drawable0 != null) {
            if(drawable0 instanceof g) {
                ((g)drawable0).j(z);
                return;
            }
            drawable0.setColorFilter((z ? a0.A : null));
        }
    }

    public static void w(View view0, int v, float f) {
        if(view0 == null) {
            return;
        }
        view0.setBackground(new s(v, f));
        if(Build.VERSION.SDK_INT >= 21) {
            view0.setClipToOutline(true);
            if(v >>> 24 >= 0xF0) {
                view0.setElevation(view0.getResources().getDisplayMetrics().density * 4.0f);
            }
        }
    }

    private static void x(View view0, Animation animation0, long v, String s) {
        animation0.setDuration(((long)(((float)v) * Settings.Global.getFloat(view0.getContext().getContentResolver(), s, 1.0f))));
        view0.startAnimation(animation0);
    }

    public static void y(View view0, Animation animation0) {
        a0.x(view0, animation0, 200L, "transition_animation_scale");
    }

    public static void z(View view0, Animation animation0, long v) {
        if(v > 200L) {
            v = 200L;
        }
        a0.x(view0, animation0, v, "window_animation_scale");
    }
}


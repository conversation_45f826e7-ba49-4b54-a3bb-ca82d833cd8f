package g;

import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.widget.EditText;

public class j extends Drawable {
    private final Paint a;
    private final int b;

    public j(EditText editText0, int v) {
        Paint paint0 = new Paint(1);
        this.a = paint0;
        paint0.setColor(editText0.getHighlightColor() | 0xFF000000);
        this.b = v;
        paint0.setStrokeWidth(((float)v));
    }

    @Override  // android.graphics.drawable.Drawable
    public void draw(Canvas canvas0) {
        Rect rect0 = this.getBounds();
        int v = rect0.width();
        float f = (float)(rect0.height() - this.b);
        canvas0.drawLine(0.0f, f, ((float)v), f, this.a);
    }

    @Override  // android.graphics.drawable.Drawable
    public int getOpacity() {
        return -2;
    }

    @Override  // android.graphics.drawable.Drawable
    public void setAlpha(int v) {
        this.a.setAlpha(v);
    }

    @Override  // android.graphics.drawable.Drawable
    public void setColorFilter(ColorFilter colorFilter0) {
        this.a.setColorFilter(colorFilter0);
    }
}


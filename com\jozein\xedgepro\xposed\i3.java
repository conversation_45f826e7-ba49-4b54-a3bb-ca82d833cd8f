package com.jozein.xedgepro.xposed;

import a.b;
import a.p.g;
import a.p;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.NetworkInfo.State;
import android.net.NetworkInfo;
import android.os.Build.VERSION;
import android.os.Handler;
import android.telephony.TelephonyManager;
import f.c0;
import f.l;
import f.p0;
import f.v;

class i3 implements g {
    private final p c;
    private final w1 d;
    private final Context e;
    private int f;
    private int g;
    private int h;
    private boolean i;
    private boolean j;
    private boolean k;
    private boolean l;
    private boolean m;
    private boolean n;
    private boolean o;
    private boolean p;
    private int q;
    private static final String[] r;
    private static final String[] s;

    static {
        i3.r = new String[]{"android.intent.action.BOOT_COMPLETED", "android.intent.action.SCREEN_ON", "android.intent.action.SCREEN_OFF", "android.intent.action.USER_PRESENT", "android.intent.action.BATTERY_CHANGED", "android.intent.action.BATTERY_LOW", "android.intent.action.BATTERY_OKAY", "android.intent.action.ACTION_POWER_CONNECTED", "android.intent.action.ACTION_POWER_DISCONNECTED", "android.net.wifi.STATE_CHANGE", "android.net.conn.CONNECTIVITY_CHANGE", "android.intent.action.AIRPLANE_MODE", "android.intent.action.HEADSET_PLUG", "android.hardware.usb.action.USB_STATE", "android.intent.action.REBOOT", "android.intent.action.ACTION_SHUTDOWN", "android.intent.action.TIME_SET", "android.intent.action.TIMEZONE_CHANGED", "android.intent.action.USER_SWITCHED"};
        i3.s = new String[]{"android.provider.Telephony.SMS_RECEIVED", "android.intent.action.NEW_OUTGOING_CALL", "android.intent.action.PHONE_STATE"};
    }

    i3(p p0, w1 w10, Context context0, Handler handler0) {
        this.f = 0;
        this.g = 0;
        this.h = 0;
        this.i = true;
        this.j = true;
        this.k = false;
        this.l = false;
        this.m = false;
        this.n = false;
        this.o = false;
        this.p = false;
        this.q = 0;
        this.c = p0;
        this.d = w10;
        this.e = context0;
        try {
            this.L(context0, handler0);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    boolean A() {
        return j3.f(this.e);
    }

    boolean B() {
        return this.q != 0;
    }

    boolean C() {
        return this.k;
    }

    boolean D() {
        return this.q == 3;
    }

    boolean E() {
        return j3.g();
    }

    boolean F() {
        return this.p;
    }

    boolean G() {
        return j3.h(this.e);
    }

    boolean H() {
        return this.l;
    }

    boolean I() {
        return j3.i(this.e);
    }

    private void J() {
        if(this.j) {
            this.j = false;
            this.d.h3();
            this.K(3);
        }
    }

    private void K(int v) {
        if(l.r) {
            b b0 = this.c.k(v);
            if(b0.z != 0 && b0.z != 1) {
                this.d.w3(b0);
            }
        }
    }

    private void L(Context context0, Handler handler0) {
        class a extends BroadcastReceiver {
            final i3 a;

            @Override  // android.content.BroadcastReceiver
            public void onReceive(Context context0, Intent intent0) {
                try {
                    String s = intent0.getAction();
                    if(s == null) {
                        return;
                    }
                    boolean z = true;
                    switch(s) {
                        case "android.hardware.usb.action.USB_STATE": {
                            boolean z2 = intent0.getBooleanExtra("connected", false);
                            i3.this.S(z2);
                            return;
                        }
                        case "android.intent.action.ACTION_POWER_CONNECTED": {
                            i3.this.Q(true);
                            return;
                        }
                        case "android.intent.action.ACTION_POWER_DISCONNECTED": {
                            i3.this.Q(false);
                            return;
                        }
                        case "android.intent.action.ACTION_SHUTDOWN": 
                        case "android.intent.action.REBOOT": {
                            i3.this.d.m3();
                            return;
                        }
                        case "android.intent.action.AIRPLANE_MODE": {
                            boolean z1 = intent0.getBooleanExtra("state", false);
                            i3.this.M(z1);
                            return;
                        }
                        case "android.intent.action.BATTERY_CHANGED": {
                            if(i3.this.g == 0) {
                                int v1 = intent0.getIntExtra("scale", 0);
                                i3.this.g = v1;
                                int v2 = p0.m("config_lowBatteryWarningLevel", 15);
                                i3.this.h = v2;
                            }
                            int v3 = intent0.getIntExtra("level", i3.this.f);
                            if(v3 != i3.this.f) {
                                i3.this.N(v3);
                                return;
                            }
                            break;
                        }
                        case "android.intent.action.BOOT_COMPLETED": {
                            i3.this.d.p3();
                            return;
                        }
                        case "android.intent.action.HEADSET_PLUG": {
                            int v = intent0.getIntExtra("state", -1);
                            if(v == -1) {
                                z = intent0.getBooleanExtra("state", false);
                            }
                            else if(v == 0) {
                                z = false;
                            }
                            i3.this.O(z);
                            return;
                        }
                        case "android.intent.action.NEW_OUTGOING_CALL": {
                            i3.this.U(6);
                            return;
                        }
                        case "android.intent.action.PHONE_STATE": {
                            String s1 = intent0.getStringExtra("state");
                            if(TelephonyManager.EXTRA_STATE_RINGING.equals(s1)) {
                                i3.this.U(3);
                                return;
                            }
                            if(TelephonyManager.EXTRA_STATE_OFFHOOK.equals(s1)) {
                                i3.this.U(1);
                                return;
                            }
                            i3.this.U(0);
                            return;
                        }
                        case "android.intent.action.PRECISE_CALL_STATE": {
                            if(intent0.getIntExtra("foreground_state", -1) == 1) {
                                i3.this.U(2);
                                return;
                            }
                            break;
                        }
                        case "android.intent.action.SCREEN_OFF": {
                            i3.this.R(false);
                            return;
                        }
                        case "android.intent.action.SCREEN_ON": {
                            i3.this.R(true);
                            return;
                        }
                        case "android.intent.action.TIMEZONE_CHANGED": 
                        case "android.intent.action.TIME_SET": {
                            i3.this.d.q3();
                            return;
                        }
                        case "android.intent.action.USER_PRESENT": {
                            i3.this.J();
                            return;
                        }
                        case "android.intent.action.USER_SWITCHED": {
                            int v4 = intent0.getIntExtra("android.intent.extra.user_handle", -1);
                            if(v4 >= 0) {
                                i3.this.d.A4(v4);
                                return;
                            }
                            break;
                        }
                        case "android.net.conn.CONNECTIVITY_CHANGE": {
                            i3 i30 = i3.this;
                            if(intent0.getBooleanExtra("noConnectivity", false)) {
                                z = false;
                            }
                            i30.P(z);
                            return;
                        }
                        case "android.net.wifi.STATE_CHANGE": {
                            NetworkInfo networkInfo0 = (NetworkInfo)intent0.getParcelableExtra("networkInfo");
                            if(networkInfo0 != null) {
                                NetworkInfo.State networkInfo$State0 = networkInfo0.getState();
                                if(networkInfo$State0 == NetworkInfo.State.CONNECTED) {
                                    i3.this.T(true);
                                    return;
                                }
                                if(networkInfo$State0 == NetworkInfo.State.DISCONNECTED) {
                                    i3.this.T(false);
                                    return;
                                }
                            }
                            break;
                        }
                        case "android.provider.Telephony.SMS_RECEIVED": {
                            i3.this.K(22);
                            return;
                        }
                        default: {
                            v.c(("Unhandled action: " + s));
                        }
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        IntentFilter intentFilter0 = new IntentFilter();
        intentFilter0.setPriority(1000);
        String[] arr_s = i3.r;
        for(int v1 = 0; v1 < arr_s.length; ++v1) {
            intentFilter0.addAction(arr_s[v1]);
        }
        if(l.r) {
            String[] arr_s1 = i3.s;
            for(int v = 0; v < arr_s1.length; ++v) {
                intentFilter0.addAction(arr_s1[v]);
            }
            if(Build.VERSION.SDK_INT >= 21) {
                intentFilter0.addAction("android.intent.action.PRECISE_CALL_STATE");
            }
        }
        c0.w(context0, new a(this), intentFilter0, handler0);
    }

    private void M(boolean z) {
        if(this.n != z) {
            this.n = z;
            this.K((z ? 13 : 14));
        }
    }

    private void N(int v) {
        if(v == this.g) {
            this.K(4);
        }
        else {
            int v1 = this.h;
            if(v < v1 && this.f >= v1) {
                this.K(5);
            }
            else if(v >= v1 && this.f < v1) {
                this.K(6);
            }
        }
        this.f = v;
    }

    private void O(boolean z) {
        if(this.o != z) {
            this.o = z;
            this.K((z ? 15 : 16));
        }
    }

    private void P(boolean z) {
        if(this.m != z) {
            this.m = z;
            this.K((z ? 11 : 12));
        }
    }

    private void Q(boolean z) {
        if(this.k != z) {
            this.k = z;
            this.K((z ? 7 : 8));
        }
    }

    private void R(boolean z) {
        if(this.i != z) {
            this.i = z;
            if(z) {
                this.d.l3();
                this.K(1);
                return;
            }
            this.j = true;
            this.d.k3();
            this.K(2);
        }
    }

    private void S(boolean z) {
        if(this.p != z) {
            this.p = z;
            this.K((z ? 17 : 18));
        }
    }

    private void T(boolean z) {
        if(this.l != z) {
            this.l = z;
            this.K((z ? 9 : 10));
        }
    }

    private void U(int v) {
        int v1;
        if(v != 1) {
            switch(v) {
                case 2: {
                    switch(this.q) {
                        case 3: {
                            this.q = 4;
                            this.K(24);
                            return;
                        }
                        case 6: {
                            this.q = 7;
                            v1 = 27;
                            this.K(v1);
                            return;
                        }
                        default: {
                            return;
                        }
                    }
                }
                case 3: {
                    if(this.q != 3) {
                        this.q = 3;
                        v1 = 23;
                        this.K(v1);
                        return;
                    }
                    break;
                }
                case 6: {
                    if(this.q != 6) {
                        this.q = 6;
                        v1 = 26;
                        this.K(v1);
                        return;
                    }
                    break;
                }
                default: {
                    switch(this.q) {
                        case 3: 
                        case 4: {
                            this.K(25);
                            break;
                        }
                        case 6: 
                        case 7: {
                            this.K(28);
                        }
                    }
                    this.q = 0;
                }
            }
        }
        else if(this.q == 3) {
            this.q = 4;
            this.K(24);
        }
    }

    int q() {
        return this.f;
    }

    boolean r() {
        return this.n;
    }

    boolean s() {
        return j3.a(this.e);
    }

    boolean t() {
        return j3.b(this.e);
    }

    boolean u() {
        return j3.c();
    }

    boolean v() {
        return this.o;
    }

    boolean w() {
        return this.j;
    }

    boolean x() {
        return j3.d(this.e);
    }

    boolean y() {
        return j3.e(this.e);
    }

    boolean z() {
        return this.m;
    }
}


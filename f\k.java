package f;

import android.os.Build.VERSION;
import android.os.Environment;
import java.io.File;

class k {
    static String a() [...] // Inlined contents

    // 去混淆评级： 低(40)
    static String b() {
        return "com.jozein.xedgepro".contains("pro") ? "com.jozein.xedge" : "com.jozein.xedgepro";
    }

    static String c() {
        if(Build.VERSION.SDK_INT >= 24) {
            String s = Environment.getDataDirectory() + "/user_de/0/" + "com.jozein.xedgepro" + "/";
            try {
                if(new File(s).exists()) {
                    return s;
                }
            }
            catch(Throwable unused_ex) {
            }
        }
        return Environment.getDataDirectory() + "/data/" + "com.jozein.xedgepro" + "/";
    }

    static String d() [...] // Inlined contents

    // 去混淆评级： 低(30)
    static String e() {
        return "com.jozein.xedgepro".contains("pro") ? "com.jozein.xedgepro" : "com.jozein.xedgepropro";
    }

    static String f() [...] // Inlined contents
}


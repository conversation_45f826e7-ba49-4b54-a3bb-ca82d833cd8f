package f;

import a.b.e;
import a.b.f;
import a.b.x;
import android.app.ActivityManager.RunningTaskInfo;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageItemInfo;
import android.content.pm.PackageManager.NameNotFoundException;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.os.Handler;
import android.os.Parcelable;
import android.util.Pair;
import android.util.SparseArray;
import de.robv.android.xposed.XposedHelpers;
import g.a0;
import g.g;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.CollationKey;
import java.text.Collator;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;

public class c0 implements l {
    public static class a extends b {
        ActivityInfo g;
        ComponentName h;
        private String i;

        a(ActivityInfo activityInfo0) {
            super(activityInfo0.applicationInfo);
            this.g = activityInfo0;
        }

        // 去混淆评级： 低(40)
        @Override  // f.c0$b
        public boolean equals(Object object0) {
            return this == object0 ? true : object0 != null && this.getClass() == object0.getClass() && (this.a.uid == ((a)object0).a.uid && this.g.name.equals(((a)object0).g.name) && this.g.packageName.equals(((a)object0).g.packageName));
        }

        @Override  // f.c0$b
        public int hashCode() {
            return this.g.name.hashCode() + this.a.uid;
        }

        // 去混淆评级： 低(20)
        @Override  // f.c0$b
        public boolean m() {
            return this.g.enabled && this.a.enabled;
        }

        @Override  // f.c0$b
        protected Drawable o() {
            return c0.m(this.g);
        }

        @Override  // f.c0$b
        protected CharSequence p() {
            return c0.p(this.g);
        }

        public x q() {
            return this.l() ? new f(this.g.packageName, this.g.name, this.b) : new e(this.g.packageName, this.g.name);
        }

        public final String r() {
            return this.g.name;
        }

        public final ComponentName s() {
            if(this.h == null) {
                this.h = new ComponentName(this.g.packageName, this.g.name);
            }
            return this.h;
        }

        public final String t() {
            if(this.i == null) {
                this.i = c0.A(this.g.packageName, this.g.name);
            }
            return this.i;
        }

        @Override  // f.c0$b
        public String toString() {
            StringBuilder stringBuilder0;
            String s = this.i();
            String s1 = this.t();
            if(this.b == 0) {
                stringBuilder0 = new StringBuilder();
                stringBuilder0.append(s);
                stringBuilder0.append('/');
            }
            else {
                stringBuilder0 = new StringBuilder();
                stringBuilder0.append(s);
                stringBuilder0.append("(");
                stringBuilder0.append(this.b);
                stringBuilder0.append(")/");
            }
            stringBuilder0.append(s1);
            return stringBuilder0.toString();
        }

        public final boolean u() {
            return this.g.enabled;
        }
    }

    public static class b {
        final ApplicationInfo a;
        final int b;
        private String c;
        private Locale d;
        private CollationKey e;
        private g.g.b f;

        public b(ApplicationInfo applicationInfo0) {
            this.a = applicationInfo0;
            this.b = applicationInfo0.uid / 100000;
        }

        public final ApplicationInfo a() {
            return this.a;
        }

        private g b(int v) {
            int v1 = o0.g(v);
            g.g.b g$b0 = this.f;
            if(g$b0 != null) {
                return g$b0.b(v1);
            }
            g g0 = new g(this.o(), v1);
            if(v == this.b) {
                this.f = g0.e();
            }
            return g0;
        }

        final CollationKey c(Locale locale0, Collator collator0) {
            CollationKey collationKey0 = this.e;
            if(collationKey0 == null || !locale0.equals(this.d)) {
                collationKey0 = collator0.getCollationKey(this.g(locale0));
                this.e = collationKey0;
            }
            return collationKey0;
        }

        public g d() {
            return this.b(this.b);
        }

        public final Drawable e() {
            Drawable drawable0 = this.d();
            if(!this.m()) {
                a0.v(drawable0, true);
            }
            return drawable0;
        }

        @Override
        public boolean equals(Object object0) {
            if(this == object0) {
                return true;
            }
            if(object0 != null && this.getClass() == object0.getClass()) {
                int v = this.a.uid;
                ApplicationInfo applicationInfo0 = ((b)object0).a;
                return v == applicationInfo0.uid && this.a.packageName.equals(applicationInfo0.packageName);
            }
            return false;
        }

        public final String f() {
            return this.g(Locale.getDefault());
        }

        String g(Locale locale0) {
            if(!locale0.equals(this.d)) {
                this.c = c0.l(this.p(), this.b).toString();
                this.d = locale0;
            }
            return this.c;
        }

        Locale h() {
            return this.d;
        }

        @Override
        public int hashCode() {
            return this.a.uid;
        }

        public final String i() {
            return this.a.packageName;
        }

        public g j() {
            return this.b(0);
        }

        public final int k() {
            return this.b;
        }

        public final boolean l() {
            return o0.j(this.b);
        }

        public boolean m() {
            return this.a.enabled;
        }

        public final boolean n() {
            int v = this.a.flags;
            return (v & 1) != 0 || (v & 0x80) != 0;
        }

        protected Drawable o() {
            return c0.n(this.a);
        }

        protected CharSequence p() {
            return c0.q(this.a);
        }

        @Override
        public String toString() {
            String s = this.i();
            return this.b == 0 ? s : s + "(" + this.b + ")";
        }
    }

    public static class c {
        private final SparseArray a;

        public c() {
            this.a = new SparseArray();
        }

        public void a(String s, int v) {
            Set set0 = (Set)this.a.get(v);
            if(set0 == null) {
                set0 = new HashSet();
                this.a.append(v, set0);
            }
            set0.add(s);
        }

        public void b(Set set0, int v) {
            Set set1 = (Set)this.a.get(v);
            if(set1 == null) {
                set1 = new HashSet();
                this.a.append(v, set1);
            }
            set1.addAll(set0);
        }

        public boolean c(String s, int v) {
            Set set0 = (Set)this.a.get(-1);
            if(set0 != null && set0.contains(s)) {
                return true;
            }
            Set set1 = (Set)this.a.get(v);
            return set1 != null && set1.contains(s);
        }

        public boolean d() {
            int v = this.a.size();
            if(v > 0) {
                for(int v1 = 0; v1 < v; ++v1) {
                    if(!((Set)this.a.valueAt(v1)).isEmpty()) {
                        return false;
                    }
                }
            }
            return true;
        }
    }

    public static class d extends b {
        final PackageInfo g;
        final a[] h;
        private static final a[] i;

        static {
            d.i = new a[0];
        }

        d(PackageInfo packageInfo0) {
            super(packageInfo0.applicationInfo);
            this.g = packageInfo0;
            ActivityInfo[] arr_activityInfo = packageInfo0.activities;
            if(arr_activityInfo == null || arr_activityInfo.length <= 0) {
                this.h = d.i;
            }
            else {
                this.h = new a[arr_activityInfo.length];
                for(int v = 0; true; ++v) {
                    a[] arr_c0$a = this.h;
                    if(v >= arr_c0$a.length) {
                        break;
                    }
                    arr_c0$a[v] = new a(packageInfo0.activities[v]);
                }
            }
        }

        public a[] q() {
            return this.h;
        }
    }

    public static class f.c0.e extends a {
        final ResolveInfo j;

        f.c0.e(ResolveInfo resolveInfo0) {
            super(resolveInfo0.activityInfo);
            this.j = resolveInfo0;
        }

        @Override  // f.c0$b
        public g d() {
            g g0 = super.d();
            g0.c();
            return g0;
        }

        @Override  // f.c0$a
        protected Drawable o() {
            return c0.o(this.j);
        }

        @Override  // f.c0$a
        protected CharSequence p() {
            return c0.r(this.j);
        }

        @Override  // f.c0$a
        public x q() {
            return new a.b.l(this.a.packageName, this.g.name, this.b);
        }

        public Intent v() {
            return c0.a().setComponent(this.s());
        }
    }

    public static class f.c0.f extends b {
        final ActivityManager.RunningTaskInfo g;
        final ActivityInfo h;

        public f.c0.f(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0, int v) {
            this(activityManager$RunningTaskInfo0, f.c0.f.q(activityManager$RunningTaskInfo0, v));
        }

        public f.c0.f(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0, Pair pair0) {
            super(((ApplicationInfo)pair0.first));
            this.g = activityManager$RunningTaskInfo0;
            this.h = (ActivityInfo)pair0.second;
        }

        @Override  // f.c0$b
        public g d() {
            g g0 = super.d();
            g0.c();
            return g0;
        }

        // 去混淆评级： 低(20)
        @Override  // f.c0$b
        public boolean equals(Object object0) {
            return this == object0 ? true : object0 != null && this.getClass() == object0.getClass() && this.g.id == ((f.c0.f)object0).g.id;
        }

        @Override  // f.c0$b
        public int hashCode() {
            return this.g.id;
        }

        @Override  // f.c0$b
        protected Drawable o() {
            return this.h == null ? super.o() : c0.m(this.h);
        }

        @Override  // f.c0$b
        protected CharSequence p() {
            return c0.p(this.h);
        }

        private static Pair q(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0, int v) {
            try {
                ActivityInfo activityInfo0 = c0.b(activityManager$RunningTaskInfo0.baseActivity, v);
                return new Pair(activityInfo0.applicationInfo, activityInfo0);
            }
            catch(Throwable throwable0) {
                v.c(throwable0.toString());
                return new Pair(c0.c(activityManager$RunningTaskInfo0.baseActivity.getPackageName(), v), null);
            }
        }

        public final ActivityManager.RunningTaskInfo r() {
            return this.g;
        }
    }

    private static Constructor A = null;
    private static Method B = null;
    private static PackageManager C = null;
    private static Object D = null;
    private static Method E = null;
    private static Method F = null;
    private static Method G = null;
    private static Method H = null;
    private static Method I = null;
    public static String z = " (";

    static {
    }

    public static String A(String s, String s1) {
        int v = s.length();
        return s1.length() <= v || s1.charAt(v) != 46 || !s1.startsWith(s) ? s1 : s1.substring(v);
    }

    public static String B(Intent intent0) {
        ComponentName componentName0 = intent0.getComponent();
        if(componentName0 != null) {
            return componentName0.flattenToShortString();
        }
        String s = intent0.getPackage();
        return s == null ? intent0.toString() : s;
    }

    public static String C(String s, String s1) {
        return s + '/' + c0.A(s, s1);
    }

    public static Intent a() {
        return new Intent("android.intent.action.MAIN").addCategory("android.intent.category.LAUNCHER");
    }

    public static ActivityInfo b(ComponentName componentName0, int v) {
        if(v != o0.m()) {
            ActivityInfo[] arr_activityInfo = c0.i(componentName0.getPackageName(), 0x8201, v).activities;
            if(arr_activityInfo != null && arr_activityInfo.length > 0) {
                String s = componentName0.getClassName();
                for(int v1 = 0; v1 < arr_activityInfo.length; ++v1) {
                    ActivityInfo activityInfo0 = arr_activityInfo[v1];
                    if(s.equals(activityInfo0.name)) {
                        return activityInfo0;
                    }
                }
            }
            throw new PackageManager.NameNotFoundException(componentName0.flattenToShortString() + c0.z + v + ").");
        }
        ActivityInfo activityInfo1 = c0.j().getActivityInfo(componentName0, 0x8200);
        if(activityInfo1.applicationInfo.uid / 100000 != v) {
            throw new PackageManager.NameNotFoundException(componentName0.flattenToShortString() + "(" + v + ")");
        }
        return activityInfo1;
    }

    public static ApplicationInfo c(String s, int v) {
        ApplicationInfo applicationInfo0 = null;
        Class class0 = String.class;
        if(v != o0.m()) {
            try {
                if(Build.VERSION.SDK_INT >= 24) {
                    if(c0.H == null) {
                        c0.H = f0.n().h(PackageManager.class, "getApplicationInfoAsUser", new Class[]{class0, Integer.TYPE, Integer.TYPE});
                    }
                    applicationInfo0 = (ApplicationInfo)c0.H.invoke(c0.j(), s, 0x8200, v);
                }
                else {
                    Object object0 = c0.d();
                    if(object0 != null) {
                        if(c0.H == null) {
                            c0.H = f0.n().h(object0.getClass(), "getApplicationInfo", new Class[]{class0, Integer.TYPE, Integer.TYPE});
                        }
                        applicationInfo0 = (ApplicationInfo)c0.H.invoke(object0, s, 0x8200, v);
                    }
                }
            }
            catch(InvocationTargetException | Throwable unused_ex) {
            }
            if(applicationInfo0 == null || applicationInfo0.uid / 100000 != v) {
                throw new PackageManager.NameNotFoundException(s + " (" + v + ")");
            }
            return applicationInfo0;
        }
        return c0.j().getApplicationInfo(s, 0x8200);
    }

    private static Object d() {
        Field field0 = c0.D;
        if(field0 == null) {
            PackageManager packageManager0 = c0.j();
            try {
                field0 = f0.n().g(packageManager0.getClass(), "mPm");
                c0.D = field0;
                return field0;
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        return field0;
    }

    public static List e(int v, int v1) {
        if(v1 != o0.m()) {
            try {
                if(c0.G == null) {
                    c0.G = f0.n().h(PackageManager.class, (Build.VERSION.SDK_INT < 24 ? "getInstalledPackages" : "getInstalledPackagesAsUser"), new Class[]{Integer.TYPE, Integer.TYPE});
                }
                return (List)c0.G.invoke(c0.j(), v, v1);
            }
            catch(InvocationTargetException | Throwable unused_ex) {
                return null;
            }
        }
        return c0.j().getInstalledPackages(v);
    }

    public static String f(Intent intent0) {
        if(intent0 == null) {
            return null;
        }
        String s = intent0.getPackage();
        if(s == null) {
            ComponentName componentName0 = intent0.getComponent();
            return componentName0 == null ? null : componentName0.getPackageName();
        }
        return s;
    }

    public static Intent g(String s, int v) {
        Intent intent0 = c0.a();
        intent0.setPackage(s);
        List list0 = c0.v(intent0, 0, v);
        if(list0 == null || list0.size() == 0) {
            intent0.removeCategory("android.intent.category.LAUNCHER");
            intent0.addCategory("android.intent.category.INFO");
            list0 = c0.v(intent0, 0, v);
        }
        if(list0 == null || list0.size() <= 0) {
            throw new PackageManager.NameNotFoundException(s + c0.z + v + ").");
        }
        ActivityInfo activityInfo0 = ((ResolveInfo)list0.get(0)).activityInfo;
        intent0.setClassName(activityInfo0.packageName, activityInfo0.name);
        intent0.setFlags(0x10000000);
        return intent0;
    }

    public static List h(Parcelable parcelable0) {
        Method method0 = c0.B;
        if(method0 == null) {
            method0 = f0.n().j(parcelable0.getClass(), "getList", new Class[0]);
            c0.B = method0;
        }
        return (List)method0.invoke(parcelable0);
    }

    public static PackageInfo i(String s, int v, int v1) {
        PackageInfo packageInfo0 = null;
        Class class0 = String.class;
        if(v1 != o0.m()) {
            try {
                if(Build.VERSION.SDK_INT >= 24) {
                    if(c0.I == null) {
                        c0.I = f0.n().h(PackageManager.class, "getPackageInfoAsUser", new Class[]{class0, Integer.TYPE, Integer.TYPE});
                    }
                    packageInfo0 = (PackageInfo)c0.I.invoke(c0.j(), s, v, v1);
                }
                else {
                    Object object0 = c0.d();
                    if(object0 != null) {
                        if(c0.I == null) {
                            c0.I = f0.n().h(object0.getClass(), "getPackageInfo", new Class[]{class0, Integer.TYPE, Integer.TYPE});
                        }
                        packageInfo0 = (PackageInfo)c0.I.invoke(object0, s, v, v1);
                    }
                }
            }
            catch(InvocationTargetException | Throwable unused_ex) {
            }
            if(packageInfo0 == null || packageInfo0.applicationInfo.uid / 100000 != v1) {
                throw new PackageManager.NameNotFoundException(s + " (" + v1 + ")");
            }
            return packageInfo0;
        }
        return c0.j().getPackageInfo(s, v);
    }

    public static PackageManager j() {
        if(c0.C == null) {
            c0.C = z.P();
        }
        return c0.C;
    }

    public static g k(Drawable drawable0, int v) {
        return new g(drawable0, o0.g(v));
    }

    public static CharSequence l(CharSequence charSequence0, int v) {
        return charSequence0;
    }

    public static Drawable m(ActivityInfo activityInfo0) {
        Drawable drawable0 = c0.s(activityInfo0);
        return drawable0 == null ? c0.s(activityInfo0) : drawable0;
    }

    public static Drawable n(ApplicationInfo applicationInfo0) {
        return c0.s(applicationInfo0);
    }

    public static Drawable o(ResolveInfo resolveInfo0) {
        Drawable drawable0 = c0.t(resolveInfo0);
        return drawable0 == null ? c0.s(resolveInfo0.activityInfo.applicationInfo) : drawable0;
    }

    public static CharSequence p(ActivityInfo activityInfo0) {
        PackageManager packageManager0 = c0.j();
        CharSequence charSequence0 = activityInfo0.loadLabel(packageManager0);
        return charSequence0.length() > 0 ? charSequence0 : activityInfo0.applicationInfo.loadLabel(packageManager0);
    }

    public static CharSequence q(ApplicationInfo applicationInfo0) {
        return applicationInfo0.loadLabel(c0.j());
    }

    public static CharSequence r(ResolveInfo resolveInfo0) {
        PackageManager packageManager0 = c0.j();
        CharSequence charSequence0 = resolveInfo0.loadLabel(packageManager0);
        return charSequence0.length() > 0 ? charSequence0 : resolveInfo0.activityInfo.applicationInfo.loadLabel(packageManager0);
    }

    private static Drawable s(PackageItemInfo packageItemInfo0) {
        return Build.VERSION.SDK_INT < 22 ? packageItemInfo0.loadIcon(c0.j()) : packageItemInfo0.loadUnbadgedIcon(c0.j());
    }

    private static Drawable t(ResolveInfo resolveInfo0) {
        if(Build.VERSION.SDK_INT >= 23) {
            PackageManager packageManager0 = c0.j();
            int v = resolveInfo0.icon;
            if(v != 0) {
                Drawable drawable0 = resolveInfo0.resolvePackageName == null ? null : packageManager0.getDrawable(resolveInfo0.resolvePackageName, v, null);
                if(drawable0 == null) {
                    drawable0 = packageManager0.getDrawable(resolveInfo0.activityInfo.packageName, v, resolveInfo0.activityInfo.applicationInfo);
                }
                return drawable0 == null ? resolveInfo0.activityInfo.loadUnbadgedIcon(packageManager0) : drawable0;
            }
            return resolveInfo0.activityInfo.loadUnbadgedIcon(packageManager0);
        }
        return resolveInfo0.loadIcon(c0.j());
    }

    public static Parcelable u(List list0) {
        Constructor constructor0 = c0.A;
        if(constructor0 == null) {
            f0 f00 = f0.n();
            constructor0 = f00.e(f00.c("android.content.pm.ParceledListSlice"), new Class[]{List.class});
            c0.A = constructor0;
        }
        return (Parcelable)constructor0.newInstance(list0);
    }

    public static List v(Intent intent0, int v, int v1) {
        if(v1 != o0.m()) {
            try {
                if(c0.F == null) {
                    c0.F = f0.n().h(PackageManager.class, "queryIntentActivitiesAsUser", new Class[]{Intent.class, Integer.TYPE, Integer.TYPE});
                }
                return (List)c0.F.invoke(c0.j(), intent0, v, v1);
            }
            catch(InvocationTargetException | Throwable unused_ex) {
                return null;
            }
        }
        return c0.j().queryIntentActivities(intent0, v);
    }

    public static void w(Context context0, BroadcastReceiver broadcastReceiver0, IntentFilter intentFilter0, Handler handler0) {
        try {
            if(Build.VERSION.SDK_INT >= 30) {
                XposedHelpers.callMethod(context0, "registerReceiverForAllUsers", new Object[]{broadcastReceiver0, intentFilter0, null, handler0});
                return;
            }
            XposedHelpers.callMethod(context0, "registerReceiverAsUser", new Object[]{broadcastReceiver0, o0.p(), intentFilter0, null, handler0});
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            context0.registerReceiver(broadcastReceiver0, intentFilter0, null, handler0);
        }
    }

    public static ResolveInfo x(Intent intent0, int v, int v1) {
        try {
            if(v1 == o0.m()) {
                return c0.j().resolveActivity(intent0, v);
            }
            if(c0.E == null) {
                c0.E = f0.n().h(PackageManager.class, "resolveActivityAsUser", new Class[]{Intent.class, Integer.TYPE, Integer.TYPE});
            }
            ResolveInfo resolveInfo0 = (ResolveInfo)c0.E.invoke(c0.j(), intent0, v, v1);
            return resolveInfo0 == null || resolveInfo0.activityInfo.applicationInfo.uid / 100000 != v1 ? null : resolveInfo0;
        }
        catch(InvocationTargetException | Throwable unused_ex) {
        }
        return null;
    }

    public static void y(Object object0) {
        c0.D = object0;
    }

    static void z(PackageManager packageManager0) {
        c0.C = packageManager0;
    }
}


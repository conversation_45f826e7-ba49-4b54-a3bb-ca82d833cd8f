package d;

import a.k;
import android.os.Bundle;
import android.view.View;
import android.widget.CompoundButton;
import e.j.f;
import e.j.j;
import e.m;
import f.c0.d;

public class h0 extends a {
    private k N;

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F060124);  // string:hidden_apps "Hidden apps on drawers"
        this.S(0x7F04002C, (View view0) -> if(this.N != null && !this.N.j()) {
            this.N(new m().u(this.u(0x7F06008D)), 1);  // string:check_clear_all "Are you sure to clear all?"
        });
    }

    @Override  // d.a
    protected int B0() {
        this.N = k.h();
        return super.B0();
    }

    @Override  // d.a
    protected e.j.k B1(int v) {
        d c0$d0 = (d)this.M.get(v);
        String s = c0$d0.i();
        e.j.k j$k0 = new f(this, c0$d0.d(), c0$d0.f(), s, this.N.g(s));
        ((j)j$k0).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> try {
            if(z) {
                this.N.e(compoundButton0.getContext(), s);
                return;
            }
            this.N.m(compoundButton0.getContext(), s);
        }
        catch(Throwable throwable0) {
            this.g0(throwable0);
        });
        if(!c0$d0.m()) {
            j$k0.e();
        }
        return j$k0;
    }

    // 检测为 Lambda 实现
    private void E1(String s, CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void F1(View view0) [...]

    @Override  // e.j0$c
    protected void J(Bundle bundle0, int v) {
        if(bundle0 == null) {
            return;
        }
        if(v == 1 && bundle0.getBoolean("result", false)) {
            try {
                this.N.f(this.f());
                int v1 = this.H0();
            label_6:
                for(int v2 = 0; v2 < v1; ++v2) {
                    ((f)this.L0(v2)).setChecked(false);
                }
            }
            catch(Throwable throwable0) {
                this.g0(throwable0);
                if(true) {
                    return;
                }
                goto label_6;
            }
        }
    }

    @Override  // d.a
    protected View h1(int v) {
        return this.B1(v);
    }

    @Override  // e.j
    protected void k1(int v) {
        ((f)this.L0(v)).h();
    }
}


package d;

import a.b;
import a.n;
import a.o;
import android.os.Bundle;
import android.view.View;
import com.jozein.xedgepro.ui.ActivityPerformAction;
import e.j.g;
import e.j.k;
import e.j;
import e.y;
import e.z;
import java.util.ArrayList;

public abstract class t0 extends j {
    private n M;
    private ArrayList N;
    private int[] O;
    private int[] P;
    private CharSequence[] Q;
    private CharSequence[] R;

    public t0() {
        this.O = null;
        this.P = null;
        this.Q = null;
        this.R = null;
    }

    @Override  // e.j
    protected int B0() {
        n n0 = this.B1();
        this.M = n0;
        this.N = n0.i();
        boolean z = this.h().getBoolean("selectable", false);
        int[] arr_v = z ? null : new int[]{0x7F060159, 0x7F060158, 0x7F0600CD};  // string:move_up "Move up"
        this.O = arr_v;
        this.P = new int[]{(z ? 0x7F060222 : 0x7F0600EE), 0x1040001, 0x7F06016F, 0x7F060182, 0x7F06002A};  // string:view "View"
        if(arr_v != null) {
            this.Q = new CharSequence[arr_v.length];
            for(int v1 = 0; true; ++v1) {
                int[] arr_v1 = this.O;
                if(v1 >= arr_v1.length) {
                    break;
                }
                CharSequence[] arr_charSequence = this.Q;
                arr_charSequence[v1] = this.u(arr_v1[v1]);
            }
        }
        this.R = new CharSequence[this.P.length];
        for(int v = 0; true; ++v) {
            int[] arr_v2 = this.P;
            if(v >= arr_v2.length) {
                break;
            }
            CharSequence[] arr_charSequence1 = this.R;
            arr_charSequence1[v] = this.u(arr_v2[v]);
        }
        return z ? this.N.size() : this.N.size() + 1;
    }

    protected abstract n B1();

    protected abstract b[] C1(Bundle arg1);

    protected abstract b D1(int arg1);

    protected abstract j E1(CharSequence arg1, b[] arg2, boolean arg3);

    protected abstract void F1();

    protected k G1(int v) {
        if(v < this.N.size()) {
            int v1 = (int)(((Integer)this.N.get(v)));
            return new g(this, this.D0(this.D1(v1)), ((o)this.M.h(v1)).a, null);
        }
        return this.b1();
    }

    protected void H1(j j0) {
        this.P(j0, 0x10003);
    }

    public t0 I1(boolean z) {
        this.h().putBoolean("selectable", z);
        return this;
    }

    @Override  // e.j0$c
    protected void J(Bundle bundle0, int v) {
        j j0;
        c.b b0;
        if(bundle0 == null) {
            return;
        }
        switch(v) {
            case 0xFFFF: 
            case 0x10000: {
                int v1 = this.E0();
                int v2 = bundle0.getInt("result", -1);
                if(v2 != -1) {
                    int v3 = 0x10004;
                    switch((v == 0xFFFF ? this.O[v2] : this.P[v2])) {
                        case 0x1040001: {
                            this.r().o().putParcelable("clipboard", this.D1(((int)(((Integer)this.N.get(v1))))));
                            return;
                        }
                        case 0x7F06002A: {  // string:action_info "Action info"
                            int v4 = (int)(((Integer)this.N.get(v1)));
                            b0 = new c.b().w(this.D1(v4));
                            this.N(b0, v3);
                            return;
                        }
                        case 0x7F0600CD: {  // string:delete "Delete"
                            try {
                                this.M.p(this.M0(), ((int)(((Integer)this.N.get(v1)))));
                                this.N.remove(v1);
                                this.u1(v1);
                            }
                            catch(Throwable throwable0) {
                                this.g0(throwable0);
                            }
                            return;
                        }
                        case 0x7F0600EE: {  // string:edit_action "Edit action"
                            o o0 = (o)this.M.h(((int)(((Integer)this.N.get(v1)))));
                            b[] arr_b = new b[o0.b.length];
                            System.arraycopy(o0.b, 0, arr_b, 0, o0.b.length);
                            j0 = this.E1(o0.a, arr_b, true);
                            v3 = 0x10002;
                            this.P(j0, v3);
                            return;
                        }
                        case 0x7F060158: {  // string:move_down "Move down"
                            if(v1 < this.N.size() - 1) {
                                try {
                                    this.M.j(((int)(((Integer)this.N.get(v1)))));
                                    this.N.add(v1 + 1, ((Integer)this.N.remove(v1)));
                                    this.a1(v1, v1 + 1);
                                    return;
                                label_32:
                                    if(v1 > 0) {
                                        this.M.l(((int)(((Integer)this.N.get(v1)))));
                                        this.N.add(v1 - 1, ((Integer)this.N.remove(v1)));
                                        this.a1(v1, v1 - 1);
                                        return;
                                    }
                                }
                                catch(Throwable throwable0) {
                                    this.g0(throwable0);
                                    return;
                                }
                            }
                            break;
                        }
                        case 0x7F060159: {  // string:move_up "Move up"
                            goto label_32;
                        }
                        case 0x7F06016F: {  // string:perform "Perform"
                            ActivityPerformAction.a(this.f(), this.D1(((int)(((Integer)this.N.get(v1))))));
                            return;
                        }
                        case 0x7F060182: {  // string:rename "Rename"
                            b0 = new y();
                            ((y)b0).J(this.u(0x7F0600F8), null, ((o)this.M.h(((int)(((Integer)this.N.get(v1)))))).a, 3, 0x40);  // string:enter_name "Enter name"
                            v3 = 0x10001;
                            this.N(b0, v3);
                            return;
                        }
                        case 0x7F060222: {  // string:view "View"
                            o o1 = (o)this.M.h(((int)(((Integer)this.N.get(v1)))));
                            j0 = this.E1(o1.a, o1.b, false);
                            this.P(j0, v3);
                            return;
                        }
                        default: {
                            return;
                        }
                    }
                }
                break;
            }
            case 0x10001: {
                CharSequence charSequence0 = bundle0.getCharSequence("result");
                if(charSequence0 != null) {
                    int v5 = this.E0();
                    int v6 = (int)(((Integer)this.N.get(v5)));
                    try {
                        this.M.t(this.M0(), v6, new o(charSequence0.toString(), ((o)this.M.h(v6)).b));
                        ((k)this.L0(v5)).setText(charSequence0);
                    }
                    catch(Throwable throwable0) {
                        this.g0(throwable0);
                    }
                    return;
                }
                break;
            }
            case 0x10002: {
                b[] arr_b1 = this.C1(bundle0);
                if(arr_b1 != null) {
                    int v7 = this.E0();
                    int v8 = (int)(((Integer)this.N.get(v7)));
                    try {
                        this.M.t(this.M0(), v8, new o(((o)this.M.h(v8)).a, arr_b1));
                        this.Q0(v7);
                    }
                    catch(Throwable throwable0) {
                        this.g0(throwable0);
                    }
                    return;
                }
                break;
            }
            case 0x10003: {
                b[] arr_b2 = this.C1(bundle0);
                if(arr_b2 != null) {
                    try {
                        Integer integer0 = this.M.e(this.M0(), new o(null, arr_b2));
                        this.N.add(integer0);
                        this.x0(this.E0());
                    }
                    catch(Throwable throwable0) {
                        this.g0(throwable0);
                    }
                    return;
                }
                break;
            }
        }
    }

    @Override  // e.j
    protected View h1(int v) {
        return this.G1(v);
    }

    @Override  // e.j
    protected void k1(int v) {
        if(v < this.N.size()) {
            if(this.h().getBoolean("selectable", false)) {
                this.W("result", this.D1(((int)(((Integer)this.N.get(v))))));
                this.L();
                return;
            }
            this.N(new z().u(this.Q), 0xFFFF);
            return;
        }
        this.F1();
    }

    @Override  // e.j
    protected boolean l1(int v) {
        if(v < this.N.size()) {
            this.N(new z().u(this.R), 0x10000);
            return true;
        }
        return super.l1(v);
    }
}


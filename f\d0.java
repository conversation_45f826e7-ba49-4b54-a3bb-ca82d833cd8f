package f;

import a.b;
import android.os.Parcel;

public class d0 implements s {
    private final Parcel z;

    public d0(Parcel parcel0) {
        this.z = parcel0;
    }

    @Override  // f.s
    public String a() {
        return this.z.readString();
    }

    @Override  // f.s
    public s b(String s) {
        return this.p(s);
    }

    @Override  // f.s
    public s c(String s) {
        return this.o(s);
    }

    @Override  // f.s
    public s d(int v) {
        return this.m(v);
    }

    @Override  // f.s
    public String e() {
        return this.a();
    }

    @Override  // f.s
    public s f(b b0) {
        return this.n(b0);
    }

    @Override  // f.s
    public b g() {
        return b.s(this);
    }

    @Override  // f.s
    public int h() {
        return this.z.readInt();
    }

    public boolean i() [...] // Inlined contents

    public d0 j() {
        return this;
    }

    public void k() {
    }

    public void l() {
    }

    public d0 m(int v) {
        this.z.writeInt(v);
        return this;
    }

    public d0 n(b b0) {
        b0.y(this);
        return this;
    }

    public d0 o(String s) {
        this.z.writeString(s);
        return this;
    }

    public d0 p(String s) {
        return this.o(s);
    }
}


package e;

import android.app.ActionBar;
import android.app.Activity;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowInsetsController;
import android.view.WindowManager.LayoutParams;

public class r extends c {
    @Override  // e.j0$c
    protected void B() {
        super.B();
        ActionBar actionBar0 = this.getActivity().getActionBar();
        if(actionBar0 != null) {
            actionBar0.hide();
        }
        this.h0();
    }

    @Override  // e.j0$c
    protected void I() {
        super.I();
        ActionBar actionBar0 = this.getActivity().getActionBar();
        if(actionBar0 != null) {
            actionBar0.show();
        }
        this.i0();
    }

    private void h0() {
        this.j0(true);
    }

    private void i0() {
        this.j0(false);
    }

    private void j0(boolean z) {
        Activity activity0 = this.getActivity();
        if(activity0 != null) {
            Window window0 = activity0.getWindow();
            if(window0 != null) {
                WindowManager.LayoutParams windowManager$LayoutParams0 = window0.getAttributes();
                windowManager$LayoutParams0.flags = z ? windowManager$LayoutParams0.flags | 0x400 : windowManager$LayoutParams0.flags & 0xFFFFFBFF;
                int v = Build.VERSION.SDK_INT;
                if(v >= 28) {
                    windowManager$LayoutParams0.layoutInDisplayCutoutMode = z;
                }
                window0.setAttributes(windowManager$LayoutParams0);
                if(v >= 30) {
                    WindowInsetsController windowInsetsController0 = window0.getDecorView().getWindowInsetsController();
                    if(windowInsetsController0 != null) {
                        if(z) {
                            windowInsetsController0.hide(0x207);
                            return;
                        }
                        windowInsetsController0.show(0x207);
                    }
                }
            }
        }
    }

    @Override  // android.app.Fragment
    public void onViewCreated(View view0, Bundle bundle0) {
        if(view0 != null) {
            view0.setSystemUiVisibility(0x1606);
        }
    }
}


package c;

import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface;

public final class a implements DialogInterface.OnClickListener {
    public final CharSequence A;
    public final b z;

    public a(b b0, CharSequence charSequence0) {
        this.z = b0;
        this.A = charSequence0;
    }

    @Override  // android.content.DialogInterface$OnClickListener
    public final void onClick(DialogInterface dialogInterface0, int v) {
        this.z.v(this.A, dialogInterface0, v);
    }
}


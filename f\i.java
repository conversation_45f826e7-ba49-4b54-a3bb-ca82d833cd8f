package f;

public abstract class i {
    static class a extends i {
        private final i a;

        a(i i0) {
            this.a = i0;
        }

        @Override  // f.i
        public int a(f i$f0) {
            return ~this.a.a(i$f0);
        }

        @Override  // f.i
        public Integer b() {
            Integer integer0 = this.a.b();
            return integer0 == null ? null : ((int)(~((int)integer0)));
        }

        @Override
        public String toString() {
            return '~' + this.a.toString();
        }
    }

    static class b extends i {
        private final i a;
        private final char b;
        private final i c;

        b(i i0, char c, i i1) {
            this.a = i0;
            this.b = c;
            this.c = i1;
        }

        @Override  // f.i
        public int a(f i$f0) {
            return this.g(this.a.a(i$f0), this.c.a(i$f0));
        }

        @Override  // f.i
        public Integer b() {
            Integer integer0 = this.a.b();
            if(integer0 == null) {
                return null;
            }
            Integer integer1 = this.c.b();
            return integer1 == null ? null : this.g(((int)integer0), ((int)integer1));
        }

        private int g(int v, int v1) {
            switch(this.b) {
                case 35: {
                    return v >> v1;
                }
                case 37: {
                    return v % v1;
                }
                case 38: {
                    return v & v1;
                }
                case 42: {
                    return v * v1;
                }
                case 43: {
                    return v + v1;
                }
                case 45: {
                    return v - v1;
                }
                case 0x2F: {
                    return v / v1;
                }
                case 0x40: {
                    return v << v1;
                }
                case 94: {
                    return v ^ v1;
                }
                case 0x7C: {
                    return v | v1;
                }
                default: {
                    throw new RuntimeException("Invalid operator: " + this.b);
                }
            }
        }

        @Override
        public String toString() {
            int v = this.b;
            if(v == 0x40) {
                return '(' + this.a.toString() + ' ' + "<<" + ' ' + this.c.toString() + ')';
            }
            return v == 35 ? '(' + this.a.toString() + ' ' + ">>" + ' ' + this.c.toString() + ')' : '(' + this.a.toString() + ' ' + Character.toString(((char)v)) + ' ' + this.c.toString() + ')';
        }
    }

    static class c extends i {
        private final String a;
        private final char b;

        c(String s, char c) {
            this.a = s;
            this.b = c;
        }

        @Override  // f.i
        public int a(f i$f0) {
            t t0 = i$f0.b(this.a);
            if(t0 != null) {
                switch(this.b) {
                    case 34: {
                        int v = t0.a + 1;
                        t0.a = v;
                        return v;
                    }
                    case 39: {
                        int v1 = t0.a;
                        t0.a = v1 + 1;
                        return v1;
                    }
                    case 44: {
                        int v2 = t0.a;
                        t0.a = v2 - 1;
                        return v2;
                    }
                    case 59: {
                        int v3 = t0.a - 1;
                        t0.a = v3;
                        return v3;
                    }
                    default: {
                        throw new RuntimeException("Invalid operator: " + this.b);
                    }
                }
            }
            throw new RuntimeException("Variable not exists: " + this.a);
        }

        @Override  // f.i
        public Integer b() {
            return null;
        }

        @Override
        public String toString() {
            StringBuilder stringBuilder0;
            int v = this.b;
            String s = "++";
            switch(v) {
                case 34: {
                    return s + this.a;
                }
                case 39: {
                    stringBuilder0 = new StringBuilder();
                    break;
                }
                default: {
                    s = "--";
                    switch(v) {
                        case 44: {
                            stringBuilder0 = new StringBuilder();
                            break;
                        }
                        case 59: {
                            return s + this.a;
                        }
                        default: {
                            throw new RuntimeException("Invalid operator: " + this.b);
                        }
                    }
                }
            }
            stringBuilder0.append(this.a);
            stringBuilder0.append(s);
            return stringBuilder0.toString();
        }
    }

    static class d extends i {
        private final int a;

        d(int v) {
            this.a = v;
        }

        @Override  // f.i
        public int a(f i$f0) {
            return this.a;
        }

        @Override  // f.i
        public Integer b() {
            return this.a;
        }

        @Override
        public String toString() {
            return Integer.toString(this.a);
        }
    }

    static class e extends i {
        private final i a;

        e(i i0) {
            this.a = i0;
        }

        @Override  // f.i
        public int a(f i$f0) {
            return -this.a.a(i$f0);
        }

        @Override  // f.i
        public Integer b() {
            Integer integer0 = this.a.b();
            return integer0 == null ? null : ((int)(-((int)integer0)));
        }

        @Override
        public String toString() {
            return '-' + this.a.toString();
        }
    }

    public interface f {
        int a(String arg1);

        t b(String arg1);
    }

    static class g extends i {
        private final String a;

        g(String s) {
            this.a = s;
        }

        @Override  // f.i
        public int a(f i$f0) {
            return i$f0.a(this.a);
        }

        @Override  // f.i
        public Integer b() {
            return null;
        }

        @Override
        public String toString() {
            return this.a;
        }
    }

    public abstract int a(f arg1);

    public abstract Integer b();

    private static int c(String s, int v, int v1) {
        int v2 = v1 - 1;
        int v3 = 0;
        int v4 = -1;
        int v5 = -1;
        int v6 = -1;
        int v7 = -1;
        int v8 = -1;
        while(v2 > v) {
            switch(s.charAt(v2)) {
                case 38: {
                    if(v3 == 0 && v7 < 0) {
                        v7 = v2;
                    }
                    break;
                }
                case 40: {
                    --v3;
                    break;
                }
                case 41: {
                    ++v3;
                    break;
                }
                case 43: 
                case 45: {
                    if(v3 == 0 && v8 < 0 && "|^&<>+-*/%@#\"\';,".indexOf(s.charAt(v2 - 1)) == -1) {
                        v8 = v2;
                    }
                    break;
                }
                case 37: 
                case 42: 
                case 0x2F: {
                    if(v3 == 0 && v6 < 0) {
                        v6 = v2;
                    }
                    break;
                }
                case 35: 
                case 0x40: {
                    if(v3 == 0 && v5 < 0) {
                        v5 = v2;
                    }
                    break;
                }
                case 94: {
                    if(v3 == 0 && v4 < 0) {
                        v4 = v2;
                    }
                    break;
                }
                case 0x7C: {
                    if(v3 == 0) {
                        return v2;
                    }
                }
            }
            if(v3 < 0) {
                throw new RuntimeException("Invalid expression.");
            }
            --v2;
        }
        if(s.charAt(v) == 40) {
            --v3;
        }
        if(v3 != 0) {
            throw new RuntimeException("Invalid expression.");
        }
        if(v4 <= 0) {
            if(v7 > 0) {
                return v7;
            }
            if(v5 > 0) {
                return v5;
            }
            return v8 <= 0 ? v6 : v8;
        }
        return v4;
    }

    public static i d(String s) {
        if(s == null || s.length() == 0) {
            throw new RuntimeException("Empty expression.");
        }
        String s1 = i.f(s);
        return i.e(s1, 0, s1.length());
    }

    private static i e(String s, int v, int v1) {
        int v2 = i.c(s, v, v1);
        if(v2 == -1) {
            int v3 = s.charAt(v);
            switch(v3) {
                case 40: {
                    if(s.charAt(v1 - 1) == 41) {
                        return i.e(s, v + 1, v1 - 1);
                    }
                    break;
                }
                case 43: {
                    return i.e(s, v + 1, v1);
                }
                case 45: {
                    i i0 = i.e(s, v + 1, v1);
                    Integer integer0 = i0.b();
                    return integer0 != null ? new d(-((int)integer0)) : new e(i0);
                }
                case 34: 
                case 39: 
                case 44: 
                case 59: {
                    int v4 = s.charAt(v + 1);
                    if((v4 < 97 || v4 > 0x7A) && (v4 < 65 || v4 > 90) && (v4 != 36 && v4 != 0x5F)) {
                        throw new RuntimeException("Invalid expression.");
                    }
                    return new c(s.substring(v + 1, v1), ((char)v3));
                }
                case 0x7E: {
                    i i1 = i.e(s, v + 1, v1);
                    Integer integer1 = i1.b();
                    return integer1 != null ? new d(~((int)integer1)) : new a(i1);
                }
            }
            return (v3 < 97 || v3 > 0x7A) && (v3 < 65 || v3 > 90) && (v3 != 36 && v3 != 0x5F) ? new d(Integer.parseInt(s.substring(v, v1))) : new g(s.substring(v, v1));
        }
        i i2 = i.e(s, v, v2);
        int v5 = s.charAt(v2);
        i i3 = i.e(s, v2 + 1, v1);
        Integer integer2 = new b(i2, ((char)v5), i3).b();
        return integer2 != null ? new d(((int)integer2)) : new b(i2, ((char)v5), i3);
    }

    private static String f(String s) {
        int v4;
        int v = s.length();
        StringBuilder stringBuilder0 = new StringBuilder(v);
        int v2 = -1;
        boolean z = false;
        for(int v1 = 0; v1 < v; ++v1) {
            int v3 = s.charAt(v1);
            switch(v3) {
                case 0x20: {
                    break;
                }
                case 43: {
                    if(s.charAt(v1 + 1) == 43) {
                        if(z) {
                            throw new RuntimeException("Invalid expression.");
                        }
                        if(v2 < 0) {
                            stringBuilder0.append('\"');
                        }
                        else {
                            stringBuilder0.insert(v2, '\'');
                        }
                        ++v1;
                        z = true;
                    }
                    else {
                        z = false;
                        stringBuilder0.append('+');
                    }
                    break;
                }
                case 45: {
                    if(s.charAt(v1 + 1) == 45) {
                        if(z) {
                            throw new RuntimeException("Invalid expression.");
                        }
                        if(v2 < 0) {
                            stringBuilder0.append(';');
                        }
                        else {
                            stringBuilder0.insert(v2, ',');
                        }
                        ++v1;
                        z = true;
                        break;
                    }
                    else {
                        z = false;
                        stringBuilder0.append('-');
                    }
                    break;
                }
                case 60: {
                    ++v1;
                    if(s.charAt(v1) != 60) {
                        throw new RuntimeException("Invalid expression.");
                    }
                    v4 = 0x40;
                    stringBuilder0.append(((char)v4));
                    v2 = -1;
                    z = false;
                    break;
                }
                case 62: {
                    ++v1;
                    if(s.charAt(v1) != 62) {
                        throw new RuntimeException("Invalid expression.");
                    }
                    v4 = 35;
                    stringBuilder0.append(((char)v4));
                    v2 = -1;
                    z = false;
                    break;
                }
                case 37: 
                case 38: 
                case 42: 
                case 0x2F: 
                case 94: 
                case 0x7C: {
                    v2 = -1;
                    z = false;
                    stringBuilder0.append(((char)v3));
                    break;
                }
                default: {
                    if(v2 < 0 && (v3 >= 97 && v3 <= 0x7A || v3 >= 65 && v3 <= 90 || v3 == 0x5F || v3 == 36)) {
                        v2 = stringBuilder0.length();
                    }
                    stringBuilder0.append(((char)v3));
                }
            }
        }
        return stringBuilder0.length() == v ? s : stringBuilder0.toString();
    }
}


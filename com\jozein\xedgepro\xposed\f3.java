package com.jozein.xedgepro.xposed;

import android.app.ActivityManager.RunningTaskInfo;
import android.app.TaskInfo;
import android.os.Build.VERSION;
import de.robv.android.xposed.XposedHelpers;
import f.o0;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

class f3 {
    private static Field a;
    private static Field b;
    private static Method c;
    private static Field d;
    private static Field e;
    private static Method f;
    private static Field g;

    static {
    }

    static int a(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0) {
        if(Build.VERSION.SDK_INT >= 29 && v.f) {
            try {
                if(f3.a == null) {
                    f3.a = XposedHelpers.findField(TaskInfo.class, "displayId");
                }
                int v = f3.a.getInt(activityManager$RunningTaskInfo0);
                return v == -1 ? 0 : v;
            }
            catch(Throwable throwable0) {
                f.v.d(throwable0);
            }
        }
        return 0;
    }

    static int b(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0) {
        if(f3.g == null) {
            f3.g = XposedHelpers.findField(ActivityManager.RunningTaskInfo.class, "stackId");
        }
        return f3.g.getInt(activityManager$RunningTaskInfo0);
    }

    static int c(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0) {
        if(Build.VERSION.SDK_INT >= 29) {
            try {
                if(f3.b == null) {
                    f3.b = XposedHelpers.findField(TaskInfo.class, "userId");
                }
                return f3.b.getInt(activityManager$RunningTaskInfo0);
            }
            catch(Throwable throwable0) {
                f.v.d(throwable0);
            }
        }
        return o0.d();
    }

    static int d(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0) {
        try {
            int v = Build.VERSION.SDK_INT;
            if(v >= 0x1F) {
                if(f3.c == null) {
                    f3.c = XposedHelpers.findMethodExact(TaskInfo.class, "getWindowingMode", new Class[0]);
                }
                return (int)(((Integer)f3.c.invoke(activityManager$RunningTaskInfo0)));
            }
            if(v >= 28) {
                if(f3.d == null) {
                    f3.d = XposedHelpers.findField(ActivityManager.RunningTaskInfo.class, "configuration");
                }
                if(f3.e == null) {
                    f3.e = XposedHelpers.findField(f3.d.getType(), "windowConfiguration");
                }
                if(f3.f == null) {
                    f3.f = XposedHelpers.findMethodExact(f3.e.getType(), "getWindowingMode", new Class[0]);
                }
                Object object0 = f3.d.get(activityManager$RunningTaskInfo0);
                Object object1 = f3.e.get(object0);
                return (int)(((Integer)f3.f.invoke(object1)));
            }
        }
        catch(Throwable throwable0) {
            f.v.d(throwable0);
        }
        return 0;
    }

    static boolean e(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0) {
        try {
            int v = Build.VERSION.SDK_INT;
            if(v >= 28) {
                return f3.d(activityManager$RunningTaskInfo0) == 2;
            }
            if(v >= 24 && f3.b(activityManager$RunningTaskInfo0) == 4) {
                return true;
            }
        }
        catch(Throwable throwable0) {
            f.v.c(throwable0.toString());
        }
        return false;
    }

    static boolean f(ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0) {
        try {
            int v = Build.VERSION.SDK_INT;
            if(v >= 28) {
                int v1 = f3.d(activityManager$RunningTaskInfo0);
                return v < 33 ? v1 == 3 || v1 == 4 : v1 == 6;
            }
            if(v >= 24 && f3.b(activityManager$RunningTaskInfo0) == 3) {
                return true;
            }
        }
        catch(Throwable throwable0) {
            f.v.c(throwable0.toString());
        }
        return false;
    }
}


package com.jozein.xedgepro.xposed;

public final class x2 implements Runnable {
    public final int A;
    public final float B;
    public final float C;
    public final y2 z;

    public x2(y2 y20, int v, float f, float f1) {
        this.z = y20;
        this.A = v;
        this.B = f;
        this.C = f1;
    }

    @Override
    public final void run() {
        this.z.s(this.A, this.B, this.C);
    }
}


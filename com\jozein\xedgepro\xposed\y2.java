package com.jozein.xedgepro.xposed;

import a.p;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint.Style;
import android.graphics.Paint;
import android.graphics.Point;
import android.graphics.PorterDuff.Mode;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.util.Property;
import android.view.View;
import f.v;
import g.a0;
import g.g;
import g.h;

class y2 implements a {
    class com.jozein.xedgepro.xposed.y2.a extends Property {
        com.jozein.xedgepro.xposed.y2.a(Class class0, String s) {
            super(class0, s);
        }

        public Float a(b y2$b0) {
            return y2$b0.T;
        }

        public void b(b y2$b0, Float float0) {
            y2$b0.i(((float)float0));
        }

        @Override  // android.util.Property
        public Object get(Object object0) {
            return this.a(((b)object0));
        }

        @Override  // android.util.Property
        public void set(Object object0, Object object1) {
            this.b(((b)object0), ((Float)object1));
        }
    }

    class b extends View {
        private final float A;
        private final float B;
        private final Drawable[] C;
        private final Drawable[] D;
        private final int E;
        private final int F;
        private final int G;
        private final int H;
        private final int I;
        private final int J;
        private final float K;
        private final float L;
        private final RectF M;
        private final RectF N;
        private final int[] O;
        private final int[] P;
        private final Point Q;
        private int R;
        private boolean S;
        float T;
        final y2 U;
        private final int z;

        public b(int v, float f, float f1, Drawable[] arr_drawable, Drawable[] arr_drawable1, int v1, int v2) {
            super(y20.z);
            this.Q = new Point();
            this.R = -1;
            this.S = false;
            this.T = 1.0f;
            this.z = v;
            this.A = f;
            this.B = f1;
            this.C = arr_drawable;
            this.D = arr_drawable1;
            this.E = v1;
            this.L = 160.0f / ((float)v1);
            switch(v) {
                case 1: {
                    this.K = 100.0f;
                    break;
                }
                case 2: {
                    this.K = 10.0f;
                    break;
                }
                case 3: {
                    this.K = 190.0f;
                    break;
                }
                default: {
                    this.K = -80.0f;
                }
            }
            float f2 = a0.m(y20.z) * ((float)y20.C.v(26, 100)) / 100.0f;
            int v4 = (int)(0.4f * f2 + 0.5f);
            int v5 = (int)(((float)v4) * 0.23f + 0.5f);
            this.G = v5;
            this.F = v5;
            int v6 = (int)(0.02f * f2 + 0.5f);
            int v7 = (int)(f2 * 0.93f + 0.5f);
            this.I = v7;
            this.H = v7 - v4;
            int v8 = v7 - v4 / 2;
            if(arr_drawable1 != null) {
                v7 += v4;
            }
            this.J = v7;
            int v9 = v7 - v4 / 2;
            y20.E.setColor(v2);
            float f3 = (float)(v4 - v6);
            y20.E.setStrokeWidth(f3);
            y20.F.setColor(this.h(v2));
            y20.F.setStrokeWidth(f3);
            y20.G.setStrokeWidth(((float)v6));
            this.M = new RectF(f - ((float)v8), f1 - ((float)v8), f + ((float)v8), ((float)v8) + f1);
            this.N = new RectF(f - ((float)v9), f1 - ((float)v9), f + ((float)v9), f1 + ((float)v9));
            this.O = new int[v1 * 2];
            this.P = new int[v1 * 2];
            float f4 = this.K + 160.0f / ((float)v1) / 2.0f;
            for(int v10 = 0; v10 < v1; ++v10) {
                float f5 = this.L * ((float)v10) + f4;
                Point point0 = this.g(f5, v8);
                this.O[v10 * 2] = point0.x;
                int v11 = v10 * 2 + 1;
                this.O[v11] = point0.y;
                Point point1 = this.g(f5, v9);
                this.P[v10 * 2] = point1.x;
                this.P[v11] = point1.y;
            }
            if(this.e()) {
                for(int v3 = 0; v3 < v1 / 2; ++v3) {
                    int v12 = v1 - 1 - v3;
                    Drawable drawable0 = arr_drawable[v3];
                    arr_drawable[v3] = arr_drawable[v12];
                    arr_drawable[v12] = drawable0;
                    if(arr_drawable1 != null) {
                        Drawable drawable1 = arr_drawable1[v3];
                        arr_drawable1[v3] = arr_drawable1[v12];
                        arr_drawable1[v12] = drawable1;
                    }
                }
            }
            this.setKeepScreenOn(true);
            this.j();
        }

        private float a(float f, float f1) {
            float f2 = f - this.A;
            float f3 = f1 - this.B;
            float f4 = (((float)(Math.atan2(f3, f2) * 180.0 / 3.141593)) + 360.0f) % 90.0f;
            if(f2 > 0.0f) {
                return f3 > 0.0f ? f4 : f4 + 270.0f;
            }
            return f3 > 0.0f ? f4 + 90.0f : f4 + 180.0f;
        }

        private void b(Canvas canvas0) {
            RectF rectF1;
            RectF rectF0;
            int v = this.R;
            float f = this.K;
            float f1 = this.T;
            if(f1 < 1.0f) {
                float f2 = 160.0f - f1 * 160.0f;
                canvas0.save();
                if(!this.e()) {
                    f2 = -f2;
                }
                canvas0.rotate(f2, this.A, this.B);
                canvas0.drawArc(this.M, f, 160.0f, false, y2.this.E);
                if(this.D != null) {
                    canvas0.drawArc(this.N, f, 160.0f, false, y2.this.E);
                }
            }
            else if(v < 0) {
                canvas0.drawArc(this.M, f, 160.0f, false, y2.this.E);
                if(this.D != null) {
                    canvas0.drawArc(this.N, f, 160.0f, false, y2.this.E);
                }
            }
            else if(this.D == null) {
                for(int v2 = 0; v2 < this.E; ++v2) {
                    canvas0.drawArc(this.M, f + ((float)v2) * this.L, this.L, false, ((v & 0xFFFF) == v2 ? y2.this.F : y2.this.E));
                }
            }
            else {
                if(v >>> 16 == 0) {
                    rectF0 = this.N;
                    rectF1 = this.M;
                }
                else {
                    rectF0 = this.M;
                    rectF1 = this.N;
                }
                canvas0.drawArc(rectF0, f, 160.0f, false, y2.this.E);
                for(int v3 = 0; v3 < this.E; ++v3) {
                    canvas0.drawArc(rectF1, f + ((float)v3) * this.L, this.L, false, ((v & 0xFFFF) == v3 ? y2.this.F : y2.this.E));
                }
            }
            for(int v4 = 0; v4 <= this.E; ++v4) {
                Point point0 = this.g(this.L * ((float)v4) + f, this.H);
                int v5 = point0.x;
                int v6 = point0.y;
                Point point1 = this.g(this.L * ((float)v4) + f, this.J);
                canvas0.drawLine(((float)v5), ((float)v6), ((float)point1.x), ((float)point1.y), y2.this.G);
            }
            for(int v1 = 0; v1 < this.E; ++v1) {
                Drawable drawable0 = this.C[v1];
                if(drawable0 != null) {
                    int v7 = this.O[v1 * 2];
                    int v8 = this.O[v1 * 2 + 1];
                    drawable0.setBounds(v7 - this.F, v8 - this.F, v7 + this.F, v8 + this.F);
                    drawable0.draw(canvas0);
                }
                Drawable drawable1 = this.D == null ? null : this.D[v1];
                if(drawable1 != null) {
                    int v9 = this.P[v1 * 2];
                    int v10 = this.P[v1 * 2 + 1];
                    drawable1.setBounds(v9 - this.G, v10 - this.G, v9 + this.G, v10 + this.G);
                    drawable1.draw(canvas0);
                }
            }
            if(this.T < 1.0f) {
                canvas0.restore();
            }
        }

        private void c() {
            Drawable[] arr_drawable = this.C;
            if(arr_drawable != null) {
                for(int v1 = 0; v1 < arr_drawable.length; ++v1) {
                    Drawable drawable0 = arr_drawable[v1];
                    if(drawable0 instanceof g) {
                        ((g)drawable0).c();
                    }
                }
            }
            Drawable[] arr_drawable1 = this.D;
            if(arr_drawable1 != null) {
                for(int v = 0; v < arr_drawable1.length; ++v) {
                    Drawable drawable1 = arr_drawable1[v];
                    if(drawable1 instanceof g) {
                        ((g)drawable1).c();
                    }
                }
            }
        }

        public void d() {
            this.S = true;
        }

        public boolean e() {
            return this.z == 1 || this.z == 2;
        }

        public void f() {
            if(!this.S) {
                int v = this.R;
                if(v >= 0 && this.T >= 1.0f) {
                    int v1 = this.e() ? this.E - 1 - (0xFFFF & v) : 0xFFFF & v;
                    if(v >>> 16 != 0) {
                        v1 += 6;
                    }
                    a.b b0 = y2.this.C.l(this.z, v1);
                    if(b0.z != 0 && b0.z != 1) {
                        y2.this.D.u3(b0);
                    }
                }
            }
        }

        private Point g(float f, int v) {
            this.Q.x = (int)(((double)this.A) + Math.cos(f * 0.017453f) * ((double)v) + 0.5);
            this.Q.y = (int)(((double)this.B) + Math.sin(f * 0.017453f) * ((double)v) + 0.5);
            return this.Q;
        }

        private int h(int v) {
            int v1 = Color.alpha(v);
            int v2 = Color.red(v);
            int v3 = Color.green(v);
            int v4 = Color.blue(v);
            if(v2 + v3 + v4 >= 0x180) {
                int v5 = v2 - 0x30 >= 0 ? v2 - 0x30 : 0;
                int v6 = v3 - 0x30 >= 0 ? v3 - 0x30 : 0;
                return v4 - 0x30 < 0 ? Color.argb(v1, v5, v6, 0) : Color.argb(v1, v5, v6, v4 - 0x30);
            }
            return Color.argb(v1, (v2 + 0x30 <= 0xFF ? v2 + 0x30 : 0xFF), (v3 + 0x30 <= 0xFF ? v3 + 0x30 : 0xFF), (v4 + 0x30 <= 0xFF ? v4 + 0x30 : 0xFF));
        }

        void i(float f) {
            this.T = f;
            this.invalidate();
        }

        private void j() {
            this.T = 0.0f;
            this.c();
            ObjectAnimator objectAnimator0 = ObjectAnimator.ofFloat(this, y2.J, new float[]{0.0f, 1.0f});
            objectAnimator0.setDuration(100L);
            objectAnimator0.start();
        }

        public boolean k(float f, float f1) {
            float f2 = f - this.A;
            float f3 = f1 - this.B;
            float f4 = f2 * f2 + f3 * f3;
            if(f4 >= ((float)(this.H * this.H)) && f4 <= ((float)(this.J * this.J))) {
                float f5 = this.a(f, f1);
                if(this.z == 0 && f5 >= 270.0f) {
                    f5 -= 360.0f;
                }
                for(int v = 0; v < this.E; ++v) {
                    float f6 = this.K + ((float)v) * this.L;
                    if(f5 > f6 && f5 < f6 + this.L) {
                        if(f4 > ((float)(this.I * this.I))) {
                            v += 0x10000;
                        }
                        if(this.R != v) {
                            this.R = v;
                            return true;
                        }
                        return false;
                    }
                }
            }
            if(this.R != -1) {
                this.R = -1;
                return true;
            }
            return false;
        }

        @Override  // android.view.View
        protected void onDraw(Canvas canvas0) {
            try {
                this.b(canvas0);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    private final Context A;
    private final Handler B;
    private final p C;
    private final w1 D;
    private final Paint E;
    private final Paint F;
    private final Paint G;
    private final Runnable H;
    private b I;
    private static final Property J;
    private final Context z;

    static {
        y2.J = new com.jozein.xedgepro.xposed.y2.a(Float.class, "setAnimateProgress");
    }

    y2(p p0, w1 w10, Context context0, Handler handler0) {
        this.H = () -> {
            b y2$b0 = this.I;
            this.I = null;
            if(y2$b0 != null) {
                y2$b0.d();
                t3.n().t(6);
            }
        };
        this.I = null;
        this.C = p0;
        this.D = w10;
        this.B = handler0;
        this.z = context0;
        this.A = p2.d(context0);
        Paint paint0 = new Paint(1);
        this.E = paint0;
        paint0.setStyle(Paint.Style.STROKE);
        this.F = new Paint(paint0);
        Paint paint1 = new Paint(1);
        this.G = paint1;
        paint1.setStyle(Paint.Style.STROKE);
        paint1.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.DST_OUT));
    }

    @Override  // com.jozein.xedgepro.xposed.b0$a
    public void b(float f, float f1, long v) {
        v2 v20 = () -> {
            b y2$b0 = this.I;
            if(y2$b0 != null) {
                y2$b0.k(f, f1);
                y2$b0.f();
            }
            this.q();
        };
        this.B.post(v20);
    }

    @Override  // com.jozein.xedgepro.xposed.b0$a
    public void c() {
        this.B.post(this.H);
    }

    @Override  // com.jozein.xedgepro.xposed.b0$a
    public void d(float f, float f1, long v) {
        w2 w20 = () -> {
            b y2$b0 = this.I;
            if(y2$b0 != null && y2$b0.k(f, f1)) {
                y2$b0.invalidate();
            }
        };
        this.B.post(w20);
    }

    @Override  // com.jozein.xedgepro.xposed.b0$a
    public void e(int v, float f, float f1, long v1) {
        x2 x20 = () -> {
            int v1 = this.C.v(4, -14509620);
            int v2 = this.C.v(30, 0);
            int v3 = -1;
            if(v2 == -1) {
                v2 = 0;
            }
            h h0 = h.a();
            Drawable[] arr_drawable = new Drawable[6];
            Drawable[] arr_drawable1 = new Drawable[6];
            int v4 = -1;
            for(int v5 = 0; v5 < 6; ++v5) {
                a.b b0 = this.C.l(v, v5);
                if(b0.z != 0 && b0.z != 1) {
                    Drawable drawable0 = b0.m(this.A);
                    h0.e(drawable0, v2);
                    arr_drawable[v5] = drawable0;
                    v4 = v5;
                }
                a.b b1 = this.C.l(v, v5 + 6);
                if(b1.z != 0 && b1.z != 1) {
                    Drawable drawable1 = b1.m(this.A);
                    h0.e(drawable1, v2);
                    arr_drawable1[v5] = drawable1;
                    v3 = v5;
                }
            }
            if(v3 < 0) {
                arr_drawable1 = null;
            }
            int v6 = Math.max(v4 + 1, v3 + 1);
            this.I = new b(this, v, f, f1, arr_drawable, arr_drawable1, (v6 >= 1 ? v6 : 1), v1);
            t3.n().j(this.I, 6);
        };
        this.B.post(x20);
    }

    void p() {
        if(this.I != null) {
            this.c();
        }
    }

    // 检测为 Lambda 实现
    private void q() [...]

    // 检测为 Lambda 实现
    private void r(float f, float f1) [...]

    // 检测为 Lambda 实现
    private void s(int v, float f, float f1) [...]

    // 检测为 Lambda 实现
    private void t(float f, float f1) [...]
}


package f;

import android.content.Context;
import java.text.DateFormatSymbols;
import java.util.Calendar;

public class j {
    public static class a {
        public final long a;
        public final int b;
        public final int c;
        public final int d;
        public final int e;
        public final int f;
        public final int g;

        public a(long v) {
            long v1 = v / 60000L * 60000L;
            this.a = v1;
            j j0 = new j(v1);
            this.b = j0.q();
            this.c = j0.i();
            this.d = j0.c();
            this.e = j0.d();
            this.f = j0.g();
            this.g = j0.h();
        }

        @Override
        public String toString() {
            return "TimePoint { " + this.b + ", " + this.c + "+1/" + this.d + "+1, " + this.e + ", " + this.f + ":" + this.g + " }";
        }
    }

    private final Calendar a;
    private static final int[] b;

    static {
        j.b = new int[]{0x1F, 28, 0x1F, 30, 0x1F, 30, 0x1F, 0x1F, 30, 0x1F, 30, 0x1F};
    }

    public j() {
        this.a = Calendar.getInstance();
    }

    public j(long v) {
        Calendar calendar0 = Calendar.getInstance();
        this.a = calendar0;
        calendar0.setTimeInMillis(v);
    }

    public int a() {
        return this.a.getActualMaximum(5);
    }

    public static int b(int v, int v1) {
        if(v1 != 1) {
            return j.b[v1];
        }
        return j.r(v) ? 29 : 28;
    }

    public int c() {
        return this.a.get(5) - 1;
    }

    public int d() {
        return this.a.get(7) - 1;
    }

    public static String[] e(Context context0) {
        return new String[]{"1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "Last"};
    }

    public static String f(Context context0, int v) {
        if(v == 0) {
            return "-";
        }
        StringBuilder stringBuilder0 = new StringBuilder();
        String[] arr_s = j.e(context0);
        for(int v1 = 0; v1 < arr_s.length; ++v1) {
            if((1 << v1 & v) != 0) {
                if(stringBuilder0.length() > 0) {
                    stringBuilder0.append(' ');
                }
                stringBuilder0.append(arr_s[v1]);
            }
        }
        return stringBuilder0.toString();
    }

    public int g() {
        return this.a.get(11);
    }

    public int h() {
        return this.a.get(12);
    }

    public int i() {
        return this.a.get(2);
    }

    public static String[] j() {
        return new DateFormatSymbols().getMonths();
    }

    public static String k(int v) {
        if(v == 0) {
            return "-";
        }
        StringBuilder stringBuilder0 = new StringBuilder();
        String[] arr_s = j.m();
        for(int v1 = 0; v1 < arr_s.length; ++v1) {
            if((1 << v1 & v) != 0) {
                if(stringBuilder0.length() > 0) {
                    stringBuilder0.append(' ');
                }
                stringBuilder0.append(arr_s[v1]);
            }
        }
        return stringBuilder0.toString();
    }

    public static long l() {
        Calendar calendar0 = Calendar.getInstance();
        int v = calendar0.get(1);
        for(int v1 = v + 1; v1 < v + 9; ++v1) {
            if(j.r(v1)) {
                calendar0.set(v1, 1, 29, 0, 0, 0);
                return calendar0.getTimeInMillis();
            }
        }
        return 0L;
    }

    public static String[] m() {
        return new DateFormatSymbols().getShortMonths();
    }

    public static String[] n() {
        String[] arr_s = new String[7];
        System.arraycopy(new DateFormatSymbols().getShortWeekdays(), 1, arr_s, 0, 7);
        return arr_s;
    }

    public static String[] o() {
        String[] arr_s = new String[7];
        System.arraycopy(new DateFormatSymbols().getWeekdays(), 1, arr_s, 0, 7);
        return arr_s;
    }

    public static String p(int v) {
        if(v == 0) {
            return "-";
        }
        StringBuilder stringBuilder0 = new StringBuilder();
        String[] arr_s = j.n();
        for(int v1 = 0; v1 < arr_s.length; ++v1) {
            if((1 << v1 & v) != 0) {
                if(stringBuilder0.length() > 0) {
                    stringBuilder0.append(' ');
                }
                stringBuilder0.append(arr_s[v1]);
            }
        }
        return stringBuilder0.toString();
    }

    public int q() {
        return this.a.get(1);
    }

    public static boolean r(int v) {
        return v % 100 == 0 ? v % 400 == 0 : v % 4 == 0;
    }
}


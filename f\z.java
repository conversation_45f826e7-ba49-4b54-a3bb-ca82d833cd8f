package f;

import android.app.Activity;
import android.app.Application.ActivityLifecycleCallbacks;
import android.app.Application;
import android.app.Service;
import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProviderInfo;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager.NameNotFoundException;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.Handler;
import android.os.Parcelable;
import android.os.SystemClock;
import com.jozein.xedgepro.ApplicationMain;
import com.jozein.xedgepro.ui.ActivityPerformAction;
import java.text.Collator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;

public abstract class z {
    static class a {
    }

    static class b extends z {
        private final boolean h;
        private int i;
        private final List j;
        private int[] k;
        private List l;
        private List m;

        b(Context context0) {
            super(context0);
            boolean z = false;
            this.i = 0;
            this.j = new ArrayList();
            if(Build.VERSION.SDK_INT < 30 || p0.q(context0, l.j, "android.permission.QUERY_ALL_PACKAGES")) {
                z = true;
            }
            this.h = z;
            this.F0(context0);
        }

        private void A0(g z$g0) {
            int v = z$g0.c();
            switch(z$g0.b) {
                case -4: {
                    this.B0(z$g0.d, v);
                    this.b0(z$g0.d, v);
                    return;
                }
                case -3: {
                    PackageInfo packageInfo0 = z$g0.b();
                    this.C0(packageInfo0, v);
                    this.a0(packageInfo0, v);
                    return;
                }
                case -2: {
                    PackageInfo packageInfo1 = z$g0.b();
                    this.C0(packageInfo1, v);
                    this.Z(packageInfo1, v);
                }
            }
        }

        void B0(String s, int v) {
            int[] arr_v;
            List list0;
            synchronized(this) {
                list0 = this.l;
                arr_v = this.k;
            }
            if(list0 != null) {
                int v1 = z.j(list0, s, v);
                if(v1 >= 0) {
                    ArrayList arrayList0 = new ArrayList(list0);
                    arrayList0.remove(v1);
                    synchronized(this) {
                        this.l = arrayList0;
                        this.m = arrayList0;
                        this.k = arr_v;
                    }
                }
            }
        }

        void C0(PackageInfo packageInfo0, int v) {
            int[] arr_v;
            List list0;
            synchronized(this) {
                list0 = this.l;
                arr_v = this.k;
            }
            if(list0 != null) {
                ArrayList arrayList0 = new ArrayList(list0);
                d c0$d0 = new d(packageInfo0);
                int v1 = z.j(arrayList0, packageInfo0.packageName, v);
                if(v1 >= 0) {
                    arrayList0.set(v1, c0$d0);
                }
                else {
                    arrayList0.add(~v1, c0$d0);
                }
                synchronized(this) {
                    this.l = arrayList0;
                    this.m = arrayList0;
                    this.k = arr_v;
                }
            }
        }

        void D0(Intent intent0) {
            g z$g0 = g.e(intent0);
            if(z$g0 != null) {
                if(z$g0.b > 0) {
                    int v = z$g0.a;
                    if(v == 0) {
                        this.E0(z$g0);
                        return;
                    }
                    h z$h0 = this.G0(v);
                    if(z$h0 != null) {
                        z$h0.a(z$g0);
                    }
                }
                else {
                    this.A0(z$g0);
                }
            }
        }

        private void E0(g z$g0) {
            try {
                List list0 = z$g0.a();
                if(list0 != null) {
                    List list1 = z.o0(list0);
                    Collections.sort(list1, z.f);
                    synchronized(this) {
                        this.l = list1;
                        this.m = list1;
                        this.k = z$g0.c;
                    }
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        private void F0(Context context0) {
            class f.z.b.a implements Application.ActivityLifecycleCallbacks {
                private boolean a;
                final b b;

                f.z.b.a() {
                    this.a = false;
                }

                private boolean b(Activity activity0) {
                    return activity0 != null && activity0.getClass() != ActivityPerformAction.class;
                }

                // 检测为 Lambda 实现
                private void c() [...]

                @Override  // android.app.Application$ActivityLifecycleCallbacks
                public void onActivityCreated(Activity activity0, Bundle bundle0) {
                    if(this.b(activity0) && bundle0 == null) {
                        if(b.this.i <= 0) {
                            b.this.i = 1;
                            this.a = true;
                            return;
                        }
                        b.s0(b.this);
                    }
                }

                @Override  // android.app.Application$ActivityLifecycleCallbacks
                public void onActivityDestroyed(Activity activity0) {
                    if(this.b(activity0) && activity0.isFinishing()) {
                        if(b.this.i <= 1) {
                            b.this.i = 0;
                            this.a = false;
                            b.this.w0();
                            return;
                        }
                        b.t0(b.this);
                    }
                }

                @Override  // android.app.Application$ActivityLifecycleCallbacks
                public void onActivityPaused(Activity activity0) {
                }

                @Override  // android.app.Application$ActivityLifecycleCallbacks
                public void onActivityResumed(Activity activity0) {
                }

                @Override  // android.app.Application$ActivityLifecycleCallbacks
                public void onActivitySaveInstanceState(Activity activity0, Bundle bundle0) {
                }

                @Override  // android.app.Application$ActivityLifecycleCallbacks
                public void onActivityStarted(Activity activity0) {
                    if(this.a) {
                        this.a = false;
                        h0.a().post(() -> if(b.this.z0()) {
                            b.this.L0();
                        });
                    }
                }

                @Override  // android.app.Application$ActivityLifecycleCallbacks
                public void onActivityStopped(Activity activity0) {
                }
            }


            class f.z.b.b extends BroadcastReceiver {
                final b a;

                @Override  // android.content.BroadcastReceiver
                public void onReceive(Context context0, Intent intent0) {
                    ApplicationMain.g();
                    try {
                        b.this.D0(intent0);
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }

            Application application0;
            if(context0 instanceof Application) {
                application0 = (Application)context0;
            }
            else if(context0 instanceof Activity) {
                application0 = ((Activity)context0).getApplication();
            }
            else if(context0 instanceof Service) {
                application0 = ((Service)context0).getApplication();
            }
            else {
                application0 = (Application)context0.getApplicationContext();
            }
            application0.registerActivityLifecycleCallbacks(new f.z.b.a(this));
            application0.registerReceiver(new f.z.b.b(this), new IntentFilter(g.f), null, this.x0());
        }

        private h G0(int v) {
            synchronized(this.j) {
                int v2 = this.j.size();
                for(int v3 = 0; v3 < v2; ++v3) {
                    h z$h0 = (h)this.j.get(v3);
                    if(z$h0.a == v) {
                        this.j.remove(v3);
                        return z$h0;
                    }
                }
                return null;
            }
        }

        private void H0(h z$h0) {
            synchronized(this.j) {
                this.j.remove(z$h0);
            }
        }

        private g I0(int v, int[] arr_v) {
            g z$g0 = null;
            if(h0.c(this.x0())) {
                v.d(new UnsupportedOperationException("Waiting on ui thread!!!"));
                return null;
            }
            h z$h0 = new h(v, arr_v);
            this.u0(z$h0);
            try {
                z$g0 = z$h0.e(this.a);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            this.H0(z$h0);
            return z$g0;
        }

        @Override  // f.z
        d J(String s, int v) {
            return z.n(this.y0(v), s, v);
        }

        private List J0(int v, int[] arr_v) {
            g z$g0 = this.I0(v, arr_v);
            if(z$g0 != null) {
                try {
                    return z$g0.a();
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
            return null;
        }

        private void K0() {
            int[] arr_v = o0.e(this.a);
            new h(0, (this.v0(arr_v) ? 1 : 2), arr_v).d(this.a);
        }

        void L0() {
            this.K0();
        }

        @Override  // f.z
        List S(boolean z, boolean z1) {
            __monitor_enter(this);
            List list0 = this.l;
            int[] arr_v = this.k;
            __monitor_exit(this);
            int[] arr_v1 = o0.e(this.a);
            if(list0 == null || !z.W(arr_v, arr_v1)) {
                try {
                    List list1 = this.J0(5, arr_v1);
                    if(list1 != null) {
                        list0 = z.o0(list1);
                        Collections.sort(list0, z.f);
                        synchronized(this) {
                            this.l = list0;
                            this.m = list0;
                            this.k = arr_v1;
                        }
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
            if(list0 == null) {
                list0 = z.o0(this.b.getInstalledPackages((z1 ? 0x8201 : 0)));
            }
            List list2 = z.l(list0, z, z1);
            Collections.sort(list2, new f.z.d(Locale.getDefault()));
            return list2;
        }

        @Override  // f.z
        List d0() {
            if(!this.h) {
                List list0 = this.J0(8, null);
                return list0 == null ? super.d0() : list0;
            }
            return super.d0();
        }

        @Override  // f.z
        List g0(int[] arr_v) {
            if(!this.v0(arr_v)) {
                List list0 = this.J0(6, arr_v);
                return list0 == null ? super.g0(arr_v) : list0;
            }
            return super.g0(arr_v);
        }

        @Override  // f.z
        List h0(int v, int[] arr_v) {
            if(!this.v0(arr_v)) {
                List list0 = this.J0(((v & 1) == 0 ? 4 : 5), arr_v);
                return list0 == null ? super.h0(v, arr_v) : list0;
            }
            return super.h0(v, arr_v);
        }

        @Override  // f.z
        List i0() {
            if(!this.h) {
                List list0 = this.J0(7, null);
                return list0 == null ? super.i0() : list0;
            }
            return super.i0();
        }

        static int s0(b z$b0) {
            int v = z$b0.i + 1;
            z$b0.i = v;
            return v;
        }

        static int t0(b z$b0) {
            int v = z$b0.i - 1;
            z$b0.i = v;
            return v;
        }

        private void u0(h z$h0) {
            synchronized(this.j) {
                this.j.add(z$h0);
            }
        }

        // 去混淆评级： 低(20)
        private boolean v0(int[] arr_v) {
            return this.h && (arr_v == null || arr_v.length == 0 || arr_v.length == 1 && arr_v[0] == 0);
        }

        void w0() {
            synchronized(this) {
                this.l = null;
                this.k = null;
            }
            new h(0, 3, null).d(this.a);
        }

        Handler x0() {
            return h0.a();
        }

        private List y0(int v) {
            if(this.h && v == 0) {
                return null;
            }
            List list0 = this.m;
            if(list0 == null) {
                if(!this.z0()) {
                    this.K0();
                }
                return null;
            }
            return list0;
        }

        boolean z0() {
            return this.i > 0;
        }
    }

    static class c extends z {
        c(Context context0) {
            super(context0);
        }
    }

    static final class f.z.d implements Comparator {
        private final Collator A;
        final Locale z;

        f.z.d(Locale locale0) {
            this.z = locale0;
            this.A = "en".equals(locale0.getLanguage()) ? null : Collator.getInstance(locale0);
        }

        public int a(f.c0.b c0$b0, f.c0.b c0$b1) {
            int v = c0$b0.m();
            int v1 = c0$b1.m();
            if(v != v1) {
                return v - v1;
            }
            int v2 = this.A == null ? c0$b0.g(this.z).compareToIgnoreCase(c0$b1.g(this.z)) : c0$b0.c(this.z, this.A).compareTo(c0$b1.c(this.z, this.A));
            return v2 == 0 ? c0$b0.k() - c0$b1.k() : v2;
        }

        @Override
        public int compare(Object object0, Object object1) {
            return this.a(((f.c0.b)object0), ((f.c0.b)object1));
        }
    }

    public interface e {
        void a(d arg1);

        void b(String arg1, int arg2);

        void c(d arg1);
    }

    static final class f implements Comparator {
        private f() {
        }

        f(a z$a0) {
        }

        public int a(f.c0.b c0$b0, f.c0.b c0$b1) {
            int v = c0$b0.i().compareTo(c0$b1.i());
            return v == 0 ? c0$b0.k() - c0$b1.k() : v;
        }

        public int b(f.c0.b c0$b0, String s, int v) {
            int v1 = c0$b0.i().compareTo(s);
            return v1 == 0 ? c0$b0.k() - v : v1;
        }

        @Override
        public int compare(Object object0, Object object1) {
            return this.a(((f.c0.b)object0), ((f.c0.b)object1));
        }
    }

    static final class g {
        final int a;
        final int b;
        final int[] c;
        final String d;
        private final Parcelable e;
        static final String f;

        static {
            g.f = l.k + "REPLY_PACKAGES";
        }

        private g(int v, int v1, int[] arr_v, String s, Parcelable parcelable0) {
            this.a = v;
            this.b = v1;
            this.c = arr_v;
            this.d = s;
            this.e = parcelable0;
        }

        g(int v, PackageInfo packageInfo0, String s, int v1) {
            this(v1, v, null, s, packageInfo0);
        }

        g(h z$h0, List list0) {
            Parcelable parcelable0 = c0.u(list0);
            this(z$h0.a, z$h0.b, z$h0.c, null, parcelable0);
        }

        List a() {
            return this.e == null ? null : c0.h(this.e);
        }

        PackageInfo b() {
            return (PackageInfo)this.e;
        }

        int c() {
            return this.a;
        }

        static String d(int v) {
            switch(v) {
                case -4: {
                    return "PACKAGE_REMOVED";
                }
                case -3: {
                    return "PACKAGE_CHANGED";
                }
                case -2: {
                    return "PACKAGE_ADDED";
                }
                default: {
                    return h.b(v);
                }
            }
        }

        static g e(Intent intent0) {
            if(intent0 != null) {
                String s = intent0.getAction();
                return g.f.equals(s) ? new g(intent0.getIntExtra("id", 0), intent0.getIntExtra("option", 0), intent0.getIntArrayExtra("users"), intent0.getStringExtra("package"), intent0.getParcelableExtra("data")) : null;
            }
            return null;
        }

        void f(Context context0) {
            context0.sendBroadcast(new Intent(g.f).setPackage(l.j).putExtra("id", this.a).putExtra("option", this.b).putExtra("users", this.c).putExtra("package", this.d).putExtra("data", this.e));
        }

        @Override
        public String toString() {
            String s;
            StringBuilder stringBuilder0;
            if(this.b > 0) {
                stringBuilder0 = new StringBuilder();
                stringBuilder0.append("ReplyData{ id=");
                stringBuilder0.append(this.a);
                stringBuilder0.append(" option=");
                stringBuilder0.append(g.d(this.b));
                stringBuilder0.append(", ");
                stringBuilder0.append(this.e);
                s = "}";
            }
            else {
                stringBuilder0 = new StringBuilder();
                stringBuilder0.append("ReplyData{ ");
                stringBuilder0.append(g.d(this.b));
                stringBuilder0.append(", ");
                stringBuilder0.append(this.d);
                stringBuilder0.append("(");
                stringBuilder0.append(this.a);
                s = ")}";
            }
            stringBuilder0.append(s);
            return stringBuilder0.toString();
        }
    }

    static final class h {
        final int a;
        final int b;
        final int[] c;
        private volatile g d;
        static final String e;
        private static final AtomicInteger f;

        static {
            h.e = l.k + "REQUEST_PACKAGES";
            h.f = new AtomicInteger(1);
        }

        h(int v, int v1, int[] arr_v) {
            this.a = v;
            this.b = v1;
            this.c = arr_v;
        }

        h(int v, int[] arr_v) {
            this.a = h.f.getAndIncrement();
            this.b = v;
            this.c = arr_v;
        }

        void a(g z$g0) {
            synchronized(this) {
                this.d = z$g0;
                this.notifyAll();
            }
        }

        static String b(int v) {
            switch(v) {
                case 0: {
                    return "INVALID_OPTION";
                }
                case 1: {
                    return "REGISTER_SYNC";
                }
                case 2: {
                    return "REGISTER_SYNC_AND_GET";
                }
                case 3: {
                    return "UNREGISTER_SYN";
                }
                case 4: {
                    return "GET_PACKAGES";
                }
                case 5: {
                    return "GET_PACKAGES_WITH_ACTIVITIES";
                }
                case 6: {
                    return "GET_LAUNCHER_ACTIVITIES";
                }
                case 7: {
                    return "GET_SHORTCUTS";
                }
                case 8: {
                    return "GET_APP_WIDGET_PROVIDERS";
                }
                default: {
                    return Integer.toString(v);
                }
            }
        }

        static h c(Intent intent0) {
            String s = intent0.getAction();
            return h.e.equals(s) ? new h(intent0.getIntExtra("id", 0), intent0.getIntExtra("option", 0), intent0.getIntArrayExtra("users")) : null;
        }

        void d(Context context0) {
            context0.sendBroadcast(new Intent(h.e).setPackage("android").putExtra("id", this.a).putExtra("option", this.b).putExtra("users", this.c));
        }

        g e(Context context0) {
            this.d(context0);
            synchronized(this) {
                long v1 = SystemClock.uptimeMillis();
                while(this.d == null) {
                    long v2 = v1 + 6000L - SystemClock.uptimeMillis();
                    if(v2 <= 0L) {
                        return null;
                    }
                    try {
                        this.wait(v2);
                    }
                    catch(InterruptedException unused_ex) {
                    }
                }
                return this.d;
            }
        }

        @Override
        public boolean equals(Object object0) {
            if(this == object0) {
                return true;
            }
            if(object0 != null) {
                Class class0 = object0.getClass();
                return h.class == class0 && this.a == ((h)object0).a;
            }
            return false;
        }

        @Override
        public int hashCode() {
            return this.a;
        }

        @Override
        public String toString() {
            return "RequestData{id=" + this.a + ", option=" + h.b(this.b) + ", userIds=" + Arrays.toString(this.c) + '}';
        }
    }

    static class i extends z {
        private boolean h;
        private BroadcastReceiver i;
        private Handler j;
        private int[] k;
        private Locale l;
        private List m;
        private long n;

        i(Context context0) {
            super(context0);
            this.h = false;
            this.n = 0L;
            this.z0();
        }

        void A0() {
            class f.z.i.b extends BroadcastReceiver {
                final i a;

                @Override  // android.content.BroadcastReceiver
                public void onReceive(Context context0, Intent intent0) {
                    try {
                        Uri uri0 = intent0.getData();
                        if(uri0 == null) {
                            return;
                        }
                        String s = uri0.getSchemeSpecificPart();
                        int v = o0.h(intent0.getIntExtra("android.intent.extra.UID", 0));
                        String s1 = intent0.getAction();
                        i.this.u0(s1, s, v);
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }

            if(!z.e && this.i == null) {
                this.i = new f.z.i.b(this);
                IntentFilter intentFilter0 = new IntentFilter();
                intentFilter0.addAction("android.intent.action.PACKAGE_ADDED");
                intentFilter0.addAction("android.intent.action.PACKAGE_REMOVED");
                intentFilter0.addAction("android.intent.action.PACKAGE_CHANGED");
                intentFilter0.addDataScheme("package");
                BroadcastReceiver broadcastReceiver0 = this.i;
                Handler handler0 = this.s0();
                c0.w(this.a, broadcastReceiver0, intentFilter0, handler0);
            }
        }

        void B0(h z$h0) {
            List list0;
            switch(z$h0.b) {
                case 4: {
                    list0 = this.h0(0, z$h0.c);
                    break;
                }
                case 2: 
                case 5: {
                    list0 = this.h0(0x8201, z$h0.c);
                    break;
                }
                case 6: {
                    list0 = this.g0(z$h0.c);
                    break;
                }
                case 7: {
                    list0 = this.i0();
                    break;
                }
                case 8: {
                    list0 = this.d0();
                    break;
                }
                default: {
                    v.c(("Unknown option: " + z$h0.b));
                    return;
                }
            }
            new g(z$h0, list0).f(this.a);
        }

        // 去混淆评级： 低(20)
        @Override  // f.z
        List O(f.c0.c c0$c0, int v) {
            return h0.c(this.s0()) ? this.t0(c0$c0, v) : super.O(c0$c0, v);
        }

        @Override  // f.z
        void c0(String s, String s1, int v) {
            this.s0().post(() -> this.u0(s, s1, v));
        }

        Handler s0() {
            if(this.j == null) {
                this.j = h0.b();
            }
            return this.j;
        }

        List t0(f.c0.c c0$c0, int v) {
            long v2;
            this.A0();
            Locale locale0 = Locale.getDefault();
            int[] arr_v = o0.e(this.a);
            long v1 = SystemClock.uptimeMillis();
            List list0 = null;
            if(this.m != null) {
                if(v1 > this.n) {
                    this.m = null;
                }
                else if(z.W(arr_v, this.k)) {
                    list0 = this.m;
                }
                else {
                    this.m = null;
                }
            }
            if(list0 == null) {
                list0 = z.p0(this.g0(arr_v));
                v2 = arr_v == null || (this.k == null || arr_v.length > this.k.length) ? 60000L : 7200000L;
            }
            else {
                v2 = 0L;
            }
            if(this.m == null || !locale0.equals(this.l)) {
                Collections.sort(list0, new f.z.d(locale0));
            }
            this.k = arr_v;
            this.l = locale0;
            this.m = list0;
            if(v2 > 0L) {
                this.n = v2 + SystemClock.uptimeMillis();
            }
            return z.m(list0, c0$c0, v);
        }

        void u0(String s, String s1, int v) {
            if(s != null && s1 != null) {
                try {
                    if(o0.i(this.a, v)) {
                        PackageInfo packageInfo0 = null;
                        int v1 = 0;
                        switch(s) {
                            case "android.intent.action.PACKAGE_ADDED": {
                                packageInfo0 = c0.i(s1, 0x8201, v);
                                this.x0(packageInfo0, v);
                                this.Z(packageInfo0, v);
                                if(v == 0) {
                                    String s2 = l.j;
                                    if(s2.equals(s1)) {
                                        v.c(("<<< " + s2 + " reinstalled >>>"));
                                    }
                                    else {
                                        v1 = -2;
                                    }
                                }
                                else {
                                    v1 = -2;
                                }
                                break;
                            }
                            case "android.intent.action.PACKAGE_CHANGED": {
                                v1 = -3;
                                packageInfo0 = c0.i(s1, 0x8201, v);
                                this.x0(packageInfo0, v);
                                this.a0(packageInfo0, v);
                                break;
                            }
                            case "android.intent.action.PACKAGE_REMOVED": {
                                v1 = -4;
                                this.w0(s1, v);
                                this.b0(s1, v);
                            }
                        }
                        if(this.h && v1 != 0) {
                            new g(v1, packageInfo0, s1, v).f(this.a);
                        }
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        // 检测为 Lambda 实现
        private void v0(String s, String s1, int v) [...]

        void w0(String s, int v) {
            List list0 = this.m;
            if(list0 != null) {
                this.m = z.j0(list0, s, v);
            }
        }

        void x0(PackageInfo packageInfo0, int v) {
            List list0 = this.m;
            if(list0 != null) {
                this.m = z.j0(list0, packageInfo0.packageName, v);
                List list1 = this.f0(c0.a().setPackage(packageInfo0.packageName), 0x8200, v);
                if(list1 != null && list1.size() > 0) {
                    List list2 = z.p0(list1);
                    this.m.addAll(list2);
                    this.l = null;
                }
            }
        }

        private void y0(Intent intent0) {
            this.A0();
            h z$h0 = h.c(intent0);
            if(z$h0 != null) {
                switch(z$h0.b) {
                    case 1: {
                        this.h = true;
                        return;
                    }
                    case 2: {
                        this.h = true;
                        break;
                    }
                    case 3: {
                        this.h = false;
                        return;
                    }
                }
                this.B0(z$h0);
            }
        }

        private void z0() {
            class f.z.i.a extends BroadcastReceiver {
                final i a;

                @Override  // android.content.BroadcastReceiver
                public void onReceive(Context context0, Intent intent0) {
                    try {
                        i.this.y0(intent0);
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }

            f.z.i.a z$i$a0 = new f.z.i.a(this);
            IntentFilter intentFilter0 = new IntentFilter(h.e);
            Handler handler0 = this.s0();
            this.a.registerReceiver(z$i$a0, intentFilter0, null, handler0);
        }
    }

    final Context a;
    final PackageManager b;
    private e c;
    private static volatile z d = null;
    private static boolean e = false;
    private static final f f;
    private static final Comparator g;

    static {
        z.f = new f(null);
        z.g = (ResolveInfo resolveInfo0, ResolveInfo resolveInfo1) -> {
            ActivityInfo activityInfo0 = resolveInfo0.activityInfo;
            ActivityInfo activityInfo1 = resolveInfo1.activityInfo;
            int v = activityInfo0.applicationInfo.uid - activityInfo1.applicationInfo.uid;
            if(v != 0) {
                return v;
            }
            int v1 = activityInfo0.name.compareTo(activityInfo1.name);
            return v1 == 0 ? resolveInfo0.activityInfo.packageName.compareTo(resolveInfo1.activityInfo.packageName) : v1;
        };
    }

    z(Context context0) {
        Context context1 = context0.getApplicationContext();
        if(context1 != null) {
            context0 = context1;
        }
        this.a = context0;
        this.b = context0.getPackageManager();
    }

    public final CharSequence A(String s, String s1, int v) {
        return this.w(null, s, s1, v);
    }

    public u B() {
        List list0 = this.d0();
        u u0 = new u();
        if(list0 != null) {
            for(Object object0: list0) {
                String s = ((AppWidgetProviderInfo)object0).provider.getPackageName();
                ArrayList arrayList0 = (ArrayList)u0.get(s);
                if(arrayList0 == null) {
                    arrayList0 = new ArrayList();
                    u0.put(s, arrayList0);
                }
                arrayList0.add(((AppWidgetProviderInfo)object0));
            }
        }
        return u0;
    }

    public final Drawable C(String s) {
        return this.D(s, o0.d());
    }

    public final Drawable D(String s, int v) {
        d c0$d0 = this.J(s, v);
        return c0$d0 == null ? c0.k(c0.n(c0.c(s, v)), v) : c0$d0.d();
    }

    public final ApplicationInfo E(String s) {
        return this.F(s, o0.d());
    }

    public final ApplicationInfo F(String s, int v) {
        d c0$d0 = this.J(s, v);
        return c0$d0 == null ? c0.c(s, v) : c0$d0.a;
    }

    public final CharSequence G(String s) {
        return this.H(s, o0.d());
    }

    public final CharSequence H(String s, int v) {
        d c0$d0 = this.J(s, v);
        return c0$d0 != null ? c0$d0.f() : c0.l(c0.q(c0.c(s, v)), v);
    }

    f.c0.a I(String s, String s1, int v) {
        d c0$d0 = this.J(s, v);
        if(c0$d0 != null) {
            f.c0.a[] arr_c0$a = c0$d0.q();
            for(int v1 = 0; v1 < arr_c0$a.length; ++v1) {
                f.c0.a c0$a0 = arr_c0$a[v1];
                if(c0$a0.r().equals(s1)) {
                    return c0$a0;
                }
            }
        }
        return null;
    }

    d J(String s, int v) {
        return null;
    }

    public static z K(Context context0) {
        i z$i0;
        if(z.d == null) {
            c0.z(context0.getPackageManager());
            synchronized(z.class) {
                if(z.d == null) {
                    if(o0.l()) {
                        z$i0 = new i(context0);
                    }
                    else if(o0.m() == 0) {
                        z$i0 = new b(context0);
                    }
                    else {
                        z$i0 = new c(context0);
                    }
                    z.d = z$i0;
                }
                return z.d;
            }
        }
        return z.d;
    }

    public static String[] L(List list0) {
        if(list0 == null) {
            return new String[0];
        }
        int v1 = list0.size();
        String[] arr_s = new String[v1];
        Locale locale0 = Locale.getDefault();
        for(int v = 0; v < v1; ++v) {
            arr_s[v] = ((f.c0.b)list0.get(v)).g(locale0);
        }
        return arr_s;
    }

    public final List M() {
        return this.O(null, 0);
    }

    public final List N(f.c0.c c0$c0, int v) {
        return this.O(c0$c0, v);
    }

    List O(f.c0.c c0$c0, int v) {
        List list0 = z.m(z.p0(this.g0(o0.e(this.a))), c0$c0, v);
        Collections.sort(list0, new f.z.d(Locale.getDefault()));
        return list0;
    }

    static PackageManager P() {
        return z.d.b;
    }

    public final List Q(boolean z) {
        return this.S(z, false);
    }

    public final List R() {
        return this.S(true, true);
    }

    List S(boolean z, boolean z1) {
        List list0 = z.l(z.o0(this.h0((z1 ? 0x8201 : 0), (z ? o0.e(this.a) : null))), z, z1);
        Collections.sort(list0, new f.z.d(Locale.getDefault()));
        return list0;
    }

    public List T() {
        List list0 = this.i0();
        int v = list0.size();
        List list1 = new ArrayList(v);
        for(int v1 = 0; v1 < v; ++v1) {
            list1.add(new f.c0.e(((ResolveInfo)list0.get(v1))));
        }
        Collections.sort(list1, new f.z.d(Locale.getDefault()));
        return list1;
    }

    public final boolean U(String s) {
        return this.V(s, o0.d());
    }

    public final boolean V(String s, int v) {
        if(this.J(s, v) != null) {
            return true;
        }
        try {
            c0.c(s, v);
            return true;
        }
        catch(PackageManager.NameNotFoundException unused_ex) {
            return false;
        }
    }

    static boolean W(int[] arr_v, int[] arr_v1) {
        return Arrays.equals(arr_v, arr_v1);
    }

    // 检测为 Lambda 实现
    private static boolean X(String s, int v, f.c0.e c0$e0) [...]

    // 检测为 Lambda 实现
    private static int Y(ResolveInfo resolveInfo0, ResolveInfo resolveInfo1) [...]

    final void Z(PackageInfo packageInfo0, int v) {
        if(this.c != null) {
            d c0$d0 = this.J(packageInfo0.packageName, v);
            if(c0$d0 == null) {
                c0$d0 = new d(packageInfo0);
            }
            try {
                this.c.c(c0$d0);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    final void a0(PackageInfo packageInfo0, int v) {
        if(this.c != null) {
            d c0$d0 = this.J(packageInfo0.packageName, v);
            if(c0$d0 == null) {
                c0$d0 = new d(packageInfo0);
            }
            try {
                this.c.a(c0$d0);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    final void b0(String s, int v) {
        e z$e0 = this.c;
        if(z$e0 != null) {
            try {
                z$e0.b(s, v);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    void c0(String s, String s1, int v) {
    }

    List d0() {
        return AppWidgetManager.getInstance(this.a).getInstalledProviders();
    }

    List e0(Intent intent0, int v, int[] arr_v) {
        List list1;
        if(arr_v != null) {
            switch(arr_v.length) {
                case 0: {
                    break;
                }
                case 1: {
                    list1 = this.f0(intent0, v, arr_v[0]);
                    return list1 == null || list1.size() <= 0 ? this.b.queryIntentActivities(intent0, v) : list1;
                }
                default: {
                    ArrayList arrayList0 = new ArrayList();
                    for(int v1 = 0; v1 < arr_v.length; ++v1) {
                        List list0 = this.f0(intent0, v, arr_v[v1]);
                        if(list0 != null) {
                            arrayList0.addAll(list0);
                        }
                    }
                    return arrayList0 == null || arrayList0.size() <= 0 ? this.b.queryIntentActivities(intent0, v) : arrayList0;
                }
            }
        }
        list1 = this.f0(intent0, v, 0);
        return list1 == null || list1.size() <= 0 ? this.b.queryIntentActivities(intent0, v) : list1;
    }

    List f0(Intent intent0, int v, int v1) {
        List list0 = c0.v(intent0, v, v1);
        if(list0 != null && list0.size() > 0 && v != 0) {
            List list1 = c0.v(intent0, 0, v1);
            if(list1 != null) {
                int v2 = list1.size();
                if(v2 == 0) {
                    list1 = null;
                }
                else if(v2 > 1) {
                    Collections.sort(list1, z.g);
                }
            }
            int v3 = list0.size();
            ArrayList arrayList0 = new ArrayList(v3);
            for(int v4 = 0; v4 < v3; ++v4) {
                ResolveInfo resolveInfo0 = (ResolveInfo)list0.get(v4);
                if(resolveInfo0.activityInfo.enabled) {
                    if(list1 == null) {
                        arrayList0.add(resolveInfo0);
                    }
                    else {
                        boolean z = Collections.binarySearch(list1, resolveInfo0, z.g) >= 0;
                        ApplicationInfo applicationInfo0 = resolveInfo0.activityInfo.applicationInfo;
                        if(z == applicationInfo0.enabled) {
                            arrayList0.add(resolveInfo0);
                        }
                        else if(z) {
                            applicationInfo0.enabled = true;
                            arrayList0.add(resolveInfo0);
                        }
                    }
                }
            }
            return arrayList0;
        }
        return list0;
    }

    List g0(int[] arr_v) {
        return this.e0(c0.a(), 0x8200, arr_v);
    }

    List h0(int v, int[] arr_v) {
        List list1;
        if(arr_v != null) {
            switch(arr_v.length) {
                case 0: {
                    break;
                }
                case 1: {
                    list1 = c0.e(v, arr_v[0]);
                    return list1 == null || list1.size() <= 0 ? this.b.getInstalledPackages(v) : list1;
                }
                default: {
                    ArrayList arrayList0 = new ArrayList();
                    for(int v1 = 0; v1 < arr_v.length; ++v1) {
                        List list0 = c0.e(v, arr_v[v1]);
                        if(list0 != null) {
                            arrayList0.addAll(list0);
                        }
                    }
                    return arrayList0 == null || arrayList0.size() <= 0 ? this.b.getInstalledPackages(v) : arrayList0;
                }
            }
        }
        list1 = c0.e(v, 0);
        return list1 == null || list1.size() <= 0 ? this.b.getInstalledPackages(v) : list1;
    }

    // 去混淆评级： 低(20)
    public static int i(List list0, f.c0.b c0$b0) {
        return list0.isEmpty() ? -1 : Collections.binarySearch(list0, c0$b0, new f.z.d(Locale.getDefault()));
    }

    List i0() {
        return this.e0(new Intent("android.intent.action.CREATE_SHORTCUT"), 0, null);
    }

    static int j(List list0, String s, int v) {
        int v1 = list0.size() - 1;
        int v2 = 0;
        while(v2 <= v1) {
            int v3 = v2 + v1 >>> 1;
            f.c0.b c0$b0 = (f.c0.b)list0.get(v3);
            int v4 = z.f.b(c0$b0, s, v);
            if(v4 < 0) {
                v2 = v3 + 1;
                continue;
            }
            if(v4 > 0) {
                v1 = v3 - 1;
                continue;
            }
            return v3;
        }
        return ~v2;
    }

    static List j0(List list0, String s, int v) {
        if(Build.VERSION.SDK_INT >= 24) {
            list0.removeIf((f.c0.e c0$e0) -> s.equals(c0$e0.i()) && v == c0$e0.k());
            return list0;
        }
        int v1 = list0.size();
        List list1 = new ArrayList(v1);
        for(int v2 = 0; v2 < v1; ++v2) {
            f.c0.e c0$e0 = (f.c0.e)list0.get(v2);
            if(!s.equals(c0$e0.i()) || v != c0$e0.k()) {
                list1.add(c0$e0);
            }
        }
        return list1;
    }

    public static void k(List list0) {
        if(list0 != null && !list0.isEmpty()) {
            Locale locale0 = Locale.getDefault();
            if(!locale0.equals(((f.c0.b)list0.get(0)).h())) {
                Collections.sort(list0, new f.z.d(locale0));
            }
        }
    }

    public static void k0(String s, String s1, int v) {
        z z0 = z.d;
        if(z0 != null) {
            z0.c0(s, s1, v);
        }
    }

    private static List l(List list0, boolean z, boolean z1) {
        if(list0 != null && list0.size() != 0) {
            int v = list0.size();
            List list1 = new ArrayList(v);
            for(int v1 = 0; v1 < v; ++v1) {
                d c0$d0 = (d)list0.get(v1);
                if((!z1 || c0$d0.h.length > 0) && (z || !c0$d0.l())) {
                    list1.add(c0$d0);
                }
            }
            return list1;
        }
        return list0;
    }

    public static void l0(String s, String s1, int[] arr_v) {
        z z0 = z.d;
        if(z0 != null && arr_v != null) {
            for(int v = 0; v < arr_v.length; ++v) {
                z0.c0(s, s1, arr_v[v]);
            }
        }
    }

    private static List m(List list0, f.c0.c c0$c0, int v) {
        if(c0$c0 != null && c0$c0.d()) {
            c0$c0 = null;
        }
        if(c0$c0 == null && v == 0) {
            return list0;
        }
        int v1 = list0.size();
        List list1 = new ArrayList(v1);
        int v2 = 0;
        while(v2 < v1) {
            f.c0.e c0$e0 = (f.c0.e)list0.get(v2);
            if((v & 15) == 1) {
                if(c0$e0.n()) {
                    goto label_13;
                }
                else {
                    goto label_20;
                }
                goto label_12;
            }
            else {
            label_12:
                if((v & 15) != 2 || !c0$e0.n()) {
                label_13:
                    switch(v & 0xF0000) {
                        case 0x10000: {
                            if(c0$e0.m()) {
                                goto label_18;
                            }
                            else {
                                break;
                            }
                            goto label_17;
                        }
                        case 0x20000: {
                        label_17:
                            if(!c0$e0.m()) {
                            label_18:
                                if(c0$c0 == null || !c0$c0.c(c0$e0.i(), c0$e0.k())) {
                                    list1.add(c0$e0);
                                }
                            }
                            break;
                        }
                        default: {
                            goto label_18;
                        }
                    }
                }
            }
        label_20:
            ++v2;
        }
        return list1;
    }

    public static void m0(boolean z) {
        z.e = z;
    }

    static d n(List list0, String s, int v) {
        if(list0 != null) {
            int v1 = z.j(list0, s, v);
            return v1 < 0 ? null : ((d)list0.get(v1));
        }
        return null;
    }

    public void n0(e z$e0) {
        if(z$e0 != null && this.c != null) {
            v.c("Override original listener!");
        }
        this.c = z$e0;
    }

    public final Drawable o(ComponentName componentName0, int v) {
        return this.p(componentName0, componentName0.getPackageName(), componentName0.getClassName(), v);
    }

    private static List o0(List list0) {
        int v = list0.size();
        List list1 = new ArrayList(v);
        for(int v1 = 0; v1 < v; ++v1) {
            list1.add(new d(((PackageInfo)list0.get(v1))));
        }
        return list1;
    }

    private Drawable p(ComponentName componentName0, String s, String s1, int v) {
        f.c0.a c0$a0 = this.I(s, s1, v);
        if(c0$a0 != null) {
            return c0$a0.d();
        }
        if(componentName0 == null) {
            componentName0 = new ComponentName(s, s1);
        }
        return c0.k(c0.m(c0.b(componentName0, v)), v);
    }

    private static List p0(List list0) {
        int v = list0.size();
        List list1 = new ArrayList(v);
        for(int v1 = 0; v1 < v; ++v1) {
            list1.add(new f.c0.e(((ResolveInfo)list0.get(v1))));
        }
        return list1;
    }

    public final Drawable q(Intent intent0) {
        return this.r(intent0, o0.d());
    }

    public final Drawable r(Intent intent0, int v) {
        ComponentName componentName0 = intent0.getComponent();
        if(componentName0 != null) {
            return this.o(componentName0, v);
        }
        ResolveInfo resolveInfo0 = c0.x(intent0, 0x10000, v);
        if(resolveInfo0 != null) {
            return c0.k(c0.o(resolveInfo0), v);
        }
        String s = intent0.getPackage();
        if(s == null) {
            throw new PackageManager.NameNotFoundException(intent0.toUri(0));
        }
        return this.D(s, v);
    }

    public final Drawable s(String s, String s1) {
        return this.t(s, s1, o0.d());
    }

    public final Drawable t(String s, String s1, int v) {
        return this.p(null, s, s1, v);
    }

    // 去混淆评级： 低(20)
    public final CharSequence u(ComponentName componentName0) {
        return this.v(componentName0, 0);
    }

    public final CharSequence v(ComponentName componentName0, int v) {
        return this.w(componentName0, componentName0.getPackageName(), componentName0.getClassName(), v);
    }

    private CharSequence w(ComponentName componentName0, String s, String s1, int v) {
        f.c0.a c0$a0 = this.I(s, s1, v);
        if(c0$a0 != null) {
            return c0$a0.f();
        }
        if(componentName0 == null) {
            componentName0 = new ComponentName(s, s1);
        }
        return c0.l(c0.p(c0.b(componentName0, v)), v);
    }

    public final CharSequence x(Intent intent0) {
        return this.y(intent0, o0.d());
    }

    public final CharSequence y(Intent intent0, int v) {
        ComponentName componentName0 = intent0.getComponent();
        if(componentName0 != null) {
            return this.v(componentName0, v);
        }
        ResolveInfo resolveInfo0 = c0.x(intent0, 0x10000, v);
        if(resolveInfo0 != null) {
            return c0.l(c0.r(resolveInfo0), v);
        }
        String s = intent0.getPackage();
        if(s == null) {
            throw new PackageManager.NameNotFoundException(intent0.toUri(0));
        }
        return this.H(s, v);
    }

    // 去混淆评级： 低(20)
    public final CharSequence z(String s, String s1) {
        return this.A(s, s1, 0);
    }
}


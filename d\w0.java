package d;

import a.b;
import a.z;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import e.j.g;
import e.j.k;

public class w0 extends c implements a {
    private z M;
    private CharSequence[] N;

    public w0() {
        this.N = null;
    }

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F060173);  // string:pie "Pie"
    }

    @Override  // e.j
    protected int B0() {
        this.N = new CharSequence[]{this.u(0x7F0600E0), this.u(0x7F0600E4), this.u(0x7F0600E8), this.u(0x7F0600DB)};  // string:edge_left "Left edge"
        this.M = this.g().h();
        return this.N.length;
    }

    @Override  // d.c
    protected void C1(Bundle bundle0, int v) {
        Context context0 = this.f();
        b b0 = (b)bundle0.getParcelable("result");
        if(b0 != null) {
            int v1 = this.E0();
            int v2 = this.F0();
            this.M.l0(context0, 2, v1, v2, b0);
            g j$g0 = (g)this.K0(v2);
            j$g0.setSubText(b0.n(context0));
            j$g0.setImageDrawable(this.D0(b0));
        }
    }

    public String I1(int v) {
        return v >= 6 ? this.getString(0x7F060177, new Object[]{((int)(v - 5))}) : this.getString(0x7F060176, new Object[]{((int)(v + 1))});  // string:pie_level_2_item_index_f "Level 2 item %1$d"
    }

    protected k J1(int v) {
        return new k(this, this.N[v]);
    }

    protected k K1(int v) {
        Context context0 = this.M0();
        b b0 = this.M.l(this.I0(), v);
        return new g(this, this.I1(v), b0.n(context0), this.D0(b0));
    }

    @Override  // e.j
    protected boolean N0(int v) {
        return true;
    }

    @Override  // e.j
    protected View h1(int v) {
        return this.J1(v);
    }

    @Override  // e.j
    protected View i1(int v) {
        return this.K1(v);
    }

    @Override  // e.j
    protected void k1(int v) {
        this.y1(v, 12);
    }

    @Override  // e.j
    protected void o1(int v) {
        this.O(new d().J1(6, this.u(0x7F060173), this.N[this.I0()], this.I1(v)));  // string:pie "Pie"
    }

    @Override  // e.j
    protected boolean p1(int v) {
        this.F1(this.M.l(this.I0(), v), 1, 6);
        return true;
    }
}


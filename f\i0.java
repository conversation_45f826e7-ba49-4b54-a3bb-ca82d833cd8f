package f;

import a.b.a0;
import android.content.Context;
import android.os.Handler;
import android.os.SystemClock;
import android.widget.Toast;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.util.ArrayList;

public class i0 {
    public static class a {
        private final String a;
        private final int b;
        private int c;

        public a(String s) {
            this.c = 0;
            this.a = s;
            this.b = s.length();
        }

        private String a(int v, int v1) {
            int v3;
            int v2;
            boolean z;
            StringBuilder stringBuilder0 = new StringBuilder(v1 - v);
        alab1:
            while(true) {
                z = false;
            label_2:
                if(v >= v1) {
                    return stringBuilder0.toString();
                }
                v2 = this.a.charAt(v);
                v3 = 92;
                if(!z) {
                    goto label_26;
                }
                switch(v2) {
                    case 34: {
                        break alab1;
                    }
                    case 39: {
                        v3 = 39;
                        stringBuilder0.append(((char)v3));
                        z = false;
                        ++v;
                        goto label_2;
                    }
                    case 92: {
                        stringBuilder0.append(((char)v3));
                        z = false;
                        ++v;
                        goto label_2;
                    }
                    case 98: {
                        goto label_14;
                    }
                    case 102: {
                        goto label_16;
                    }
                    case 110: {
                        goto label_18;
                    }
                    case 0x72: {
                        goto label_20;
                    }
                    case 0x74: {
                        goto label_22;
                    }
                    default: {
                        stringBuilder0.append(((char)v2));
                        ++v;
                    }
                }
            }
            v3 = 34;
            stringBuilder0.append(((char)v3));
            z = false;
            ++v;
            goto label_2;
        label_14:
            v3 = 8;
            stringBuilder0.append(((char)v3));
            z = false;
            ++v;
            goto label_2;
        label_16:
            v3 = 12;
            stringBuilder0.append(((char)v3));
            z = false;
            ++v;
            goto label_2;
        label_18:
            v3 = 10;
            stringBuilder0.append(((char)v3));
            z = false;
            ++v;
            goto label_2;
        label_20:
            v3 = 13;
            stringBuilder0.append(((char)v3));
            z = false;
            ++v;
            goto label_2;
        label_22:
            v3 = 9;
            stringBuilder0.append(((char)v3));
            z = false;
            ++v;
            goto label_2;
        label_26:
            if(v2 == 92) {
                z = true;
            }
            else {
                stringBuilder0.append(((char)v2));
            }
            ++v;
            goto label_2;
        }

        public String[] b() {
            ArrayList arrayList0 = new ArrayList();
            String s;
            while((s = this.c()) != null) {
                arrayList0.add(s);
            }
            return (String[])arrayList0.toArray(new String[arrayList0.size()]);
        }

        public String c() {
            int v = 0;
            int v1;
            while((v1 = this.c) < this.b) {
                v = this.a.charAt(v1);
                if(v > 0x20) {
                    break;
                }
                ++this.c;
            }
            int v2 = this.c;
            if(v2 >= this.b) {
                return null;
            }
            if(v != 34 && v != 39) {
                while(this.c < this.b && this.a.charAt(this.c) > 0x20) {
                    ++this.c;
                }
                return v2 >= this.c ? null : this.a.substring(v2, this.c);
            }
            this.c = v2 + 1;
            int v3 = 0;
            boolean z = false;
            int v4;
            while((v4 = this.c) < this.b) {
                this.c = v4 + 1;
                int v5 = this.a.charAt(v4);
                if(v5 == 92) {
                    v3 ^= 1;
                    z = true;
                }
                else {
                    if(v5 == v && v3 == 0) {
                        break;
                    }
                    v3 = 0;
                }
            }
            ++v2;
            int v6 = this.c - 1;
            if(z && v == 34) {
                return this.a(v2, v6);
            }
            return v2 >= v6 ? null : this.a.substring(v2, v6);
        }
    }

    static class b implements Runnable {
        private final Context A;
        private final Handler B;
        private final boolean C;
        private final boolean D;
        private final long E;
        private final a0 z;

        b(a0 b$a00, Context context0, Handler handler0, boolean z, boolean z1) {
            this.z = b$a00;
            this.A = context0;
            this.B = handler0;
            this.C = z;
            this.D = z1;
            this.E = SystemClock.uptimeMillis();
        }

        // 检测为 Lambda 实现
        private void b(CharSequence charSequence0) [...]

        void c(CharSequence charSequence0) {
            if(this.A != null) {
                Handler handler0 = this.B;
                if(handler0 != null) {
                    handler0.post(() -> Toast.makeText(this.A, charSequence0, 0).show());
                }
            }
        }

        private void d(String s) {
        }

        void e(Throwable throwable0) {
            String s = throwable0.getMessage();
            if(throwable0 instanceof IOException && s != null && (s.contains("EPIPE") || s.contains("EBADF") || s.contains("Stream closed"))) {
                v.c(s);
                return;
            }
            this.c(s);
            v.d(throwable0);
        }

        @Override
        public void run() {
            Process process0;
            if(this.E < 0L) {
                return;
            }
            try {
                process0 = i0.d((this.z.J ? "su" : "sh"), this.z.A());
                if(process0 == null) {
                    v.c(("Empty shell: " + this.z.K));
                    return;
                }
            }
            catch(Throwable throwable0) {
                this.e(throwable0);
                return;
            }
            boolean z = false;
            synchronized(i0.a) {
                if(this.E < 0L) {
                    z = true;
                }
                else {
                    i0.a.add(process0);
                }
            }
            if(z) {
                process0.destroy();
                return;
            }
            try {
                if(process0.waitFor() == 0) {
                    CharSequence charSequence0 = i0.e(process0.getInputStream());
                    v.c(("Console output: \n" + ((String)charSequence0)));
                    if(this.D) {
                        if(this.C && this.A != null) {
                            charSequence0 = this.z.n(this.A) + "\n" + ((String)charSequence0);
                        }
                        this.c(charSequence0);
                    }
                    else if(this.C) {
                        Context context0 = this.A;
                        if(context0 != null) {
                            this.c(this.z.n(context0));
                        }
                    }
                }
                else {
                    String s = i0.e(process0.getErrorStream());
                    v.c(("Execution failed: " + this.z.K + ", \n" + s));
                    if(this.A != null) {
                        this.c("Failed to run command:  " + s);
                    }
                }
            }
            catch(Throwable throwable1) {
                this.e(throwable1);
            }
            finally {
                synchronized(i0.a) {
                    i0.a.remove(process0);
                }
            }
        }
    }

    private static final ArrayList a;
    private static volatile long b;

    static {
        i0.a = new ArrayList();
        i0.b = 0L;
    }

    static long a() [...] // 潜在的解密器

    public static void c(a0 b$a00, Context context0, Handler handler0, boolean z, boolean z1) {
        if(b$a00 == null) {
            v.d(new NullPointerException("Command == null"));
            return;
        }
        d.b(new b(b$a00, context0, handler0, z, z1));
    }

    static Process d(String s, String[] arr_s) {
        Process process0;
        if(arr_s != null && arr_s.length != 0) {
            String[] arr_s1 = i0.f(arr_s);
            if(arr_s1.length == 0) {
                return null;
            }
            try {
                process0 = Runtime.getRuntime().exec(s);
            }
            catch(IOException iOException0) {
                if(arr_s1.length != 1 || !"sh".equals(s) || !o0.l()) {
                    throw iOException0;
                }
                return Runtime.getRuntime().exec(new a(arr_s1[0]).b());
            }
            OutputStream outputStream0 = process0.getOutputStream();
            for(int v = 0; v < arr_s1.length; ++v) {
                outputStream0.write((arr_s1[v] + '\n').getBytes(p0.z));
                outputStream0.flush();
            }
            outputStream0.write("exit\n".getBytes(p0.z));
            outputStream0.flush();
            return process0;
        }
        return null;
    }

    static String e(InputStream inputStream0) {
        BufferedReader bufferedReader0 = new BufferedReader(new InputStreamReader(inputStream0));
        StringBuilder stringBuilder0 = new StringBuilder();
        String s = bufferedReader0.readLine();
        if(s != null) {
            while(true) {
                stringBuilder0.append(s);
                s = bufferedReader0.readLine();
                if(s == null) {
                    break;
                }
                stringBuilder0.append('\n');
            }
        }
        return stringBuilder0.toString();
    }

    private static String[] f(String[] arr_s) {
        ArrayList arrayList0 = new ArrayList(arr_s.length);
        for(int v = 0; v < arr_s.length; ++v) {
            String s = arr_s[v];
            if(s != null) {
                String s1 = s.trim();
                if(s1.length() > 0 && s1.charAt(0) != 35) {
                    arrayList0.add(s1);
                }
            }
        }
        return (String[])arrayList0.toArray(new String[arrayList0.size()]);
    }

    public static void g() {
        i0.b = SystemClock.uptimeMillis();
        ArrayList arrayList0 = i0.a;
        synchronized(arrayList0) {
            if(arrayList0.size() > 0) {
                for(Object object0: arrayList0) {
                    Process process0 = (Process)object0;
                    try {
                        process0.destroy();
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
                i0.a.clear();
            }
        }
    }
}


package a;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.provider.Settings.System;
import android.util.SparseArray;
import f.l;
import f.m;
import f.s;
import f.v;
import g.a0;
import java.util.ArrayList;

public class p extends r {
    public interface a {
        public static final int[] a;
        public static final int[] b;
        public static final int[] c;
        public static final int[] d;

        static {
            a.a = new int[]{0x7F0600E0, 0x7F0600E4, 0x7F0600E8, 0x7F0600DB};  // string:edge_left "Left edge"
            a.b = new int[]{0x7F060132, 0x7F060139, 0x7F06013F, 0x7F06013D, 0x7F060141, 0x7F060140};  // string:key_back "Back"
            a.c = new int[]{16, 17, 18, 19};
            a.d = new int[]{0, 1, 2, 3, 4, 5};
        }
    }

    public interface b {
        public static final int[] e;
        public static final int[] f;
        public static final int[] g;
        public static final int[] h;

        static {
            b.e = new int[]{4, 5, 6, 0, 7, 8, 9, 1, 10, 11, 12, 2, 13, 14, 15, 3};
            b.f = new int[]{0x7F040035, 0x7F040034, 0x7F040033, 0x7F040032, 0x7F040039, 0x7F040038, 0x7F040037, 0x7F040036, 0x7F04003C, 0x7F04003B, 0x7F04003D, 0x7F04003A, 0x7F040030, 0x7F04002F, 0x7F040031, 0x7F04002E};  // drawable:ic_edge_left_top
            b.g = new int[]{0x7F0600E3, 0x7F0600E2, 0x7F0600E1, 0x7F0600E0, 0x7F0600E7, 0x7F0600E6, 0x7F0600E5, 0x7F0600E4, 0x7F0600EA, 0x7F0600E9, 0x7F0600EB, 0x7F0600E8, 0x7F0600DD, 0x7F0600DC, 0x7F0600DE, 0x7F0600DB};  // string:edge_left_top "Left top"
            b.h = new int[]{12, 14, 13, 0, 15, 17, 16, 1, 18, 20, 19, 2, 21, 23, 22, 3};
        }
    }

    static class c {
        final a.b[][] a;
        int[] b;
        private int[] c;
        private SparseArray d;
        private final q e;
        private boolean f;
        private int g;

        c() {
            this.a = new a.b[320][];
            this.b = new int[10];
            this.c = null;
            this.d = new SparseArray();
            this.e = a.b.e();
            this.f = false;
            this.g = 0;
        }

        void c(int v) {
            this.p(v, 0, this.e);
        }

        private d d(int v, boolean z) {
            d p$d0 = new d(z, this.e, this.e, this.e);
            if(this.f) {
                SparseArray sparseArray0 = this.d.clone();
                sparseArray0.append(v, p$d0);
                this.d = sparseArray0;
                return p$d0;
            }
            this.d.append(v, p$d0);
            return p$d0;
        }

        private void e() {
            p.N(this.a, this.e);
            this.b = new int[10];
            this.d = new SparseArray();
        }

        void f() {
            if(this.c != null && this.g >= this.a.length) {
                int v = this.d.size();
                for(int v1 = 0; v1 < v; ++v1) {
                    int v2 = this.d.keyAt(v1);
                    if(v2 <= this.g && (1 << v2 % 0x20 & this.c[v2 / 0x20]) != 0) {
                        ((d)this.d.valueAt(v1)).a = true;
                    }
                }
                this.c = null;
            }
        }

        a.b g(int v, int v1) {
            a.b[][] arr2_b = this.a;
            if(v > arr2_b.length) {
                d p$d0 = (d)this.d.get(v);
                return p$d0 != null ? p$d0.a(v1, this.e) : this.e;
            }
            if(v < 0) {
                return this.e;
            }
            a.b[] arr_b = arr2_b[v];
            if(arr_b == null) {
                return this.e;
            }
            a.b b0 = arr_b[v1];
            return b0 == null ? this.e : b0;
        }

        ArrayList h() {
            ArrayList arrayList0 = new ArrayList(16);
            for(int v1 = 0; true; ++v1) {
                a.b[][] arr2_b = this.a;
                if(v1 >= arr2_b.length) {
                    break;
                }
                if(arr2_b[v1] != null) {
                    arrayList0.add(v1);
                }
            }
            int v2 = this.d.size();
            for(int v = 0; v < v2; ++v) {
                arrayList0.add(this.d.keyAt(v));
            }
            return arrayList0;
        }

        boolean i(int v) {
            if(v < 0) {
                return false;
            }
            if(v < this.a.length) {
                return (1 << v % 0x20 & this.b[v / 0x20]) != 0;
            }
            d p$d0 = (d)this.d.get(v);
            return p$d0 != null && p$d0.a;
        }

        boolean j(int v) {
            a.b[][] arr2_b = this.a;
            if(v < arr2_b.length) {
                a.b[] arr_b = arr2_b[v];
                return arr_b != null && this.i(v) && (arr_b[0] != null && arr_b[0].z != 0 || arr_b[1] != null && arr_b[1].z != 0 || arr_b[2] != null && arr_b[2].z != 0);
            }
            d p$d0 = (d)this.d.get(v);
            return p$d0 != null && p$d0.b();
        }

        void k(s s0) {
            int v = s0.h();
            boolean z = true;
            if(v >= this.a.length) {
                if(s0.h() == 0) {
                    z = false;
                }
                d p$d0 = new d(z, s0.g(), s0.g(), s0.g());
                this.d.append(v, p$d0);
                return;
            }
            this.q(v, s0.h() != 0);
            this.p(v, 0, s0.g());
            this.p(v, 1, s0.g());
            this.p(v, 2, s0.g());
        }

        void l(s s0) {
            int v = s0.h();
            if(v > this.g) {
                this.g = v;
            }
            this.p(v, s0.h(), s0.g());
        }

        void m(s s0) {
            for(int v = 0; true; ++v) {
                int[] arr_v = this.b;
                if(v >= arr_v.length) {
                    break;
                }
                arr_v[v] = s0.h();
            }
            if(337 > this.a.length) {
                int[] arr_v1 = new int[11];
                this.c = arr_v1;
                int[] arr_v2 = this.b;
                arr_v1[arr_v2.length - 1] = arr_v2[arr_v2.length - 1];
                for(int v1 = arr_v2.length; true; ++v1) {
                    int[] arr_v3 = this.c;
                    if(v1 >= arr_v3.length) {
                        break;
                    }
                    arr_v3[v1] = s0.h();
                }
            }
        }

        void n(int v) {
            a.b[][] arr2_b = this.a;
            if(v < arr2_b.length) {
                arr2_b[v] = null;
                return;
            }
            this.d.remove(v);
        }

        void o(f.p p0) {
            int v = this.d.size();
            for(int v1 = 0; v1 < v; ++v1) {
                int v2 = this.d.keyAt(v1);
                d p$d0 = (d)this.d.valueAt(v1);
                if(p$d0 != null) {
                    p0.w(11).w(v2).w(((int)p$d0.a)).x(p$d0.b).x(p$d0.c).x(p$d0.d).C();
                }
            }
        }

        void p(int v, int v1, a.b b0) {
            if(v < 0) {
                return;
            }
            if(b0 == null) {
                b0 = this.e;
            }
            a.b[][] arr2_b = this.a;
            if(v < arr2_b.length) {
                a.b[] arr_b = arr2_b[v];
                if(arr_b == null) {
                    arr_b = new a.b[3];
                    arr2_b[v] = arr_b;
                }
                arr_b[v1] = b0;
                return;
            }
            d p$d0 = (d)this.d.get(v);
            if(p$d0 == null) {
                p$d0 = this.d(v, false);
            }
            p$d0.c(v1, b0);
        }

        void q(int v, boolean z) {
            if(v < 0) {
                return;
            }
            if(v < this.a.length) {
                if(z) {
                    this.b[v / 0x20] |= 1 << v % 0x20;
                    return;
                }
                this.b[v / 0x20] &= ~(1 << v % 0x20);
                return;
            }
            d p$d0 = (d)this.d.get(v);
            if(p$d0 != null) {
                p$d0.a = z;
                return;
            }
            this.d(v, z);
        }
    }

    static class d {
        boolean a;
        a.b b;
        a.b c;
        a.b d;

        d(boolean z, a.b b0, a.b b1, a.b b2) {
            this.a = z;
            this.b = b0;
            this.c = b1;
            this.d = b2;
        }

        a.b a(int v, a.b b0) {
            switch(v) {
                case 0: {
                    return this.b == null ? b0 : this.b;
                }
                case 1: {
                    return this.c == null ? b0 : this.c;
                }
                case 2: {
                    return this.d == null ? b0 : this.d;
                }
                default: {
                    return b0;
                }
            }
        }

        // 去混淆评级： 低(40)
        boolean b() {
            return this.a && (this.b != null && this.b.z != 0 || this.c != null && this.c.z != 0 || this.d != null && this.d.z != 0);
        }

        void c(int v, a.b b0) {
            switch(v) {
                case 0: {
                    this.b = b0;
                    return;
                }
                case 1: {
                    this.c = b0;
                    return;
                }
                case 2: {
                    this.d = b0;
                }
            }
        }
    }

    public interface e {
        public static final int[] a;

        static {
            e.a = new int[]{0x7F06002E, 0x7F060061, 0x7F06004F, 0x7F060026, 0x7F060024, 0x7F06002F, 0x7F060023};  // string:action_keep_screen_on "Stay awake"
        }
    }

    public interface f {
        public static final int[] i;

        static {
            f.i = new int[]{0x7F06014E, 0x7F060151, 0x7F060150, 0x7F06014F, 0x7F060152};  // string:mode_default "Default"
        }
    }

    public interface g {
        public static final int[] a;
        public static final int b;

        static {
            g.a = new int[]{0, 0x7F0601C7, 1, 0x7F0601D9, 2, 0x7F0601D8, 3, 0x7F0601E4, 4, 0x7F0601C4, 5, 0x7F0601C5, 6, 0x7F0601C6, 7, 0x7F0601D6, 8, 0x7F0601D7, 9, 0x7F0601E5, 10, 0x7F0601E6, 11, 0x7F0601D1, 12, 0x7F0601D2, 13, 0x7F0601C3, 14, 0x7F0601C2, 15, 0x7F0601CA, 16, 0x7F0601CB, 17, 0x7F0601E2, 18, 0x7F0601E3, 19, 0x7F0601DA, 20, 0x7F0601DC, 21, 0x7F0601DB, 29, 0x7F0601DF, 30, 0x7F0601DE, 0x1F, 0x7F0601E1, 0x20, 0x7F0601E0, 33, 0x7F0601D0, 34, 0x7F0601CF, 35, 0x7F0601C9, 36, 0x7F0601C8, 22, 0x7F0601DD, 23, 0x7F0601CE, 24, 0x7F0601CC, 25, 0x7F0601CD, 26, 0x7F0601D4, 27, 0x7F0601D3, 28, 0x7F0601D5};  // string:state_boot_completed "Boot completed"
            g.b = 37;
        }
    }

    public interface h {
        public static final int[] a;

        static {
            h.a = new int[]{0x7F06021B, 0x7F06021A, 0x7F060219, 0x7F06021C, 0x7F060221, 0x7F06021F};  // string:vibrate_disable "Disable"
        }
    }

    protected final q B;
    protected final t1 C;
    protected final a.b[][] D;
    protected final c E;
    protected final a.b[][] F;
    protected final a.b[][] G;
    protected final a.b[] H;
    protected final q1[] I;
    protected int J;
    protected int K;
    protected int[] L;
    protected int[] M;
    protected boolean N;
    private boolean O;
    public static final String P;
    static final String Q;

    static {
        p.P = l.m + "prefs";
        p.Q = l.r ? "xedgepro_state" : "xedge_state";
    }

    public p() {
        super(l.k + "PREFS_CHANGED");
        this.B = a.b.e();
        this.C = a.b.r();
        this.D = new a.b[16][];
        this.E = new c();
        this.F = new a.b[4][];
        this.G = new a.b[4][];
        this.H = new a.b[g.b];
        this.I = new q1[2];
        this.J = 0;
        this.K = 0;
        this.L = new int[39];
        this.M = new int[0];
        this.N = false;
        this.O = false;
        this.L();
    }

    f.p A() {
        return new f.p(p.P);
    }

    public int B() {
        int v = this.u(1);
        if(v == 0) {
            v = 0x55222200;
        }
        int v1 = this.J >>> 4 & 15;
        return v1 == 0 ? v : v & 0xFFFFFF | v1 * 0xD0 / 15 << 24;
    }

    public int C() {
        return this.J >>> 4 & 15;
    }

    static int D(String s) {
        s.hashCode();
        if(!s.equals("toast_command_output")) {
            return s.equals("show_app_process_state") ? 39 : -1;
        }
        return 36;
    }

    public static boolean E(int v, int v1) {
        switch(v) {
            case 0: 
            case 4: 
            case 5: 
            case 6: {
                return 3 == v1;
            }
            case 1: 
            case 7: 
            case 8: 
            case 9: {
                return 4 == v1;
            }
            case 2: 
            case 10: 
            case 11: 
            case 12: {
                return 5 == v1;
            }
            case 3: 
            case 13: 
            case 14: 
            case 15: {
                return 6 == v1;
            }
            default: {
                return false;
            }
        }
    }

    public boolean F(int v) {
        switch(v) {
            case 0: 
            case 4: 
            case 5: 
            case 6: {
                return (0x10000 & this.u(24)) != 0;
            }
            case 1: 
            case 7: 
            case 8: 
            case 9: {
                return (0x20000 & this.u(24)) != 0;
            }
            case 2: 
            case 10: 
            case 11: 
            case 12: {
                return (0x40000 & this.u(24)) != 0;
            }
            case 3: 
            case 13: 
            case 14: 
            case 15: {
                return (0x80000 & this.u(24)) != 0;
            }
            default: {
                return false;
            }
        }
    }

    public boolean G(int v) {
        int v1 = 4;
        switch(v) {
            case 3: {
                v1 = 1;
                break;
            }
            case 4: {
                return (this.u(24) & 1) != 0;
            }
            case 24: {
                break;
            }
            case 25: {
                return (this.u(24) & 0x20) != 0;
            }
            case 82: {
                return (this.u(24) & 8) != 0;
            }
            case 0xBB: {
                return (this.u(24) & 4) != 0;
            }
            default: {
                return false;
            }
        }
        return (this.u(24) & 1 << v1) != 0;
    }

    public boolean H(int v) {
        if(v >= 0 && v < 16 && this.p(1)) {
            for(int v1 = 0; v1 < 2; ++v1) {
                if(this.s(v)) {
                    a.b[][] arr2_b = this.D;
                    if(arr2_b[v] != null) {
                        a.b[] arr_b = arr2_b[v];
                        for(int v2 = 0; v2 < arr_b.length; ++v2) {
                            a.b b0 = arr_b[v2];
                            if(b0 != null && b0.z != 0) {
                                return true;
                            }
                        }
                    }
                }
                v = p.h(v);
            }
        }
        return false;
    }

    // 去混淆评级： 低(20)
    public boolean I(int v) {
        return this.p(0) && this.E.j(v);
    }

    public boolean J() {
        return this.O;
    }

    private void K(int v, s s0) {
        boolean z = true;
        int v1 = 0;
    alab1:
        switch(v) {
            case 0: {
                this.Q(s0.h(), s0.h(), s0.g());
                return;
            }
            case 1: {
                this.E.l(s0);
                return;
            }
            case 2: {
                this.S(s0.h(), s0.h(), s0.g());
                return;
            }
            case 3: {
                this.T(s0.h(), s0.h(), s0.g());
                return;
            }
            case 4: {
                if(s0 instanceof m) {
                    int v2 = s0.h();
                    if(s0.h() == 0) {
                        z = false;
                    }
                    this.V(v2, z);
                    return;
                }
                this.K = s0.h();
                return;
            }
            case 6: {
                if(s0 instanceof m) {
                    int v3 = s0.h();
                    if(s0.h() == 0) {
                        z = false;
                    }
                    this.a0(v3, z);
                    return;
                }
                this.E.m(s0);
                return;
            }
            case 7: {
                if(s0 instanceof m) {
                    int v5 = s0.h();
                    int v6 = s0.h();
                    if(v5 != 36 && v5 != 37 && v5 != 38) {
                        this.Z(v5, v6);
                        return;
                    }
                    this.Y(v5, a.q.b(v6));
                    return;
                }
                while(true) {
                    int[] arr_v1 = this.L;
                    if(v1 >= arr_v1.length) {
                        break alab1;
                    }
                    arr_v1[v1] = s0.h();
                    ++v1;
                }
            }
            case 8: {
                this.R(s0.h(), s0.g());
                return;
            }
            case 9: {
                int v7 = s0.h();
                a.b b0 = s0.g();
                if(b0.z == 0x3F) {
                    this.U(v7, ((q1)b0));
                    return;
                }
                break;
            }
            case 10: {
                int v4 = s0.h();
                int[] arr_v = new int[v4];
                while(v1 < v4) {
                    arr_v[v1] = s0.h();
                    ++v1;
                }
                this.M = arr_v;
                return;
            }
            case 11: {
                this.E.k(s0);
                return;
            }
            default: {
                v.c(("Unknown type: " + v));
            }
        }
    }

    public boolean L() {
        f.p p0 = this.A();
        if(p0.E()) {
            this.O = true;
            int v = p0.h();
            if(v != -1) {
                while(true) {
                    this.K(v, p0);
                    if(!p0.t()) {
                        break;
                    }
                    v = p0.h();
                }
            }
            this.E.f();
        }
        else {
            this.O = false;
        }
        p0.m();
        return this.O;
    }

    private static void M(a.b[] arr_b, a.b b0) {
        for(int v = arr_b.length - 1; v >= 0; --v) {
            arr_b[v] = b0;
        }
    }

    private static void N(a.b[][] arr2_b, a.b b0) {
        for(int v = arr2_b.length - 1; v >= 0; --v) {
            a.b[] arr_b = arr2_b[v];
            if(arr_b != null) {
                p.M(arr_b, b0);
            }
        }
    }

    protected void O(f.p p0, int v, a.b[][] arr2_b) {
        for(int v1 = 0; v1 < arr2_b.length; ++v1) {
            if(arr2_b[v1] != null) {
                a.b[] arr_b = arr2_b[v1];
                if(arr_b[0] != null) {
                    p0.w(v).w(v1).w(0).x(arr_b[0]).C();
                }
                for(int v2 = 1; v2 < arr_b.length; ++v2) {
                    if(arr_b[v2] != null && arr_b[v2].z != 0) {
                        p0.w(v).w(v1).w(v2).x(arr_b[v2]).C();
                    }
                }
            }
        }
    }

    protected void P(int v, int v1, int v2, a.b b0) {
        switch(v) {
            case 0: {
                this.Q(v1, v2, b0);
                break;
            }
            case 1: {
                this.E.p(v1, v2, b0);
                break;
            }
            case 2: {
                this.S(v1, v2, b0);
                break;
            }
            case 3: {
                this.T(v1, v2, b0);
                break;
            }
            default: {
                v.c(("unknown type: " + v));
            }
        }
        this.N = true;
    }

    private void Q(int v, int v1, a.b b0) {
        a.b[][] arr2_b = this.D;
        if(arr2_b[v] == null) {
            arr2_b[v] = new a.b[7];
        }
        arr2_b[v][v1] = b0;
    }

    protected void R(int v, a.b b0) {
        a.b[] arr_b = this.H;
        if(v < arr_b.length) {
            arr_b[v] = b0;
            this.N = true;
        }
    }

    private void S(int v, int v1, a.b b0) {
        a.b[][] arr2_b = this.F;
        if(arr2_b[v] == null) {
            arr2_b[v] = new a.b[12];
        }
        arr2_b[v][v1] = b0;
    }

    private void T(int v, int v1, a.b b0) {
        a.b[][] arr2_b = this.G;
        if(arr2_b[v] == null) {
            arr2_b[v] = new a.b[4];
        }
        arr2_b[v][v1] = b0;
    }

    protected void U(int v, q1 b$q10) {
        this.I[v] = b$q10;
        this.N = true;
    }

    protected void V(int v, boolean z) {
        if(v < 0x20) {
            this.K = z ? 1 << v | this.K : ~(1 << v) & this.K;
        }
        this.N = true;
    }

    private void W(int v, boolean z) {
        this.J = z ? 1 << v | this.J : ~(1 << v) & this.J;
        this.N = true;
    }

    public void X(Context context0, int v, boolean z) {
        this.W(v, z);
        Settings.System.putInt(context0.getContentResolver(), p.Q, this.J);
    }

    protected void Y(int v, float f) {
        int v1 = a.q.c(v);
        if(v1 != v) {
            this.L[v] = a.q.a(f);
            int[] arr_v = this.L;
            arr_v[v1] = f <= 0.0f ? 0 : ((int)(f * a0.j()));
            this.N = true;
            return;
        }
        v.d(new IllegalArgumentException("Unknown flag: " + v));
    }

    protected void Z(int v, int v1) {
        if(v >= 0) {
            int[] arr_v = this.L;
            if(v < arr_v.length) {
                arr_v[v] = v1;
                this.N = true;
            }
        }
    }

    protected void a0(int v, boolean z) {
        this.E.q(v, z);
        this.N = true;
    }

    @Override  // a.r
    protected void b(Intent intent0) {
        m m0 = new m(intent0);
        if(m0.i()) {
            int v = m0.h();
            if(v != -1) {
                this.K(v, m0);
            }
        }
        m0.k();
    }

    protected void b0(int[] arr_v) {
        this.M = arr_v;
    }

    public void c0(Context context0, int v) {
        if(v > 15) {
            v = 15;
        }
        if(v == this.C()) {
            return;
        }
        this.J = v << 4 | this.J & 0xFFFFFF0F;
        Settings.System.putInt(context0.getContentResolver(), p.Q, this.J);
    }

    @Override  // a.r
    public void d(Context context0, Handler handler0) {
        this.J = Settings.System.getInt(context0.getContentResolver(), p.Q, this.J);
        this.E.f = true;
        super.d(context0, handler0);
    }

    public static boolean f() {
        try {
            byte[] arr_b = f.q.c(p.P).j();
            return arr_b == null ? false : arr_b.length > 0;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return false;
        }
    }

    public void g() {
        p.N(this.D, this.B);
        this.E.e();
        p.N(this.F, this.C);
        p.N(this.G, this.C);
        p.M(this.H, this.C);
        q1 b$q10 = new q1(new a.b[0]);
        p.M(this.I, b$q10);
        this.O = false;
    }

    public static int h(int v) {
        switch(v) {
            case 0: 
            case 4: 
            case 5: 
            case 6: {
                return 0;
            }
            case 1: 
            case 7: 
            case 8: 
            case 9: {
                return 1;
            }
            case 2: 
            case 10: 
            case 11: 
            case 12: {
                return 2;
            }
            case 3: 
            case 13: 
            case 14: 
            case 15: {
                return 3;
            }
            default: {
                return -1;
            }
        }
    }

    public a.b i(int v, int v1) {
        a.b[][] arr2_b = this.D;
        return arr2_b[v] != null && arr2_b[v][v1] != null ? arr2_b[v][v1] : this.B;
    }

    public a.b j(int v, int v1) {
        return this.E.g(v, v1);
    }

    public a.b k(int v) {
        a.b[] arr_b = this.H;
        if(v >= arr_b.length) {
            return this.C;
        }
        a.b b0 = arr_b[v];
        return b0 == null || b0.z == 0 ? this.C : b0;
    }

    public a.b l(int v, int v1) {
        a.b b0 = this.F[v] == null ? null : this.F[v][v1];
        return b0 == null || b0.z == 0 ? this.C : b0;
    }

    public a.b m(int v, int v1) {
        a.b b0 = this.G[v] == null ? null : this.G[v][v1];
        return b0 == null || b0.z == 0 ? this.C : b0;
    }

    public q1 n(int v) {
        q1 b$q10 = this.I[v];
        return b$q10 == null ? new q1(new ArrayList()) : b$q10;
    }

    public boolean o(int v) {
        return v >= 0x20 ? (1 << v - 0x20 & this.u(21)) != 0 : (1 << v & this.K) != 0;
    }

    public boolean p(int v) {
        return (1 << v & this.J) != 0;
    }

    public static boolean q(Context context0, int v) {
        return (Settings.System.getInt(context0.getContentResolver(), p.Q, 0) & 1 << v) != 0;
    }

    public int r() {
        int v = this.u(18);
        if(v < 0) {
            return 0;
        }
        return v == 0 ? 16 : v;
    }

    public boolean s(int v) {
        int v1 = 0;
        switch(v) {
            case 0: {
                break;
            }
            case 1: {
                v1 = 1;
                break;
            }
            case 2: {
                return this.o(2);
            }
            case 3: {
                return this.o(3);
            }
            case 4: {
                return this.o(12);
            }
            case 5: {
                return this.o(14);
            }
            case 6: {
                return this.o(13);
            }
            case 7: {
                return this.o(15);
            }
            case 8: {
                return this.o(17);
            }
            case 9: {
                return this.o(16);
            }
            case 10: {
                return this.o(18);
            }
            case 11: {
                return this.o(20);
            }
            case 12: {
                return this.o(19);
            }
            case 13: {
                return this.o(21);
            }
            case 14: {
                return this.o(23);
            }
            case 15: {
                return this.o(22);
            }
            default: {
                return false;
            }
        }
        return this.o(v1);
    }

    public float t(int v, float f) {
        int v2;
        int v1;
        switch(v) {
            case 36: {
                v1 = 0;
                v2 = this.L[36];
                break;
            }
            case 37: {
                v1 = 8;
                v2 = this.L[37];
                break;
            }
            case 38: {
                v1 = 9;
                v2 = this.L[38];
                break;
            }
            default: {
                return f;
            }
        }
        if(v2 != 0) {
            return a.q.b(v2);
        }
        int v3 = this.L[v1];
        return v3 <= 0 ? f : ((float)v3) / a0.j();
    }

    public int u(int v) {
        if(v >= 0) {
            return v >= this.L.length ? 0 : this.L[v];
        }
        return 0;
    }

    public int v(int v, int v1) {
        int v2 = this.u(v);
        return v2 == 0 ? v1 : v2;
    }

    public boolean w(int v) {
        return this.E.i(v);
    }

    public ArrayList x() {
        return this.E.h();
    }

    public int[] y() {
        return this.M;
    }

    public int[] z() {
        int v = this.v(20, 202050057);
        int[] arr_v = new int[4];
        int v1 = 0;
        for(int v2 = 0; v2 < 4; ++v2) {
            int v3 = v >>> v2 * 8;
            if((v3 & 8) != 0) {
                arr_v[v1] = v3 & 7;
                ++v1;
            }
        }
        return arr_v;
    }
}


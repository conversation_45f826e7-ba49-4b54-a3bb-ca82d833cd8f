package com.jozein.xedgepro.xposed;

import android.content.Context;
import android.widget.AbsListView;
import android.widget.Toast;
import de.robv.android.xposed.XposedHelpers;
import f.v;
import java.io.File;
import java.lang.reflect.Constructor;

class o2 {
    private final Context a;
    private final int b;
    private boolean c;
    private static final String d;
    private static int e;
    private static Constructor f;

    static {
        o2.d = p2.z + "miui_fling_fix";
        o2.e = new File(p2.z + "miui_fling_fix").exists() ? 1 : -1;
        o2.f = null;
    }

    o2(Context context0, int v) {
        this.c = false;
        this.a = context0;
        this.b = v;
    }

    void a(AbsListView absListView0) {
        if(o2.e != 1) {
            return;
        }
        try {
            if(o2.f == null) {
                Constructor constructor0 = XposedHelpers.findClass((AbsListView.class.getName() + "$FlingRunnable"), AbsListView.class.getClassLoader()).getDeclaredConstructor(AbsListView.class);
                o2.f = constructor0;
                constructor0.setAccessible(true);
            }
            XposedHelpers.setObjectField(absListView0, "mFlingRunnable", o2.f.newInstance(absListView0));
        }
        catch(Throwable throwable0) {
            o2.e = 0;
            v.d(throwable0);
        }
    }

    void b(Throwable throwable0) {
        if(this.c) {
            return;
        }
        this.c = true;
        if(o2.e == -1) {
            if(this.d(throwable0)) {
                o2.e = 1;
                v.c("MIUI system bug has occurred! Try to fix it.");
                Toast.makeText(this.a, "MIUI system bug has occurred! Try to fix it.", 1).show();
                try {
                    new File(o2.d).createNewFile();
                }
                catch(Throwable throwable1) {
                    v.d(throwable1);
                }
            }
            else {
                o2.e = 0;
            }
        }
        t3.n().t(this.b);
    }

    boolean c() {
        return this.c;
    }

    private boolean d(Throwable throwable0) {
        StackTraceElement[] arr_stackTraceElement = throwable0.getStackTrace();
        for(int v = 0; v < arr_stackTraceElement.length; ++v) {
            if("com.miui.internal.dynamicanimation.animation.DynamicAnimation".equals(arr_stackTraceElement[v].getClassName())) {
                return true;
            }
        }
        return false;
    }
}


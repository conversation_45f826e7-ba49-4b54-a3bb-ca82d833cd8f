package com.jozein.xedgepro.xposed;

import android.app.ActivityManager;
import android.content.pm.PackageManager;
import android.os.Build.VERSION;
import android.os.IBinder;
import de.robv.android.xposed.XposedHelpers;
import f.v;

class g3 extends p2 {
    private static Class E;
    private static Class F;
    private static final String[] G;

    static {
        g3.G = new String[]{"activity", "android.app.IActivityManager", "activity_task", "android.app.IActivityTaskManager", "package", "android.content.pm.IPackageManager", "window", "android.view.IWindowManager", "input", "android.hardware.input.IInputManager", "input_method", "com.android.internal.view.IInputMethodManager", "accessibility", "android.view.accessibility.IAccessibilityManager", "statusbar", "com.android.internal.statusbar.IStatusBarService"};
    }

    static Object h() {
        return g3.n("accessibility");
    }

    static Object i() {
        if(Build.VERSION.SDK_INT >= 26) {
            try {
                Object object0 = XposedHelpers.callStaticMethod(ActivityManager.class, "getService", new Object[0]);
                return object0 == null ? g3.n("activity") : object0;
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return g3.n("activity");
            }
        }
        return g3.n("activity");
    }

    static Object j() {
        if(Build.VERSION.SDK_INT >= 29) {
            try {
                Object object0 = XposedHelpers.callStaticMethod(ActivityManager.class, "getTaskService", new Object[0]);
                return object0 == null ? g3.n("activity") : object0;
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return g3.n("activity");
            }
        }
        throw new UnsupportedOperationException();
    }

    static Object k() {
        try {
            Object object0 = XposedHelpers.callStaticMethod(XposedHelpers.findClass("android.app.AppGlobals", PackageManager.class.getClassLoader()), "getPackageManager", new Object[0]);
            if(object0 != null) {
                return object0;
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        return g3.n("package");
    }

    static Object l() {
        return g3.n("input");
    }

    static Object m() {
        return g3.n("input_method");
    }

    private static Object n(String s) {
        for(int v = 0; true; v += 2) {
            String[] arr_s = g3.G;
            if(v >= arr_s.length) {
                break;
            }
            if(arr_s[v].equals(s)) {
                return g3.o(arr_s[v + 1], s);
            }
        }
        throw new IllegalArgumentException("Interface name not added for: " + s);
    }

    static Object o(String s, String s1) {
        IBinder iBinder0 = g3.r(s1);
        return XposedHelpers.findClass(s, p2.c()).isInstance(iBinder0) ? iBinder0 : XposedHelpers.callStaticMethod(XposedHelpers.findClass((s + ".Stub"), p2.c()), "asInterface", new Object[]{iBinder0});
    }

    static Object p(Class class0) {
        if(g3.E == null) {
            g3.E = XposedHelpers.findClass("com.android.server.LocalServices", class0.getClassLoader());
        }
        return XposedHelpers.callStaticMethod(g3.E, "getService", new Object[]{class0});
    }

    static Object q(String s) {
        return g3.p(XposedHelpers.findClass(s, p2.c()));
    }

    static IBinder r(String s) {
        if(g3.F == null) {
            g3.F = XposedHelpers.findClass("android.os.ServiceManager", p2.c());
        }
        return (IBinder)XposedHelpers.callStaticMethod(g3.F, "getService", new Object[]{s});
    }

    static Object s() {
        return g3.n("statusbar");
    }

    static Object t(String s) {
        return XposedHelpers.getSurroundingThis(g3.q(s));
    }

    static Object u() {
        int v = Build.VERSION.SDK_INT;
        if(v >= 21) {
            try {
                Object object0 = g3.t((v < 28 ? "android.view.WindowManagerInternal" : "com.android.server.wm.WindowManagerInternal"));
                if(object0 != null) {
                    return object0;
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            try {
                Object object1 = XposedHelpers.callStaticMethod(XposedHelpers.findClass("android.view.WindowManagerGlobal", p2.c()), "getWindowManagerService", new Object[0]);
                return object1 == null ? g3.n("window") : object1;
            }
            catch(Throwable throwable1) {
                v.d(throwable1);
                return g3.n("window");
            }
        }
        return g3.n("window");
    }
}


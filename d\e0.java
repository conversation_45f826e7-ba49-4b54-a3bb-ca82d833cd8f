package d;

import android.content.Intent;
import android.net.Uri;
import android.view.View;
import e.j.e;
import e.j.g;
import e.j;
import f.v;

public class e0 extends j {
    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F06011B);  // string:help_and_support "Help and support"
    }

    @Override  // e.j
    protected int B0() {
        return 5;
    }

    private void B1(String s) {
        Intent intent0 = new Intent("android.intent.action.VIEW", Uri.parse(s));
        intent0.addFlags(0x48080000);
        try {
            this.startActivity(intent0);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private void C1() {
        this.s1(new e[]{new e(0, 0, this.u(0x7F06011D).toString()), new e(0, 5, this.u(0x7F06011E).toString()), new e(0, 1, this.u(0x7F06011F).toString()), new e(1, 5, this.u(0x7F060120).toString()), new e(1, 1, this.u(0x7F060121).toString()), new e(4, 3, this.u(0x7F060122).toString()), new e(3, 1, this.u(0x7F060123).toString())});  // string:help_item_0 "- Click the check box of [Gestures] to enable edge gesture."
        this.X("result", true);
    }

    @Override  // e.j
    protected View h1(int v) {
        switch(v) {
            case 0: {
                return new g(this, this.C0(0x7F04004D), this.u(0x7F06011C), null);  // drawable:ic_help
            }
            case 1: {
                return this.c1(this.u(0x7F06011D) + "\n\n" + this.u(0x7F06011E) + "\n\n" + this.u(0x7F06011F) + "\n\n" + this.u(0x7F060120) + "\n\n" + this.u(0x7F060121) + "\n\n" + this.u(0x7F060122) + "\n\n" + this.u(0x7F060123));  // string:help_item_0 "- Click the check box of [Gestures] to enable edge gesture."
            }
            case 2: {
                return new g(this, this.C0(0x7F0400B7), this.u(0x7F060224), null);  // drawable:ic_support
            }
            case 3: {
                return new g(this, this.C0(0x7F0400C0), this.u(0x7F06020D), null);  // drawable:ic_tutorial_online
            }
            case 4: {
                return new g(this, this.C0(0x7F0400BF), this.u(0x7F06006B), null);  // drawable:ic_translate
            }
            default: {
                return null;
            }
        }
    }

    @Override  // e.j
    protected void k1(int v) {
        String s;
        switch(v) {
            case 0: {
                this.C1();
                this.L();
                return;
            }
            case 2: {
                s = "https://forum.xda-developers.com/t/xposed-edge-pro.3525566/";
                break;
            }
            case 3: {
                s = "https://sites.google.com/view/tutorial-for-xposed-edge/main";
                break;
            }
            case 4: {
                s = "https://github.com/jozein/Xposed-edge-translation";
                break;
            }
            default: {
                return;
            }
        }
        this.B1(s);
    }
}


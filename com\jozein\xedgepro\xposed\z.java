package com.jozein.xedgepro.xposed;

import android.os.Build.VERSION;
import android.os.Handler;
import android.view.KeyEvent;
import android.view.MotionEvent;
import de.robv.android.xposed.XC_MethodHook.MethodHookParam;
import f.v;

abstract class z {
    private final Handler A;
    private final Runnable B;
    private final Runnable C;
    private volatile boolean D;
    static final boolean E;
    final y0 z;

    static {
        z.E = Build.VERSION.SDK_INT > 29;
    }

    z(y0 y00, Handler handler0) {
        this.D = false;
        this.z = y00;
        this.A = handler0;
        this.B = () -> {
            try {
                synchronized(this) {
                    if(this.D) {
                        this.e();
                    }
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            this.D = false;
        };
        this.C = () -> {
            try {
                synchronized(this) {
                    if(this.D) {
                        this.f();
                    }
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            this.D = false;
        };
    }

    protected boolean c(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, KeyEvent keyEvent0) {
        return false;
    }

    protected boolean d(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, MotionEvent motionEvent0) {
        return false;
    }

    protected abstract void e();

    protected abstract void f();

    void g(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
        try {
            this.z.j0(xC_MethodHook$MethodHookParam0);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    // 检测为 Lambda 实现
    private void h() [...]

    // 检测为 Lambda 实现
    private void i() [...]

    final boolean j(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, KeyEvent keyEvent0) {
        return this.D ? this.c(xC_MethodHook$MethodHookParam0, keyEvent0) : this.c(xC_MethodHook$MethodHookParam0, keyEvent0);
    }

    final boolean k(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, MotionEvent motionEvent0) {
        return this.D ? this.d(xC_MethodHook$MethodHookParam0, motionEvent0) : this.d(xC_MethodHook$MethodHookParam0, motionEvent0);
    }

    protected void l(long v) {
        this.D = true;
        this.A.postDelayed(this.B, v);
    }

    protected void m(long v) {
        this.D = true;
        this.A.postDelayed(this.C, v);
    }

    protected void n() {
        if(this.D) {
            this.D = false;
            this.A.removeCallbacks(this.B);
        }
    }

    protected void o() {
        if(this.D) {
            this.D = false;
            this.A.removeCallbacks(this.C);
        }
    }
}


package e;

import android.app.Activity;
import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.InputFilter.LengthFilter;
import android.text.InputFilter;
import android.text.Spanned;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import g.x;

public class y extends b {
    private TextView C;
    private EditText D;

    public static String A(CharSequence charSequence0, int v, int v1, Spanned spanned0, int v2, int v3) {
        StringBuilder stringBuilder0 = new StringBuilder(spanned0.length() - v3 + v2 + v1 - v);
        stringBuilder0.append(spanned0);
        stringBuilder0.replace(v2, v3, charSequence0.subSequence(v, v1).toString());
        return stringBuilder0.toString();
    }

    private static boolean B(String s) {
        for(int v = s.length() - 1; v >= 0; --v) {
            if("/\\:*?\"<>|".indexOf(s.charAt(v)) >= 0) {
                return false;
            }
        }
        return true;
    }

    public static boolean C(String s) {
        try {
            Integer.parseInt(s);
            return true;
        }
        catch(NumberFormatException unused_ex) {
            return false;
        }
    }

    // 检测为 Lambda 实现
    private static CharSequence D(CharSequence charSequence0, int v, int v1, Spanned spanned0, int v2, int v3) [...]

    // 检测为 Lambda 实现
    private static CharSequence E(CharSequence charSequence0, int v, int v1, Spanned spanned0, int v2, int v3) [...]

    // 检测为 Lambda 实现
    private static CharSequence F(CharSequence charSequence0, int v, int v1, Spanned spanned0, int v2, int v3) [...]

    // 检测为 Lambda 实现
    private static CharSequence G(CharSequence charSequence0, int v, int v1, Spanned spanned0, int v2, int v3) [...]

    // 检测为 Lambda 实现
    private static CharSequence H(CharSequence charSequence0, int v, int v1, Spanned spanned0, int v2, int v3) [...]

    // 检测为 Lambda 实现
    private void I(DialogInterface dialogInterface0, int v) [...]

    public y J(CharSequence charSequence0, CharSequence charSequence1, CharSequence charSequence2, int v, int v1) {
        Bundle bundle0 = this.e();
        bundle0.putCharSequence("title", charSequence0);
        bundle0.putCharSequence("description", charSequence1);
        bundle0.putCharSequence("text", charSequence2);
        bundle0.putInt("input_type", v);
        bundle0.putInt("max_length", v1);
        return this;
    }

    public y K(CharSequence charSequence0, CharSequence charSequence1, CharSequence charSequence2) {
        return this.J(charSequence0, charSequence1, charSequence2, 3, 0x40);
    }

    public static void L(x x0, TextView textView0, EditText editText0) {
        if(textView0 != null) {
            textView0.setTextSize(0, ((float)x0.d));
        }
        if(editText0 != null) {
            editText0.setTextSize(0, ((float)x0.e));
        }
    }

    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        w w0;
        Bundle bundle1 = this.e();
        Activity activity0 = this.getActivity();
        LinearLayout linearLayout0 = new LinearLayout(activity0);
        linearLayout0.setOrientation(1);
        x x0 = this.g();
        linearLayout0.setPadding(x0.f, x0.f, x0.f, x0.f);
        TextView textView0 = new TextView(activity0);
        this.C = textView0;
        textView0.setText(bundle1.getCharSequence("description"));
        linearLayout0.addView(this.C);
        EditText editText0 = new EditText(activity0);
        this.D = editText0;
        this.r(editText0);
        switch(bundle1.getInt("input_type")) {
            case 1: {
                this.D.setInputType(2);
                w0 = null;
                break;
            }
            case 2: {
                this.D.setInputType(1);
                w0 = (CharSequence charSequence0, int v, int v1, Spanned spanned0, int v2, int v3) -> {
                    if(v == v1) {
                        return null;
                    }
                    while(v < v1) {
                        int v4 = charSequence0.charAt(v);
                        if((v4 < 65 || v4 > 90) && (v4 < 97 || v4 > 0x7A) && (v4 < 0x30 || v4 > 57) && v4 != 0x5F && v4 != 36) {
                            return "";
                        }
                        ++v;
                    }
                    return null;
                };
                break;
            }
            case 3: {
                this.D.setInputType(1);
                w0 = null;
                break;
            }
            case 4: {
                this.D.setInputType(0x20001);
                w0 = (CharSequence charSequence0, int v, int v1, Spanned spanned0, int v2, int v3) -> {
                    if(v == v1) {
                        return null;
                    }
                    while(v < v1) {
                        int v4 = charSequence0.charAt(v);
                        if(v4 > 0 && v4 < 0x80) {
                            ++v;
                            continue;
                        }
                        return "";
                    }
                    return null;
                };
                break;
            }
            case 5: {
                this.D.setInputType(0x20001);
                w0 = null;
                break;
            }
            case 6: {
                this.D.setInputType(1);
                w0 = (CharSequence charSequence0, int v, int v1, Spanned spanned0, int v2, int v3) -> {
                    if(v == v1) {
                        return null;
                    }
                    if(v2 == 0 && charSequence0.length() > 0) {
                        int v4 = charSequence0.charAt(0);
                        if(0x30 <= v4 && v4 <= 57) {
                            return "";
                        }
                    }
                    while(v < v1) {
                        int v5 = charSequence0.charAt(v);
                        if((v5 < 65 || v5 > 90) && (v5 < 97 || v5 > 0x7A) && (v5 < 0x30 || v5 > 57) && v5 != 0x5F && v5 != 36) {
                            return "";
                        }
                        ++v;
                    }
                    return null;
                };
                break;
            }
            case 7: {
                this.D.setInputType(2);
                w0 = (CharSequence charSequence0, int v, int v1, Spanned spanned0, int v2, int v3) -> (v != v1 && !y.C(y.A(charSequence0, v, v1, spanned0, v2, v3)) ? "" : null);
                break;
            }
            case 8: {
                this.D.setInputType(1);
                w0 = (CharSequence charSequence0, int v, int v1, Spanned spanned0, int v2, int v3) -> (v != v1 && !y.B(charSequence0.subSequence(v, v1).toString()) ? "" : null);
                break;
            }
            default: {
                w0 = null;
            }
        }
        InputFilter[] arr_inputFilter = w0 == null ? new InputFilter[]{new InputFilter.LengthFilter(bundle1.getInt("max_length"))} : new InputFilter[]{new InputFilter.LengthFilter(bundle1.getInt("max_length")), w0};
        this.D.setFilters(arr_inputFilter);
        CharSequence charSequence0 = bundle1.getCharSequence("text", "");
        this.D.setText(charSequence0);
        this.D.setSelection(charSequence0.length());
        linearLayout0.addView(this.D);
        y.L(x0, this.C, this.D);
        return new AlertDialog.Builder(activity0).setTitle(bundle1.getCharSequence("title")).setView(linearLayout0).setPositiveButton(0x104000A, (DialogInterface dialogInterface0, int v) -> {
            Bundle bundle0 = new Bundle(1);
            bundle0.putCharSequence("result", this.D.getText());
            this.o(bundle0);
        }).setNegativeButton(0x1040000, b.B).create();
    }

    @Override  // e.j0$b
    public void onSaveInstanceState(Bundle bundle0) {
        super.onSaveInstanceState(bundle0);
        Bundle bundle1 = this.e();
        bundle1.putCharSequence("description", this.C.getText());
        bundle1.putCharSequence("text", this.D.getText());
    }
}


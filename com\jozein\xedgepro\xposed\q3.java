package com.jozein.xedgepro.xposed;

import android.graphics.Rect;
import android.os.Build.VERSION;
import android.os.SystemClock;
import android.util.SparseLongArray;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityNodeInfo;
import android.view.accessibility.AccessibilityNodeProvider;
import android.webkit.WebView;
import android.widget.ImageView;
import android.widget.TextView;
import de.robv.android.xposed.XposedHelpers;
import f.v;
import g.a0;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

class q3 {
    static class a implements Comparable {
        final CharSequence A;
        final String B;
        final View C;
        final int z;

        a(int v, CharSequence charSequence0, String s) {
            this.z = v;
            this.A = charSequence0;
            this.B = s;
            this.C = null;
        }

        a(ViewGroup viewGroup0) {
            this.z = viewGroup0 instanceof WebView || viewGroup0.getClass().getName().contains("WebView") ? 1 : 8;
            this.A = null;
            this.B = viewGroup0.getClass().getName();
            this.C = viewGroup0;
        }

        public int a(a q3$a0) {
            return this.z - q3$a0.z;
        }

        String b() {
            return q3.c(this.A);
        }

        @Override
        public int compareTo(Object object0) {
            return this.a(((a)object0));
        }

        @Override
        public String toString() {
            return "Node{" + q3.b(this.z) + ", " + this.B + ", [" + this.A + "]}";
        }
    }

    private static final Rect a;
    private static int b;
    private static long c;
    private static Method d;
    private static Method e;
    private static int f;
    private static int g;

    static {
        q3.a = new Rect();
        q3.d = null;
        q3.e = null;
        q3.f = 0;
        q3.g = 0;
    }

    private static String b(int v) {
        switch(v) {
            case 0: {
                return "T";
            }
            case 1: {
                return "W";
            }
            case 8: {
                return "V";
            }
            default: {
                StringBuilder stringBuilder0 = new StringBuilder();
                if((v & 16) != 0) {
                    stringBuilder0.append('o');
                }
                if((v & 4) != 0) {
                    stringBuilder0.append('u');
                }
                if((v & 2) != 0) {
                    stringBuilder0.append('d');
                }
                return stringBuilder0.toString();
            }
        }
    }

    static String c(CharSequence charSequence0) {
        if(charSequence0 == null) {
            return null;
        }
        if(charSequence0.length() > 0x10000) {
            charSequence0 = charSequence0.subSequence(0, 0x10000);
        }
        return charSequence0.toString();
    }

    private static void d(AccessibilityNodeInfo accessibilityNodeInfo0, List list0) {
        if(accessibilityNodeInfo0 == null) {
            v.c("node == null");
            return;
        }
        int v = 0;
        String s = accessibilityNodeInfo0.getClassName().toString();
        if(s.contains("Image")) {
            return;
        }
        if(!s.contains("Text") && !accessibilityNodeInfo0.isEditable()) {
            v = 4;
        }
        CharSequence charSequence0 = accessibilityNodeInfo0.getText();
        if(!q3.l(charSequence0)) {
            charSequence0 = accessibilityNodeInfo0.getContentDescription();
            if(q3.l(charSequence0)) {
                v |= 2;
                list0.add(new a(v, charSequence0, s));
                return;
            }
            return;
        }
        list0.add(new a(v, charSequence0, s));
    }

    static a e(View view0, int v, int v1) {
        if(view0 != null) {
            q3.b = 0;
            q3.c = SystemClock.uptimeMillis();
            ArrayList arrayList0 = new ArrayList();
            q3.h(view0, v, v1, arrayList0);
            return q3.m(arrayList0);
        }
        return null;
    }

    static a f(AccessibilityNodeInfo accessibilityNodeInfo0, int v, int v1) {
        if(accessibilityNodeInfo0 != null) {
            q3.b = 0;
            q3.c = SystemClock.uptimeMillis();
            ArrayList arrayList0 = new ArrayList();
            q3.i(accessibilityNodeInfo0, v, v1, arrayList0);
            return q3.m(arrayList0);
        }
        return null;
    }

    static a g(a q3$a0, int v, int v1) {
        if(q3$a0.C != null) {
            q3.b = 0;
            q3.c = SystemClock.uptimeMillis();
            AccessibilityNodeProvider accessibilityNodeProvider0 = q3$a0.C.getAccessibilityNodeProvider();
            if(accessibilityNodeProvider0 == null) {
                return q3$a0;
            }
            AccessibilityNodeInfo accessibilityNodeInfo0 = accessibilityNodeProvider0.createAccessibilityNodeInfo(-1);
            if(accessibilityNodeInfo0 == null) {
                return q3$a0;
            }
            ArrayList arrayList0 = new ArrayList();
            q3.j(accessibilityNodeProvider0, accessibilityNodeInfo0, v, v1, arrayList0);
            return q3.m(arrayList0);
        }
        return q3$a0;
    }

    private static void h(View view0, int v, int v1, List list0) {
        if(view0 == null) {
            v.c("view == null");
            return;
        }
        ++q3.b;
        if(view0.getVisibility() == 0 && view0.getAlpha() > 0.0f && (Build.VERSION.SDK_INT < 29 || view0.getTransitionAlpha() > 0.0f)) {
            view0.getGlobalVisibleRect(q3.a);
            if(!q3.k(v, v1, q3.a)) {
                return;
            }
            if(view0 instanceof ViewGroup) {
                int v2 = ((ViewGroup)view0).getChildCount();
                for(int v3 = v2 - 1; v3 >= 0; --v3) {
                    q3.h(((ViewGroup)view0).getChildAt(v3), v, v1, list0);
                }
                if(v2 == 0) {
                    list0.add(new a(((ViewGroup)view0)));
                }
                return;
            }
            if(view0 instanceof ImageView) {
                return;
            }
            if(view0 instanceof TextView) {
                CharSequence charSequence0 = ((TextView)view0).getText();
                if(q3.l(charSequence0)) {
                    list0.add(new a(0, charSequence0, view0.getClass().getName()));
                }
            }
            else {
                if(view0.onCheckIsTextEditor() || view0.getClass().getName().contains("Text")) {
                    try {
                        CharSequence charSequence1 = (CharSequence)XposedHelpers.callMethod(view0, "getText", new Object[0]);
                        if(charSequence1 != null) {
                            list0.add(new a(0, charSequence1, view0.getClass().getName()));
                            return;
                        }
                    }
                    catch(Throwable throwable0) {
                        v.c(throwable0.toString());
                    }
                }
                q3.d(view0.createAccessibilityNodeInfo(), list0);
            }
        }
    }

    private static void i(AccessibilityNodeInfo accessibilityNodeInfo0, int v, int v1, List list0) {
        if(accessibilityNodeInfo0 == null) {
            return;
        }
        ++q3.b;
        if(!accessibilityNodeInfo0.isVisibleToUser()) {
            return;
        }
        accessibilityNodeInfo0.getBoundsInScreen(q3.a);
        if(!q3.k(v, v1, q3.a)) {
            return;
        }
        int v2 = accessibilityNodeInfo0.getChildCount();
        if(v2 > 0) {
            for(int v3 = v2 - 1; v3 >= 0; --v3) {
                q3.i(accessibilityNodeInfo0.getChild(v3), v, v1, list0);
            }
            return;
        }
        q3.d(accessibilityNodeInfo0, list0);
    }

    private static void j(AccessibilityNodeProvider accessibilityNodeProvider0, AccessibilityNodeInfo accessibilityNodeInfo0, int v, int v1, List list0) {
        long v4;
        if(accessibilityNodeInfo0 == null) {
            v.c("node == null");
            return;
        }
        ++q3.b;
        if(!accessibilityNodeInfo0.isVisibleToUser()) {
            return;
        }
        accessibilityNodeInfo0.getBoundsInScreen(q3.a);
        if(!q3.k(v, v1, q3.a)) {
            return;
        }
        int v2 = accessibilityNodeInfo0.getChildCount();
        if(v2 > 0) {
            SparseLongArray sparseLongArray0 = null;
            for(int v3 = v2 - 1; v3 >= 0; --v3) {
                if(Build.VERSION.SDK_INT >= 21) {
                    if(q3.d == null) {
                        q3.d = XposedHelpers.findMethodExact(AccessibilityNodeInfo.class, "getChildId", new Class[]{Integer.TYPE});
                    }
                    v4 = (long)(((Long)q3.d.invoke(accessibilityNodeInfo0, v3)));
                }
                else {
                    if(sparseLongArray0 == null) {
                        if(q3.e == null) {
                            q3.e = XposedHelpers.findMethodExact(AccessibilityNodeInfo.class, "getChildNodeIds", new Class[0]);
                        }
                        sparseLongArray0 = (SparseLongArray)q3.e.invoke(accessibilityNodeInfo0);
                    }
                    v4 = sparseLongArray0.get(v3);
                }
                q3.j(accessibilityNodeProvider0, accessibilityNodeProvider0.createAccessibilityNodeInfo(((int)(v4 >> 0x20))), v, v1, list0);
            }
            return;
        }
        q3.d(accessibilityNodeInfo0, list0);
    }

    private static boolean k(int v, int v1, Rect rect0) {
        if(q3.f <= 0) {
            float f = a0.j();
            q3.g = (int)(f / 10.0f);
            q3.f = (int)(f / 80.0f);
        }
        int v2 = rect0.top;
        int v3 = rect0.bottom;
        if(v2 > v3 && v3 - v2 < q3.g) {
            v2 -= q3.f;
            v3 += q3.f;
        }
        return v >= rect0.left && v <= rect0.right && v1 >= v2 && v1 <= v3;
    }

    static boolean l(CharSequence charSequence0) {
        if(charSequence0 == null) {
            return false;
        }
        switch(charSequence0.length()) {
            case 0: {
                return false;
            }
            case 1: {
                int v = charSequence0.charAt(0);
                return v > 0x20 && v <= 0x7E || v > 0xA0;
            }
            case 2: {
                return charSequence0.charAt(0) != 0x20 || charSequence0.charAt(1) != 0x20;
            }
            default: {
                return true;
            }
        }
    }

    private static a m(List list0) {
        int v = list0.size();
        if(v > 0) {
            if(v > 1) {
                Collections.sort(list0);
            }
            return (a)list0.get(0);
        }
        return null;
    }
}


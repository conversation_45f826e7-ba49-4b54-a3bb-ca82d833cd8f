package d;

import a.b.a0;
import a.b.b0;
import a.b.b1;
import a.b.c3;
import a.b.d0;
import a.b.d;
import a.b.f2;
import a.b.g0;
import a.b.g;
import a.b.h3;
import a.b.h;
import a.b.p2;
import a.b.q1;
import a.b.u1;
import a.b.u2;
import a.b.z0;
import a.b;
import android.app.ActionBar;
import android.os.Bundle;
import c.k;
import c.r;
import c.s;
import c.v;
import com.jozein.xedgepro.ui.ActivityPerformAction;
import e.j;
import e.m;
import e.q;
import e.y;
import e.z;
import f.l0;
import f.l;
import java.util.ArrayList;

public abstract class c extends j {
    private int[] B1(int v, boolean z) {
        ArrayList arrayList0 = new ArrayList();
        if(z) {
            switch(v) {
                case 54: 
                case 72: 
                case 76: 
                case 88: 
                case 100: 
                case 101: 
                case 105: {
                    arrayList0.add(0x7F0600EE);  // string:edit_action "Edit action"
                    break;
                }
                default: {
                    if(v == 61 || v == 62 || v == 0x3F || (v == 107 || v == 108 || v == 109 || v == 110 || v == 0x6F)) {
                        arrayList0.add(0x7F0600EE);  // string:edit_action "Edit action"
                    }
                }
            }
            if(l.r && this instanceof a) {
                arrayList0.add(0x7F0600EF);  // string:edit_icon_and_label "Edit icon and label"
            }
        }
        arrayList0.add(0x1040001);
        if(z && ((b)this.r().o().getParcelable("clipboard")) != null) {
            arrayList0.add(0x104000B);
        }
        switch(v) {
            case 0: 
            case 1: 
            case 51: 
            case 68: 
            case 106: 
            case 107: {
                break;
            }
            default: {
                arrayList0.add(0x7F06016F);  // string:perform "Perform"
                arrayList0.add(0x7F06017D);  // string:put_on_launcher "Put on launcher"
                arrayList0.add(0x7F0600FB);  // string:export_as_command "Export as command"
            }
        }
        arrayList0.add(0x7F06002A);  // string:action_info "Action info"
        int v1 = arrayList0.size();
        int[] arr_v = new int[v1];
        for(int v2 = 0; v2 < v1; ++v2) {
            arr_v[v2] = (int)(((Integer)arrayList0.get(v2)));
        }
        return arr_v;
    }

    protected abstract void C1(Bundle arg1, int arg2);

    private void D1(b b0, int v) {
        t t0;
        m m0;
        switch(b0.z) {
            case 54: {
                y y0 = new y();
                y0.J(this.u(0x7F0600F7), this.u(0x7F060204), Integer.toString(((g0)b0).I), 1, 9);  // string:enter_duration "Enter duration"
                this.N(y0, 0x10002);
                return;
            }
            case 61: {
                m0 = new c.j().E(((b0)b0).J, ((b0)b0).I, false);
                break;
            }
            case 62: {
                t0 = new o().K1(((d0)b0).I, ((d0)b0).J, ((d0)b0).K);
                this.P(t0, v);
                return;
            }
            case 0x3F: {
                b[] arr_b = ((q1)b0).I;
                if(arr_b != null) {
                    b[] arr_b1 = new b[arr_b.length];
                    System.arraycopy(arr_b, 0, arr_b1, 0, arr_b.length);
                    t0 = new s0().S1(arr_b1, true);
                    this.P(t0, v);
                    return;
                }
                return;
            }
            case 72: {
                Bundle bundle0 = this.h();
                t0 = new t().K1(((d)b0), bundle0.getInt("flag"));
                v = bundle0.getInt("code");
                this.P(t0, v);
                return;
            }
            case 88: {
                m0 = new v().A(((p2)b0).I, ((p2)b0).J, ((p2)b0).K);
                break;
            }
            case 100: {
                m0 = new c.j().E(((a0)b0).K, ((a0)b0).I, ((a0)b0).J);
                break;
            }
            case 101: {
                t0 = new p1().M1(((f2)b0).I, ((f2)b0).J);
                this.P(t0, v);
                return;
            }
            case 105: {
                m0 = new k().D(((z0)b0).I, ((z0)b0).J, ((z0)b0).K);
                break;
            }
            case 107: {
                z1 z10 = new z1();
                z10.L1(((h3)b0).I);
                z10.b0(this.h().getCharSequence("next_sub_title"));
                this.P(z10, v);
                return;
            }
            case 108: {
                m0 = new c.o().M(((b1)b0).I);
                break;
            }
            case 109: {
                m0 = new c.m().z(((u1)b0).J, ((u1)b0).I);
                break;
            }
            case 110: {
                m0 = new r().y(((c3)b0).I);
                break;
            }
            case 0x6F: {
                m0 = new s().M(((u2)b0).I);
                break;
            }
            case 76: 
            case 0x72: {
                m0 = new m().u(this.u(0x7F060180));  // string:remove_activity_params "Remove activity parameters"
                v = 0x10000;
                break;
            }
            default: {
                return;
            }
        }
        this.N(m0, v);
    }

    protected void E1(int v, int v1) {
        ActionBar actionBar0 = this.getActivity().getActionBar();
        CharSequence charSequence0 = actionBar0 == null ? null : actionBar0.getTitle();
        this.P(new d.d().H1(v, charSequence0), v1);
    }

    protected void F1(b b0, int v, int v1) {
        this.G1(b0, v, v1, true);
    }

    protected void G1(b b0, int v, int v1, boolean z) {
        Bundle bundle0 = this.h();
        bundle0.putParcelable("action", b0);
        bundle0.putInt("code", v);
        bundle0.putInt("flag", v1);
        b b1 = (b)this.r().o().getParcelable("clipboard");
        int[] arr_v = this.B1(b0.z, z);
        CharSequence[] arr_charSequence = new CharSequence[arr_v.length];
        for(int v2 = 0; v2 < arr_v.length; ++v2) {
            arr_charSequence[v2] = this.u(arr_v[v2]);
            if(arr_v[v2] == 0x104000B && b1 != null) {
                arr_charSequence[v2] = arr_charSequence[v2] + "(" + b1.n(this.getActivity()) + ')';
            }
        }
        bundle0.putIntArray("items", arr_v);
        this.N(new z().u(arr_charSequence), 0xFFFF);
    }

    protected void H1(CharSequence charSequence0) {
        this.h().putCharSequence("next_sub_title", charSequence0);
    }

    @Override  // e.j0$c
    protected final void J(Bundle bundle0, int v) {
        c.b b3;
        if(bundle0 == null) {
            return;
        }
        b b0 = null;
        switch(v) {
            case 0xFFFF: {
                int v1 = bundle0.getInt("result", -1);
                if(v1 >= 0) {
                    Bundle bundle1 = this.h();
                    int[] arr_v = bundle1.getIntArray("items");
                    if(arr_v != null) {
                        int v2 = arr_v[v1];
                        if(v2 == 0x104000B) {
                            b b1 = (b)this.r().o().getParcelable("clipboard");
                            if(b1 == null) {
                                return;
                            }
                            bundle1.putParcelable("result", b1);
                            this.C1(bundle1, bundle1.getInt("code"));
                            return;
                        }
                        b b2 = (b)bundle1.getParcelable("action");
                        if(b2 == null) {
                            return;
                        }
                        switch(v2) {
                            case 0x1040001: {
                                this.r().o().putParcelable("clipboard", b2);
                                return;
                            }
                            case 0x7F06002A: {  // string:action_info "Action info"
                                b3 = new c.b().w(b2);
                                break;
                            }
                            case 0x7F0600EE: {  // string:edit_action "Edit action"
                                this.D1(b2, bundle1.getInt("code"));
                                return;
                            }
                            case 0x7F0600EF: {  // string:edit_icon_and_label "Edit icon and label"
                                if(b2.z != 72) {
                                    b2 = new d(b2);
                                }
                                this.D1(b2, bundle1.getInt("code"));
                                return;
                            }
                            case 0x7F0600FB: {  // string:export_as_command "Export as command"
                                try {
                                    a.z z0 = this.g().h();
                                    if(z0 == null || !z0.o(0x20)) {
                                        b0 = "#" + this.u(0x7F060170);  // string:perform_by_broadcast_not_allowed "Perform by broadcast not allowed!"
                                    }
                                }
                                catch(Throwable throwable0) {
                                    f.v.d(throwable0);
                                }
                                b3 = new q().w(b2.n(this.f()), b.g(b2), ((CharSequence)b0));
                                break;
                            }
                            case 0x7F06016F: {  // string:perform "Perform"
                                ActivityPerformAction.a(this.f(), b2);
                                return;
                            }
                            case 0x7F06017D: {  // string:put_on_launcher "Put on launcher"
                                l0.i(this.f(), b2);
                                return;
                            }
                            default: {
                                return;
                            }
                        }
                        this.N(b3, 0x10001);
                        return;
                    }
                }
                break;
            }
            case 0x10000: {
                bundle0 = this.h();
                b b4 = (b)bundle0.getParcelable("action");
                if(b4 instanceof g) {
                    b0 = ((g)b4).B();
                }
                else if(b4 instanceof h) {
                    b0 = ((h)b4).B();
                }
                if(b0 != null) {
                    bundle0.putParcelable("result", b0);
                    v = bundle0.getInt("code");
                    this.C1(bundle0, v);
                    return;
                }
                break;
            }
            case 0x10001: {
                break;
            }
            case 0x10002: {
                CharSequence charSequence0 = bundle0.getCharSequence("result");
                if(charSequence0 != null) {
                    try {
                        int v3 = Integer.parseInt(charSequence0.toString());
                        Bundle bundle2 = this.h();
                        bundle2.putParcelable("result", new g0(v3));
                        this.C1(bundle2, bundle2.getInt("code"));
                    }
                    catch(NumberFormatException numberFormatException0) {
                        f.v.d(numberFormatException0);
                    }
                    return;
                }
                break;
            }
            default: {
                this.C1(bundle0, v);
            }
        }
    }
}


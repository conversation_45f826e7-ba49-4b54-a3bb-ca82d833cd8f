package d;

import a.b.s0;
import a.m;
import a.p.e;
import a.p.h;
import a.z;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.CompoundButton;
import c.n;
import com.jozein.xedgepro.ApplicationMain;
import e.b0;
import e.g0;
import e.j.g;
import e.j.k;
import e.j;
import e.o;
import java.util.Locale;

public class o1 extends j {
    private z M;
    private CharSequence[] N;
    private CharSequence[] O;
    private CharSequence[] P;
    private CharSequence[] Q;

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F06017B);  // string:preferences "Preferences"
    }

    @Override  // e.j
    protected int B0() {
        this.M = this.g().h();
        CharSequence[] arr_charSequence = new CharSequence[3];
        arr_charSequence[0] = this.u(0x7F06012A);  // string:important "Important"
        arr_charSequence[1] = this.u(0x7F060104);  // string:general "General"
        arr_charSequence[2] = this.u(0x7F060217);  // string:verbose "Verbose"
        this.N = arr_charSequence;
        this.P = new CharSequence[]{this.u(0x7F06015B), this.u(0x7F06010F), this.u(0x7F060109), this.u(0x7F060088)};  // string:never "Never"
        this.O = new CharSequence[h.a.length];
        for(int v1 = 0; true; ++v1) {
            CharSequence[] arr_charSequence1 = this.O;
            if(v1 >= arr_charSequence1.length) {
                break;
            }
            arr_charSequence1[v1] = this.u(h.a[v1]);
        }
        this.Q = new CharSequence[e.a.length];
        for(int v = 0; true; ++v) {
            CharSequence[] arr_charSequence2 = this.Q;
            if(v >= arr_charSequence2.length) {
                break;
            }
            arr_charSequence2[v] = this.u(e.a[v]);
        }
        return 52;
    }

    @Override  // e.j0$c
    protected void J(Bundle bundle0, int v) {
        boolean z;
        ColorDrawable colorDrawable0;
        g j$g0;
        StringBuilder stringBuilder0;
        int v13;
        CharSequence charSequence1;
        k j$k1;
        CharSequence charSequence0;
        k j$k0;
        float f2;
        if(bundle0 == null) {
            return;
        }
        Context context0 = this.f();
        int v1 = 1;
        switch(v) {
            case 0: 
            case 1: {
                if(v != 0) {
                    v1 = 4;
                }
                m m0 = m.b();
                int v2 = m0.a(v1);
                int v3 = bundle0.getInt("result", v2);
                if(v2 != v3) {
                    try {
                        m0.f(v1, v3);
                        this.getActivity().recreate();
                    }
                    catch(Throwable throwable0) {
                        this.g0(throwable0);
                    }
                    return;
                }
                break;
            }
            case 2: {
                float f = bundle0.getFloat("result_inch", this.M.t(36, 0.12f));
                this.M.r0(context0, 36, f);
                float f1 = f <= 0.0f ? 0.12f : f;
                ((k)this.L0(2)).setSubText(this.V1(f1));
                f2 = this.M.t(37, f1);
                ((k)this.L0(3)).setSubText(this.V1(f2));
                s0.C(context0, 8, -1);
                return;
            }
            case 3: {
                f2 = bundle0.getFloat("result_inch", this.M.t(37, 0.12f));
                this.M.r0(context0, 37, f2);
                if(f2 <= 0.0f) {
                    f2 = this.M.t(36, 0.12f);
                }
                ((k)this.L0(3)).setSubText(this.V1(f2));
                s0.C(context0, 8, -1);
                return;
            }
            case 5: {
                float f3 = bundle0.getFloat("result_inch", this.M.t(38, 0.0f));
                this.M.r0(context0, 38, f3);
                if(f3 <= 0.0f) {
                    f3 = 0.3f;
                }
                j$k0 = (k)this.L0(5);
                charSequence0 = this.V1(f3);
                j$k0.setSubText(charSequence0);
                return;
            }
            case 8: {
                int v4 = bundle0.getInt("result", -1);
                if(v4 != -1) {
                    this.M.s0(context0, 10, v4 * 50);
                    j$k0 = (k)this.L0(8);
                    charSequence0 = this.v2(v4 * 50);
                    j$k0.setSubText(charSequence0);
                    return;
                }
                break;
            }
            case 9: {
                int v5 = bundle0.getInt("result", -1);
                if(v5 != -1) {
                    this.M.s0(context0, 11, v5 * 50);
                    j$k0 = (k)this.L0(9);
                    charSequence0 = this.v2(v5 * 50);
                    j$k0.setSubText(charSequence0);
                    return;
                }
                break;
            }
            case 10: {
                int v6 = bundle0.getInt("result", -1);
                if(v6 != -1) {
                    this.M.s0(context0, 12, v6 * 50);
                    j$k0 = (k)this.L0(10);
                    charSequence0 = this.v2(v6 * 50);
                    j$k0.setSubText(charSequence0);
                    return;
                }
                break;
            }
            case 11: {
                int v7 = bundle0.getInt("result", -1);
                if(v7 != -1) {
                    this.M.s0(context0, 13, v7 * 50);
                    j$k0 = (k)this.L0(11);
                    charSequence0 = this.v2(v7 * 50);
                    j$k0.setSubText(charSequence0);
                    return;
                }
                break;
            }
            case 13: {
                int v8 = bundle0.getInt("result", -1);
                if(v8 != -1) {
                    this.M.s0(context0, 29, v8 * 50);
                    j$k0 = (k)this.L0(13);
                    charSequence0 = this.u2(v8 * 50);
                    j$k0.setSubText(charSequence0);
                    return;
                }
                break;
            }
            case 14: {
                int v9 = bundle0.getInt("result", -1);
                if(v9 != -1) {
                    this.M.s0(context0, 27, v9);
                    j$k0 = (k)this.L0(14);
                    charSequence0 = this.t2(v9);
                    j$k0.setSubText(charSequence0);
                    return;
                }
                break;
            }
            case 15: {
                int v10 = bundle0.getInt("result", -1);
                if(v10 > 0) {
                    this.M.s0(context0, 17, v10 * 10);
                    j$k0 = (k)this.L0(15);
                    charSequence0 = Integer.toString(v10 * 10);
                    j$k0.setSubText(charSequence0);
                    return;
                }
                break;
            }
            case 17: {
                int v11 = bundle0.getInt("result", -1);
                if(v11 >= 0) {
                    this.M.p0(context0, v11);
                    s0.B(this.f(), 15);
                    j$k1 = (k)this.L0(17);
                    charSequence1 = Integer.toString(this.M.r());
                    j$k1.setSubText(charSequence1);
                    return;
                }
                break;
            }
            case 20: {
                int v12 = bundle0.getInt("result", -1);
                if(v12 != -1) {
                    v13 = v12 * 10;
                    this.M.s0(context0, 15, v13);
                    j$k0 = (k)this.L0(20);
                    stringBuilder0 = new StringBuilder();
                    goto label_125;
                }
                break;
            }
            case 21: {
                int v14 = o1.R1(bundle0.getInt("result"), -14509620);
                this.M.s0(context0, 16, v14);
                j$g0 = (g)this.L0(21);
                j$g0.setSubText(o1.T1(v14));
                colorDrawable0 = new ColorDrawable(v14);
                j$g0.setImageDrawable(colorDrawable0);
                return;
            }
            case 22: {
                int v15 = bundle0.getInt("result", -1);
                if(v15 != -1) {
                    v13 = v15 * 5;
                    this.M.s0(context0, 26, v13);
                    j$k0 = (k)this.L0(22);
                    stringBuilder0 = new StringBuilder();
                    goto label_125;
                }
                break;
            }
            case 23: {
                int v16 = o1.R1(bundle0.getInt("result"), -14509620);
                this.M.s0(context0, 4, v16);
                j$g0 = (g)this.L0(23);
                j$g0.setSubText(o1.T1(v16));
                colorDrawable0 = new ColorDrawable(v16);
                j$g0.setImageDrawable(colorDrawable0);
                return;
            }
            case 24: {
                int v17 = o1.R1(bundle0.getInt("result"), -1);
                this.M.s0(context0, 30, v17);
                j$g0 = (g)this.L0(24);
                j$g0.setSubText(o1.U1(v17));
                colorDrawable0 = new ColorDrawable(v17);
                j$g0.setImageDrawable(colorDrawable0);
                return;
            }
            case 25: {
                int v18 = bundle0.getInt("result", -1);
                if(v18 != -1) {
                    v13 = v18 * 5;
                    this.M.s0(context0, 28, v13);
                    j$k0 = (k)this.L0(25);
                    stringBuilder0 = new StringBuilder();
                label_125:
                    stringBuilder0.append(v13);
                    stringBuilder0.append("%");
                    charSequence0 = stringBuilder0.toString();
                    j$k0.setSubText(charSequence0);
                    return;
                }
                break;
            }
            case 26: {
                int v19 = o1.R1(bundle0.getInt("result"), 0xFF003037);
                this.M.s0(context0, 2, v19);
                j$g0 = (g)this.L0(26);
                j$g0.setSubText(o1.T1(v19));
                colorDrawable0 = new ColorDrawable(v19);
                j$g0.setImageDrawable(colorDrawable0);
                return;
            }
            case 27: {
                int v20 = o1.R1(bundle0.getInt("result"), -1);
                this.M.s0(context0, 0x1F, v20);
                j$g0 = (g)this.L0(27);
                j$g0.setSubText(o1.U1(v20));
                colorDrawable0 = new ColorDrawable(v20);
                j$g0.setImageDrawable(colorDrawable0);
                return;
            }
            case 29: {
                int v21 = o1.R1(bundle0.getInt("result"), 0xFF167C80);
                this.M.s0(context0, 0x20, v21);
                j$g0 = (g)this.L0(29);
                j$g0.setSubText(o1.U1(v21));
                colorDrawable0 = new ColorDrawable(v21);
                j$g0.setImageDrawable(colorDrawable0);
                return;
            }
            case 30: {
                int v22 = this.M.v(33, 0xFF003037);
                int v23 = o1.R1(bundle0.getInt("result"), v22);
                if(v22 != v23) {
                    this.M.s0(context0, 33, v23);
                    j$g0 = (g)this.L0(30);
                    j$g0.setSubText(o1.T1(v23));
                    colorDrawable0 = new ColorDrawable(v23);
                    j$g0.setImageDrawable(colorDrawable0);
                    return;
                }
                break;
            }
            case 0x1F: {
                int v24 = o1.R1(bundle0.getInt("result"), 0x55222200);
                this.M.v0(context0, v24);
                j$g0 = (g)this.L0(0x1F);
                j$g0.setSubText(o1.T1(v24));
                colorDrawable0 = new ColorDrawable(v24);
                j$g0.setImageDrawable(colorDrawable0);
                return;
            }
            case 0x20: {
                int v25 = o1.R1(bundle0.getInt("result"), 0x7F007FA7);
                this.M.s0(context0, 22, v25);
                j$g0 = (g)this.L0(0x20);
                j$g0.setSubText(o1.T1(v25));
                colorDrawable0 = new ColorDrawable(v25);
                j$g0.setImageDrawable(colorDrawable0);
                return;
            }
            case 33: {
                int v26 = o1.R1(bundle0.getInt("result"), 0xFF003037);
                this.M.s0(context0, 25, v26);
                j$g0 = (g)this.L0(33);
                j$g0.setSubText(o1.U1(v26));
                colorDrawable0 = new ColorDrawable(v26);
                j$g0.setImageDrawable(colorDrawable0);
                return;
            }
            case 37: {
                int v27 = this.M.v(23, 0);
                int v28 = bundle0.getInt("result", v27);
                if(v27 != v28) {
                    this.M.s0(context0, 23, v28);
                    j$k0 = (k)this.L0(37);
                    charSequence0 = v.M[v28];
                    j$k0.setSubText(charSequence0);
                    return;
                }
                break;
            }
            case 38: {
                int v29 = bundle0.getInt("result", -1);
                if(v29 >= 0) {
                    this.M.s0(this.f(), 34, v29);
                    int v30 = this.S1();
                    ((k)this.L0(38)).setSubText(this.O[v30]);
                    this.L0(39).setEnabled(v30 == 1);
                    return;
                }
                break;
            }
            case 39: {
                int v31 = bundle0.getInt("result", -1);
                if(v31 != -1) {
                    this.M.s0(context0, 6, v31);
                    ((k)this.L0(39)).setSubText(Integer.toString(this.M.v(6, 50)));
                    if(this.S1() == 1) {
                        s0.B(context0, 10);
                        return;
                    }
                }
                break;
            }
            case 41: {
                int v32 = bundle0.getInt("result");
                switch(v32) {
                    case 1: {
                        z = true;
                        v1 = 0;
                        break;
                    }
                    case 2: {
                        z = false;
                        break;
                    }
                    case 3: {
                        z = true;
                        break;
                    }
                    default: {
                        return;
                    }
                }
                this.M.o0(context0, 6, z);
                this.M.o0(context0, 7, ((boolean)v1));
                j$k0 = (k)this.L0(41);
                charSequence0 = this.P[v32];
                j$k0.setSubText(charSequence0);
                return;
            }
            case 42: {
                int v33 = bundle0.getInt("result", -1);
                if(v33 >= 0) {
                    this.M.s0(context0, 7, v33);
                    j$k1 = (k)this.L0(42);
                    charSequence1 = this.X1();
                    j$k1.setSubText(charSequence1);
                    return;
                }
                break;
            }
            case 43: {
                int v34 = bundle0.getInt("result", -1);
                if(v34 >= 0) {
                    this.x2(v34);
                    j$k1 = (k)this.L0(43);
                    charSequence1 = this.N[this.b2()];
                    j$k1.setSubText(charSequence1);
                    return;
                }
                break;
            }
            case 51: {
                int v35 = this.M.u(14);
                int v36 = bundle0.getInt("result", v35);
                if(v35 != v36) {
                    this.M.s0(context0, 14, v36);
                    s0.B(context0, 28);
                    ((k)this.L0(51)).setSubText(this.Z1());
                    return;
                }
                break;
            }
        }
    }

    private static int R1(int v, int v1) {
        return v == 0 ? v1 : v;
    }

    private int S1() {
        int v = this.M.v(34, 0);
        return v < h.a.length ? v : 1;
    }

    public static String T1(int v) {
        return String.format(Locale.ENGLISH, "#%08X", v);
    }

    public static String U1(int v) {
        return String.format(Locale.ENGLISH, "#%06X", ((int)(v & 0xFFFFFF)));
    }

    private CharSequence V1(float f) {
        return String.format(Locale.ENGLISH, "%.2f inch, %.2f mm.", f, ((float)(f * 25.4f)));
    }

    private CharSequence W1(int v, float f) {
        return this.V1(this.M.t(v, f));
    }

    private CharSequence X1() {
        int v = this.M.u(7);
        if(v == 0) {
            return this.u(0x7F06015B);  // string:never "Never"
        }
        StringBuilder stringBuilder0 = new StringBuilder();
        for(int v1 = 0; v1 < e.a.length; ++v1) {
            if((1 << v1 & v) != 0) {
                if(stringBuilder0.length() != 0) {
                    stringBuilder0.append(", ");
                }
                stringBuilder0.append(this.Q[v1]);
            }
        }
        return stringBuilder0.toString();
    }

    private CharSequence Y1(int v) {
        switch(v) {
            case 0: {
                return this.u(0x7F060099);  // string:colorful_circle_icon "Colorful circle icon"
            }
            case 1: {
                return this.u(0x7F060090);  // string:circle_icon "Circle icon"
            }
            default: {
                return this.u(0x7F0600CC);  // string:default_icon "Default icon"
            }
        }
    }

    private int Z1() {
        int v = this.M.u(14);
        if(v == 1) {
            return 0x7F060100;  // string:force_no_nav_bar "Force no navigation bar"
        }
        return v == 2 ? 0x7F0600FE : 0x7F0601F1;  // string:force_has_nav_bar "Force has navigation bar"
    }

    private CharSequence a2(int v) {
        switch(v) {
            case 1: {
                return this.u(0x7F0600CB);  // string:dark_theme "Dark"
            }
            case 2: {
                return this.u(0x7F060149);  // string:light_gray_theme "Light gray"
            }
            case 3: {
                return this.u(0x7F060087);  // string:black_theme "Black"
            }
            case 4: {
                return this.u(0x7F060083);  // string:auto_dark_mode "Automatic"
            }
            default: {
                return this.u(0x7F06014A);  // string:light_theme "Light"
            }
        }
    }

    private int b2() {
        if(this.M.o(25)) {
            return 2;
        }
        return this.M.o(24) ? 1 : 0;
    }

    private int c2() {
        if(this.M.o(6)) {
            return this.M.o(7) ? 3 : 1;
        }
        return this.M.o(7) ? 2 : 0;
    }

    // 检测为 Lambda 实现
    private void d2(Context context0, CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void e2(Context context0, CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void f2(Context context0, CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void g2(Context context0, CompoundButton compoundButton0, boolean z) [...]

    @Override  // e.j
    protected View h1(int v) {
        return this.w2(v);
    }

    // 检测为 Lambda 实现
    private void h2(Context context0, CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void i2(Context context0, CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void j2(Context context0, CompoundButton compoundButton0, boolean z) [...]

    @Override  // e.j
    protected void k1(int v) {
        int v13;
        d.z z0;
        g0 g00;
        int v1 = 1;
        int v2 = 0;
        switch(v) {
            case 0: {
                CharSequence[] arr_charSequence = {this.a2(0), this.a2(1), this.a2(2), this.a2(3), this.a2(4)};
                int v3 = m.b().a(1);
                if(v3 != 1 && v3 != 2 && v3 != 3 && v3 != 4) {
                    v3 = 0;
                }
                this.N(new e.z().v(arr_charSequence, v3), 0);
                return;
            }
            case 1: {
                CharSequence[] arr_charSequence1 = {this.Y1(0), this.Y1(1), this.Y1(2)};
                int v7 = m.b().a(4);
                if(v7 == 0 || v7 == 1 || v7 == 2) {
                    v2 = v7;
                }
                g00 = new e.z().v(arr_charSequence1, v2);
                break;
            }
            case 2: {
                this.N(new n().B(this.u(0x7F0600EC), this.M.t(36, 0.12f), 0.8f), 2);  // string:edge_width "Edge width"
                return;
            }
            case 3: {
                this.N(new n().B(this.u(0x7F0600DF), this.M.t(37, this.M.t(36, 0.12f)), 0.8f), 3);  // string:edge_height "Edge height"
                return;
            }
            case 5: {
                if(this.M.o(27)) {
                    this.N(new n().B(this.u(0x7F0600D5), this.M.t(38, 0.3f), 1.0f), 5);  // string:distance_to_trigger_swipe "Distance to trigger swipe"
                    return;
                }
                return;
            }
            case 8: {
                int v8 = this.M.u(10);
                if(v8 == 0) {
                    v8 = ViewConfiguration.getDoubleTapTimeout();
                }
                g00 = new g0().y(this.u(0x7F060106), null, 0, v8 / 50, 12, 50);  // string:gesture_double_tap_timeout "Gesture double tap timeout"
                v1 = 8;
                break;
            }
            case 9: {
                int v9 = this.M.u(11);
                if(v9 == 0) {
                    v9 = ViewConfiguration.getLongPressTimeout();
                }
                g00 = new g0().y(this.u(0x7F06010B), null, 0, v9 / 50, 20, 50);  // string:gesture_long_press_timeout "Gesture long press timeout"
                v1 = 9;
                break;
            }
            case 10: {
                int v10 = this.M.u(12);
                if(v10 == 0) {
                    v10 = ViewConfiguration.getDoubleTapTimeout();
                }
                g00 = new g0().y(this.u(0x7F060137), null, 0, v10 / 50, 12, 50);  // string:key_double_click_timeout "Double click timeout"
                v1 = 10;
                break;
            }
            case 11: {
                int v11 = this.M.u(13);
                if(v11 == 0) {
                    v11 = ViewConfiguration.getLongPressTimeout();
                }
                g00 = new g0().y(this.u(0x7F06013A), null, 0, v11 / 50, 20, 50);  // string:key_long_click_timeout "Long click timeout"
                v1 = 11;
                break;
            }
            case 13: {
                int v12 = this.M.v(29, 300);
                g00 = new g0().y(this.u(0x7F06013A), null, 2, v12 / 50, 20, 50);  // string:key_long_click_timeout "Long click timeout"
                v1 = 13;
                break;
            }
            case 14: {
                int v4 = this.M.u(27);
                this.N(new g0().x(this.u(0x7F0600FC), null, 0, v4, 60), 14);  // string:floating_widget_timeout "Floating widget timeout"
                return;
            }
            case 15: {
                g00 = new g0().y(this.u(0x7F06019D), null, 1, this.M.v(17, 100) / 10, 40, 10);  // string:scroll_speed "Scrolling speed of successive adjust"
                v1 = 15;
                break;
            }
            case 17: {
                if(!this.M.o(0x1F)) {
                    g00 = new g0().x(this.u(0x7F060094), null, 0, this.M.r(), 100);  // string:clip_history_size "Clipboard history size"
                    v1 = 17;
                    break;
                }
                return;
            }
            case 18: {
                z0 = new d.z();
                v13 = 18;
                this.P(z0, v13);
                return;
            }
            case 19: {
                z0 = new x0();
                v13 = 19;
                this.P(z0, v13);
                return;
            }
            case 20: {
                int v14 = this.M.v(15, 0xFA);
                g00 = new g0().y(this.u(0x7F06010E), null, 20, v14 / 10, 50, 10);  // string:gesture_pointer_scale "Gesture pointer scale"
                v1 = 20;
                break;
            }
            case 21: {
                int v15 = this.M.v(16, -14509620);
                g00 = new o().H(v15, 0xFF);
                v1 = 21;
                break;
            }
            case 22: {
                int v16 = this.M.v(26, 100);
                g00 = new g0().y(this.u(0x7F060178), null, 15, v16 / 5, 40, 5);  // string:pie_scale "Pie scale"
                v1 = 22;
                break;
            }
            case 23: {
                int v17 = this.M.v(4, -14509620);
                g00 = new o().H(v17, 0xFF);
                v1 = 23;
                break;
            }
            case 24: {
                int v18 = this.M.v(30, -1);
                g00 = new o().H(v18, 0);
                v1 = 24;
                break;
            }
            case 25: {
                int v19 = this.M.v(28, 100);
                g00 = new g0().y(this.u(0x7F06016D), null, 15, v19 / 5, 40, 5);  // string:panel_scale "Panel scale"
                v1 = 25;
                break;
            }
            case 26: {
                int v20 = this.M.v(2, 0xFF003037);
                g00 = new o().H(v20, 0xFF);
                v1 = 26;
                break;
            }
            case 27: {
                int v5 = this.M.v(0x1F, -1);
                this.N(new o().H(v5, 0), 27);
                return;
            }
            case 29: {
                if(this.M.o(42)) {
                    int v21 = this.M.v(0x20, 0xFF167C80);
                    this.N(new o().H(v21, 0), 29);
                    return;
                }
                return;
            }
            case 30: {
                int v22 = this.M.v(33, 0xFF003037);
                g00 = new o().H(v22, 0xFF);
                v1 = 30;
                break;
            }
            case 0x1F: {
                if(!ApplicationMain.isModuleActivated() && this.M.C() != 0) {
                    this.e0(0x7F060155);  // string:module_not_activated "Module not activated yet(or not updated)! Please activate 
                                          // this module in Xposed installer, and reboot."
                    return;
                }
                int v6 = this.M.B();
                this.N(new o().H(v6, 0xD0), 0x1F);
                return;
            }
            case 0x20: {
                int v23 = this.M.v(22, 0x7F007FA7);
                g00 = new o().H(v23, 0xFF);
                v1 = 0x20;
                break;
            }
            case 33: {
                int v24 = this.M.v(25, 0xFF003037);
                g00 = new o().H(v24, 0);
                v1 = 33;
                break;
            }
            case 34: {
                z0 = new v0();
                v13 = 34;
                this.P(z0, v13);
                return;
            }
            case 37: {
                if(this.M.o(4)) {
                    this.P(new v(), 37);
                    return;
                }
                return;
            }
            case 38: {
                g00 = new e.z().v(this.O, this.S1());
                v1 = 38;
                break;
            }
            case 39: {
                if(this.S1() == 1) {
                    int v25 = this.M.v(6, 50);
                    g00 = new g0().w(this.u(0x7F060220), null, v25, 100);  // string:vibrate_strength "Vibrate strength"
                    v1 = 39;
                    break;
                }
                return;
            }
            case 41: {
                g00 = new e.z().v(this.P, this.c2());
                v1 = 41;
                break;
            }
            case 42: {
                g00 = new b0().v(this.Q, this.M.u(7));
                v1 = 42;
                break;
            }
            case 43: {
                g00 = new e.z().v(this.N, this.b2());
                v1 = 43;
                break;
            }
            case 4: 
            case 6: 
            case 7: 
            case 12: 
            case 16: 
            case 28: 
            case 35: 
            case 36: 
            case 40: 
            case 44: 
            case 45: 
            case 46: 
            case 0x2F: 
            case 0x30: 
            case 49: 
            case 50: {
                ((e.j.j)this.L0(v)).h();
                return;
            }
            case 51: {
                g00 = new e.z().v(new CharSequence[]{this.u(0x7F0601F1), this.u(0x7F060100), this.u(0x7F0600FE)}, this.M.u(14));  // string:system_default "System default"
                v1 = 51;
                break;
            }
            default: {
                return;
            }
        }
        this.N(g00, v1);
    }

    // 检测为 Lambda 实现
    private void k2(Context context0, CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void l2(CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void m2(CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void n2(Context context0, CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void o2(Context context0, CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void p2(Context context0, CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void q2(Context context0, CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void r2(Context context0, CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void s2(Context context0, CompoundButton compoundButton0, boolean z) [...]

    private CharSequence t2(int v) {
        return v == 0 ? "0: " + this.u(0x7F06008A) : v + " s";  // string:by_two_finger "Only by two finger"
    }

    private CharSequence u2(int v) {
        if(v == 0) {
            v = 300;
        }
        return v + " ms";
    }

    private CharSequence v2(int v) {
        return v == 0 ? "0: " + this.u(0x7F0601F1) : v + " ms";  // string:system_default "System default"
    }

    protected k w2(int v) {
        Context context0 = this.M0();
        boolean z = false;
        switch(v) {
            case 0: {
                int v1 = m.b().a(1);
                return new k(this, this.u(0x7F0601F3), this.a2(v1));  // string:theme "Theme"
            }
            case 1: {
                int v2 = m.b().a(4);
                return new k(this, this.u(0x7F060127), this.Y1(v2));  // string:icon_style_in_app "Icon style in app"
            }
            case 2: {
                return new k(this, this.u(0x7F0600EC), this.W1(36, 0.12f));  // string:edge_width "Edge width"
            }
            case 3: {
                return new k(this, this.u(0x7F0600DF), this.W1(37, this.M.t(36, 0.12f)));  // string:edge_height "Edge height"
            }
            case 4: {
                k j$k0 = new e.j.j(this, this.u(0x7F0601EF), null, !this.M.o(27));  // string:swipe_triggers_after_finger_upped "Swipe triggers after finger upped"
                ((e.j.j)j$k0).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> {
                    this.M.o0(compoundButton0.getContext(), 27, !z);
                    ((k)this.L0(5)).setEnabled(!z);
                    this.e0((!z == 0 ? 0x7F060117 : 0x7F060118));  // string:gestures_triggers_when_swiping_off_hint "You can cancel a swipe by swipe 
                                                                   // back."
                });
                return j$k0;
            }
            case 5: {
                k j$k1 = new k(this, this.u(0x7F0600D5), this.W1(38, 0.3f));  // string:distance_to_trigger_swipe "Distance to trigger swipe"
                j$k1.setEnabled(this.M.o(27));
                return j$k1;
            }
            case 6: {
                k j$k2 = new e.j.j(this, this.u(0x7F0600D3), null, this.M.o(8));  // string:disable_gesture_on_soft_keyboard "Disable gesture on soft keyboard"
                ((e.j.j)j$k2).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> this.M.o0(context0, 8, z));
                return j$k2;
            }
            case 7: {
                k j$k3 = new e.j.j(this, this.u(0x7F0600D2), null, this.M.o(33));  // string:disable_gesture_on_keyguard "Disable gesture on keyguard"
                ((e.j.j)j$k3).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> this.M.o0(context0, 33, z));
                return j$k3;
            }
            case 8: {
                return new k(this, this.u(0x7F060106), this.v2(this.M.u(10)));  // string:gesture_double_tap_timeout "Gesture double tap timeout"
            }
            case 9: {
                return new k(this, this.u(0x7F06010B), this.v2(this.M.u(11)));  // string:gesture_long_press_timeout "Gesture long press timeout"
            }
            case 10: {
                return new k(this, this.u(0x7F060137), this.v2(this.M.u(12)));  // string:key_double_click_timeout "Double click timeout"
            }
            case 11: {
                return new k(this, this.u(0x7F06013A), this.v2(this.M.u(13)));  // string:key_long_click_timeout "Long click timeout"
            }
            case 12: {
                k j$k4 = new e.j.j(this, this.u(0x7F0601EE), null, this.M.o(40));  // string:sub_gesture_triggers_when_swiping "Sub gesture triggers when swiping"
                ((e.j.j)j$k4).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> this.M.o0(compoundButton0.getContext(), 40, z));
                return j$k4;
            }
            case 13: {
                return new k(this, this.u(0x7F060108), this.u2(this.M.v(29, 300)));  // string:gesture_hold_timeout "Hold timeout of sub gesture"
            }
            case 14: {
                return new k(this, this.u(0x7F0600FC), this.t2(this.M.u(27)));  // string:floating_widget_timeout "Floating widget timeout"
            }
            case 15: {
                return new k(this, this.u(0x7F06019D), Integer.toString(this.M.v(17, 100)));  // string:scroll_speed "Scrolling speed of successive adjust"
            }
            case 16: {
                k j$k5 = new e.j.j(this, this.u(0x7F060193), null, !this.M.o(0x1F));  // string:save_clip_data "Save clipboard data"
                ((e.j.j)j$k5).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> {
                    this.M.o0(context0, 0x1F, !z);
                    this.L0(17).setEnabled(z);
                    s0.B(compoundButton0.getContext(), 15);
                });
                return j$k5;
            }
            case 17: {
                k j$k6 = new k(this, this.u(0x7F060094), Integer.toString(this.M.r()));  // string:clip_history_size "Clipboard history size"
                j$k6.setEnabled(!this.M.o(0x1F));
                return j$k6;
            }
            case 18: {
                return new k(this, this.u(0x7F060023));  // string:action_game_mode "Game mode"
            }
            case 19: {
                return new k(this, this.u(0x7F06010D));  // string:gesture_pointer_modes "Gesture pointer modes"
            }
            case 20: {
                return new k(this, this.u(0x7F06010E), this.M.v(15, 0xFA) + "%");  // string:gesture_pointer_scale "Gesture pointer scale"
            }
            case 21: {
                int v3 = this.M.v(16, -14509620);
                return new g(this, this.u(0x7F06010C), o1.T1(v3), new ColorDrawable(v3));  // string:gesture_pointer_color "Gesture pointer color"
            }
            case 22: {
                return new k(this, this.u(0x7F060178), this.M.v(26, 100) + "%");  // string:pie_scale "Pie scale"
            }
            case 23: {
                int v4 = this.M.v(4, -14509620);
                return new g(this, this.u(0x7F060174), o1.T1(v4), new ColorDrawable(v4));  // string:pie_color "Pie color"
            }
            case 24: {
                int v5 = this.M.v(30, -1);
                return new g(this, this.u(0x7F060175), o1.U1(v5), new ColorDrawable(v5));  // string:pie_icon_color "Pie icon color"
            }
            case 25: {
                return new k(this, this.u(0x7F06016D), this.M.v(28, 100) + "%");  // string:panel_scale "Panel scale"
            }
            case 26: {
                int v6 = this.M.v(2, 0xFF003037);
                return new g(this, this.u(0x7F06016B), o1.T1(v6), new ColorDrawable(v6));  // string:panel_color "Panel color"
            }
            case 27: {
                int v7 = this.M.v(0x1F, -1);
                return new g(this, this.u(0x7F06016C), o1.U1(v7), new ColorDrawable(v7));  // string:panel_icon_color "Panel icon color"
            }
            case 28: {
                k j$k7 = new e.j.j(this, this.u(0x7F060091), null, this.M.o(42));  // string:circle_icon_on_panels "Circle icon on panels"
                ((e.j.j)j$k7).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> {
                    this.M.o0(context0, 42, z);
                    ((g)this.L0(29)).setEnabled(z);
                });
                return j$k7;
            }
            case 29: {
                int v8 = this.M.v(0x20, 0xFF167C80);
                k j$k8 = new g(this, this.u(0x7F06016A), o1.U1(v8), new ColorDrawable(v8));  // string:panel_circle_background_color "Panel circle background color"
                j$k8.setEnabled(this.M.o(42));
                return j$k8;
            }
            case 30: {
                int v9 = this.M.v(33, 0xFF003037);
                return new g(this, this.u(0x7F0600DA), o1.T1(v9), new ColorDrawable(v9));  // string:drawer_color "Drawer color"
            }
            case 0x1F: {
                int v10 = this.M.B();
                return new g(this, this.u(0x7F060199), o1.T1(v10), new ColorDrawable(v10));  // string:screen_filter_color "Screen filter color"
            }
            case 0x20: {
                int v11 = this.M.v(22, 0x7F007FA7);
                return new g(this, this.u(0x7F0601BD), o1.T1(v11), new ColorDrawable(v11));  // string:slide_adjust_color "Slide adjust color"
            }
            case 33: {
                int v12 = this.M.v(25, 0xFF003037);
                return new g(this, this.u(0x7F060095), o1.U1(v12), new ColorDrawable(v12));  // string:clipboard_color "Clipboard color"
            }
            case 34: {
                return new k(this, this.u(0x7F06016E));  // string:pen_colors "Pen colors"
            }
            case 35: {
                k j$k9 = new e.j.j(this, this.u(0x7F0600D6), this.u(0x7F0600D7), this.M.o(37));  // string:do_not_show_pen_toolbar_automatically "Do not show pen toolbar automatically"
                ((e.j.j)j$k9).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> this.M.o0(context0, 37, z));
                return j$k9;
            }
            case 36: {
                k j$k10 = new e.j.j(this, this.u(0x7F0601BF), null, this.M.o(4));  // string:sound_effect "Sound effect"
                ((e.j.j)j$k10).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> {
                    this.M.o0(context0, 4, z);
                    this.L0(37).setEnabled(z);
                });
                return j$k10;
            }
            case 37: {
                k j$k11 = new k(this, this.u(0x7F0600F2), v.M[this.M.v(23, 0)]);  // string:effect_sound "Effect sound"
                j$k11.setEnabled(this.M.o(4));
                return j$k11;
            }
            case 38: {
                return new k(this, this.u(0x7F06021E), this.O[this.S1()]);  // string:vibrate_effect "Vibrate effect"
            }
            case 39: {
                int v13 = this.M.v(6, 50);
                k j$k12 = new k(this, this.u(0x7F060220), Integer.toString(v13));  // string:vibrate_strength "Vibrate strength"
                if(this.S1() == 1) {
                    z = true;
                }
                j$k12.setEnabled(z);
                return j$k12;
            }
            case 40: {
                k j$k13 = new e.j.j(this, this.u(0x7F0600F1), null, this.M.o(9));  // string:effect_only_screen_on "Sound/vibrate only when screen on"
                ((e.j.j)j$k13).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> this.M.o0(context0, 9, z));
                return j$k13;
            }
            case 41: {
                return new k(this, this.u(0x7F060225), this.P[this.c2()]);  // string:visual_effect "Visual effect"
            }
            case 42: {
                return new k(this, this.u(0x7F060163), this.X1());  // string:notify_for_actions "Notify for actions"
            }
            case 43: {
                return new k(this, this.u(0x7F060207), this.N[this.b2()]);  // string:toast_level "Toast level"
            }
            case 44: {
                k j$k14 = new e.j.j(this, this.u(0x7F060234), this.u(0x7F060235), this.M.o(35));  // string:windows_not_focusable "Windows not focusable"
                ((e.j.j)j$k14).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> {
                    this.M.o0(context0, 35, z);
                    s0.F(context0, 19, z);
                });
                return j$k14;
            }
            case 45: {
                k j$k15 = new e.j.j(this, this.u(0x7F0601B7), null, this.M.o(10));  // string:show_text_on_shortcut_panel "Show text on shortcut panel"
                ((e.j.j)j$k15).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> this.M.o0(context0, 10, z));
                return j$k15;
            }
            case 46: {
                k j$k16 = new e.j.j(this, this.u(0x7F0601B8), null, this.M.o(26));  // string:show_text_on_side_bar "Show text on side bar"
                ((e.j.j)j$k16).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> this.M.o0(context0, 26, z));
                return j$k16;
            }
            case 0x2F: {
                k j$k17 = new e.j.j(this, this.u(0x7F0601B6), null, this.M.o(11));  // string:show_text_on_other_drawers "Show text on other drawers"
                ((e.j.j)j$k17).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> this.M.o0(context0, 11, z));
                return j$k17;
            }
            case 0x30: {
                k j$k18 = new e.j.j(this, this.u(0x7F0600D1), null, !this.M.o(34));  // string:disable_auto_brightness_when_brightness_adjusted "Disable auto brightness 
                                                                                     // when brightness adjusted"
                ((e.j.j)j$k18).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> this.M.o0(context0, 34, !z));
                return j$k18;
            }
            case 49: {
                k j$k19 = new e.j.j(this, this.u(0x7F060084), null, this.M.o(29));  // string:auto_freeze_only_app_died "Auto freeze only when app died"
                ((e.j.j)j$k19).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> this.M.o0(context0, 29, z));
                return j$k19;
            }
            case 50: {
                k j$k20 = new e.j.j(this, this.u(0x7F060074), null, this.M.o(0x20));  // string:allows_perform_by_broadcast "Allows perform by broadcast"
                ((e.j.j)j$k20).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> this.M.o0(context0, 0x20, z));
                return j$k20;
            }
            case 51: {
                return new k(this, 0x7F0600FF, this.Z1());  // string:force_nav_bar_state "Force navigation bar state(need reboot)"
            }
            default: {
                return null;
            }
        }
    }

    private void x2(int v) {
        Context context0 = this.f();
        switch(v) {
            case 1: {
                this.M.o0(context0, 24, true);
                break;
            }
            case 2: {
                this.M.o0(context0, 24, true);
                this.M.o0(context0, 25, true);
                return;
            }
            default: {
                this.M.o0(context0, 24, false);
                break;
            }
        }
        this.M.o0(context0, 25, false);
    }
}


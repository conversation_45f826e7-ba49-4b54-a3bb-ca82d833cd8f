package e;

import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface;

public final class p implements DialogInterface.OnClickListener {
    public final CharSequence A;
    public final q z;

    public p(q q0, CharSequence charSequence0) {
        this.z = q0;
        this.A = charSequence0;
    }

    @Override  // android.content.DialogInterface$OnClickListener
    public final void onClick(DialogInterface dialogInterface0, int v) {
        this.z.v(this.A, dialogInterface0, v);
    }
}


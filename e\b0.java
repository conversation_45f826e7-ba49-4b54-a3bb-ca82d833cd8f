package e;

import android.os.Bundle;

public class b0 extends c0 {
    @Override  // e.j0$b
    protected void o(Bundle bundle0) {
        if(bundle0 != null) {
            boolean[] arr_z = bundle0.getBooleanArray("result");
            if(arr_z != null) {
                int v1 = 0;
                for(int v = 0; v < arr_z.length; ++v) {
                    if(arr_z[v]) {
                        v1 |= 1 << v;
                    }
                }
                bundle0.putInt("result", v1);
                super.o(bundle0);
            }
        }
    }

    public b0 v(CharSequence[] arr_charSequence, int v) {
        boolean[] arr_z = new boolean[arr_charSequence.length];
        for(int v1 = 0; true; ++v1) {
            boolean z = true;
            if(v1 >= arr_charSequence.length) {
                break;
            }
            if((1 << v1 & v) == 0) {
                z = false;
            }
            arr_z[v1] = z;
        }
        this.u(arr_charSequence, arr_z);
        return this;
    }
}


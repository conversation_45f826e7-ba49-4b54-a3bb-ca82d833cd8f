package com.jozein.xedgepro.service;

import android.content.Intent;
import android.os.Handler;
import f.h0;

public class AdapterService extends a implements Runnable {
    private boolean F;

    public AdapterService() {
        this.F = false;
    }

    @Override  // com.jozein.xedgepro.service.a
    protected boolean d(Intent intent0) {
        if(BinderService.u(this, intent0)) {
            Handler handler0 = h0.a();
            if(this.F) {
                handler0.removeCallbacks(this);
            }
            this.F = true;
            handler0.postDelayed(this, 5000L);
        }
        return this.F;
    }

    @Override  // com.jozein.xedgepro.service.a
    public void onDestroy() {
        super.onDestroy();
        if(this.F) {
            this.F = false;
            h0.a().removeCallbacks(this);
        }
    }

    @Override
    public void run() {
        this.F = false;
        this.stopSelf();
    }
}


package com.jozein.xedgepro.service;

import android.content.ComponentName;
import android.content.Intent;
import b.c;
import f.l;
import g.b0;

public class ServiceWidget extends a {
    private b0 F;
    private static final String G;

    static {
        ServiceWidget.G = l.k + "WIDGET";
    }

    @Override  // com.jozein.xedgepro.service.a
    protected void c() {
        this.a(0x7F040043, this.getText(0x7F060021));  // drawable:ic_floating_widget
    }

    @Override  // com.jozein.xedgepro.service.a
    public boolean d(Intent intent0) {
        String s = intent0.getAction();
        if(ServiceWidget.G.equals(s)) {
            int v = intent0.getIntExtra("widget_id", 0);
            return v == 0 || this.F.k() == v ? false : this.F.s(v, intent0.getIntExtra("dismiss_delay", 0));
        }
        return false;
    }

    public static Intent f(int v, int v1) {
        Intent intent0 = new Intent(ServiceWidget.G);
        intent0.setComponent(new ComponentName(l.j, ServiceWidget.class.getName()));
        intent0.putExtra("widget_id", v);
        intent0.putExtra("dismiss_delay", v1);
        return intent0;
    }

    @Override  // com.jozein.xedgepro.service.a
    public void onCreate() {
        super.onCreate();
        b0 b00 = new b0(this);
        this.F = b00;
        b00.r(new c(this));
    }

    @Override  // com.jozein.xedgepro.service.a
    public void onDestroy() {
        super.onDestroy();
        b0 b00 = this.F;
        if(b00 != null) {
            b00.j();
            this.F = null;
        }
    }
}


package com.jozein.xedgepro.xposed;

import de.robv.android.xposed.XposedHelpers;
import java.lang.reflect.Field;

class c {
    private static Field a;

    static {
    }

    static int a(Object object0) {
        if(!v.f) {
            return 0;
        }
        if(c.a == null) {
            c.a = XposedHelpers.findField(object0.getClass(), "mDisplayId");
        }
        int v = c.a.getInt(object0);
        return v == -1 ? 0 : v;
    }
}


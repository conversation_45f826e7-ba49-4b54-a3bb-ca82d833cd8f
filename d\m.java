package d;

import a.b.s0;
import a.p;
import a.z;
import android.app.Activity;
import android.app.DialogFragment;
import android.app.Fragment;
import android.content.BroadcastReceiver;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.AssetFileDescriptor;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.Environment;
import android.view.View;
import android.webkit.MimeTypeMap;
import e.e0;
import e.j.k;
import e.j;
import e.y;
import f.l;
import f.p0;
import f.r0;
import f.r;
import f.v;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;

public class m extends j {
    static class b implements f {
        private final OutputStream a;
        private final String b;

        b(OutputStream outputStream0, String s) {
            this.a = outputStream0;
            this.b = s;
        }

        @Override  // d.m$f
        public String a() {
            r0 r00 = new r0(this.b);
            r00.f(a.j.F);
            try(FileInputStream fileInputStream0 = new FileInputStream(this.b)) {
                r.c(fileInputStream0, this.a);
            }
            finally {
                this.a.close();
            }
            try {
                r00.a().delete();
            }
            catch(Throwable unused_ex) {
            }
            return "";
        }
    }

    static class c implements f {
        private c() {
        }

        c(a m$a0) {
        }

        @Override  // d.m$f
        public String a() {
            String s;
            File file0 = new File(m.P);
            if(!file0.exists() && !file0.mkdir()) {
                throw new IOException("Failed to make dir!");
            }
            do {
                s = p0.f();
            }
            while(new File(m.P + s + ".bak").exists());
            new r0(m.P + s + ".bak").f(a.j.F);
            return s;
        }
    }

    static class d extends AsyncTask {
        private m a;
        private String b;

        d(m m0) {
            this.b = null;
            this.a = m0;
        }

        protected String a(f[] arr_m$f) {
            try {
                return arr_m$f[0].a();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                this.b = throwable0.getMessage();
                return null;
            }
        }

        protected void b(String s) {
            m.R = null;
            m m0 = this.a;
            if(m0 != null) {
                m0.P1(s, this.b);
            }
        }

        void c(m m0) {
            this.a = m0;
        }

        @Override  // android.os.AsyncTask
        protected Object doInBackground(Object[] arr_object) {
            return this.a(((f[])arr_object));
        }

        @Override  // android.os.AsyncTask
        protected void onPostExecute(Object object0) {
            this.b(((String)object0));
        }

        @Override  // android.os.AsyncTask
        protected void onPreExecute() {
            m m0 = this.a;
            if(m0 != null) {
                m0.O = true;
                m0.N(new e0(), 0);
            }
        }
    }

    static class e extends g {
        private final InputStream b;

        e(InputStream inputStream0, String s) {
            super(s);
            this.b = inputStream0;
        }

        @Override  // d.m$g
        public String a() {
            try(FileOutputStream fileOutputStream0 = new FileOutputStream(this.a)) {
                r.c(this.b, fileOutputStream0);
            }
            finally {
                this.b.close();
            }
            super.a();
            try {
                this.a.delete();
            }
            catch(Throwable unused_ex) {
            }
            return null;
        }
    }

    interface f {
        String a();
    }

    static class g implements f {
        final File a;

        g(String s) {
            this.a = new File(s);
        }

        @Override  // d.m$f
        public String a() {
            r0 r00 = new r0(this.a);
            String s = l.l;
            if(!r00.b(p.P.substring(s.length()), l.p)) {
                throw new IOException("Invalid file!");
            }
            r.g(a.j.F);
            r00.d(s);
            z.w0();
            return null;
        }
    }

    private ArrayList M;
    private boolean N;
    private boolean O;
    private static final String P;
    private static final String Q;
    private static d R;

    static {
        String s = Environment.getExternalStorageDirectory().getAbsolutePath();
        String s1 = l.j;
        if(s.endsWith(s1)) {
            s = s.substring(0, s.length() - s1.length() - 1);
        }
        m.P = s + "/XEdgePro/";
        m.Q = s + "/Android/data/XEdgePro/";
        m.R = null;
    }

    public m() {
        this.N = false;
        this.O = false;
    }

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F060086);  // string:backups "Backups"
    }

    @Override  // e.j
    protected int B0() {
        File[] arr_file = new File(m.P).listFiles((File file0, String s) -> s.endsWith(".bak"));
        if(arr_file == null) {
            this.M = new ArrayList();
        }
        else {
            ArrayList arrayList0 = new ArrayList(arr_file.length);
            this.M = arrayList0;
            Collections.addAll(arrayList0, arr_file);
        }
        this.N = this.I1();
        return this.M.size() + 4;
    }

    // 去混淆评级： 低(20)
    private boolean I1() {
        return new File(p.P.replace(l.j, l.v)).exists() || f.z.K(this.f()).U(l.v);
    }

    @Override  // e.j0$c
    protected void J(Bundle bundle0, int v) {
        if(bundle0 == null) {
            return;
        }
        switch(v) {
            case 1: {
                int v1 = bundle0.getInt("result", -1);
                int v2 = this.E0();
                switch(v1) {
                    case 0: {
                        this.T1(new g(((File)this.M.get(v2 - 4)).getPath()));
                        return;
                    }
                    case 1: {
                        try {
                            ((File)this.M.get(v2 - 4)).delete();
                            this.M.remove(v2 - 4);
                            this.u1(v2);
                        }
                        catch(Throwable throwable0) {
                            this.g0(throwable0);
                        }
                        return;
                    }
                    case 2: {
                        this.N(new y().J(this.u(0x7F0600F8), null, this.M1(v2), 8, 0x40), 3);  // string:enter_name "Enter name"
                        return;
                    }
                    default: {
                        return;
                    }
                }
            }
            case 2: {
                if(bundle0.getBoolean("result", false)) {
                    try {
                        this.U1();
                    }
                    catch(Throwable throwable0) {
                        this.g0(throwable0);
                    }
                    return;
                }
                break;
            }
            case 3: {
                CharSequence charSequence0 = bundle0.getCharSequence("result", null);
                if(charSequence0 != null && charSequence0.length() > 0) {
                    String s = charSequence0.toString().trim();
                    if(s.length() > 0) {
                        int v3 = this.E0();
                        String s1 = m.P + s + ".bak";
                        File file0 = (File)this.M.get(v3 - 4);
                        if(!s1.equals(file0.getAbsolutePath())) {
                            File file1 = new File(s1);
                            if(file0.renameTo(file1)) {
                                this.M.set(v3 - 4, file1);
                                ((k)this.L0(v3)).setText(s);
                                return;
                            }
                            v.c(("Failed to rename to " + s1));
                            return;
                        }
                    }
                }
                break;
            }
        }
    }

    static boolean J1(Context context0) {
        int v = Build.VERSION.SDK_INT;
        return v < 30 || Environment.isExternalStorageLegacy() ? v < 23 || context0.checkSelfPermission("android.permission.WRITE_EXTERNAL_STORAGE") == 0 : Environment.isExternalStorageManager();
    }

    private static long K1(ContentResolver contentResolver0, Uri uri0) {
        String s = uri0.getScheme();
        try {
            if("android.resource".equals(s)) {
                return -1L;
            }
            if("file".equals(s)) {
                return uri0.getPath() == null ? -1L : new File(uri0.getPath()).length();
            }
            AssetFileDescriptor assetFileDescriptor0 = contentResolver0.openAssetFileDescriptor(uri0, "r", null);
            return assetFileDescriptor0 == null ? -1L : assetFileDescriptor0.getLength();
        }
        catch(Throwable unused_ex) {
        }
        return -1L;
    }

    private static void L1(Intent intent0) {
        intent0.addCategory("android.intent.category.OPENABLE");
        intent0.putExtra("android.content.extra.SHOW_ADVANCED", true);
        String s = MimeTypeMap.getSingleton().getMimeTypeFromExtension("bak");
        if(s == null) {
            s = "*/*";
        }
        intent0.setType(s);
    }

    private String M1(int v) {
        String s = ((File)this.M.get(v - 4)).getName();
        return s.substring(0, s.length() - 4);
    }

    // 检测为 Lambda 实现
    private static boolean N1(File file0, String s) [...]

    private void O1() {
        try {
            String s = m.Q;
            File file0 = new File(s);
            if(file0.exists()) {
                r.i(file0, new File(m.P));
                v.c(("Backups moved from " + s + " to " + m.P));
            }
        }
        catch(Throwable unused_ex) {
        }
    }

    private void P1(String s, String s1) {
        Fragment fragment0 = this.n();
        if(fragment0 instanceof e0) {
            ((e0)fragment0).dismiss();
        }
        this.O = false;
        if("".equals(s)) {
            this.e0(0x7F060195);  // string:saved "Saved"
            return;
        }
        if(s != null) {
            this.M.add(new File(m.P + s + ".bak"));
            this.w0();
            return;
        }
        if(s1 != null) {
            if("Invalid file!".equals(s1)) {
                this.e0(0x7F060130);  // string:invalid_file "Invalid file"
                return;
            }
            this.f0(s1);
            return;
        }
        this.Q1();
    }

    private void Q1() {
        this.f0(this.u(0x7F0601AE));  // string:settings_restored "Settings restored!"
        try {
            this.g().n();
            this.getActivity().recreate();
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        Context context0 = this.f();
        if(a.j.m(context0)) {
            v.c("send files after restored.");
            a.j.g(context0, true);
            return;
        }
        v.c("notify restore.");
        s0.B(context0, 25);
    }

    private void R1() {
        Intent intent0 = new Intent("android.intent.action.GET_CONTENT");
        m.L1(intent0);
        this.startActivityForResult(intent0, 1);
    }

    private void S1() {
        Intent intent0 = new Intent("android.intent.action.CREATE_DOCUMENT");
        intent0.putExtra("android.intent.extra.TITLE", "xposed-edge-pro-backup.bak");
        m.L1(intent0);
        this.startActivityForResult(intent0, 2);
    }

    private void T1(f m$f0) {
        d m$d0 = new d(this);
        m.R = m$d0;
        m$d0.execute(new f[]{m$f0});
    }

    private void U1() {
        this.V1();
    }

    private void V1() {
        class a extends BroadcastReceiver {
            final m a;

            private void a(Context context0, String s) {
                m.this.W1(s);
                try {
                    context0.unregisterReceiver(this);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }

            @Override  // android.content.BroadcastReceiver
            public void onReceive(Context context0, Intent intent0) {
                String s1;
                byte[] arr_b = intent0.getByteArrayExtra("buffer");
                String s = intent0.getStringExtra("name");
                if(arr_b == null || s == null) {
                    s1 = null;
                }
                else {
                    try {
                        File file0 = new File(l.l + s);
                        File file1 = file0.getParentFile();
                        if(file1 != null && !file1.exists() && !file1.mkdir()) {
                            v.c(("Error import " + file0.getName()));
                            return;
                        }
                        try(FileOutputStream fileOutputStream0 = new FileOutputStream(file0)) {
                            fileOutputStream0.write(arr_b);
                        }
                        return;
                    }
                    catch(IOException iOException0) {
                        v.d(iOException0);
                        s1 = iOException0.getMessage();
                    }
                }
                this.a(context0, s1);
            }
        }

        Throwable throwable1;
        a m$a0;
        Context context0;
        try {
            this.O = true;
            context0 = this.f();
            m$a0 = new a(this);
        }
        catch(Throwable throwable0) {
            m$a0 = null;
            throwable1 = throwable0;
            goto label_13;
        }
        try {
            context0.registerReceiver(m$a0, new IntentFilter(a.j.A));
            r.g(a.j.F);
            a.j.f(context0);
            goto label_17;
        }
        catch(Throwable throwable1) {
        }
    label_13:
        this.O = false;
        if(m$a0 != null) {
            context0.unregisterReceiver(m$a0);
        }
        throw throwable1;
    label_17:
        this.N(new e0(), 0);
    }

    private void W1(String s) {
        this.O = false;
        z.w0();
        Fragment fragment0 = this.n();
        if(fragment0 instanceof DialogFragment) {
            ((DialogFragment)fragment0).dismiss();
        }
        if(s != null) {
            this.f0(s);
            return;
        }
        this.Q1();
    }

    @Override  // e.j
    protected View h1(int v) {
        switch(v) {
            case 0: {
                View view0 = new k(this, this.u(0x7F060067), null);  // string:add_backup "Add backup"
                if(!m.J1(this.M0())) {
                    ((k)view0).e();
                }
                return view0;
            }
            case 1: {
                return new k(this, this.u(0x7F060068), null);  // string:add_backup_to "Add backup to…"
            }
            case 2: {
                View view1 = new k(this, this.u(0x7F060129), null);  // string:import_from_free_version "Import from free version"
                ((k)view1).setEnabled(this.N);
                return view1;
            }
            case 3: {
                return new k(this, this.u(0x7F060167), null);  // string:open_from "Open from…"
            }
            default: {
                return new k(this, this.M1(v));
            }
        }
    }

    @Override  // e.j
    protected void k1(int v) {
        if(this.O) {
            v.c("working...");
            return;
        }
        switch(v) {
            case 0: {
                if(m.J1(this.M0())) {
                    this.T1(new c(null));
                    return;
                }
                this.e0(0x7F060172);  // string:permission_denied "Permission denied!"
                return;
            }
            case 1: {
                this.S1();
                return;
            }
            case 2: {
                if(this.N) {
                    this.N(new e.m().u(this.u(0x7F06008E)), 2);  // string:check_import_from_free_version "Import from free version. Are you sure continue?"
                    return;
                }
                return;
            }
            case 3: {
                this.R1();
                return;
            }
            default: {
                this.N(new e.z().u(new CharSequence[]{this.u(0x7F060184), this.u(0x7F0600CD), this.u(0x7F060182)}), 1);  // string:restore "Import"
            }
        }
    }

    @Override  // android.app.Fragment
    public void onActivityResult(int v, int v1, Intent intent0) {
        e m$e0;
        if(v1 != -1) {
            return;
        }
        Uri uri0 = intent0.getData();
        Activity activity0 = this.getActivity();
        if(uri0 != null && activity0 != null) {
            try {
                ContentResolver contentResolver0 = activity0.getContentResolver();
                switch(v) {
                    case 1: {
                        if(m.K1(contentResolver0, uri0) > 0x1400000L) {
                            this.e0(0x7F060130);  // string:invalid_file "Invalid file"
                            return;
                        }
                        InputStream inputStream0 = contentResolver0.openInputStream(uri0);
                        if(inputStream0 == null) {
                            throw new IOException("Failed to open input stream!");
                        }
                        m$e0 = new e(inputStream0, activity0.getCacheDir() + "/tmp.bak");
                        this.T1(m$e0);
                        return;
                    }
                    case 2: {
                        OutputStream outputStream0 = contentResolver0.openOutputStream(uri0);
                        if(outputStream0 == null) {
                            throw new IOException("Failed to open output stream!");
                        }
                        m$e0 = new b(outputStream0, activity0.getCacheDir() + "/tmp.bak");
                        this.T1(m$e0);
                        return;
                    }
                    default: {
                        v.c(Arrays.toString(new File(m.P + "XEdgePro").list()));
                    }
                }
            }
            catch(Throwable throwable0) {
                this.g0(throwable0);
            }
        }
    }

    @Override  // e.j
    public void onCreate(Bundle bundle0) {
        super.onCreate(bundle0);
        d m$d0 = m.R;
        if(m$d0 != null) {
            m$d0.c(this);
        }
        this.O1();
    }

    @Override  // e.j
    public void onDestroy() {
        super.onDestroy();
        d m$d0 = m.R;
        if(m$d0 != null) {
            m$d0.c(null);
        }
    }

    @Override  // e.j0$c
    protected void y() {
        if(this.O) {
            this.W1(null);
            return;
        }
        super.y();
    }
}


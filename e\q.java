package e;

import android.app.Activity;
import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.ClipboardManager;
import android.content.DialogInterface;
import android.os.Bundle;
import android.widget.FrameLayout.LayoutParams;
import android.widget.FrameLayout;
import android.widget.ScrollView;
import android.widget.TextView;

public class q extends b {
    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        Activity activity0 = this.getActivity();
        int v = this.g().f;
        Bundle bundle1 = this.e();
        ScrollView scrollView0 = new ScrollView(activity0);
        scrollView0.setFillViewport(true);
        FrameLayout frameLayout0 = new FrameLayout(activity0);
        scrollView0.addView(frameLayout0);
        TextView textView0 = new TextView(activity0);
        CharSequence charSequence0 = bundle1.getCharSequence("description");
        CharSequence charSequence1 = bundle1.getCharSequence("text", "");
        textView0.setText((charSequence0 == null ? charSequence1.toString() : charSequence0 + "\n\n" + charSequence1));
        textView0.setTextIsSelectable(true);
        FrameLayout.LayoutParams frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(-1, -1, 17);
        frameLayout$LayoutParams0.leftMargin = v * 2;
        frameLayout$LayoutParams0.topMargin = v * 2;
        frameLayout$LayoutParams0.rightMargin = v * 2;
        frameLayout$LayoutParams0.bottomMargin = v * 2;
        frameLayout0.addView(textView0, frameLayout$LayoutParams0);
        return new AlertDialog.Builder(activity0).setTitle(bundle1.getCharSequence("title")).setView(scrollView0).setPositiveButton(0x104000A, b.B).setNegativeButton(0x1040001, (DialogInterface dialogInterface0, int v) -> if(charSequence1 != null && charSequence1.length() != 0) {
            ClipboardManager clipboardManager0 = (ClipboardManager)this.getActivity().getSystemService("clipboard");
            if(clipboardManager0 != null) {
                clipboardManager0.setText(charSequence1);
                this.s(0x7F0601F2);  // string:text_copied "Text copied."
            }
        }).create();
    }

    // 检测为 Lambda 实现
    private void v(CharSequence charSequence0, DialogInterface dialogInterface0, int v) [...]

    public q w(CharSequence charSequence0, CharSequence charSequence1, CharSequence charSequence2) {
        Bundle bundle0 = this.e();
        bundle0.putCharSequence("title", charSequence0);
        bundle0.putCharSequence("text", charSequence1);
        bundle0.putCharSequence("description", charSequence2);
        return this;
    }
}


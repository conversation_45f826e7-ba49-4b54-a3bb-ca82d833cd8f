package com.jozein.xedgepro.xposed;

import a.b.a3;
import a.b.b1;
import a.b.c3;
import a.b.d0;
import a.b.d1;
import a.b.d;
import a.b.e1;
import a.b.f1;
import a.b.f2;
import a.b.g0;
import a.b.g2;
import a.b.h2;
import a.b.i3;
import a.b.j1;
import a.b.k2;
import a.b.k3;
import a.b.l3;
import a.b.m2;
import a.b.n3;
import a.b.q1;
import a.b.s2;
import a.b.u1;
import a.b.u2;
import a.b.x2;
import a.b.y1;
import a.b.z0;
import a.b.z2;
import a.b;
import android.app.ActivityManager.RunningTaskInfo;
import android.app.ActivityOptions;
import android.app.Notification.BigTextStyle;
import android.app.Notification.Builder;
import android.app.Notification;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.bluetooth.BluetoothAdapter;
import android.content.BroadcastReceiver;
import android.content.ClipboardManager;
import android.content.ComponentName;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager.NameNotFoundException;
import android.content.res.Resources;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint.Style;
import android.graphics.Paint;
import android.graphics.Point;
import android.graphics.PorterDuff.Mode;
import android.graphics.PorterDuffColorFilter;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Region;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.Icon;
import android.media.MediaPlayer.OnCompletionListener;
import android.media.MediaPlayer.OnPreparedListener;
import android.media.MediaPlayer;
import android.media.RingtoneManager;
import android.net.ConnectivityManager;
import android.net.Uri;
import android.net.wifi.WifiManager;
import android.nfc.NfcAdapter;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.PowerManager.WakeLock;
import android.os.PowerManager;
import android.os.ResultReceiver;
import android.os.SystemClock;
import android.provider.Settings.System;
import android.telephony.TelephonyManager;
import android.text.Html;
import android.text.TextUtils.TruncateAt;
import android.view.Display;
import android.view.MotionEvent;
import android.view.RoundedCorner;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.WindowManager.LayoutParams;
import android.view.animation.AlphaAnimation;
import android.view.animation.AnimationSet;
import android.view.animation.TranslateAnimation;
import android.view.inputmethod.InputMethodManager;
import android.widget.AbsListView.LayoutParams;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.FrameLayout.LayoutParams;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout.LayoutParams;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;
import com.jozein.xedgepro.service.AdapterService;
import com.jozein.xedgepro.service.BinderService;
import com.jozein.xedgepro.service.ServiceTorch;
import com.jozein.xedgepro.service.ServiceWidget;
import de.robv.android.xposed.XC_MethodHook.MethodHookParam;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import f.a.c;
import f.l;
import f.t;
import f.v;
import f.w;
import f.z;
import g.a0;
import g.h;
import g.o;
import g.x;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

class w1 extends j2 {
    class c0 implements n0 {
        final w1 a;

        @Override  // com.jozein.xedgepro.xposed.w1$n0
        public boolean a() {
            return false;
        }

        @Override  // com.jozein.xedgepro.xposed.w1$n0
        public b b() {
            return null;
        }
    }

    class k0 extends BaseAdapter implements AdapterView.OnItemClickListener {
        private final int A;
        private final int B;
        private final int C;
        private final AbsListView.LayoutParams D;
        private final PorterDuffColorFilter E;
        private int[] F;
        private final f G;
        private final Resources H;
        final w1 I;
        private final int z;

        k0(f l0$f0) {
            this.G = l0$f0;
            if(a0.r(w10.H.v(33, 0xFF003037))) {
                this.z = -1;
                this.E = null;
            }
            else {
                this.z = 0xFF000000;
                this.E = new PorterDuffColorFilter(0xFF000000, PorterDuff.Mode.SRC_ATOP);
            }
            x x0 = x.c(w10.a0);
            this.A = x0.a;
            this.B = x0.b;
            this.C = x0.f;
            this.D = new AbsListView.LayoutParams(-1, x0.a);
            this.H = w10.b0.getResources();
            ArrayList arrayList0 = new ArrayList();
            arrayList0.add(0);
            if(!l0$f0.B()) {
                arrayList0.add(1);
            }
            if(l0$f0.u()) {
                arrayList0.add(2);
            }
            if(l0$f0.C()) {
                arrayList0.add(3);
            }
            else if(l0$f0.s()) {
                arrayList0.add(4);
            }
            if(l0$f0.x() != null) {
                arrayList0.add(6);
            }
            arrayList0.add(7);
            arrayList0.add(5);
            this.F = this.d(arrayList0);
        }

        @Override  // android.widget.BaseAdapter
        public boolean areAllItemsEnabled() {
            return false;
        }

        private void b(b b0) {
            if(b0 != null) {
                BinderService.h(w1.this.a0, b0);
            }
        }

        private Drawable c(int v) {
            Drawable drawable0 = this.H.getDrawable(v);
            h.a().b(drawable0);
            return drawable0;
        }

        private int[] d(List list0) {
            int v = list0.size();
            int[] arr_v = new int[v];
            for(int v1 = 0; v1 < v; ++v1) {
                arr_v[v1] = (int)(((Integer)list0.get(v1)));
            }
            return arr_v;
        }

        private LinearLayout e(int v, int v1) {
            return this.f(this.c(v), this.H.getText(v1));
        }

        private LinearLayout f(Drawable drawable0, CharSequence charSequence0) {
            return this.g(drawable0, charSequence0, null);
        }

        private LinearLayout g(Drawable drawable0, CharSequence charSequence0, CharSequence charSequence1) {
            LinearLayout linearLayout0 = new LinearLayout(w1.this.a0);
            linearLayout0.setOrientation(0);
            linearLayout0.setLayoutParams(this.D);
            linearLayout0.setPadding(this.C + this.C, 0, 0, 0);
            ImageView imageView0 = new ImageView(w1.this.a0);
            drawable0.setColorFilter(this.E);
            imageView0.setImageDrawable(drawable0);
            imageView0.setPadding(this.C, 0, this.C, 0);
            LinearLayout.LayoutParams linearLayout$LayoutParams0 = new LinearLayout.LayoutParams(this.B, this.B);
            linearLayout$LayoutParams0.gravity = 16;
            linearLayout0.addView(imageView0, 0, linearLayout$LayoutParams0);
            TextView textView0 = new TextView(w1.this.a0);
            textView0.setText(charSequence0);
            textView0.setTextSize(0, ((float)this.A) / 3.0f);
            textView0.setSingleLine(true);
            textView0.setEllipsize(TextUtils.TruncateAt.END);
            textView0.setTextColor(this.z);
            LinearLayout.LayoutParams linearLayout$LayoutParams1 = new LinearLayout.LayoutParams(-1, -2, 1.0f);
            linearLayout$LayoutParams1.gravity = 16;
            if(charSequence1 != null) {
                LinearLayout linearLayout1 = new LinearLayout(w1.this.a0);
                linearLayout1.setOrientation(1);
                linearLayout1.addView(textView0);
                TextView textView1 = new TextView(w1.this.a0);
                textView1.setText(charSequence1);
                textView1.setTextSize(0, ((float)this.A) / 4.0f);
                textView1.setSingleLine(true);
                textView1.setEllipsize(TextUtils.TruncateAt.END);
                textView1.setTextColor(this.z);
                linearLayout1.addView(textView1);
                linearLayout0.addView(linearLayout1, linearLayout$LayoutParams1);
                return linearLayout0;
            }
            linearLayout0.addView(textView0, linearLayout$LayoutParams1);
            return linearLayout0;
        }

        @Override  // android.widget.Adapter
        public int getCount() {
            return this.F.length;
        }

        @Override  // android.widget.Adapter
        public Object getItem(int v) {
            return null;
        }

        @Override  // android.widget.Adapter
        public long getItemId(int v) {
            return (long)v;
        }

        @Override  // android.widget.Adapter
        public View getView(int v, View view0, ViewGroup viewGroup0) {
            try {
                switch(this.F[v]) {
                    case 0: {
                        View view1 = this.f(this.G.d(), this.G.f());
                        view1.setPadding(view1.getPaddingLeft() / 2, 0, 0, 0);
                        return view1;
                    }
                    case 1: {
                        return this.e(0x7F040083, 0x7F060146);  // drawable:ic_play
                    }
                    case 2: {
                        return this.e(0x7F040059, 0x7F0601E7);  // drawable:ic_kill_app
                    }
                    case 3: {
                        return this.e(0x7F0400C1, 0x7F060210);  // drawable:ic_unfreeze
                    }
                    case 4: {
                        return this.e(0x7F040044, 0x7F060101);  // drawable:ic_freezer
                    }
                    case 5: {
                        return this.e(0x7F040001, 0x7F060078);  // drawable:ic_about
                    }
                    case 6: {
                        return this.e(0x7F040004, 0x7F060005);  // drawable:ic_activity
                    }
                    case 7: {
                        return this.e(0x7F040086, 0x7F060223);  // drawable:ic_play_store
                    }
                    case 8: {
                        return this.e(0x7F040044, 0x7F0600C8);  // drawable:ic_freezer
                    }
                    case 9: {
                        View view2 = this.g(this.G.d(), this.G.f(), this.G.z());
                        view2.setPadding(view2.getPaddingLeft() / 2, 0, 0, 0);
                        return view2;
                    }
                    case 10: {
                        return this.e(0x7F040042, 0x7F060020);  // drawable:ic_finish_activity
                    }
                    case 11: {
                        return this.e(0x7F04001E, 0x7F060097);  // drawable:ic_collect_activity
                    }
                    case 12: {
                        return this.e(0x7F04001F, 0x7F060098);  // drawable:ic_collect_activity_with_params
                    }
                    case 13: {
                        return this.f(this.c(0x7F040009), "Install exists");  // drawable:ic_app
                    }
                    default: {
                        return new View(w1.this.a0);
                    }
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return new View(w1.this.a0);
            }
        }

        private void h(AdapterView adapterView0) {
            ArrayList arrayList0 = new ArrayList();
            arrayList0.add(9);
            if(this.G.t()) {
                arrayList0.add(10);
            }
            if(this.G.q() != null) {
                arrayList0.add(11);
            }
            if(this.G.r() != null) {
                arrayList0.add(12);
            }
            this.F = this.d(arrayList0);
            this.notifyDataSetChanged();
            if(adapterView0 instanceof ListView) {
                ((ListView)adapterView0).setPressed(false);
                ((ListView)adapterView0).jumpDrawablesToCurrentState();
                ((ListView)adapterView0).setSelectionFromTop(0, 0);
            }
            a0.z(adapterView0, new TranslateAnimation(0.0f, 0.0f, ((float)(this.A * this.F.length)), 0.0f), ((long)this.F.length) * 0xC6L / 4L);
        }

        @Override  // android.widget.BaseAdapter
        public boolean isEnabled(int v) {
            return v != 0;
        }

        @Override  // android.widget.AdapterView$OnItemClickListener
        public void onItemClick(AdapterView adapterView0, View view0, int v, long v1) {
            try {
                switch(this.F[v]) {
                    case 1: {
                        Bundle bundle0 = a.d(w1.this.P, 3);
                        this.G.E(bundle0);
                        break;
                    }
                    case 2: {
                        this.G.w();
                        break;
                    }
                    case 3: {
                        this.G.F(false);
                        break;
                    }
                    case 4: {
                        this.F = new int[]{0, 8};
                        this.notifyDataSetChanged();
                        a0.z(adapterView0, new TranslateAnimation(0.0f, 0.0f, ((float)(this.A * this.F.length)), 0.0f), ((long)this.F.length) * 0xC6L / 4L);
                        return;
                    }
                    case 5: {
                        this.G.G();
                        break;
                    }
                    case 6: {
                        this.h(adapterView0);
                        return;
                    }
                    case 7: {
                        this.G.H();
                        break;
                    }
                    case 8: {
                        this.G.F(true);
                        break;
                    }
                    case 0: 
                    case 9: {
                        return;
                    }
                    case 10: {
                        this.G.v();
                        break;
                    }
                    case 11: {
                        this.b(this.G.q());
                        break;
                    }
                    case 12: {
                        this.b(this.G.r());
                        break;
                    }
                    case 13: {
                        this.G.A();
                    }
                }
            }
            catch(Throwable throwable0) {
                w1.this.G4(throwable0.getMessage());
                v.d(throwable0);
            }
            try {
                t3.n().t(19);
            }
            catch(Throwable throwable1) {
                v.d(throwable1);
            }
        }
    }

    class k implements f.i.f {
        final w1 a;

        @Override  // f.i$f
        public int a(String s) {
            return w1.this.L.g(s).a;
        }

        @Override  // f.i$f
        public t b(String s) {
            return w1.this.L.g(s);
        }
    }

    class l0 implements n0 {
        private final d0 a;
        private n0 b;
        final w1 c;

        l0(d0 b$d00) {
            this.b = null;
            this.a = b$d00;
        }

        @Override  // com.jozein.xedgepro.xposed.w1$n0
        public boolean a() {
            return this.b == null || this.b.a();
        }

        @Override  // com.jozein.xedgepro.xposed.w1$n0
        public b b() {
            b b0;
            w1 w10;
            if(this.b == null) {
                if(w1.this.l1(this.a.I, false)) {
                    w10 = w1.this;
                    b0 = this.a.J;
                }
                else {
                    w10 = w1.this;
                    b0 = this.a.K;
                }
                this.b = w10.X2(b0);
            }
            return this.b.b();
        }
    }

    class m0 extends View {
        private final Paint A;
        private final Paint B;
        private final Rect C;
        private final int D;
        final w1 E;
        private final boolean z;

        private m0(boolean z, int v) {
            super(w10.a0);
            Paint paint0 = new Paint(1);
            this.A = paint0;
            Paint paint1 = new Paint(1);
            this.B = paint1;
            this.C = new Rect();
            this.z = z;
            paint0.setColor(0x7FFF8F00);
            paint1.setColor(0x7F007FA7);
            this.D = v;
        }

        m0(boolean z, int v, k w1$k0) {
            this(z, v);
        }

        @Override  // android.view.View
        protected void onDraw(Canvas canvas0) {
            canvas0.getClipBounds(this.C);
            int v = this.D;
            if(v == 0) {
                canvas0.drawColor(this.A.getColor());
                return;
            }
            if(this.z) {
                Rect rect0 = this.C;
                int v1 = rect0.bottom / 3;
                if((v & 1) != 0) {
                    canvas0.drawRect(0.0f, 0.0f, ((float)rect0.right), ((float)v1), this.A);
                }
                int v2 = this.D;
                if((v2 & 2) != 0) {
                    canvas0.drawRect(0.0f, ((float)v1), ((float)this.C.right), ((float)(v1 * 2)), (v2 == 7 ? this.B : this.A));
                }
                if((this.D & 4) != 0) {
                    canvas0.drawRect(0.0f, ((float)(v1 * 2)), ((float)this.C.right), ((float)this.C.bottom), this.A);
                }
            }
            else {
                Rect rect1 = this.C;
                int v3 = rect1.right / 3;
                if((v & 1) != 0) {
                    canvas0.drawRect(0.0f, 0.0f, ((float)v3), ((float)rect1.bottom), this.A);
                }
                int v4 = this.D;
                if((v4 & 2) != 0) {
                    canvas0.drawRect(((float)v3), 0.0f, ((float)(v3 * 2)), ((float)this.C.bottom), (v4 == 7 ? this.B : this.A));
                }
                if((this.D & 4) != 0) {
                    canvas0.drawRect(((float)(v3 * 2)), 0.0f, ((float)this.C.right), ((float)this.C.bottom), this.A);
                }
            }
        }
    }

    interface n0 {
        boolean a();

        b b();
    }

    class o0 implements n0 {
        private final b[] a;
        private int b;
        private final n0[] c;
        final w1 d;

        o0(b[] arr_b) {
            this.a = arr_b;
            this.b = 0;
            this.c = new n0[arr_b.length];
        }

        @Override  // com.jozein.xedgepro.xposed.w1$n0
        public boolean a() {
            int v = this.c.length - 1;
            return this.b < v || this.b == v && (this.c[v] == null || this.c[v].a());
        }

        @Override  // com.jozein.xedgepro.xposed.w1$n0
        public b b() {
            int v = this.b;
            n0[] arr_w1$n0 = this.c;
            if(v >= arr_w1$n0.length) {
                return null;
            }
            if(arr_w1$n0[v] == null) {
                arr_w1$n0[v] = w1.this.X2(this.a[v]);
            }
            b b0 = this.c[this.b].b();
            if(b0 != null) {
                return b0;
            }
            ++this.b;
            return this.b();
        }
    }

    class p0 implements MediaPlayer.OnCompletionListener, MediaPlayer.OnPreparedListener, Runnable {
        final w1 A;
        private final ArrayList z;

        private p0() {
            this.z = new ArrayList();
        }

        p0(k w1$k0) {
        }

        void a(Uri uri0, int v, boolean z) {
            MediaPlayer mediaPlayer0 = new MediaPlayer();
            try {
                mediaPlayer0.setDataSource(w1.this.a0, uri0);
            }
            catch(IOException unused_ex) {
                v.c(("Error playing " + uri0 + ". Using default " + this.c(v) + " instead."));
                Uri uri1 = RingtoneManager.getDefaultUri(this.d(v));
                mediaPlayer0.setDataSource(w1.this.a0, uri1);
            }
            mediaPlayer0.setAudioStreamType(v);
            mediaPlayer0.setLooping(z);
            if(!z) {
                mediaPlayer0.setOnCompletionListener(this);
            }
            mediaPlayer0.setOnPreparedListener(this);
            mediaPlayer0.prepareAsync();
        }

        void b() {
            for(Object object0: this.z) {
                ((MediaPlayer)object0).release();
            }
            this.z.clear();
        }

        private String c(int v) {
            switch(v) {
                case 2: {
                    return "ringtone";
                }
                case 4: {
                    return "alarm";
                }
                default: {
                    return "notification";
                }
            }
        }

        private int d(int v) {
            switch(v) {
                case 2: {
                    return 1;
                }
                case 4: {
                    return 4;
                }
                default: {
                    return 2;
                }
            }
        }

        @Override  // android.media.MediaPlayer$OnCompletionListener
        public void onCompletion(MediaPlayer mediaPlayer0) {
            try {
                mediaPlayer0.release();
                this.z.remove(mediaPlayer0);
                if(this.z.size() == 0) {
                    w1.this.c0.removeCallbacks(this);
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        @Override  // android.media.MediaPlayer$OnPreparedListener
        public void onPrepared(MediaPlayer mediaPlayer0) {
            try {
                mediaPlayer0.start();
                int v = this.z.size();
                if(v > 8) {
                    ((MediaPlayer)this.z.remove(0)).release();
                }
                this.z.add(mediaPlayer0);
                if(v > 0) {
                    w1.this.c0.removeCallbacks(this);
                    w1.this.c0.postDelayed(this, 600000L);
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        @Override
        public void run() {
            this.b();
        }
    }

    class p implements Runnable {
        final w1 z;

        @Override
        public void run() {
            w1.this.O.V().S();
            t3.n().u(7, 100L);
        }
    }

    class q0 {
        private NotificationManager a;
        private BroadcastReceiver b;
        private int c;
        final w1 d;

        q0() {
            this.a = null;
            this.b = null;
            this.c = -1;
        }

        void a() {
            if(this.b != null) {
                w1.this.a0.unregisterReceiver(this.b);
                this.b = null;
                this.a.cancel(this.c);
            }
        }

        void b(CharSequence charSequence0, int v, b b0) {
            this.c(charSequence0, null, v, 0, false, true, b0);
        }

        void c(CharSequence charSequence0, CharSequence charSequence1, int v, int v1, boolean z, boolean z1, b b0) {
            class com.jozein.xedgepro.xposed.w1.q0.a extends BroadcastReceiver {
                final b a;
                final q0 b;

                com.jozein.xedgepro.xposed.w1.q0.a(b b0) {
                    this.a = b0;
                    super();
                }

                @Override  // android.content.BroadcastReceiver
                public void onReceive(Context context0, Intent intent0) {
                    try {
                        q0.this.a();
                        b b0 = this.a;
                        if(b0 != null) {
                            w1.this.w3(b0);
                        }
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }

            Context context0 = w1.this.b0;
            int v2 = Build.VERSION.SDK_INT;
            Context context1 = v2 < 29 ? context0 : w1.this.a0;
            this.c = w1.M0(w1.this);
            String s = w1.T0 + this.c;
            CharSequence charSequence2 = context0.getText(l.y);
            Intent intent0 = new Intent(s);
            intent0.setPackage("android");
            PendingIntent pendingIntent0 = PendingIntent.getBroadcast(w1.this.a0, 0, intent0, (v2 < 0x1F ? 0 : 0x4000000));
            if(w1.this.P0 == null) {
                w w0 = w.a(l.x, charSequence2);
                w1.this.P0 = w0;
            }
            w1.this.P0.c(v1);
            if(z1) {
                w1.this.P0.d(w.D);
            }
            Notification.Builder notification$Builder0 = w1.this.P0.b(context1).setContentTitle(charSequence2).setContentText(charSequence0).setContentIntent(pendingIntent0).setDeleteIntent(pendingIntent0);
            if(v2 >= 29) {
                Icon icon0 = Icon.createWithResource(context0, v);
                notification$Builder0.setLargeIcon(icon0).setSmallIcon(icon0);
            }
            else {
                notification$Builder0.setLargeIcon(BitmapFactory.decodeResource(context0.getResources(), v)).setSmallIcon(v);
            }
            if(charSequence1 != null) {
                notification$Builder0.setTicker(charSequence1);
            }
            if(z) {
                Notification.BigTextStyle notification$BigTextStyle0 = new Notification.BigTextStyle();
                notification$BigTextStyle0.setBigContentTitle(charSequence2);
                notification$BigTextStyle0.bigText(charSequence0);
                notification$Builder0.setStyle(notification$BigTextStyle0);
            }
            Notification notification0 = notification$Builder0.build();
            if(z1) {
                notification0.flags |= 0x20;
            }
            NotificationManager notificationManager0 = (NotificationManager)context1.getSystemService("notification");
            this.a = notificationManager0;
            notificationManager0.notify(this.c, notification0);
            this.b = new com.jozein.xedgepro.xposed.w1.q0.a(this, b0);
            BroadcastReceiver broadcastReceiver0 = this.b;
            IntentFilter intentFilter0 = new IntentFilter(s);
            w1.this.a0.registerReceiver(broadcastReceiver0, intentFilter0, null, w1.this.c0);
        }
    }

    class r0 implements c, Runnable {
        private final long A;
        private final PowerManager.WakeLock B;
        private final String C;
        private f.a D;
        final w1 E;
        private final n0 z;

        r0(b b0, long v) {
            this.D = null;
            this.z = w10.X2(b0);
            this.A = v;
            String s = "Queue-" + w1.T0(w10);
            this.C = s;
            PowerManager.WakeLock powerManager$WakeLock0 = w10.d0.newWakeLock(1, s);
            this.B = powerManager$WakeLock0;
            powerManager$WakeLock0.setReferenceCounted(false);
        }

        @Override  // f.a$c
        public void a(f.a a0, long v) {
            try {
                this.c();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        private void b() {
            this.B.acquire();
        }

        void c() {
            this.b();
            this.d();
        }

        private void d() {
            try {
                long v = 0L;
                b b0 = this.z.b();
                if(b0 == null || b0.z == 102 || w1.this.s2(this.A)) {
                    this.e();
                    return;
                }
                if(b0.z == 54) {
                    v = (long)((g0)b0).I;
                }
                else {
                    try {
                        w1.this.y3(b0, -1, null);
                    }
                    catch(Throwable throwable1) {
                        v.d(throwable1);
                        w1.this.F4(b0, throwable1);
                        this.e();
                        return;
                    }
                }
                if(this.z.a()) {
                    this.f(v);
                    return;
                }
                this.e();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                this.e();
            }
        }

        private void e() {
            this.B.release();
        }

        private void f(long v) {
            if(v < 30000L) {
                this.b();
                w1.this.c0.postDelayed(this, v);
                return;
            }
            if(this.D == null) {
                this.D = f.a.c(w1.this.a0, w1.this.c0, this, 2);
            }
            this.D.d(SystemClock.elapsedRealtime() + v);
            this.e();
        }

        @Override
        protected void finalize() {
            if(this.B.isHeld()) {
                this.B.release();
                v.c(("WakeLock finalized while still held " + this.C));
            }
        }

        @Override
        public void run() {
            try {
                this.d();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    class s0 implements n0 {
        private final b a;
        private int b;
        private n0 c;
        final w1 d;

        s0(f2 b$f20) {
            this.a = b$f20.J;
            this.b = b$f20.I;
            this.c = w10.X2(b$f20.J);
        }

        // 去混淆评级： 低(20)
        @Override  // com.jozein.xedgepro.xposed.w1$n0
        public boolean a() {
            return this.b > 1 || this.b == 1 && this.c.a();
        }

        @Override  // com.jozein.xedgepro.xposed.w1$n0
        public b b() {
            if(this.b <= 0) {
                return null;
            }
            b b0 = this.c.b();
            if(b0 == null) {
                int v = this.b - 1;
                this.b = v;
                if(v > 0) {
                    n0 w1$n00 = w1.this.X2(this.a);
                    this.c = w1$n00;
                    return w1$n00.b();
                }
            }
            return b0;
        }
    }

    class t0 implements e {
        final o a;
        final b[] b;
        final boolean c;
        w0 d;
        final w1 e;

        t0(b[] arr_b, boolean z) {
            this.a = new g.o.b(w10.H, w10.b0.getResources());
            this.d = null;
            this.b = arr_b;
            this.c = z;
        }

        @Override  // com.jozein.xedgepro.xposed.t2$e
        public boolean a() {
            return this.c;
        }

        @Override  // com.jozein.xedgepro.xposed.t2$e
        public boolean b(int v) {
            b b0 = w1.this.y4(this.b[v], true);
            if(b0.z == 86) {
                w0 w1$w00 = w1.this.f2(((i3)b0));
                this.d = w1$w00;
                w1$w00.a();
                w1.this.y4(this.b[v], false);
                return true;
            }
            return false;
        }

        @Override  // com.jozein.xedgepro.xposed.t2$e
        public Drawable c(int v) {
            return this.g(this.b[v]);
        }

        @Override  // com.jozein.xedgepro.xposed.t2$e
        public CharSequence d(int v) {
            return this.h(this.b[v]);
        }

        @Override  // com.jozein.xedgepro.xposed.t2$e
        public void e(int v, View view0) {
            b b0 = this.b[v];
            Bundle bundle0 = a.c(view0);
            w1.this.v3(b0, -1, bundle0);
        }

        @Override  // com.jozein.xedgepro.xposed.t2$e
        public void f() {
            w0 w1$w00 = this.d;
            if(w1$w00 != null) {
                w1$w00.f();
                this.d = null;
            }
        }

        private Drawable g(b b0) {
            boolean z = true;
            switch(b0.z) {
                case 0: 
                case 1: {
                    return null;
                }
                case 70: {
                    if(((g2)b0).I == 0) {
                        switch(w1.this.V.d()) {
                            case 0: {
                                return this.a.a(0x7F040095);  // drawable:ic_ringer_mode_silent
                            }
                            case 1: {
                                return this.a.a(0x7F040096);  // drawable:ic_ringer_mode_vibrate
                            }
                        }
                    }
                    break;
                }
                case 72: {
                    if(!((d)b0).B()) {
                        return this.g(((d)b0).K);
                    }
                }
            }
            o o0 = this.a;
            Drawable drawable0 = b0.m(w1.this.b0);
            if(b0 instanceof k3 && !w1.this.Q1(b0)) {
                z = false;
            }
            return o0.e(drawable0, z);
        }

        @Override  // com.jozein.xedgepro.xposed.t2$e
        public int getCount() {
            return this.b.length;
        }

        private CharSequence h(b b0) {
            switch(b0.z) {
                case 0: 
                case 1: {
                    return null;
                }
                case 21: {
                    if(w1.this.T.H()) {
                        WifiManager wifiManager0 = (WifiManager)w1.this.a0.getSystemService("wifi");
                        if(wifiManager0 != null) {
                            String s = wifiManager0.getConnectionInfo().getSSID();
                            CharSequence charSequence0 = s.substring(1, s.length() - 1);
                            if(!w1.this.T.z()) {
                                try {
                                    return Html.fromHtml(("<font color=\"#888888\">" + ((String)charSequence0) + "</font>"));
                                }
                                catch(Throwable throwable0) {
                                    v.d(throwable0);
                                }
                            }
                            return charSequence0;
                        }
                    }
                    break;
                }
                case 70: {
                    if(((g2)b0).I == 0) {
                        int v = w1.this.V.d();
                        switch(v) {
                            case 0: {
                                return w1.this.b0.getText(0x7F060187);  // string:ringer_mode_silent "Silent"
                            }
                            case 1: {
                                return w1.this.b0.getText(0x7F060188);  // string:ringer_mode_vibrate "Vibrate"
                            }
                        }
                        if(v == 2) {
                            return w1.this.b0.getText(0x7F060186);  // string:ringer_mode_normal "Normal"
                        }
                    }
                    break;
                }
                case 72: {
                    if(!((d)b0).C()) {
                        return this.h(((d)b0).K);
                    }
                    break;
                }
                default: {
                    return b0 instanceof k3 ? b0.p(w1.this.b0) : b0.n(w1.this.b0);
                }
            }
            return b0 instanceof k3 ? b0.p(w1.this.b0) : b0.n(w1.this.b0);
        }
    }

    static class u0 implements n0 {
        private b a;

        u0(b b0) {
            this.a = b0;
        }

        @Override  // com.jozein.xedgepro.xposed.w1$n0
        public boolean a() {
            return this.a != null;
        }

        @Override  // com.jozein.xedgepro.xposed.w1$n0
        public b b() {
            b b0 = this.a;
            this.a = null;
            return b0;
        }
    }

    abstract class v0 implements com.jozein.xedgepro.xposed.b0.a {
        private final CharSequence A;
        private final int B;
        private boolean C;
        private float D;
        private float E;
        private float F;
        private int G;
        private int H;
        private View I;
        final w1 J;
        private final Paint z;

        private v0(CharSequence charSequence0) {
            this.H = -1;
            Paint paint0 = new Paint(1);
            this.z = paint0;
            paint0.setColor(w10.H.v(22, 0x7F007FA7));
            this.A = charSequence0;
            this.B = (int)(a0.j() * 0.1f);
        }

        v0(CharSequence charSequence0, k w1$k0) {
            this(charSequence0);
        }

        @Override  // com.jozein.xedgepro.xposed.b0$a
        public final void b(float f, float f1, long v) {
            this.d(f, f1, v);
            this.c();
        }

        @Override  // com.jozein.xedgepro.xposed.b0$a
        public final void c() {
            e2 e20 = () -> if(this.I != null) {
                t3.n().t(5);
                this.I = null;
            };
            w1.this.c0.post(e20);
        }

        @Override  // com.jozein.xedgepro.xposed.b0$a
        public final void d(float f, float f1, long v) {
            int v1 = 0;
            if(this.C) {
                float f2 = this.D;
                if(f > f2) {
                    v1 = f < this.E ? ((int)((f - f2 + this.F / 2.0f) / this.F)) : this.G;
                }
            }
            else if(f1 <= this.D) {
                v1 = this.G;
            }
            else {
                float f3 = this.E;
                if(f < f3) {
                    v1 = (int)((f3 - f1 + this.F / 2.0f) / this.F);
                }
            }
            if(this.H != v1) {
                this.H = v1;
                com.jozein.xedgepro.xposed.f2 f20 = () -> {
                    View view0 = this.I;
                    if(view0 != null) {
                        view0.invalidate();
                    }
                    this.r(v1);
                };
                w1.this.c0.post(f20);
            }
        }

        @Override  // com.jozein.xedgepro.xposed.b0$a
        public final void e(int v, float f, float f1, long v1) {
            FrameLayout.LayoutParams frameLayout$LayoutParams0;
            int v2 = a0.q();
            switch(v) {
                case 0: {
                    this.C = false;
                    frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(v2, -1, 3);
                    break;
                }
                case 1: {
                    this.C = false;
                    frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(v2, -1, 5);
                    break;
                }
                default: {
                    this.C = true;
                    frameLayout$LayoutParams0 = v == 3 ? new FrameLayout.LayoutParams(-1, v2, 80) : new FrameLayout.LayoutParams(-1, v2, 0x30);
                }
            }
            Point point0 = w1.this.g2().I(new Point());
            this.D = (float)this.B;
            this.E = (float)((this.C ? point0.x : point0.y) - this.B);
            int v3 = this.m();
            this.G = v3;
            this.F = (this.E - this.D) / ((float)v3);
            com.jozein.xedgepro.xposed.g2 g20 = () -> {
                class com.jozein.xedgepro.xposed.w1.v0.a extends View {
                    final v0 A;
                    final Rect z;

                    com.jozein.xedgepro.xposed.w1.v0.a(Context context0) {
                        super(context0);
                        this.z = new Rect();
                    }

                    @Override  // android.view.View
                    protected void onDraw(Canvas canvas0) {
                        canvas0.getClipBounds(this.z);
                        if(v0.this.H == v0.this.G) {
                            canvas0.drawRect(this.z, v0.this.z);
                            return;
                        }
                        if(v0.this.H != 0) {
                            if(v0.this.C) {
                                canvas0.drawRect(0.0f, 0.0f, ((float)v0.this.H) * ((float)this.z.right) / ((float)v0.this.G), ((float)this.z.bottom), v0.this.z);
                                return;
                            }
                            canvas0.drawRect(0.0f, (((float)v0.this.G) - ((float)v0.this.H)) * ((float)this.z.bottom) / ((float)v0.this.G), ((float)this.z.right), ((float)this.z.bottom), v0.this.z);
                        }
                    }
                }

                com.jozein.xedgepro.xposed.w1.v0.a w1$v0$a0 = new com.jozein.xedgepro.xposed.w1.v0.a(this, w1.this.a0);
                this.I = w1$v0$a0;
                w1$v0$a0.setKeepScreenOn(true);
                t3.n().k(this.I, 5, frameLayout$LayoutParams0);
                CharSequence charSequence0 = this.A;
                if(charSequence0 != null) {
                    w1.this.J4(charSequence0);
                }
            };
            w1.this.c0.post(g20);
            this.d(f, f1, v1);
        }

        @Override
        protected void finalize() {
            View view0 = this.I;
            if(view0 != null) {
                view0.post(() -> t3.n().t(5));
            }
        }

        protected abstract int m();

        // 检测为 Lambda 实现
        private static void n() [...]

        // 检测为 Lambda 实现
        private void o() [...]

        // 检测为 Lambda 实现
        private void p(int v) [...]

        // 检测为 Lambda 实现
        private void q(FrameLayout.LayoutParams frameLayout$LayoutParams0) [...]

        protected abstract void r(int arg1);
    }

    class com.jozein.xedgepro.xposed.w1.v implements f.i.f {
        final w1 a;

        @Override  // f.i$f
        public int a(String s) {
            return w1.this.L.g(s).a;
        }

        @Override  // f.i$f
        public t b(String s) {
            t t0 = w1.this.L.g(s);
            return t0 == null ? null : new t(t0.a);
        }
    }

    static abstract class w0 implements com.jozein.xedgepro.xposed.a0.a, com.jozein.xedgepro.xposed.b0.a, Runnable {
        private final Handler A;
        private boolean B;
        private final int z;

        private w0(Handler handler0) {
            this.B = false;
            this.A = handler0;
            this.z = ViewConfiguration.getKeyRepeatDelay();
        }

        w0(Handler handler0, k w1$k0) {
            this(handler0);
        }

        @Override  // com.jozein.xedgepro.xposed.a0$a
        public final void a() {
            this.A.post(this);
        }

        @Override  // com.jozein.xedgepro.xposed.b0$a
        public final void b(float f, float f1, long v) {
            this.f();
        }

        @Override  // com.jozein.xedgepro.xposed.b0$a
        public final void c() {
            this.f();
        }

        @Override  // com.jozein.xedgepro.xposed.b0$a
        public final void d(float f, float f1, long v) {
        }

        @Override  // com.jozein.xedgepro.xposed.b0$a
        public final void e(int v, float f, float f1, long v1) {
            this.a();
        }

        @Override  // com.jozein.xedgepro.xposed.a0$a
        public final void f() {
            this.B = true;
            this.A.removeCallbacks(this);
        }

        protected abstract void g();

        @Override
        public void run() {
            if(this.B) {
                return;
            }
            try {
                this.g();
                this.A.postDelayed(this, ((long)this.z));
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    private r3 A0;
    private Toast B0;
    private volatile boolean C0;
    private com.jozein.xedgepro.xposed.k3 D0;
    private final Runnable E0;
    private final Runnable F0;
    private t2 G0;
    private final a.p H;
    private final q0[] H0;
    private final a.k I;
    private PowerManager.WakeLock I0;
    private final a.f J;
    private f.n0 J0;
    private final a.s.e K;
    private View K0;
    private final a.v L;
    private boolean L0;
    private final f.i.f M;
    private final p0 M0;
    private final f.i.f N;
    private f.m0 N0;
    private final y0 O;
    private int O0;
    private final com.jozein.xedgepro.xposed.l0 P;
    private w P0;
    private final com.jozein.xedgepro.xposed.n0 Q;
    private final n0 Q0;
    private final com.jozein.xedgepro.xposed.p0 R;
    private int R0;
    private final com.jozein.xedgepro.xposed.v S;
    private static final AtomicInteger S0;
    private com.jozein.xedgepro.xposed.i3 T;
    private static final String T0;
    private a.t U;
    private com.jozein.xedgepro.xposed.e V;
    private v3 W;
    private y2 X;
    private d3 Y;
    private final r Z;
    private Context a0;
    private Context b0;
    private Handler c0;
    private PowerManager d0;
    private PowerManager.WakeLock e0;
    private boolean f0;
    private int g0;
    private volatile long h0;
    private final boolean i0;
    private boolean j0;
    private boolean k0;
    private boolean l0;
    private boolean m0;
    private boolean n0;
    private f.g0 o0;
    private float p0;
    private boolean q0;
    private long r0;
    private boolean s0;
    private Method t0;
    private final Runnable u0;
    private boolean v0;
    private volatile boolean w0;
    private Rect x0;
    private int y0;
    private Method z0;

    static {
        w1.S0 = new AtomicInteger(0);
        w1.T0 = l.k + "NOTIFICATION.";
    }

    public w1(ClassLoader classLoader0, a.p p0, a.f f0, a.k k0, a.s.e s$e0, a.v v0) {
        class com.jozein.xedgepro.xposed.w1.a extends XC_MethodHook {
            final w1 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                w1.this.A4(((int)(((Integer)xC_MethodHook$MethodHookParam0.args[0]))));
            }
        }


        class com.jozein.xedgepro.xposed.w1.d0 extends XC_MethodHook {
            final w1 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                w1.this.A(xC_MethodHook$MethodHookParam0.thisObject);
                v.c("Initializing...");
                try {
                    w1.this.a0 = (Context)xC_MethodHook$MethodHookParam0.args[0];
                    Handler handler0 = w1.this.L1();
                    w1.this.c0 = handler0;
                    w1.this.Q.e0(w1.this.c0, (xC_MethodHook$MethodHookParam0.args[1] == null ? xC_MethodHook$MethodHookParam0.args[2] : xC_MethodHook$MethodHookParam0.args[1]));
                    w1.this.i2();
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }


        class e0 extends XC_MethodHook {
            final w1 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                w1.this.A(xC_MethodHook$MethodHookParam0.thisObject);
                v.c("System booted.");
                if(w1.this.c0 == null) {
                    Handler handler0 = w1.this.L1();
                    w1.this.c0 = handler0;
                }
                b2 b20 = () -> {
                    if(!w1.this.j0) {
                        w1.this.i2();
                        Object object0 = null;
                        try {
                            object0 = w1.this.u("mWindowManager");
                            if(object0 == null) {
                                object0 = w1.this.u("mWindowManagerFuncs");
                            }
                        }
                        catch(Throwable throwable0) {
                            v.d(throwable0);
                        }
                        w1.this.Q.e0(w1.this.c0, object0);
                    }
                    w1.this.p3();
                };
                w1.this.c0.post(b20);
            }

            // 检测为 Lambda 实现
            private void b() [...]
        }


        class f0 extends XC_MethodHook {
            final int a;
            final w1 b;

            f0() {
                this.a = Build.VERSION.SDK_INT <= 28 ? 1 : 0;
            }

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                if(!w1.this.k0) {
                    return;
                }
                try {
                    w1.this.Q.d0().T(xC_MethodHook$MethodHookParam0.args[this.a]);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }


        class com.jozein.xedgepro.xposed.w1.g0 extends XC_MethodHook {
            class com.jozein.xedgepro.xposed.w1.g0.a implements Runnable {
                final com.jozein.xedgepro.xposed.w1.g0 z;

                @Override
                public void run() {
                    View view0 = w1.this.K0;
                    if(view0 != null) {
                        view0.setVisibility(0);
                        view0.invalidate();
                    }
                }
            }

            final Runnable a;
            final w1 b;

            com.jozein.xedgepro.xposed.w1.g0() {
                this.a = new com.jozein.xedgepro.xposed.w1.g0.a(this);
            }

            protected void beforeHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                long v = 6000L;
                if(w1.this.K0 == null) {
                    return;
                }
                if(!"takeScreenshot".equals(xC_MethodHook$MethodHookParam0.method.getName())) {
                    if(XposedHelpers.getIntField(xC_MethodHook$MethodHookParam0.thisObject, "mScreenshotType") != 2) {
                        v = 2000L;
                    }
                }
                else if(xC_MethodHook$MethodHookParam0.args.length <= 0 || ((int)(((Integer)xC_MethodHook$MethodHookParam0.args[0]))) != 2) {
                    v = 2000L;
                }
                d2 d20 = () -> {
                    View view0 = w1.this.K0;
                    if(view0 != null) {
                        view0.setVisibility(4);
                        view0.invalidate();
                    }
                    c2 c20 = () -> try {
                        XposedBridge.invokeOriginalMethod(xC_MethodHook$MethodHookParam0.method, xC_MethodHook$MethodHookParam0.thisObject, xC_MethodHook$MethodHookParam0.args);
                        w1.this.c0.postDelayed(this.a, v);
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    };
                    w1.this.c0.postDelayed(c20, 700L);
                };
                w1.this.c0.post(d20);
                xC_MethodHook$MethodHookParam0.setResult(null);
            }

            // 检测为 Lambda 实现
            private void c(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, long v) [...]

            // 检测为 Lambda 实现
            private void d(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, long v) [...]
        }


        class h0 extends XC_MethodHook {
            final w1 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                try {
                    if(w1.this.Z.V()) {
                        Rect rect0 = w1.this.w0 ? w1.this.Q.d0().E() : null;
                        w1.this.o5(rect0);
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }


        class i0 extends XC_MethodHook {
            private final Region a;
            private final Rect b;
            final w1 c;

            i0() {
                this.a = new Region();
                this.b = new Rect();
            }

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                if(w1.this.w0) {
                    try {
                        Object object0 = xC_MethodHook$MethodHookParam0.args[0];
                        if(u3.c(object0).type == 2011) {
                            try {
                                u3.e(object0, this.a).getBounds(this.b);
                            }
                            catch(Throwable throwable1) {
                                v.d(throwable1);
                                u3.g(object0, this.b);
                            }
                            w1.this.o5(this.b);
                        }
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }
        }


        class j0 extends XC_MethodHook {
            final w1 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                w1.this.y1();
            }
        }

        super((Build.VERSION.SDK_INT >= 23 ? "com.android.server.policy.PhoneWindowManager" : "com.android.internal.policy.impl.PhoneWindowManager"), classLoader0);
        this.M = new k(this);
        this.N = new com.jozein.xedgepro.xposed.w1.v(this);
        this.W = null;
        this.X = null;
        this.Y = null;
        this.d0 = null;
        this.e0 = null;
        this.f0 = true;
        this.g0 = 0;
        this.h0 = 0L;
        this.j0 = false;
        this.k0 = false;
        this.l0 = false;
        this.m0 = false;
        this.n0 = false;
        this.o0 = null;
        this.p0 = -1.0f;
        this.q0 = false;
        this.r0 = 0L;
        this.s0 = false;
        this.t0 = null;
        this.u0 = new p(this);
        this.v0 = false;
        this.w0 = false;
        this.x0 = null;
        this.y0 = -1;
        this.z0 = null;
        this.A0 = null;
        this.B0 = null;
        this.C0 = false;
        this.D0 = null;
        this.E0 = () -> if(!this.e2().D() && !this.P1().performGlobalAction(4)) {
            this.O.Z(42, 0x10000);
        };
        this.F0 = () -> if(!this.e2().E()) {
            this.P1().performGlobalAction(5);
        };
        this.G0 = null;
        this.H0 = new q0[7];
        this.I0 = null;
        this.J0 = null;
        this.K0 = null;
        this.L0 = false;
        this.M0 = new p0(this, null);
        this.N0 = null;
        this.O0 = 0;
        this.P0 = null;
        this.Q0 = new c0(this);
        this.R0 = 0;
        this.H = p0;
        this.J = f0;
        this.I = k0;
        this.K = s$e0;
        this.L = v0;
        this.O = new y0(this, p0, classLoader0);
        this.Q = new com.jozein.xedgepro.xposed.n0(this.m(), classLoader0, this, p0);
        boolean z = this.m().getSimpleName().contains("Miui");
        this.i0 = z;
        com.jozein.xedgepro.xposed.v v1 = new com.jozein.xedgepro.xposed.v(this);
        this.S = v1;
        com.jozein.xedgepro.xposed.l0 l00 = new com.jozein.xedgepro.xposed.l0(classLoader0, p0, this, v1, z);
        this.P = l00;
        this.Z = new r(classLoader0, this, l00, p0);
        try {
            this.o("init", new com.jozein.xedgepro.xposed.w1.d0(this));
        }
        catch(Throwable throwable0) {
            p2.g(throwable0);
        }
        try {
            this.p("systemBooted", new Object[]{new e0(this)});
        }
        catch(Throwable throwable1) {
            p2.g(throwable1);
        }
        int v2 = Build.VERSION.SDK_INT;
        if(v2 < 0x1F) {
            try {
                f0 w1$f00 = new f0(this);
                if(v2 >= 29) {
                    this.o("onDefaultDisplayFocusChangedLw", w1$f00);
                }
                else if(v2 < 24 || v2 > 27) {
                label_77:
                    this.o("focusChangedLw", w1$f00);
                }
                else {
                    try {
                        XposedHelpers.findAndHookMethod("com.android.server.policy.OemPhoneWindowManager", classLoader0, "focusChangedLw", new Object[]{"android.view.WindowManagerPolicy$WindowState", "android.view.WindowManagerPolicy$WindowState", w1$f00});
                        goto label_81;
                    }
                    catch(Throwable unused_ex) {
                    }
                    goto label_77;
                }
            }
            catch(Throwable throwable2) {
                p2.g(throwable2);
            }
        }
    label_81:
        if(!t3.D) {
            try {
                com.jozein.xedgepro.xposed.w1.g0 w1$g00 = new com.jozein.xedgepro.xposed.w1.g0(this);
                if(Build.VERSION.SDK_INT <= 27) {
                    this.o("takeScreenshot", w1$g00);
                }
                else {
                    XposedHelpers.findAndHookMethod((this.m().getName() + "$ScreenshotRunnable"), classLoader0, "run", new Object[]{w1$g00});
                }
            }
            catch(Throwable throwable3) {
                p2.g(throwable3);
            }
        }
        this.R = new com.jozein.xedgepro.xposed.p0(classLoader0, this);
        try {
            int v3 = Build.VERSION.SDK_INT;
            if(v3 < 29) {
                this.o("layoutWindowLw", new i0(this));
            }
            else if(v3 == 29) {
                XposedHelpers.findAndHookMethod("com.android.server.wm.DisplayContent", classLoader0, "adjustForImeIfNeeded", new Object[]{new h0(this)});
            }
        }
        catch(Throwable throwable4) {
            p2.g(throwable4);
        }
        try {
            j0 w1$j00 = new j0(this);
            this.p((Build.VERSION.SDK_INT >= 21 ? "showGlobalActions" : "showGlobalActionsDialog"), new Object[]{w1$j00});
            this.p("sendCloseSystemWindows", new Object[]{String.class, w1$j00});
        }
        catch(Throwable throwable5) {
            p2.g(throwable5);
        }
        try {
            this.p("setCurrentUserLw", new Object[]{Integer.TYPE, new com.jozein.xedgepro.xposed.w1.a(this)});
        }
        catch(Throwable throwable6) {
            p2.g(throwable6);
        }
        v3.f(classLoader0);
    }

    private void A1(int v) {
        y0 y00;
        int v3;
        int v2;
        int v1 = 0x1000;
        switch(v) {
            case 0: {
                v2 = 0x1020020;
                v3 = 52;
                break;
            }
            case 1: {
                v2 = 0x1020021;
                v3 = 0x1F;
                break;
            }
            case 2: {
                v2 = 0x1020022;
                v3 = 50;
                break;
            }
            case 3: {
                v2 = 0x102001F;
                v3 = 29;
                break;
            }
            case 4: {
                y00 = this.O;
                y00.Z(54, v1);
                return;
            }
            case 5: {
                y00 = this.O;
                v1 = 0x1001;
                y00.Z(54, v1);
                return;
            }
            default: {
                return;
            }
        }
        if(!this.R.N(v2)) {
            this.O.Z(v3, 0x1000);
        }
    }

    // 检测为 Lambda 实现
    private void A2(i3 b$i30) [...]

    // 检测为 Lambda 实现
    private void A3() [...]

    void A4(int v) {
        f.o0.o(v);
        this.P.m1(v);
    }

    private void B1(a.b.s0 b$s00) {
        String s;
        switch(b$s00.I) {
            case 1: {
                this.w1(b$s00.J, b$s00.K);
                return;
            }
            case 2: {
                this.E1(b$s00.J, b$s00.K);
                return;
            }
            case 4: {
                this.v0 = true;
                return;
            }
            case 5: {
                this.v0 = false;
                return;
            }
            case 8: {
                this.a4(b$s00.K);
                return;
            }
            case 9: {
                this.Q3();
                return;
            }
            case 10: {
                this.U2();
                return;
            }
            case 11: {
                this.H.c0(this.a0, 0);
                this.n5();
                return;
            }
            case 14: {
                this.G4(b$s00.J + ": " + this.L.g(b$s00.J).a);
                return;
            }
            case 15: {
                this.Z.q0();
                return;
            }
            case 17: {
                this.P3();
                return;
            }
            case 18: {
                this.V.i(b$s00.K);
                return;
            }
            case 19: {
                t3.v(b$s00.z());
                return;
            }
            case 20: {
                this.m5(b$s00.J);
                return;
            }
            case 21: {
                this.U.g();
                return;
            }
            case 22: {
                this.e2().C(b$s00.J);
                return;
            }
            case 23: {
                this.e2().G(b$s00.J);
                return;
            }
            case 25: {
                this.G3();
                return;
            }
            case 26: {
                this.N3();
                return;
            }
            case 27: {
                s = b$s00.J;
                break;
            }
            case 28: {
                this.Q.x0();
                return;
            }
            case 29: {
                this.P.t1(b$s00.J, b$s00.K);
                return;
            }
            default: {
                s = "Unknown option id: " + b$s00.I;
                break;
            }
        }
        v.c(s);
    }

    // 检测为 Lambda 实现
    private void B2(a.b.y0 b$y00) [...]

    private void B3(y1 b$y10) {
        String s = b$y10.I;
        if(s == null) {
            return;
        }
        if(s.length() == 0) {
            this.M0.b();
            return;
        }
        Uri uri0 = Uri.parse(b$y10.I);
        this.M0.a(uri0, b$y10.J, b$y10.K);
    }

    private void B4() {
        this.C4(1);
    }

    private void C1(int v) {
        int v1;
        switch(v) {
            case 0: {
                v1 = 0x7E;
                break;
            }
            case 1: {
                v1 = 85;
                break;
            }
            case 2: {
                v1 = 86;
                break;
            }
            case 3: {
                v1 = 88;
                break;
            }
            case 4: {
                v1 = 87;
                break;
            }
            default: {
                return;
            }
        }
        this.O3(v1);
    }

    // 检测为 Lambda 实现
    private void C2() [...]

    private void C3(int v) {
        if(this.f0 && !this.t2()) {
            this.y1();
            this.P.g1(v);
        }
    }

    private void C4(int v) {
        if(!this.f0) {
            return;
        }
        if(v != 2) {
            goto label_9;
        }
        if(Build.VERSION.SDK_INT >= 24) {
            try {
                this.D4(2);
            }
            catch(Throwable unused_ex) {
                this.O.Z(0x2F, 0x11001);
            }
            this.O.V().R();
            return;
            try {
            label_9:
                if(Build.VERSION.SDK_INT < 24) {
                    this.r("takeScreenshot", new Object[0]);
                    return;
                }
                this.D4(1);
            }
            catch(Throwable unused_ex) {
                this.m2(120);
            }
        }
    }

    private void D1(i3 b$i30) {
        int v = 4;
        int v1 = 1;
        int v2 = b$i30.I;
        switch(v2) {
            case 0: {
                this.e1(b$i30.J);
                return;
            }
            case 1: {
                this.d1(b$i30.J);
                return;
            }
            case 13: {
                float f = ((float)this.H.v(17, 100)) / 100.0f;
                if(!b$i30.J) {
                    f = -f;
                }
                this.M3(f);
                return;
            }
            default: {
                if(v2 != 14) {
                    if(this.f0 && v2 - 3 <= 4) {
                        v = 5;
                    }
                    if(!b$i30.J) {
                        v1 = -1;
                    }
                    this.V.a(v2 - 3, v1, v);
                    return;
                }
                this.V2(b$i30.J);
            }
        }
    }

    // 检测为 Lambda 实现
    private void D2() [...]

    private void D3() {
        CharSequence charSequence0 = ((ClipboardManager)this.a0.getSystemService("clipboard")).getText();
        if(charSequence0 == null) {
            return;
        }
        this.d2().o(charSequence0.toString(), false, true);
    }

    private void D4(int v) {
        int v1 = Build.VERSION.SDK_INT;
        if(v1 >= 33) {
            this.r("handleScreenShot", new Object[]{v, 2});
            return;
        }
        if(v1 <= 27) {
            this.r("takeScreenshot", new Object[]{v});
            return;
        }
        Runnable runnable0 = (Runnable)this.u("mScreenshotRunnable");
        XposedHelpers.callMethod(runnable0, "setScreenshotType", new Object[]{v});
        runnable0.run();
    }

    private void E1(String s, int v) {
        CharSequence charSequence0 = z.K(this.a0).G(s);
        if(this.P.l1(s, v, false)) {
            this.J4(this.b0.getString(0x7F06007F, new Object[]{charSequence0}));  // string:app_thawed_f "Thawed: %1$s"
            return;
        }
        this.L4(this.b0.getString(0x7F0600FA, new Object[]{charSequence0}));  // string:error_thaw_app_f "Failed to thaw app: %1$s"
    }

    // 检测为 Lambda 实现
    private void E2(boolean z) [...]

    private void E3() {
        long v = this.m3();
        this.c0.postDelayed(() -> {
            if(Build.VERSION.SDK_INT >= 24) {
                try {
                    this.Q.d0().V();
                    return;
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
            this.d0.reboot(null);
        }, v);
    }

    void E4(int v) {
        this.G4(this.b0.getText(v));
    }

    private void F1() {
        this.y1();
        if(this.f0 && (!this.t2() || !this.u2())) {
            if(!this.Q.l0() && this.Q.q0()) {
                this.c0.postDelayed(this.E0, 200L);
                return;
            }
            this.G1();
        }
    }

    // 检测为 Lambda 实现
    private void F2() [...]

    private void F3() {
        this.P.i1();
    }

    private void F4(b b0, Throwable throwable0) {
        String s;
        if(b0.z == 0xFFFF) {
            if(throwable0 instanceof InvocationTargetException) {
                throwable0 = ((InvocationTargetException)throwable0).getTargetException();
            }
            s = throwable0.getMessage();
            if(s == null) {
                s = throwable0.toString();
            }
        }
        else {
            s = "Failed to perform: " + b0.n(this.b0) + ", " + throwable0;
        }
        this.L4(s);
    }

    // 检测为 Lambda 实现
    private void G1() [...]

    // 检测为 Lambda 实现
    private void G2(b b0, int v, Bundle bundle0) [...]

    private void G3() {
        v.c("Start reload settings.");
        this.H.g();
        com.jozein.xedgepro.xposed.w.c();
        this.H3();
    }

    void G4(CharSequence charSequence0) {
        if(Build.VERSION.SDK_INT >= 24) {
            Toast toast0 = this.B0;
            if(toast0 != null) {
                toast0.cancel();
            }
            this.B0 = Toast.makeText(this.a0, charSequence0, 0);
            if(f.o0.d() != 0) {
                w1.S3(this.B0);
            }
        }
        else {
            Toast toast1 = this.B0;
            if(toast1 == null) {
                Toast toast2 = Toast.makeText(this.a0, charSequence0, 0);
                this.B0 = toast2;
                w1.S3(toast2);
            }
            else {
                toast1.setText(charSequence0);
            }
        }
        this.B0.show();
    }

    private void H1() {
        this.y1();
        if(this.f0 && (!this.t2() || !this.u2())) {
            if(!this.Q.l0() && this.Q.q0()) {
                this.c0.postDelayed(this.F0, 200L);
                return;
            }
            this.I1();
        }
    }

    // 检测为 Lambda 实现
    private void H2() [...]

    void H3() {
        String s;
        try {
            if(this.H.J()) {
                this.v4();
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        if(this.H.L()) {
            this.I.l();
            this.J.m();
            this.K.e();
            this.L.h();
            a.b.f0.A().o();
            k2.A().o();
            a.b.y0.L();
            if(l.r) {
                this.K.n(false);
            }
            this.Q.x0();
            s = "Settings reloaded.";
        }
        else {
            s = "Failed to reload settings!";
        }
        v.c(s);
    }

    private void H4(int v) {
        if(this.k0 && this.f0 && this.H.o(24)) {
            this.G4(this.b0.getText(v));
        }
    }

    // 检测为 Lambda 实现
    private void I1() [...]

    // 检测为 Lambda 实现
    private void I2() [...]

    void I3() {
        this.c0.removeCallbacks(this.u0);
    }

    private void I4(k3 b$k30, boolean z) {
        if(this.k0 && this.f0 && this.H.o(24)) {
            this.G4(this.b0.getString((z ? 0x7F060165 : 0x7F060164), new Object[]{b$k30.p(this.b0)}));  // string:on_f "%1$s on"
        }
    }

    private void J1(int v) {
        switch(v) {
            case 0: {
                this.M3(100000.0f);
                return;
            }
            case 1: {
                this.M3(-100000.0f);
            }
        }
    }

    // 检测为 Lambda 实现
    private static void J2(Runnable runnable0) [...]

    void J3(int v) {
        if(!this.f0) {
            return;
        }
        this.Q.r0(v);
    }

    private void J4(CharSequence charSequence0) {
        if(this.k0 && this.f0 && this.H.o(24)) {
            this.G4(charSequence0);
        }
    }

    private void K1() {
        if(!this.f0 && this.H.o(9)) {
            return;
        }
        if(this.H.o(4)) {
            int v = this.H.v(23, 0);
            this.V.i(v);
        }
        this.U2();
    }

    // 检测为 Lambda 实现
    private static void K2(t3 t30) [...]

    private void K3(a.b.a0 b$a00) {
        long v;
        if(!l.r) {
            throw new RuntimeException("!");
        }
        if(b$a00.z() == null) {
            return;
        }
        boolean z = this.H.o(25);
        boolean z1 = this.H.o(36);
        if(b$a00.I) {
            f.i0.c(b$a00, this.b0, this.c0, z, z1);
            v = 4000L;
        }
        else {
            BinderService.k(this.a0, b$a00, z, z1);
            v = 5000L;
        }
        this.b1(v);
    }

    private void K4(int v) {
        if(this.k0 && this.f0) {
            this.L4(this.b0.getText(v));
        }
    }

    private Handler L1() {
        Handler handler0;
        Handler handler2;
        Handler handler1;
        Looper looper0 = Looper.myLooper();
        if(looper0 == null || !Thread.currentThread().getName().equals("android.ui")) {
            try {
                handler1 = (Handler)this.u("mHandler");
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                handler1 = null;
            }
            if(handler1 == null) {
                try {
                    handler2 = (Handler)XposedHelpers.callStaticMethod(XposedHelpers.findClass("com.android.server.UiThread", this.m().getClassLoader()), "getHandler", new Object[0]);
                }
                catch(Throwable throwable1) {
                    v.d(throwable1);
                    handler1 = null;
                    handler0 = handler1 != null || looper0 == null ? new f.h0(Looper.getMainLooper()) : new f.h0(looper0);
                    f.h0.g(handler0);
                    return handler0;
                }
                if(handler2 == null) {
                    handler1 = null;
                }
                else {
                    try {
                        handler1 = new f.h0(handler2.getLooper());
                    }
                    catch(Throwable throwable2) {
                        v.d(throwable2);
                        handler1 = handler2;
                    }
                }
            }
            handler0 = handler1 != null || looper0 == null ? new f.h0(Looper.getMainLooper()) : new f.h0(looper0);
        }
        else {
            handler0 = new f.h0(looper0);
        }
        f.h0.g(handler0);
        return handler0;
    }

    // 检测为 Lambda 实现
    private void L2() [...]

    private void L3() {
        class u implements e {
            final List a;
            final w1 b;

            u() {
                this.a = w10.P.l0();
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public boolean a() {
                return w1.this.H.o(11);
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public boolean b(int v) {
                a2 a20 = () -> try {
                    f l0$f0 = ((j)this.a.get(v)).t();
                    w1.this.Y3(l0$f0);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                };
                w1.this.c0.post(a20);
                return true;
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public Drawable c(int v) {
                j l0$j0 = (j)this.a.get(v);
                Drawable drawable0 = l0$j0.d();
                a0.v(drawable0, w1.this.P.I0(l0$j0.i(), l0$j0.k()));
                return drawable0;
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public CharSequence d(int v) {
                return ((j)this.a.get(v)).f();
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public void e(int v, View view0) {
                ((j)this.a.get(v)).s(a.c(view0));
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public void f() {
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public int getCount() {
                return this.a.size();
            }

            // 检测为 Lambda 实现
            private void h(int v) [...]
        }

        this.t3();
        t2 t20 = this.Y1();
        if(t20.l(2)) {
            t20.j();
            return;
        }
        t20.o(new u(this), 2);
    }

    private void L4(CharSequence charSequence0) {
        if(this.k0 && this.f0) {
            Toast toast0 = this.B0;
            if(toast0 != null) {
                toast0.cancel();
                this.B0 = null;
            }
            Toast toast1 = Toast.makeText(this.a0, charSequence0, 1);
            if(f.o0.d() != 0) {
                w1.S3(toast1);
            }
            toast1.show();
        }
    }

    static int M0(w1 w10) {
        int v = w10.O0 + 1;
        w10.O0 = v;
        return v;
    }

    private void M1() {
        if(this.t2()) {
            return;
        }
        this.y1();
        this.P.e0();
    }

    // 检测为 Lambda 实现
    private void M2(Intent intent0) [...]

    private void M3(float f) {
        Point point0 = this.Q.Z();
        this.O.g0(f, ((float)point0.x), ((float)point0.y));
    }

    private void M4(b b0) {
        if(this.k0 && this.f0 && this.H.o(25)) {
            this.G4(b0.n(this.b0));
        }
    }

    private String N1(String s) {
        return s == null ? null : s.replace("%b", Integer.toString(this.T.q()));
    }

    // 检测为 Lambda 实现
    private void N2(float f, float f1, boolean z, String s) [...]

    private void N3() {
        Intent intent0 = new Intent(a.b.s0.O);
        intent0.putExtra("logs", v.b());
        intent0.setPackage(l.j);
        this.P.W(intent0, 0);
    }

    private void N4(k3 b$k30) {
        ContentResolver contentResolver0 = this.a0.getContentResolver();
        boolean z = false;
        int v = com.jozein.xedgepro.xposed.h3.a.a(contentResolver0, "airplane_mode_on", 0);
        int v1 = w1.S2(b$k30.I, v);
        if(v != v1) {
            com.jozein.xedgepro.xposed.h3.a.b(contentResolver0, "airplane_mode_on", v1);
            Intent intent0 = new Intent("android.intent.action.AIRPLANE_MODE");
            intent0.putExtra("state", v1 != 0);
            this.P.Y(intent0);
            if(v1 != 0) {
                z = true;
            }
            this.I4(b$k30, z);
        }
    }

    private void O1() {
        class com.jozein.xedgepro.xposed.w1.t implements e {
            List a;
            List b;
            final w1 c;

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public boolean a() {
                return w1.this.H.o(11);
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public boolean b(int v) {
                z1 z10 = () -> {
                    List list0 = this.a;
                    if(list0 != null) {
                        int v1 = list0.size();
                        if(v < v1) {
                            f l0$f0 = (f)this.a.get(v);
                            w1.this.Y3(l0$f0);
                            return;
                        }
                        v -= v1;
                    }
                    f.c0.e c0$e0 = (f.c0.e)this.b.get(v);
                    w1.this.Z3(c0$e0);
                };
                w1.this.c0.post(z10);
                return true;
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public Drawable c(int v) {
                List list0 = this.a;
                if(list0 != null) {
                    int v1 = list0.size();
                    if(v < v1) {
                        return ((f)this.a.get(v)).d();
                    }
                    v -= v1;
                }
                Drawable drawable0 = ((f.c0.e)this.b.get(v)).d();
                a0.v(drawable0, true);
                return drawable0;
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public CharSequence d(int v) {
                List list0 = this.a;
                if(list0 != null) {
                    int v1 = list0.size();
                    if(v < v1) {
                        return ((f)this.a.get(v)).f();
                    }
                    v -= v1;
                }
                return ((f.c0.e)this.b.get(v)).f();
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public void e(int v, View view0) {
                Bundle bundle0 = a.c(view0);
                List list0 = this.a;
                if(list0 != null) {
                    int v1 = list0.size();
                    if(v < v1) {
                        ((f)this.a.get(v)).E(bundle0);
                        return;
                    }
                    v -= v1;
                }
                f.c0.e c0$e0 = (f.c0.e)this.b.get(v);
                w1.this.p4(c0$e0, bundle0);
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public void f() {
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public int getCount() {
                this.a = w1.this.P.u0();
                f.c0.c c0$c0 = new f.c0.c();
                c0$c0.b(w1.this.I.i(), -1);
                for(Object object0: this.a) {
                    c0$c0.a(((f)object0).i(), ((f)object0).k());
                }
                List list0 = z.K(w1.this.a0).N(c0$c0, 0x20000);
                this.b = list0;
                return list0.size() + this.a.size();
            }

            // 检测为 Lambda 实现
            private void h(int v) [...]
        }

        this.t3();
        t2 t20 = this.Y1();
        if(t20.l(1)) {
            t20.j();
            return;
        }
        t20.o(new com.jozein.xedgepro.xposed.w1.t(this), 1);
    }

    // 检测为 Lambda 实现
    private void O2(float f, float f1) [...]

    private void O3(int v) {
        this.V.b(v);
    }

    private void O4(k3 b$k30) {
        ContentResolver contentResolver0 = this.a0.getContentResolver();
        boolean z = false;
        int v = com.jozein.xedgepro.xposed.h3.c.a(contentResolver0, "screen_brightness_mode", 0);
        int v1 = w1.S2(b$k30.I, v);
        if(v != v1) {
            com.jozein.xedgepro.xposed.h3.c.b(contentResolver0, "screen_brightness_mode", v1);
            if(v1 != 0) {
                z = true;
            }
            this.I4(b$k30, z);
        }
    }

    private v3 P1() {
        if(this.W == null) {
            this.W = new v3(this);
        }
        return this.W;
    }

    // 检测为 Lambda 实现
    private static void P2() [...]

    private void P3() {
        Intent intent0 = new Intent(a.b.s0.N).setPackage(l.j).putExtra("alias", f.q0.c()).putExtra("time", l.s).putExtra("build_time", l.t);
        if(this.W2()) {
            intent0.putExtra("send_files", true);
        }
        this.P.W(intent0, 0);
    }

    private void P4(k3 b$k30) {
        ContentResolver contentResolver0 = this.a0.getContentResolver();
        boolean z = false;
        int v = com.jozein.xedgepro.xposed.h3.c.a(contentResolver0, "accelerometer_rotation", 0);
        int v1 = w1.S2(b$k30.I, v);
        if(v != v1) {
            com.jozein.xedgepro.xposed.h3.c.b(contentResolver0, "accelerometer_rotation", v1);
            h3.a(contentResolver0, Settings.System.getUriFor("accelerometer_rotation"), null);
            if(v1 != 0) {
                z = true;
            }
            this.I4(b$k30, z);
        }
    }

    private boolean Q1(b b0) {
        switch(b0.z) {
            case 18: {
                return this.V.d() == 2;
            }
            case 19: {
                return this.T.s();
            }
            case 20: {
                return this.T.t();
            }
            case 21: {
                return this.T.I();
            }
            case 22: {
                return this.T.y();
            }
            case 23: {
                return this.T.x();
            }
            case 24: {
                return this.T.u();
            }
            case 25: {
                return this.T.A();
            }
            case 26: {
                return this.T.E();
            }
            case 27: {
                return this.T.G();
            }
            case 28: {
                return this.T.r();
            }
            case 29: {
                return this.Q.h0();
            }
            case 30: {
                return this.I0 != null;
            }
            case 0x20: {
                return this.x2();
            }
            case 33: {
                return this.K0 != null;
            }
            case 34: {
                return this.H.p(1);
            }
            case 35: {
                return this.H.p(0);
            }
            case 73: {
                return this.Q.i0();
            }
            case 97: {
                return this.L0;
            }
            default: {
                return true;
            }
        }
    }

    // 检测为 Lambda 实现
    private void Q2(int v) [...]

    private void Q3() {
        f.d.b(() -> {
            String[] arr_s = n2.a(this.a0);
            Intent intent0 = new Intent(a.b.s0.M);
            intent0.putExtra("list", arr_s);
            intent0.setPackage(l.j);
            try {
                this.P.W(intent0, 0);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        });
    }

    private void Q4(k3 b$k30) {
        int v = b$k30.I;
        BluetoothAdapter bluetoothAdapter0 = BluetoothAdapter.getDefaultAdapter();
        switch(bluetoothAdapter0.getState()) {
            case 11: 
            case 12: {
                if(v == 0 || v == 2) {
                    bluetoothAdapter0.disable();
                    this.I4(b$k30, false);
                    return;
                }
                break;
            }
            case 10: 
            case 13: {
                if(v == 0 || v == 1) {
                    bluetoothAdapter0.enable();
                    this.I4(b$k30, true);
                    return;
                }
                break;
            }
        }
    }

    com.jozein.xedgepro.xposed.l0 R1() {
        return this.P;
    }

    private void R2(int v) {
        if(this.f0 && !this.t2()) {
            this.y1();
            this.P.P0(v);
        }
    }

    private void R3(int v) {
        if(!this.H.o(34)) {
            com.jozein.xedgepro.xposed.f.e(this.a0, false).i(this.a0, false);
        }
        com.jozein.xedgepro.xposed.f.j(this.a0, v);
    }

    private void R4(k3 b$k30) {
        boolean z = w1.T2(b$k30.I, this.L0);
        if(this.L0 != z) {
            this.L0 = z;
            if(z) {
                this.d4(6, new a.b.o0(2), 0x7F040046);  // drawable:ic_game_mode
            }
            else {
                this.j1(6);
            }
            this.U.l(b$k30.z, z);
            this.I4(b$k30, z);
        }
    }

    v0 S1(z2 b$z20) {
        class com.jozein.xedgepro.xposed.w1.f extends v0 {
            final w1 K;

            com.jozein.xedgepro.xposed.w1.f(CharSequence charSequence0) {
                super(charSequence0, null);
            }

            @Override  // com.jozein.xedgepro.xposed.w1$v0
            protected int m() {
                return 14;
            }

            @Override  // com.jozein.xedgepro.xposed.w1$v0
            protected void r(int v) {
                w1.this.H.c0(w1.this.a0, (v + 1 < 1 ? 1 : v + 1));
                if(w1.this.K0 != null) {
                    int v1 = w1.this.H.B();
                    w1.this.K0.setBackgroundColor(v1);
                }
            }
        }


        class g extends v0 {
            final ContentResolver K;
            final com.jozein.xedgepro.xposed.f L;
            final w1 M;

            g(CharSequence charSequence0) {
                super(charSequence0, null);
                this.K = w10.a0.getContentResolver();
                this.L = com.jozein.xedgepro.xposed.f.e(w10.a0, true);
            }

            @Override  // com.jozein.xedgepro.xposed.w1$v0
            protected int m() {
                if(!w1.this.H.o(34) && com.jozein.xedgepro.xposed.h3.c.a(this.K, "screen_brightness_mode", 0) != 0) {
                    com.jozein.xedgepro.xposed.h3.c.b(this.K, "screen_brightness_mode", 0);
                }
                return this.L.f();
            }

            @Override  // com.jozein.xedgepro.xposed.w1$v0
            protected void r(int v) {
                this.L.k(w1.this.a0, v);
            }
        }


        class com.jozein.xedgepro.xposed.w1.h extends v0 {
            int K;
            int L;
            final w1 M;

            com.jozein.xedgepro.xposed.w1.h(CharSequence charSequence0) {
                super(charSequence0, null);
                this.K = -1;
                this.L = 0;
            }

            @Override  // com.jozein.xedgepro.xposed.w1$v0
            protected int m() {
                return 0x20;
            }

            @Override  // com.jozein.xedgepro.xposed.w1$v0
            protected void r(int v) {
                if(this.K == -1) {
                    this.K = v;
                }
                int v1 = v - this.K;
                int v2 = this.L;
                if(v1 != v2) {
                    for(int v3 = 0; v3 < (v1 >= v2 ? v1 - v2 : v2 - v1); ++v3) {
                        w1.this.V2(v1 < this.L);
                    }
                    this.L = v1;
                }
            }
        }


        class i extends v0 {
            final int K;
            final w1 L;

            i(CharSequence charSequence0, int v) {
                this.K = v;
                super(charSequence0, null);
            }

            @Override  // com.jozein.xedgepro.xposed.w1$v0
            protected int m() {
                return w1.this.V.e(this.K);
            }

            @Override  // com.jozein.xedgepro.xposed.w1$v0
            protected void r(int v) {
                w1.this.V.k(this.K, v, 0);
            }
        }

        CharSequence charSequence0 = this.H.o(24) ? b$z20.n(this.b0) : null;
        int v = b$z20.I;
        switch(v) {
            case 0: {
                return new com.jozein.xedgepro.xposed.w1.f(this, charSequence0);
            }
            case 1: {
                return new g(this, charSequence0);
            }
            case 2: {
                return new i(this, charSequence0, this.V.c());
            }
            case 13: {
                return new com.jozein.xedgepro.xposed.w1.h(this, charSequence0);
            }
            default: {
                return new i(this, charSequence0, v - 3);
            }
        }
    }

    private static int S2(int v, int v1) {
        if(v == 1) {
            return 1;
        }
        return v != 2 && v1 == 0 ? 1 : 0;
    }

    private static void S3(Toast toast0) {
        try {
            t3.i(((WindowManager.LayoutParams)XposedHelpers.getObjectField(XposedHelpers.getObjectField(toast0, "mTN"), "mParams")), 16);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private void S4(k3 b$k30) {
        int v = 1;
        boolean z = this.H.p(1);
        boolean z1 = w1.T2(b$k30.I, z);
        if(z != z1) {
            this.H.X(this.a0, 1, z1);
            boolean z2 = this.H.p(0);
            if(!z1 && !z2) {
                v = 0;
            }
            if((z || z2 ? 1 : 0) != v) {
                this.O.C0();
            }
            if(z1) {
                this.d4(4, new a.b.p0(2), 0x7F040048);  // drawable:ic_gesture_enabled
            }
            else {
                this.j1(4);
            }
            this.U.l(b$k30.z, z1);
            this.I4(b$k30, z1);
        }
    }

    static int T0(w1 w10) {
        int v = w10.R0 + 1;
        w10.R0 = v;
        return v;
    }

    Context T1() {
        return this.a0;
    }

    // 去混淆评级： 低(20)
    private static boolean T2(int v, boolean z) {
        return v == 1 || v != 2 && !z;
    }

    void T3() {
        this.C0 = true;
    }

    private void T4(k3 b$k30) {
        if(!this.f0) {
            return;
        }
        boolean z = this.O.k0();
        boolean z1 = w1.T2(b$k30.I, z);
        if(z != z1) {
            y0 y00 = this.O;
            if(z1) {
                y00.A0(true);
                this.d4(3, new a.b.r0(2), 0x7F04004A);  // drawable:ic_gesture_record
            }
            else {
                y00.A0(false);
                this.j1(3);
            }
            this.U.l(b$k30.z, z1);
            this.I4(b$k30, z1);
        }
    }

    Handler U1() {
        return this.c0;
    }

    private void U2() {
        try {
            if(this.A0 == null) {
                this.A0 = new r3(this.v(), this.m(), this.a0, this.H);
            }
            this.A0.s();
        }
        catch(Throwable unused_ex) {
        }
    }

    private void U3(a.b.p2 b$p20) {
        int v1;
        synchronized(this.M) {
            v1 = b$p20.A(this.M);
        }
        this.J4(b$p20.n(this.b0) + ", " + v1);
    }

    private void U4(k3 b$k30) {
        boolean z = this.Q.h0();
        boolean z1 = w1.T2(b$k30.I, z);
        if(z1 != z) {
            this.Q.t0(z1);
            this.U.l(b$k30.z, z1);
            this.I4(b$k30, z1);
        }
    }

    com.jozein.xedgepro.xposed.n0 V1() {
        return this.Q;
    }

    private void V2(boolean z) {
        if(!this.R.K((z ? 21 : 22))) {
            this.O.Y((z ? 21 : 22));
        }
    }

    private void V3(k3 b$k30, boolean z) {
        class com.jozein.xedgepro.xposed.w1.x extends ResultReceiver {
            final w1 A;
            final k3 z;

            com.jozein.xedgepro.xposed.w1.x(Handler handler0, k3 b$k30) {
                this.z = b$k30;
                super(handler0);
            }

            @Override  // android.os.ResultReceiver
            protected void onReceiveResult(int v, Bundle bundle0) {
                if(v == 0) {
                    try {
                        w1.this.I4(this.z, true);
                    }
                    catch(Throwable unused_ex) {
                    }
                }
                else {
                    v.c(("Failed to turn on Wifi hotspot with error code : " + v));
                }
            }
        }


        class com.jozein.xedgepro.xposed.w1.w extends f.f0.d {
            final w1 a;

            @Override  // f.f0$d
            protected Object a(String s, Object[] arr_object) {
                return null;
            }
        }

        com.jozein.xedgepro.xposed.w1.x w1$x0 = null;
        int v = Build.VERSION.SDK_INT;
        if(v > 29 && z) {
            Object object0 = this.a0.getSystemService("tethering");
            ClassLoader classLoader0 = p2.c();
            Object object1 = Proxy.newProxyInstance(classLoader0, new Class[]{XposedHelpers.findClass("android.net.TetheringManager$StartTetheringCallback", classLoader0)}, new com.jozein.xedgepro.xposed.w1.w(this));
            XposedHelpers.callMethod(object0, "startTethering", new Object[]{0, (Runnable runnable0) -> {
                try {
                    runnable0.run();
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
                runnable0.run();
            }, object1});
        }
        else if(v >= 26) {
            ConnectivityManager connectivityManager0 = (ConnectivityManager)this.a0.getSystemService("connectivity");
            if(z) {
                w1$x0 = new com.jozein.xedgepro.xposed.w1.x(this, this.c0, b$k30);
            }
            try {
                if(z) {
                    Object object2 = XposedHelpers.getObjectField(connectivityManager0, "mService");
                    if(v >= 27) {
                        XposedHelpers.callMethod(object2, "startTethering", new Object[]{0, w1$x0, Boolean.FALSE, "android"});
                        return;
                    }
                    XposedHelpers.callMethod(object2, "startTethering", new Object[]{0, w1$x0, Boolean.FALSE});
                    return;
                }
                XposedHelpers.callMethod(connectivityManager0, "stopTethering", new Object[]{0});
            }
            catch(Throwable unused_ex) {
                Object object3 = XposedHelpers.getObjectField(XposedHelpers.getObjectField(connectivityManager0, "mService"), "mTethering");
                if(z) {
                    XposedHelpers.callMethod(object3, "startTethering", new Object[]{0, w1$x0, Boolean.FALSE});
                    return;
                }
                XposedHelpers.callMethod(object3, "stopTethering", new Object[]{0});
            }
        }
        else {
            XposedHelpers.callMethod(this.a0.getSystemService("wifi"), "setWifiApEnabled", new Object[]{null, Boolean.valueOf(z)});
        }
        this.I4(b$k30, z);
    }

    private void V4(k3 b$k30) {
        boolean z = this.Q.i0();
        boolean z1 = w1.T2(b$k30.I, z);
        if(z1 != z) {
            this.Q.v0(z1);
            this.U.l(b$k30.z, z1);
            this.I4(b$k30, z1);
        }
    }

    y0 W1() {
        return this.O;
    }

    // 去混淆评级： 中等(50)
    private boolean W2() {
        return !this.H.J() || a.p.q(this.a0, 8);
    }

    private void W3(boolean z) {
        if(z || Build.VERSION.SDK_INT >= 0x1F) {
            this.Q.d0().W();
        }
    }

    private void W4(k3 b$k30) {
        boolean z = w1.T2(b$k30.I, this.I0 != null);
        if(this.I0 != null == z) {
            return;
        }
        if(z) {
            PowerManager.WakeLock powerManager$WakeLock0 = this.d0.newWakeLock(10, l.x + ":awake");
            this.I0 = powerManager$WakeLock0;
            powerManager$WakeLock0.setReferenceCounted(false);
            this.I0.acquire();
            this.d4(0, new d1(2), 0x7F040056);  // drawable:ic_keep_screen_on
        }
        else {
            this.I0.release();
            this.I0 = null;
            this.j1(0);
        }
        this.U.l(b$k30.z, z);
        this.I4(b$k30, z);
    }

    int X1() {
        if(!this.w0) {
            return -1;
        }
        if(Build.VERSION.SDK_INT >= 29) {
            try {
                Rect rect0 = this.Q.d0().E();
                return rect0 == null ? -1 : rect0.top;
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return -1;
            }
        }
        return this.y0;
    }

    private n0 X2(b b0) {
        switch(b0.z) {
            case 0: 
            case 1: {
                return this.Q0;
            }
            case 62: {
                return new l0(this, ((d0)b0));
            }
            case 0x3F: {
                return new o0(this, ((q1)b0).I);
            }
            case 72: {
                return this.X2(((d)b0).K);
            }
            case 84: {
                return new o0(this, ((k2)b0).z());
            }
            case 101: {
                return new s0(this, ((f2)b0));
            }
            default: {
                return new u0(b0);
            }
        }
    }

    private void X3() {
        if(!this.f0) {
            return;
        }
        this.t3();
        t2 t20 = this.Y1();
        if(t20.l(0)) {
            t20.j();
            return;
        }
        int v = 0;
        int v1 = 0;
        for(int v2 = 0; v2 < 4; ++v2) {
            for(int v3 = 0; v3 < 4; ++v3) {
                switch(this.H.m(v2, v3).z) {
                    case 0: 
                    case 1: {
                        break;
                    }
                    default: {
                        if(v2 > v1) {
                            v1 = v2;
                        }
                        if(v3 > v) {
                            v = v3;
                        }
                    }
                }
            }
        }
        b[] arr_b = new b[(v1 + 1) * (v + 1)];
        for(int v4 = 0; v4 < v1 + 1; ++v4) {
            int v6 = 0;
            for(int v5 = 0; v6 < v + 1; ++v5) {
                arr_b[v5] = this.H.m(v4, v6);
                ++v6;
            }
        }
        t20.n(new t0(this, arr_b, this.H.o(10)), 1, v + 1, 0);
    }

    private void X4(k3 b$k30) {
        int v = 0;
        boolean z = this.H.p(0);
        boolean z1 = w1.T2(b$k30.I, z);
        if(z != z1) {
            this.H.X(this.a0, 0, z1);
            boolean z2 = this.H.p(1);
            if(z1 || z2) {
                v = 1;
            }
            if((z || z2 ? 1 : 0) != v) {
                this.O.C0();
            }
            if(z1) {
                this.d4(5, new e1(2), 0x7F040057);  // drawable:ic_key_enabled
            }
            else {
                this.j1(5);
            }
            this.U.l(b$k30.z, z1);
            this.I4(b$k30, z1);
        }
    }

    t2 Y1() {
        if(this.G0 == null) {
            this.G0 = new t2(this);
        }
        return this.G0;
    }

    private void Y2(int v) {
        if(this.f0 && !this.t2()) {
            this.y1();
            this.P.Y0(v);
        }
    }

    private void Y3(f l0$f0) {
        class com.jozein.xedgepro.xposed.w1.a0 extends FrameLayout {
            final w1 z;

            com.jozein.xedgepro.xposed.w1.a0(Context context0) {
                super(context0);
            }

            @Override  // android.view.View
            public boolean onGenericMotionEvent(MotionEvent motionEvent0) {
                View view0 = this.getChildAt(0);
                return view0 == null ? super.onGenericMotionEvent(motionEvent0) : view0.onGenericMotionEvent(motionEvent0);
            }
        }


        class b0 extends ListView {
            final w1 A;
            final o2 z;

            b0(Context context0, o2 o20) {
                this.z = o20;
                super(context0);
            }

            @Override  // android.view.View
            public boolean hasWindowFocus() {
                return true;
            }

            @Override  // android.widget.AbsListView
            public boolean onTouchEvent(MotionEvent motionEvent0) {
                if(this.z.c()) {
                    return true;
                }
                try {
                    return super.onTouchEvent(motionEvent0);
                }
                catch(Throwable throwable0) {
                    this.z.b(throwable0);
                    return true;
                }
            }
        }

        this.y1();
        com.jozein.xedgepro.xposed.w1.a0 w1$a00 = new com.jozein.xedgepro.xposed.w1.a0(this, this.a0);
        o2 o20 = new o2(this.a0, 19);
        b0 w1$b00 = new b0(this, this.a0, o20);
        o20.a(w1$b00);
        w1$b00.setBackgroundColor(this.H.v(33, 0xFF003037));
        w1$b00.setSelector(a0.l());
        k0 w1$k00 = new k0(this, l0$f0);
        w1$b00.setAdapter(w1$k00);
        w1$b00.setOnItemClickListener(w1$k00);
        w1$b00.setDividerHeight(0);
        w1$a00.addView(w1$b00, new FrameLayout.LayoutParams(-1, -2, 80));
        float f = (float)w1$k00.getCount();
        if(f > 4.0f) {
            f = 4.2f;
        }
        int v = (int)(((float)w1$k00.A) * f);
        t3.n().k(w1$a00, 19, new FrameLayout.LayoutParams(-1, v, 80));
        AnimationSet animationSet0 = new AnimationSet(true);
        animationSet0.addAnimation(new TranslateAnimation(0.0f, 0.0f, ((float)v) / 2.0f, 0.0f));
        animationSet0.addAnimation(new AlphaAnimation(0.0f, 1.0f));
        a0.z(w1$b00, animationSet0, ((long)(f * 132.0f / 3.0f)));
    }

    private void Y4(k3 b$k30) {
        ContentResolver contentResolver0 = this.a0.getContentResolver();
        boolean z = false;
        int v = com.jozein.xedgepro.xposed.h3.b.a(contentResolver0, "location_mode", 0);
        int v1 = w1.S2(b$k30.I, v);
        if(v != v1) {
            if(v1 != 0) {
                v1 = 3;
            }
            com.jozein.xedgepro.xposed.h3.b.b(contentResolver0, "location_mode", v1);
            Intent intent0 = new Intent("android.location.MODE_CHANGED");
            this.P.Y(intent0);
            if(v1 != 0) {
                z = true;
            }
            this.I4(b$k30, z);
        }
    }

    y2 Z1() {
        if(this.X == null) {
            this.X = new y2(this.H, this, this.a0, this.c0);
        }
        return this.X;
    }

    private void Z2(u1 b$u10) {
        String s = f.m0.i(this.N1(b$u10.J), this.a0);
        new q0(this).c(s, s, 0x7F04005D, b$u10.I, true, false, null);  // drawable:ic_launcher
    }

    private void Z3(f.c0.e c0$e0) {
        try {
            this.Y3(new f(this.P, c0$e0.i(), c0$e0.k()));
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private void Z4(k3 b$k30) {
        boolean z1;
        int v = b$k30.I;
        if(Build.VERSION.SDK_INT < 21) {
            ConnectivityManager connectivityManager0 = (ConnectivityManager)this.a0.getSystemService("connectivity");
            boolean z = ((Boolean)XposedHelpers.callMethod(connectivityManager0, "getMobileDataEnabled", new Object[0])).booleanValue();
            z1 = w1.T2(v, z);
            if(z != z1) {
                XposedHelpers.callMethod(XposedHelpers.getObjectField(connectivityManager0, "mService"), "setMobileDataEnabled", new Object[]{Boolean.valueOf(z1)});
                this.I4(b$k30, z1);
            }
        }
        else {
            TelephonyManager telephonyManager0 = (TelephonyManager)this.a0.getSystemService("phone");
            boolean z2 = ((Boolean)XposedHelpers.callMethod(telephonyManager0, "getDataEnabled", new Object[0])).booleanValue();
            z1 = w1.T2(v, z2);
            if(z2 != z1) {
                XposedHelpers.callMethod(telephonyManager0, "setDataEnabled", new Object[]{Boolean.valueOf(z1)});
                this.I4(b$k30, z1);
            }
        }
    }

    d3 a2(int v) {
        if(this.Y == null) {
            this.Y = new d3(this);
        }
        this.Y.y(v);
        return this.Y;
    }

    void a3() {
        this.c0.post(() -> {
            this.O.A0(false);
            this.j1(3);
        });
    }

    void a4(int v) {
        m0 w1$m00;
        FrameLayout.LayoutParams frameLayout$LayoutParams3;
        Point point0 = this.g2().I(new Point());
        Point point1 = this.O.V().y();
        int v1 = point1.x;
        int v2 = point1.y;
        int v3 = point0.y - v2 * 2;
        int v4 = point0.x - v1 * 2;
        FrameLayout frameLayout0 = new FrameLayout(this.a0);
        switch(v) {
            case 0: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v1, v3, 19);
                w1$m00 = new m0(this, true, 0, null);
                break;
            }
            case 1: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v1, v3, 21);
                w1$m00 = new m0(this, true, 0, null);
                break;
            }
            case 2: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v4, v2, 49);
                w1$m00 = new m0(this, false, 0, null);
                break;
            }
            case 3: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v4, v2, 81);
                w1$m00 = new m0(this, false, 0, null);
                break;
            }
            case 4: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v1, v3, 19);
                w1$m00 = new m0(this, true, 1, null);
                break;
            }
            case 5: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v1, v3, 19);
                w1$m00 = new m0(this, true, 2, null);
                break;
            }
            case 6: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v1, v3, 19);
                w1$m00 = new m0(this, true, 4, null);
                break;
            }
            case 7: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v1, v3, 21);
                w1$m00 = new m0(this, true, 1, null);
                break;
            }
            case 8: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v1, v3, 21);
                w1$m00 = new m0(this, true, 2, null);
                break;
            }
            case 9: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v1, v3, 21);
                w1$m00 = new m0(this, true, 4, null);
                break;
            }
            case 10: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v4, v2, 49);
                w1$m00 = new m0(this, false, 1, null);
                break;
            }
            case 11: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v4, v2, 49);
                w1$m00 = new m0(this, false, 2, null);
                break;
            }
            case 12: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v4, v2, 49);
                w1$m00 = new m0(this, false, 4, null);
                break;
            }
            case 13: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v4, v2, 81);
                w1$m00 = new m0(this, false, 1, null);
                break;
            }
            case 14: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v4, v2, 81);
                w1$m00 = new m0(this, false, 2, null);
                break;
            }
            case 15: {
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v4, v2, 81);
                w1$m00 = new m0(this, false, 4, null);
                break;
            }
            default: {
                FrameLayout.LayoutParams frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(v1, v3, 19);
                frameLayout0.addView(new m0(this, true, 7, null), frameLayout$LayoutParams0);
                FrameLayout.LayoutParams frameLayout$LayoutParams1 = new FrameLayout.LayoutParams(v1, v3, 21);
                frameLayout0.addView(new m0(this, true, 7, null), frameLayout$LayoutParams1);
                FrameLayout.LayoutParams frameLayout$LayoutParams2 = new FrameLayout.LayoutParams(v4, v2, 49);
                frameLayout0.addView(new m0(this, false, 7, null), frameLayout$LayoutParams2);
                frameLayout$LayoutParams3 = new FrameLayout.LayoutParams(v4, v2, 81);
                w1$m00 = new m0(this, false, 7, null);
            }
        }
        frameLayout0.addView(w1$m00, frameLayout$LayoutParams3);
        t3 t30 = t3.n();
        t30.j(frameLayout0, 2);
        this.c0.postDelayed(() -> t30.t(2), 2000L);
    }

    private void a5(k3 b$k30) {
        int v = b$k30.I;
        NfcAdapter nfcAdapter0 = NfcAdapter.getDefaultAdapter(this.a0);
        if(nfcAdapter0 == null) {
            return;
        }
        switch(((int)(((Integer)XposedHelpers.callMethod(nfcAdapter0, "getAdapterState", new Object[0]))))) {
            case 2: 
            case 3: {
                if(v == 0 || v == 2) {
                    XposedHelpers.callMethod(nfcAdapter0, "disable", new Object[0]);
                    this.I4(b$k30, false);
                    return;
                }
                break;
            }
            case 1: 
            case 4: {
                if(v == 0 || v == 1) {
                    XposedHelpers.callMethod(nfcAdapter0, "enable", new Object[0]);
                    this.I4(b$k30, true);
                    return;
                }
                break;
            }
        }
    }

    void b1(long v) {
        if(!this.f0) {
            PowerManager.WakeLock powerManager$WakeLock0 = this.e0;
            if(powerManager$WakeLock0 != null) {
                try {
                    powerManager$WakeLock0.acquire(v);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }
    }

    a.p b2() {
        return this.H;
    }

    void b3(String s) {
        if(w1.S0.incrementAndGet() == 2 && !this.n0) {
            this.c0.post(() -> {
                class com.jozein.xedgepro.xposed.w1.d extends f.g0 {
                    final w1 D;

                    com.jozein.xedgepro.xposed.w1.d(Handler handler0, int v, long v1) {
                        super(handler0, v, v1);
                    }

                    @Override  // f.g0
                    protected boolean a(int v) {
                        if(w1.this.n0) {
                            return false;
                        }
                        if(BinderService.f(w1.this.a0)) {
                            boolean z = w1.this.P.F0(AdapterService.class.getName());
                            if(!z) {
                                v.d(new Throwable("Service was prevented!"));
                            }
                            w1.this.d3(z);
                            return false;
                        }
                        return true;
                    }

                    @Override  // f.g0
                    protected void b() {
                        if(w1.this.n0) {
                            return;
                        }
                        if((w1.this.m0 || !w1.this.t2()) && w1.S0.get() >= 2) {
                            w1.this.d3(false);
                        }
                    }
                }

                if(this.n0) {
                    return;
                }
                f.g0 g00 = this.o0;
                if(g00 == null) {
                    com.jozein.xedgepro.xposed.w1.d w1$d0 = new com.jozein.xedgepro.xposed.w1.d(this, this.c0, 3, 3000L);
                    this.o0 = w1$d0;
                    w1$d0.run();
                    return;
                }
                g00.d();
            });
        }
        this.u3(this.J.f(s, 1));
    }

    private void b4(a.b.m0 b$m00) {
        if(f.o0.d() == 0) {
            int v = this.H.u(27);
            Intent intent0 = ServiceWidget.f(b$m00.I, v);
            BinderService.p(this.a0, intent0);
        }
    }

    private void b5() {
        class com.jozein.xedgepro.xposed.w1.r extends r2 {
            boolean Q;
            final w1 R;

            com.jozein.xedgepro.xposed.w1.r(Context context0, Resources resources0, int[] arr_v, int v, boolean z) {
                super(context0, resources0, arr_v, v, z);
                this.Q = false;
            }

            @Override  // com.jozein.xedgepro.xposed.t3$c
            public void a() {
                if(this.Q) {
                    this.Q = false;
                    a.b.p0 b$p00 = new a.b.p0(1);
                    w1.this.S4(b$p00);
                }
            }

            @Override  // com.jozein.xedgepro.xposed.r2
            protected void r() {
                w1.this.t3();
            }

            @Override  // com.jozein.xedgepro.xposed.r2
            protected void v(int v) {
                w1.this.C4(v);
            }

            @Override  // com.jozein.xedgepro.xposed.r2
            protected void x(boolean z) {
                w1.this.S4(new a.b.p0((z ? 1 : 2)));
                this.Q = !z;
            }
        }

        if(!this.f0) {
            return;
        }
        t3 t30 = t3.n();
        if(t30.p(273)) {
            t30.t(273);
            return;
        }
        Context context0 = this.a0;
        Resources resources0 = this.b0.getResources();
        int v = this.H.v(2, 0xFF003037);
        boolean z = this.H.o(37);
        t30.j(new com.jozein.xedgepro.xposed.w1.r(this, context0, resources0, this.H.y(), v, z), 273);
    }

    private void c1(a.b.i b$i0) {
        int v = 4;
        int v1 = b$i0.I;
        int v2 = 1;
        switch(v1) {
            case 0: {
                int v4 = b$i0.J;
                switch(v4) {
                    case -2: {
                        this.e1(false);
                        break;
                    }
                    case -1: {
                        this.e1(true);
                        return;
                    label_19:
                        if(v4 >= 1) {
                            v2 = v4;
                        }
                        this.H.c0(this.a0, v2);
                        View view0 = this.K0;
                        if(view0 != null) {
                            view0.setBackgroundColor(this.H.B());
                            return;
                        }
                        break;
                    }
                    default: {
                        goto label_19;
                    }
                }
                return;
            }
            case 1: {
                int v5 = b$i0.J;
                switch(v5) {
                    case -2: {
                        this.d1(false);
                        return;
                    }
                    case -1: {
                        this.d1(true);
                        return;
                    }
                    default: {
                        this.R3(v5);
                        return;
                    }
                }
            }
            default: {
                if(this.f0 && v1 - 3 <= 4) {
                    v = 5;
                }
                int v3 = b$i0.J;
                switch(v3) {
                    case -2: {
                        this.V.a(v1 - 3, -1, v);
                        return;
                    }
                    case -1: {
                        this.V.a(v1 - 3, 1, v);
                        return;
                    }
                    default: {
                        this.V.k(v1 - 3, v3, v);
                    }
                }
            }
        }
    }

    private float c2() {
        float f;
        if(this.p0 < 0.0f) {
            int v = 0;
            int v1 = Build.VERSION.SDK_INT;
            if(v1 < 28) {
                this.p0 = 0.0f;
                return 0.0f;
            }
            if(v1 >= 29) {
                try {
                    f = this.s1(((float)(((Float)XposedHelpers.callMethod(this.g2().G(), "getWindowCornerRadius", new Object[0])))));
                    this.p0 = f;
                }
                catch(Throwable throwable0) {
                    v.c(throwable0.toString());
                    goto label_15;
                }
                if(f > 0.0f) {
                    return f;
                }
            }
        label_15:
            if(Build.VERSION.SDK_INT >= 0x1F) {
                Display display0 = this.a0.getDisplay();
                if(display0 != null) {
                    RoundedCorner roundedCorner0 = display0.getRoundedCorner(0);
                    int v2 = roundedCorner0 == null ? 0 : roundedCorner0.getRadius();
                    RoundedCorner roundedCorner1 = display0.getRoundedCorner(2);
                    if(roundedCorner1 != null) {
                        v = roundedCorner1.getRadius();
                    }
                    float f1 = this.s1(((float)Math.min(v2, v)));
                    this.p0 = f1;
                    if(f1 > 0.0f) {
                        return f1;
                    }
                }
            }
            Resources resources0 = this.a0.getResources();
            int v3 = resources0.getIdentifier("rounded_corner_radius", "dimen", "android");
            float f2 = v3 <= 0 ? 0.0f : resources0.getDimension(v3);
            int v4 = resources0.getIdentifier("rounded_corner_radius_top", "dimen", "android");
            float f3 = v4 <= 0 ? 0.0f : resources0.getDimension(v4);
            if(f3 <= 0.0f) {
                f3 = f2;
            }
            int v5 = resources0.getIdentifier("rounded_corner_radius_bottom", "dimen", "android");
            float f4 = v5 <= 0 ? 0.0f : resources0.getDimension(v5);
            if(f4 > 0.0f) {
                f2 = f4;
            }
            this.p0 = this.s1(Math.min(f3, f2));
        }
        return this.p0;
    }

    void c3(String s) {
        this.u3(this.J.f(s, 2));
    }

    private void c4() {
        if(this.t2()) {
            return;
        }
        this.Y3(this.P.q0());
    }

    private void c5() {
        if(this.t2()) {
            return;
        }
        this.y1();
        try {
            if(Build.VERSION.SDK_INT >= 25) {
                XposedHelpers.callMethod(XposedHelpers.getObjectField(XposedHelpers.getSurroundingThis(this.r("getStatusBarManagerInternal", new Object[0])), "mBar"), "toggleRecentApps", new Object[0]);
                return;
            }
            this.r("toggleRecentApps", new Object[0]);
        }
        catch(Throwable throwable0) {
            if(this.P1().performGlobalAction(3)) {
                throw throwable0;
            }
            v.d(throwable0);
            this.m2(0xBB);
        }
    }

    private void d1(boolean z) {
        com.jozein.xedgepro.xposed.f f0 = com.jozein.xedgepro.xposed.f.e(this.a0, false);
        if(!this.H.o(34)) {
            f0.i(this.a0, false);
        }
        f0.b(this.a0, z);
    }

    private f.m0 d2() {
        if(this.N0 == null) {
            this.N0 = new f.m0(this.a0);
        }
        return this.N0;
    }

    private void d3(boolean z) {
        if(this.n0) {
            return;
        }
        this.n0 = true;
        this.o0 = null;
        if(!z) {
            v.c("Cannot start service!");
        }
        BinderService.B(z);
        if(this.W2()) {
            BinderService.z(this.a0);
            this.c0.postDelayed(() -> {
                class com.jozein.xedgepro.xposed.w1.c implements com.jozein.xedgepro.xposed.t3.d {
                    boolean a;
                    final w1 b;

                    com.jozein.xedgepro.xposed.w1.c() {
                        this.a = false;
                    }

                    @Override  // com.jozein.xedgepro.xposed.t3$d
                    public void a() {
                        this.a = w1.this.w0 && !w1.this.H.o(35);
                    }

                    @Override  // com.jozein.xedgepro.xposed.t3$d
                    public void b() {
                        if(this.a) {
                            this.a = false;
                            x1 x10 = () -> try {
                                w1.this.e5(1);
                            }
                            catch(Throwable throwable0) {
                                v.c(throwable0.toString());
                            };
                            w1.this.c0.postDelayed(x10, 200L);
                        }
                    }

                    // 检测为 Lambda 实现
                    private void d() [...]
                }

                if(this.l0) {
                    return;
                }
                this.l0 = true;
                this.W3(false);
                try {
                    if(!this.H.J()) {
                        this.H3();
                    }
                    this.Z.k0(this.a0, this.c0);
                    t3.v(this.H.o(35));
                    t3.w(new com.jozein.xedgepro.xposed.w1.c(this));
                    this.Q.n0();
                    this.O.v0(this.a0);
                    this.b0 = p2.d(this.a0);
                    if(this.H.p(2)) {
                        this.d5(new m2(1));
                    }
                    this.u3(this.H.k(0));
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
                if(this.n0) {
                    this.o0 = null;
                }
                this.P.n1();
                v.c("Perform boot completed.");
            }, 6000L);
            return;
        }
        if(this.H.J()) {
            this.A3();
        }
    }

    private void d4(int v, k3 b$k30, int v1) {
        if(v > this.H0.length) {
            return;
        }
        if((this.H.u(7) & 1 << v) != 0) {
            this.H0[v] = new q0(this);
            this.H0[v].b(b$k30.n(this.b0), v1, b$k30);
        }
    }

    private void d5(k3 b$k30) {
        if(this.K0 == null) {
            if(b$k30.I != 2) {
                this.K0 = new View(this.a0);
                int v = this.H.B();
                this.K0.setBackgroundColor(v);
                if(t3.n().j(this.K0, 0x101)) {
                    AlphaAnimation alphaAnimation0 = new AlphaAnimation(0.0f, 1.0f);
                    alphaAnimation0.setDuration(1000L);
                    this.K0.startAnimation(alphaAnimation0);
                    this.H.X(this.a0, 2, true);
                    this.d4(2, new m2(2), 0x7F04009F);  // drawable:ic_screen_filter
                    this.U.l(b$k30.z, true);
                    this.I4(b$k30, true);
                    return;
                }
                this.K0 = null;
            }
        }
        else if(b$k30.I != 1) {
            t3.n().u(0x101, 1000L);
            this.K0 = null;
            this.H.X(this.a0, 2, false);
            this.j1(2);
            this.U.l(b$k30.z, false);
            this.I4(b$k30, false);
        }
    }

    private void e1(boolean z) {
        int v = this.H.C();
        int v1 = 1;
        int v2 = z ? v + 1 : v - 1;
        if(v2 >= 1) {
            v1 = v2;
        }
        this.H.c0(this.a0, v1);
        View view0 = this.K0;
        if(view0 != null) {
            view0.setBackgroundColor(this.H.B());
        }
    }

    private com.jozein.xedgepro.xposed.k3 e2() {
        Object object0;
        if(this.D0 == null) {
            try {
                object0 = this.r("getStatusBarService", new Object[0]);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                object0 = g3.s();
            }
            this.D0 = new com.jozein.xedgepro.xposed.k3(object0, this);
        }
        return this.D0;
    }

    void e3(Intent intent0) {
        switch(intent0.getIntExtra("extra", 0)) {
            case 1: {
                com.jozein.xedgepro.xposed.w.c();
                this.v4();
                return;
            }
            case 2: {
                if(!this.l0 || !this.H.J()) {
                    goto label_7;
                }
                return;
            }
            case 3: {
            label_7:
                this.H.g();
                com.jozein.xedgepro.xposed.w.h(this.a0);
                this.H3();
                this.A3();
                return;
            }
            default: {
                com.jozein.xedgepro.xposed.w.d(intent0);
            }
        }
    }

    private void e4() {
        this.y1();
        if(!this.f0) {
            return;
        }
        try {
            this.r((Build.VERSION.SDK_INT >= 21 ? "showGlobalActions" : "showGlobalActionsDialog"), new Object[0]);
        }
        catch(Throwable throwable0) {
            if(Build.VERSION.SDK_INT < 21) {
                throw throwable0;
            }
            v.d(throwable0);
            this.P1().performGlobalAction(6);
        }
    }

    private void e5(int v) {
        switch(v) {
            case 3: {
                if(this.w0) {
                    try {
                        this.R.P();
                        return;
                    }
                    catch(Throwable unused_ex) {
                        ((InputMethodManager)this.a0.getSystemService("input_method")).showInputMethodPicker();
                    }
                }
                break;
            }
            case 4: {
                this.R.R();
                return;
            label_4:
                if(!this.w0) {
                    goto label_8;
                }
                else if(v != 1) {
                    try {
                        this.R.J();
                        return;
                    label_8:
                        if(v != 2) {
                            this.R.Q();
                            return;
                        }
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                        return;
                    }
                }
                break;
            }
            default: {
                goto label_4;
            }
        }
        try {
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    boolean f1() {
        return this.l0;
    }

    w0 f2(i3 b$i30) {
        class com.jozein.xedgepro.xposed.w1.j extends w0 {
            final float C;
            final Point D;
            final i3 E;
            final w1 F;

            com.jozein.xedgepro.xposed.w1.j(Handler handler0, i3 b$i30) {
                this.E = b$i30;
                super(handler0, null);
                this.C = ((float)w10.H.v(17, 100)) / 100.0f;
                this.D = w10.Q.Z();
            }

            @Override  // com.jozein.xedgepro.xposed.w1$w0
            protected void g() {
                w1.this.O.g0((this.E.J ? this.C : -this.C), ((float)this.D.x), ((float)this.D.y));
            }
        }


        class com.jozein.xedgepro.xposed.w1.l extends w0 {
            final i3 C;
            final w1 D;

            com.jozein.xedgepro.xposed.w1.l(Handler handler0, i3 b$i30) {
                this.C = b$i30;
                super(handler0, null);
            }

            @Override  // com.jozein.xedgepro.xposed.w1$w0
            protected void g() {
                w1.this.V2(this.C.J);
            }
        }


        class m extends w0 {
            final i3 C;
            final w1 D;

            m(Handler handler0, i3 b$i30) {
                this.C = b$i30;
                super(handler0, null);
            }

            @Override  // com.jozein.xedgepro.xposed.w1$w0
            protected void g() {
                w1.this.e1(this.C.J);
            }
        }


        class n extends w0 {
            final i3 C;
            final w1 D;

            n(Handler handler0, i3 b$i30) {
                this.C = b$i30;
                super(handler0, null);
            }

            @Override  // com.jozein.xedgepro.xposed.w1$w0
            protected void g() {
                w1.this.d1(this.C.J);
            }
        }


        class com.jozein.xedgepro.xposed.w1.o extends w0 {
            final int C;
            final int D;
            final int E;
            final i3 F;
            final w1 G;

            com.jozein.xedgepro.xposed.w1.o(Handler handler0, i3 b$i30) {
                this.F = b$i30;
                super(handler0, null);
                this.C = b$i30.J ? 1 : -1;
                this.D = b$i30.I - 3;
                this.E = !w10.f0 || b$i30.I - 3 > 4 ? 4 : 5;
            }

            @Override  // com.jozein.xedgepro.xposed.w1$w0
            protected void g() {
                w1.this.V.a(this.D, this.C, this.E);
            }
        }

        w0 w1$w00;
        try {
            switch(b$i30.I) {
                case 0: {
                    w1$w00 = new m(this, this.c0, b$i30);
                    break;
                }
                case 1: {
                    w1$w00 = new n(this, this.c0, b$i30);
                    break;
                }
                case 2: 
                case 3: 
                case 4: 
                case 5: 
                case 6: 
                case 7: 
                case 8: 
                case 9: 
                case 10: 
                case 11: 
                case 12: {
                    w1$w00 = new com.jozein.xedgepro.xposed.w1.o(this, this.c0, b$i30);
                    break;
                }
                case 13: {
                    w1$w00 = new com.jozein.xedgepro.xposed.w1.j(this, this.c0, b$i30);
                    break;
                }
                case 14: {
                    w1$w00 = new com.jozein.xedgepro.xposed.w1.l(this, this.c0, b$i30);
                    break;
                }
                default: {
                    return null;
                }
            }
            if(this.H.o(24)) {
                this.c0.post(() -> this.G4(b$i30.n(this.b0)));
            }
            return w1$w00;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return null;
        }
    }

    void f3(String s, String s1) {
        if(!this.f1()) {
            return;
        }
        if(s != null) {
            this.u3(this.J.f(s, 4));
        }
        if(s1 != null) {
            this.u3(this.J.f(s1, 3));
        }
    }

    private void f4(int v) {
        if(!this.f0) {
            return;
        }
        int v1 = 3;
        this.t3();
        b[] arr_b = this.H.n(v).I;
        if(arr_b == null) {
            return;
        }
        int v2 = v == 0 ? 3 : 4;
        t2 t20 = this.Y1();
        if(t20.l(v2)) {
            t20.j();
            return;
        }
        t0 w1$t00 = new t0(this, arr_b, this.H.o(26));
        if(v != 0) {
            v1 = 4;
        }
        t20.n(w1$t00, v1, 1, v2);
    }

    private void f5() {
        if(this.t2()) {
            return;
        }
        this.y1();
        int v = Build.VERSION.SDK_INT;
        int v1 = 0;
        if(v >= 33) {
            List list0 = this.P.w0();
            int v2 = list0.size();
            if(v2 < 2) {
                return;
            }
            ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo0 = (ActivityManager.RunningTaskInfo)list0.get(0);
            if(q2.s(activityManager$RunningTaskInfo0.baseIntent)) {
                return;
            }
            ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo1 = null;
            int v3 = Math.min(v2, 4);
            while(v1 < v3) {
                ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo2 = (ActivityManager.RunningTaskInfo)list0.get(v1);
                if(f3.f(activityManager$RunningTaskInfo2)) {
                    activityManager$RunningTaskInfo1 = activityManager$RunningTaskInfo2;
                    break;
                }
                ++v1;
            }
            if(activityManager$RunningTaskInfo1 != null) {
                for(int v4 = v1 + 1; v4 < v2; ++v4) {
                    ActivityManager.RunningTaskInfo activityManager$RunningTaskInfo3 = (ActivityManager.RunningTaskInfo)list0.get(v4);
                    if(f3.f(activityManager$RunningTaskInfo3)) {
                        this.P.S0(activityManager$RunningTaskInfo3.taskId);
                        return;
                    }
                }
                return;
            }
            if(v2 < 3 && q2.s(((ActivityManager.RunningTaskInfo)list0.get(1)).baseIntent)) {
                return;
            }
            int v5 = f3.c(activityManager$RunningTaskInfo0);
            Intent intent0 = new Intent(activityManager$RunningTaskInfo0.baseIntent).addFlags(0x1000);
            Bundle bundle0 = ActivityOptions.makeTaskLaunchBehind().toBundle();
            a.i(bundle0, 6);
            this.P.q1(intent0, true, bundle0, v5);
            return;
        }
        if(v >= 24) {
            try {
                XposedHelpers.callMethod(this.r("getStatusBarManagerInternal", new Object[0]), "toggleSplitScreen", new Object[0]);
            }
            catch(Throwable throwable0) {
                v.c(throwable0.toString());
                this.P1().performGlobalAction(7);
            }
        }
    }

    // 去混淆评级： 低(20)
    boolean g1(b b0) {
        return b0 != null && (b0.z == 0xFFFF || this.f1());
    }

    com.jozein.xedgepro.xposed.m2 g2() {
        return this.Q.d0();
    }

    void g3(boolean z) {
        this.u3(this.H.k((z ? 35 : 36)));
    }

    private void g4(String s) {
        if(s == null) {
            return;
        }
        this.L4(f.m0.i(this.N1(s), this.a0));
    }

    private void g5(k3 b$k30) {
        boolean z = ContentResolver.getMasterSyncAutomatically();
        boolean z1 = w1.T2(b$k30.I, z);
        if(z != z1) {
            ContentResolver.setMasterSyncAutomatically(z1);
            this.I4(b$k30, z1);
        }
    }

    private void h1(int v) {
        class s implements e {
            List a;
            final int b;
            final w1 c;

            s(int v) {
                this.b = v;
                super();
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public boolean a() {
                return w1.this.H.o(11);
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public boolean b(int v) {
                com.jozein.xedgepro.xposed.y1 y10 = () -> {
                    f.c0.e c0$e0 = (f.c0.e)this.a.get(v);
                    w1.this.Z3(c0$e0);
                };
                w1.this.c0.post(y10);
                return true;
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public Drawable c(int v) {
                return ((f.c0.e)this.a.get(v)).d();
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public CharSequence d(int v) {
                return ((f.c0.e)this.a.get(v)).f();
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public void e(int v, View view0) {
                f.c0.e c0$e0 = (f.c0.e)this.a.get(v);
                Bundle bundle0 = a.c(view0);
                w1.this.p4(c0$e0, bundle0);
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public void f() {
            }

            @Override  // com.jozein.xedgepro.xposed.t2$e
            public int getCount() {
                f.c0.c c0$c0 = new f.c0.c();
                c0$c0.b(w1.this.I.i(), -1);
                w1.this.P.U(c0$c0);
                List list0 = z.K(w1.this.a0).N(c0$c0, this.b | 0x10000);
                this.a = list0;
                return list0.size();
            }

            // 检测为 Lambda 实现
            private void h(int v) [...]
        }

        this.t3();
        t2 t20 = this.Y1();
        if(t20.l(v + 0x20)) {
            t20.j();
            return;
        }
        t20.o(new s(this, v), v + 0x20);
    }

    private void h2() {
        Intent intent0;
        this.y1();
        if(this.t2()) {
            return;
        }
        try {
            this.h("awakenDreams", new Object[0]);
            this.r("sendCloseSystemWindows", new Object[0]);
            intent0 = null;
        }
        catch(Throwable unused_ex) {
        }
        try {
            intent0 = (Intent)this.r("createHomeDockIntent", new Object[0]);
        }
        catch(Throwable unused_ex) {
        }
        if(intent0 == null) {
            intent0 = new Intent("android.intent.action.MAIN");
            intent0.addCategory("android.intent.category.HOME");
            intent0.addFlags(0x10200000);
        }
        try {
            this.P.q1(intent0, false, null, 0);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            this.m2(3);
        }
    }

    void h3() {
        if(this.m0) {
            return;
        }
        this.m0 = true;
        v.c("First unlocked.");
        this.W3(false);
        this.r4();
        this.c0.postDelayed(() -> this.d3(false), 8000L);
    }

    private void h4() {
        long v = this.m3();
        this.c0.postDelayed(() -> this.Q.d0().X(), v);
    }

    private void h5(l3 b$l30) {
        if(Build.VERSION.SDK_INT >= 23) {
            this.j5(b$l30);
            return;
        }
        this.i5(b$l30);
    }

    private void i1() {
        if(!t3.o()) {
            this.m2(4);
        }
    }

    private void i2() {
        class com.jozein.xedgepro.xposed.w1.b extends BroadcastReceiver {
            final w1 a;

            @Override  // android.content.BroadcastReceiver
            public void onReceive(Context context0, Intent intent0) {
                try {
                    String s = intent0.getAction();
                    if(a.j.E.equals(s)) {
                        w1.this.e3(intent0);
                        return;
                    }
                    if(b.C.equals(s)) {
                        b b0 = f.m.m(intent0);
                        w1.this.w3(b0);
                        return;
                    }
                    if(b.D.equals(s)) {
                        if(w1.this.H.o(0x20)) {
                            b b1 = b.f(intent0.getStringExtra("data"));
                            w1.this.w3(b1);
                            return;
                        }
                        w1.this.K4(0x7F060170);  // string:perform_by_broadcast_not_allowed "Perform by broadcast not allowed!"
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        if(this.j0) {
            return;
        }
        if(this.a0 == null) {
            Context context0 = (Context)this.u("mContext");
            this.a0 = context0;
            if(context0 == null) {
                Context context1 = (Context)this.P.u("mContext");
                this.a0 = context1;
                if(context1 == null) {
                    return;
                }
            }
        }
        boolean z = true;
        this.j0 = true;
        Point point0 = a0.k(this.a0, new Point());
        this.g0 = point0.x << 16 | point0.y;
        v.c(("Screen size: " + point0.x + ", " + point0.y));
        this.P.C0(this.a0, this.c0);
        this.T = new com.jozein.xedgepro.xposed.i3(this.H, this, this.a0, this.c0);
        this.U = new a.t(this.a0);
        this.S.e();
        IntentFilter intentFilter0 = new IntentFilter();
        intentFilter0.addAction(b.C);
        intentFilter0.addAction(a.j.E);
        intentFilter0.addAction(b.D);
        this.a0.registerReceiver(new com.jozein.xedgepro.xposed.w1.b(this), intentFilter0, null, this.c0);
        if(Build.VERSION.SDK_INT < 28 || !this.i0) {
            z = false;
        }
        this.W3(z);
    }

    void i3(boolean z) {
        this.u3(this.H.k((z ? 33 : 34)));
    }

    private void i4(a.b.y2 b$y20) {
        int v = b$y20.I;
        if(this.f0) {
            if(v == 2) {
                return;
            }
            try {
                XposedHelpers.callMethod(this.d0, "goToSleep", new Object[]{SystemClock.uptimeMillis()});
            }
            catch(Throwable throwable0) {
                if(Build.VERSION.SDK_INT < 28) {
                    throw throwable0;
                }
                v.d(throwable0);
                this.P1().performGlobalAction(8);
            }
            return;
        }
        if(v == 1) {
            return;
        }
        XposedHelpers.callMethod(this.d0, "wakeUp", new Object[]{SystemClock.uptimeMillis()});
    }

    private void i5(l3 b$l30) {
        boolean z = true;
        boolean z1 = this.x2();
        if(z1 == w1.T2(b$l30.I, z1)) {
            return;
        }
        boolean z2 = this.f0 && this.H.o(24);
        if((this.H.u(7) & 2) == 0) {
            z = false;
        }
        Intent intent0 = ServiceTorch.f(b$l30, z2, z);
        BinderService.p(this.a0, intent0);
    }

    private void j1(int v) {
        q0[] arr_w1$q0 = this.H0;
        if(arr_w1$q0[v] != null) {
            arr_w1$q0[v].a();
            this.H0[v] = null;
        }
    }

    private void j2() {
        class y implements f.n0.b {
            final w1 a;

            @Override  // f.n0$b
            public void a() {
                w1.this.j1(1);
                w1.this.U.l(0x20, false);
            }

            @Override  // f.n0$b
            public void b() {
                l3 b$l30 = new l3(2);
                w1.this.d4(1, b$l30, 0x7F0400BE);  // drawable:ic_torch
                w1.this.U.l(0x20, true);
            }
        }

        if(this.J0 == null) {
            this.J0 = f.n0.b(this.c0, new com.jozein.xedgepro.xposed.u(this.a0, this.P), new y(this));
        }
    }

    void j3(boolean z) {
        this.g0 = this.g0 << 16 | this.g0 >>> 16;
        if(!this.l0) {
            return;
        }
        this.c0.post(() -> {
            t3.q();
            this.Z.R();
            this.x4();
            this.T4(new a.b.r0(2));
            this.u3(this.H.k(19));
            this.u3(this.H.k((z ? 21 : 20)));
        });
    }

    private void j4() {
        try {
            XposedHelpers.callMethod(XposedHelpers.getObjectField(this.d0, "mService"), "crash", new Object[]{"Soft reboot"});
        }
        catch(Throwable throwable0) {
            v.c(throwable0.toString());
            o3.b("ctl.restart", "zygote");
        }
    }

    private void j5(l3 b$l30) {
        boolean z = this.J0 != null && this.J0.a();
        if(w1.T2(b$l30.I, z)) {
            this.j2();
            this.J0.g();
            return;
        }
        f.n0 n00 = this.J0;
        if(n00 != null) {
            n00.e();
        }
    }

    private boolean k1(a.h.g h$g0) {
        int v = this.T.q();
        switch(h$g0.C) {
            case 0: {
                return v == h$g0.D;
            }
            case 1: {
                return v > h$g0.D;
            }
            case 2: {
                return v >= h$g0.D;
            }
            case 3: {
                return v < h$g0.D;
            }
            case 4: {
                return v <= h$g0.D;
            }
            case 5: {
                return v != h$g0.D;
            }
            default: {
                throw new RuntimeException("Invalid check battery level.");
            }
        }
    }

    private void k2(a.b.y0 b$y00, boolean z) {
        if(!this.f0) {
            return;
        }
        if(this.Q.j0() != b$y00.F()) {
            throw new UnsupportedOperationException("Gesture injection " + (b$y00.F() ? "landscape" : "portrait"));
        }
        List list0 = b$y00.D();
        if(list0 != null && list0.size() >= 2) {
            this.l2(list0);
            return;
        }
        if(z) {
            v.c("Invalid gesture!");
            return;
        }
        this.c0.postDelayed(() -> this.k2(b$y00, true), 500L);
    }

    void k3() {
        this.f0 = false;
        this.O.w0();
        if(!this.f1()) {
            return;
        }
        this.c0.post(() -> {
            this.y1();
            this.W4(new d1(2));
            this.T4(new a.b.r0(2));
            f.m0 m00 = this.N0;
            if(m00 != null && !m00.m()) {
                m00.n();
                this.N0 = null;
            }
            this.Z.m0();
        });
    }

    private void k4(c3 b$c30) {
        if(b$c30 != null && (b$c30.I != null && b$c30.I.length() != 0)) {
            this.d2().o(this.N1(b$c30.I), true, true);
            return;
        }
        f.m0 m00 = this.N0;
        if(m00 != null) {
            m00.n();
            this.N0 = null;
        }
    }

    private void k5(k3 b$k30) {
        int v = b$k30.I;
        WifiManager wifiManager0 = (WifiManager)this.a0.getSystemService("wifi");
        int v1 = wifiManager0.getWifiState();
        if(v1 != 0 && v1 != 1) {
            if((v1 == 2 || v1 == 3) && (v == 0 || v == 2)) {
                wifiManager0.setWifiEnabled(false);
                this.I4(b$k30, false);
            }
        }
        else if(v == 0 || v == 1) {
            wifiManager0.setWifiEnabled(true);
            this.I4(b$k30, true);
        }
    }

    private boolean l1(a.h h0, boolean z) {
        if(l.r) {
            switch(h0.z) {
                case 1: {
                    return this.l1(((a.h.c)h0).C, z) && this.l1(((a.h.c)h0).D, z);
                }
                case 2: {
                    return this.l1(((a.h.z)h0).C, z) || this.l1(((a.h.z)h0).D, z);
                }
                case 3: {
                    return !this.l1(((a.h.x)h0).C, z);
                }
                case 4: {
                    return this.f0;
                }
                case 5: {
                    return this.t2();
                }
                case 6: {
                    return this.T.s();
                }
                case 7: {
                    return this.T.t();
                }
                case 8: {
                    return this.T.I();
                }
                case 9: {
                    return this.T.y();
                }
                case 10: {
                    return this.T.x();
                }
                case 11: {
                    return this.T.u();
                }
                case 12: {
                    return this.T.A();
                }
                case 13: {
                    return this.T.E();
                }
                case 14: {
                    return this.T.G();
                }
                case 15: {
                    return this.T.r();
                }
                case 16: {
                    return this.H.p(1);
                }
                case 17: {
                    return this.H.p(0);
                }
                case 18: {
                    return this.T.C();
                }
                case 19: {
                    return this.T.H();
                }
                case 20: {
                    return this.T.z();
                }
                case 21: {
                    return this.T.v();
                }
                case 22: {
                    return this.T.F();
                }
                case 23: {
                    return this.V.g();
                }
                case 24: {
                    return this.T.D();
                }
                case 25: {
                    return this.T.B();
                }
                case 26: {
                    return this.k1(((a.h.g)h0));
                }
                case 27: {
                    return this.p1(((a.h.n)h0));
                }
                case 28: {
                    return this.m1(((a.h.l)h0));
                }
                case 29: {
                    return this.n1(((a.h.k)h0));
                }
                case 30: {
                    return this.o1(((a.h.m)h0));
                }
                case 0x1F: {
                    return this.r1(((a.h.m0)h0).C);
                }
                case 0x20: {
                    return this.p2(((a.h.d)h0).C);
                }
                case 33: {
                    return this.Q.j0() == ((a.h.a0)h0).C;
                }
                case 34: {
                    return this.w0;
                }
                case 35: {
                    return this.Q.l0();
                }
                case 36: {
                    return this.Q.k0();
                }
                case 37: {
                    return this.q1(((a.h.i0)h0), z);
                }
                case 38: {
                    return this.r1(((a.h.l0)h0).C);
                }
                default: {
                    throw new RuntimeException("Unknown condition id: " + h0.z);
                }
            }
        }
        throw new RuntimeException("!");
    }

    private void l2(List list0) {
        class q implements Runnable {
            int A;
            final int B;
            private final com.jozein.xedgepro.xposed.f0 C;
            final List D;
            final Handler E;
            final w1 F;
            long z;

            q(List list0, Handler handler0) {
                this.D = list0;
                this.E = handler0;
                super();
                this.z = 0L;
                this.A = 0;
                this.B = list0.size();
                this.C = w10.H.o(7) ? new com.jozein.xedgepro.xposed.f0(w10.a0, w10.c0, false) : null;
            }

            void a() {
                com.jozein.xedgepro.xposed.f0 f00 = this.C;
                if(f00 != null) {
                    f00.e();
                }
            }

            @Override
            public void run() {
                int v2;
                if(this.z == 0L) {
                    this.z = SystemClock.uptimeMillis();
                    com.jozein.xedgepro.xposed.f0 f00 = this.C;
                    if(f00 != null) {
                        f00.i();
                    }
                }
                long v = 0L;
                while(this.A < this.B) {
                    if(!w1.this.C0 && !w1.this.s2(this.z)) {
                        int v1 = this.A;
                        this.A = v1 + 1;
                        g.q q0 = (g.q)this.D.get(v1);
                        if(v == 0L) {
                            v = this.z + ((long)q0.z);
                        }
                        try {
                            w1.this.O.f0(q0.a(this.z, v));
                            com.jozein.xedgepro.xposed.f0 f01 = this.C;
                            if(f01 != null) {
                                f01.d(q0);
                            }
                            v2 = this.A;
                            if(v2 >= this.B || (q0.A & 0xFF) != 1) {
                                continue;
                            }
                        }
                        catch(Throwable throwable0) {
                            v.d(throwable0);
                            this.a();
                            return;
                        }
                        long v3 = this.z + ((long)((g.q)this.D.get(v2)).z);
                        this.E.postAtTime(this, v3);
                        break;
                    }
                    this.a();
                    return;
                }
                if(this.A >= this.B - 1) {
                    this.a();
                }
            }
        }

        Handler handler0 = this.O.W();
        this.C0 = false;
        handler0.post(new q(this, list0, handler0));
    }

    void l3() {
        this.f0 = true;
        this.O.x0();
    }

    private void l4(Intent intent0, Bundle bundle0, int v) {
        this.P.o1(intent0, bundle0, v, true);
    }

    private void l5(k3 b$k30) {
        int v = b$k30.I;
        switch(((int)(((Integer)XposedHelpers.callMethod(((WifiManager)this.a0.getSystemService("wifi")), "getWifiApState", new Object[0]))))) {
            case 10: 
            case 11: {
                if(v == 0 || v == 1) {
                    this.V3(b$k30, true);
                    return;
                }
                break;
            }
            case 12: 
            case 13: {
                if(v == 0 || v == 2) {
                    this.V3(b$k30, false);
                    return;
                }
                break;
            }
        }
    }

    private boolean m1(a.h.l h$l0) {
        int v = new f.j().d();
        return (h$l0.C & 1 << v) != 0;
    }

    private void m2(int v) {
        this.O.Z(v, 0);
    }

    long m3() {
        if(this.s0) {
            return 500L;
        }
        this.s0 = true;
        v.c("Shutting down");
        return !this.Z.m0() && !this.P.i1() ? 500L : 2000L;
    }

    private void m4(String s, String s1, Bundle bundle0, int v) {
        Intent intent0 = new Intent();
        intent0.setComponent(new ComponentName(s, s1));
        this.l4(intent0, bundle0, v);
    }

    private void m5(String s) {
        class com.jozein.xedgepro.xposed.w1.z implements g.l.e {
            final w1 a;

            @Override  // g.l$e
            public void a(Intent intent0) {
                w1.this.n4(intent0);
            }

            @Override  // g.l$e
            public boolean b(CharSequence charSequence0) {
                if(charSequence0.length() == 0) {
                    return true;
                }
                ClipboardManager clipboardManager0 = (ClipboardManager)w1.this.a0.getSystemService("clipboard");
                if(clipboardManager0 != null) {
                    clipboardManager0.setText(charSequence0);
                    CharSequence charSequence1 = w1.this.b0.getText(0x7F0601F2);  // string:text_copied "Text copied."
                    w1.this.J4(charSequence1);
                    w1.this.Z.N(charSequence0);
                }
                return true;
            }
        }

        if(s != null && s.length() != 0) {
            this.y1();
            com.jozein.xedgepro.xposed.w1.z w1$z0 = new com.jozein.xedgepro.xposed.w1.z(this);
            this.Z.S(s, null, w1$z0, null);
            return;
        }
        this.H4(0x7F060160);  // string:nothing_fetched "Nothing fetched!"
    }

    private boolean n1(a.h.k h$k0) {
        f.j j0 = new f.j();
        int v = j0.c();
        return (1 << v & h$k0.C) != 0 || (h$k0.C & 0x80000000) != 0 && v == j0.a() - 1;
    }

    private void n2(z0 b$z00) {
        switch(b$z00.J) {
            case 0: {
                this.O.Z(b$z00.I, b$z00.K);
                return;
            }
            case 1: {
                this.O.a0(b$z00.I, b$z00.K);
                return;
            }
            case 2: {
                this.O.c0(b$z00.I, b$z00.K);
                return;
            }
            default: {
                v.c("Unknown key action.");
            }
        }
    }

    void n3(boolean z) {
        this.w0 = z;
        if(!z) {
            this.o5(null);
        }
        this.u3(this.H.k((z ? 29 : 30)));
    }

    void n4(Intent intent0) {
        this.c0.postDelayed(() -> try {
            this.P.q1(intent0, false, null, 0);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            this.L4(throwable0.getMessage());
        }, 200L);
    }

    private void n5() {
        View view0 = this.K0;
        if(view0 != null) {
            view0.setBackgroundColor(this.H.B());
        }
    }

    private boolean o1(a.h.m h$m0) {
        int v = new f.j().i();
        return (h$m0.C & 1 << v) != 0;
    }

    void o2(String s, boolean z) {
        if(s != null && s.length() != 0) {
            if(z) {
                s = f.m0.i(this.N1(s), this.a0);
            }
            if(!this.Z.U(s) && !this.R.L(s)) {
                this.O.i0(s);
            }
            return;
        }
        v.c("Inject empty text.");
    }

    void o3(boolean z) {
        if(!this.f1()) {
            return;
        }
        this.u3(this.H.k((z ? 0x1F : 0x20)));
    }

    private void o4(a.b.l b$l0, Bundle bundle0) {
        if(b$l0.L != null) {
            try {
                this.l4(f.c0.a().setClassName(b$l0.K, b$l0.L), bundle0, b$l0.B());
                return;
            }
            catch(Throwable unused_ex) {
            }
        }
        int v = b$l0.B();
        this.q4(b$l0.K, bundle0, v);
    }

    private void o5(Rect rect0) {
        int v;
        if(rect0 == null || rect0.bottom == 0) {
            rect0 = null;
            v = -1;
        }
        else {
            v = rect0.top;
        }
        this.x0 = rect0;
        if(v == this.y0) {
            return;
        }
        this.y0 = v;
        if(Build.VERSION.SDK_INT < 30 && this.Z.V()) {
            this.c0.post(() -> this.Z.P(v));
        }
    }

    private boolean p1(a.h.n h$n0) {
        int v = h$n0.C << 16 | h$n0.D;
        int v1 = h$n0.F | h$n0.E << 16;
        f.j j0 = new f.j();
        int v2 = j0.g();
        int v3 = j0.h() | v2 << 16;
        if(v == v1) {
            return false;
        }
        return v >= v1 ? v3 >= v || v3 < v1 : v3 >= v && v3 < v1;
    }

    private boolean p2(String s) {
        return s.equals(this.Q.d0().L());
    }

    void p3() {
        if(this.k0) {
            return;
        }
        this.k0 = true;
        this.O.y0(this);
        this.H.d(this.a0, this.c0);
        this.U.l(34, this.H.p(1));
        this.U.l(35, this.H.p(0));
        this.Q.p0();
        this.S.e();
        if(l.r) {
            a1 a10 = (b b0) -> this.x3(b0, -1, null);
            this.K.j(a10);
            this.K.d(this.a0, this.c0);
        }
        try {
            this.V = new com.jozein.xedgepro.xposed.e(this);
            PowerManager powerManager0 = (PowerManager)this.a0.getSystemService("power");
            this.d0 = powerManager0;
            PowerManager.WakeLock powerManager$WakeLock0 = powerManager0.newWakeLock(1, l.x + ":job");
            this.e0 = powerManager$WakeLock0;
            powerManager$WakeLock0.setReferenceCounted(true);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        a.b.f0.A().d(this.a0, this.c0);
        this.I.d(this.a0, this.c0);
        if(l.r) {
            this.J.d(this.a0, this.c0);
            this.L.d(this.a0, this.c0);
            k2.A().d(this.a0, this.c0);
        }
        f.q0.e(this.V.f());
        if(Build.VERSION.SDK_INT >= 23) {
            this.j2();
        }
        boolean z = a.p.f();
        if(this.H.p(8) != !z) {
            this.H.X(this.a0, 8, !z);
        }
        if(!z == 0) {
            v.c("Can read settings.");
            if(!this.H.J()) {
                this.H3();
            }
        }
        else {
            v.c("Cannot read settings!");
            com.jozein.xedgepro.xposed.w.h(this.a0);
        }
        n2.d(this.a0, l.j, true);
        if(!this.n0) {
            this.c0.postDelayed(() -> {
                class com.jozein.xedgepro.xposed.w1.d extends f.g0 {
                    final w1 D;

                    com.jozein.xedgepro.xposed.w1.d(Handler handler0, int v, long v1) {
                        super(handler0, v, v1);
                    }

                    @Override  // f.g0
                    protected boolean a(int v) {
                        if(w1.this.n0) {
                            return false;
                        }
                        if(BinderService.f(w1.this.a0)) {
                            boolean z = w1.this.P.F0(AdapterService.class.getName());
                            if(!z) {
                                v.d(new Throwable("Service was prevented!"));
                            }
                            w1.this.d3(z);
                            return false;
                        }
                        return true;
                    }

                    @Override  // f.g0
                    protected void b() {
                        if(w1.this.n0) {
                            return;
                        }
                        if((w1.this.m0 || !w1.this.t2()) && w1.S0.get() >= 2) {
                            w1.this.d3(false);
                        }
                    }
                }

                if(this.n0) {
                    return;
                }
                f.g0 g00 = this.o0;
                if(g00 == null) {
                    com.jozein.xedgepro.xposed.w1.d w1$d0 = new com.jozein.xedgepro.xposed.w1.d(this, this.c0, 3, 3000L);
                    this.o0 = w1$d0;
                    w1$d0.run();
                    return;
                }
                g00.d();
            }, 6000L);
        }
    }

    private void p4(f.c0.e c0$e0, Bundle bundle0) {
        this.l4(c0$e0.v(), bundle0, c0$e0.k());
    }

    void p5() {
        if(!this.f0) {
            return;
        }
        try {
            if(this.z0 == null) {
                this.z0 = XposedHelpers.findMethodBestMatch(PowerManager.class, "userActivity", new Class[]{Long.TYPE, Boolean.TYPE});
            }
            this.z0.invoke(this.d0, SystemClock.uptimeMillis(), Boolean.FALSE);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private boolean q1(a.h.i0 h$i00, boolean z) {
        if(z) {
            return h$i00.h(this.N);
        }
        f.i.f i$f0 = this.M;
        return h$i00.h(this.M);
    }

    boolean q2() {
        return this.L0;
    }

    void q3() {
        if(this.k0 && l.r) {
            this.K.n(false);
        }
    }

    private void q4(String s, Bundle bundle0, int v) {
        this.P.s1(s, bundle0, v);
    }

    private void q5(int v) {
        if(this.A0 == null) {
            this.A0 = new r3(this.v(), this.m(), this.a0, this.H);
        }
        this.A0.t(v);
    }

    // 去混淆评级： 低(20)
    private boolean r1(String s) {
        return this.T.H() ? s.equals(n2.f(((WifiManager)this.a0.getSystemService("wifi")).getConnectionInfo().getSSID())) : false;
    }

    boolean r2() {
        return this.v0;
    }

    y2 r3() {
        return this.X;
    }

    // 检测为 Lambda 实现
    private void r4() [...]

    private float s1(float f) {
        if(f > 0.0f) {
            float f1 = f / a0.j();
            if(f1 < 0.393701f) {
                return f1;
            }
            Resources resources0 = this.a0.getResources();
            int v = resources0.getIdentifier("config_supportsRoundedCornersOnWindows", "bool", "android");
            v.c(("Ignored rounded corner radius: " + f1 + ", supports rounded corners: " + (v > 0 && resources0.getBoolean(v))));
        }
        return 0.0f;
    }

    private boolean s2(long v) {
        return v <= this.h0;
    }

    d3 s3() {
        return this.Y;
    }

    private void s4(Intent intent0, Bundle bundle0) {
        this.P.o1(intent0, bundle0, 0, false);
    }

    private void t1() {
        Object[] arr_object = {this.P.a0()};
        this.J4(this.b0.getString(0x7F06014D, arr_object));  // string:memory_released_size_f "Memory released: %1$dM."
    }

    boolean t2() {
        try {
            if(this.t0 == null) {
                this.t0 = this.k("keyguardOn", new Class[0]);
            }
            return ((Boolean)this.t0.invoke(this.v())).booleanValue();
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return this.T.w();
        }
    }

    private void t3() {
        t3 t30 = t3.s();
        if(t30 != null) {
            t30.t(273);
        }
    }

    void t4(float f, float f1) {
        this.c0.post(() -> this.P1().c(f, f1, (boolean z, String s) -> {
            if(z) {
                this.m5(s);
                return;
            }
            try {
                this.O.g0(l2.E, f, f1);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }));
    }

    void u1() {
        this.v0 = false;
    }

    private boolean u2() {
        try {
            return Build.VERSION.SDK_INT >= 24 ? ((Boolean)this.r("isKeyguardSecure", new Object[]{f.o0.d()})).booleanValue() : ((Boolean)this.r("isKeyguardSecure", new Object[0])).booleanValue();
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return true;
        }
    }

    void u3(b b0) {
        this.v3(b0, -1, null);
    }

    private void u4() {
        class com.jozein.xedgepro.xposed.w1.e extends View {
            final RectF A;
            final Paint B;
            final Paint C;
            final w1 D;
            final float z;

            com.jozein.xedgepro.xposed.w1.e(Context context0, Paint paint0, Paint paint1) {
                this.B = paint0;
                this.C = paint1;
                super(context0);
                this.z = w10.c2() * a0.j();
                this.A = new RectF();
            }

            @Override  // android.view.View
            protected void onDraw(Canvas canvas0) {
                this.A.right = (float)this.getRight();
                this.A.bottom = (float)this.getBottom();
                canvas0.drawRoundRect(this.A, this.z, this.z, this.B);
                canvas0.drawRoundRect(this.A, this.z, this.z, this.C);
            }
        }

        if(this.q0) {
            this.x4();
            return;
        }
        if(this.t2() && this.H.o(33)) {
            return;
        }
        if(SystemClock.uptimeMillis() - this.r0 < 1000L) {
            return;
        }
        this.Z.R();
        this.q0 = true;
        this.O.V().Q(true);
        Paint paint0 = new Paint(1);
        paint0.setStrokeWidth(a0.j() * 0.08f);
        paint0.setStyle(Paint.Style.STROKE);
        paint0.setColor(0x7FCCCCCC);
        Paint paint1 = new Paint(1);
        paint1.setStrokeWidth(a0.j() * 0.06f);
        paint1.setStyle(Paint.Style.STROKE);
        paint1.setColor(0xFF0097A7);
        t3.n().j(new com.jozein.xedgepro.xposed.w1.e(this, this.a0, paint0, paint1), 8);
    }

    private void v1(a.b.f0 b$f00) {
        int v2;
        if(!this.f0) {
            return;
        }
        this.t3();
        t2 t20 = this.Y1();
        if(t20.l(b$f00.I + 0x40)) {
            t20.j();
            return;
        }
        b[] arr_b = b$f00.z();
        if(arr_b == null) {
            v.c("Custom panel not exists!");
            return;
        }
        int v = arr_b.length;
        int v1 = v - 1;
        while(true) {
            v2 = 1;
            if(v1 > 0) {
                switch(arr_b[v1].z) {
                    case 0: 
                    case 1: {
                        --v1;
                        continue;
                    }
                    default: {
                        v2 = v1 + 1;
                    }
                }
            }
            break;
        }
        if(v2 != v) {
            b[] arr_b1 = new b[v2];
            System.arraycopy(arr_b, 0, arr_b1, 0, v2);
            arr_b = arr_b1;
        }
        t0 w1$t00 = new t0(this, arr_b, this.H.o(10));
        int v3 = 4;
        if(v <= 9) {
            v3 = v <= 4 ? 2 : 3;
        }
        t20.n(w1$t00, 1, v3, b$f00.I + 0x40);
    }

    boolean v2(int v, int v1) {
        try {
            if(!this.w0) {
                return false;
            }
            if(Build.VERSION.SDK_INT >= 29) {
                Region region0 = this.Q.d0().F();
                return region0 != null && region0.contains(v, v1);
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        Rect rect0 = this.x0;
        return rect0 != null && !this.t2() && v >= rect0.left && v <= rect0.right && v1 >= rect0.top && v1 <= rect0.bottom;
    }

    void v3(b b0, int v, Bundle bundle0) {
        if(!this.g1(b0)) {
            return;
        }
        switch(b0.z) {
            case 0: 
            case 1: {
                break;
            }
            case 51: 
            case 68: 
            case 102: 
            case 106: 
            case 107: {
                v.d(new Throwable("Invalid action: " + b0));
                this.b1(500L);
                this.c0.postDelayed(() -> this.x3(b0, v, bundle0), 0L);
                break;
            }
            default: {
                this.b1(500L);
                this.c0.postDelayed(() -> this.x3(b0, v, bundle0), 0L);
            }
        }
    }

    private void v4() {
        this.h0 = SystemClock.uptimeMillis();
        this.M0.b();
        this.k4(new c3(""));
        this.y1();
        this.x4();
        this.T4(new a.b.r0(2));
        this.h5(new l3(2));
        if(l.r) {
            f.i0.g();
            BinderService.D(this.a0);
        }
        Intent intent0 = new Intent(com.jozein.xedgepro.service.a.C);
        intent0.setPackage(l.j);
        this.P.W(intent0, 0);
    }

    private void w1(String s, int v) {
        CharSequence charSequence0 = z.K(this.a0).G(s);
        if(this.P.l1(s, v, true)) {
            this.J4(this.b0.getString(0x7F060075, new Object[]{charSequence0}));  // string:app_frozen_f "Frozen: %1$s"
            return;
        }
        this.L4(this.b0.getString(0x7F0600F9, new Object[]{charSequence0}));  // string:error_freeze_app_f "Failed to freeze app: %1$s"
    }

    boolean w2() {
        return this.f0;
    }

    // 检测为 Lambda 实现
    void w3(b b0) [...]

    void w4(long v) {
        this.c0.removeCallbacks(this.u0);
        this.c0.postDelayed(this.u0, v);
    }

    @Override  // com.jozein.xedgepro.xposed.j2
    protected Object x() {
        com.jozein.xedgepro.xposed.m2 m20 = this.Q.d0();
        if(m20.y() == null) {
            m20.A(g3.u());
        }
        return m20.u("mPolicy");
    }

    private void x1() {
        if(this.f0 && this.t2() && !this.u2()) {
            int v = Build.VERSION.SDK_INT;
            if(v <= 25) {
                this.r("dismissKeyguardLw", new Object[0]);
                return;
            }
            if(v <= 27) {
                this.r("dismissKeyguardLw", new Object[]{null});
                return;
            }
            this.r("dismissKeyguardLw", new Object[]{null, null});
        }
    }

    // 去混淆评级： 中等(50)
    private boolean x2() {
        return Build.VERSION.SDK_INT < 23 ? this.P.F0(ServiceTorch.class.getName()) : this.J0 != null && this.J0.a();
    }

    private void x3(b b0, int v, Bundle bundle0) {
        if(!this.g1(b0)) {
            return;
        }
        try {
            if(this.y3(b0, v, bundle0)) {
                this.K1();
            }
            if(this.f0 && b0.z != 0xFFFF) {
                this.p5();
            }
        }
        catch(PackageManager.NameNotFoundException packageManager$NameNotFoundException0) {
            String s = packageManager$NameNotFoundException0.getMessage();
            String s1 = s == null ? packageManager$NameNotFoundException0.toString() : s + " not found!";
            v.c(s1);
            this.G4(s1);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            this.F4(b0, throwable0);
        }
    }

    void x4() {
        if(!this.q0) {
            return;
        }
        try {
            this.q0 = false;
            this.O.V().Q(false);
            this.r0 = SystemClock.uptimeMillis();
            this.c0.post(() -> {
                t3 t30 = t3.s();
                if(t30 != null) {
                    t30.u(8, 200L);
                }
            });
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private void y1() {
        try {
            t3.q();
            this.x4();
            this.t3();
            this.Z.R();
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private void y2(String s) {
        this.P.i0(s, f.o0.d());
    }

    private boolean y3(b b0, int v, Bundle bundle0) {
        int v2;
        if(!this.g1(b0)) {
            return false;
        }
        this.b1(1500L);
        int v1 = b0.z;
        switch(v1) {
            case 2: {
                this.i1();
                break;
            }
            case 3: {
                this.h2();
                break;
            }
            case 4: {
                this.c5();
                return true;
            }
            case 5: {
                this.m2(82);
                break;
            }
            case 6: {
                this.i4(((a.b.y2)b0));
                return true;
            }
            case 9: {
                v2 = 85;
                this.O3(v2);
                break;
            }
            case 10: {
                v2 = 86;
                this.O3(v2);
                break;
            }
            case 11: {
                v2 = 88;
                this.O3(v2);
                break;
            }
            case 12: {
                this.O3(87);
                break;
            }
            case 13: {
                this.d1(true);
                break;
            }
            case 14: {
                this.d1(false);
                break;
            }
            case 15: {
                this.F1();
                return true;
            }
            case 16: {
                this.H1();
                return true;
            }
            case 17: {
                this.B4();
                return true;
            }
            case 18: {
                this.z4(0);
                break;
            }
            case 19: {
                this.O4(((k3)b0));
                break;
            }
            case 20: {
                this.P4(((k3)b0));
                break;
            }
            case 21: {
                this.k5(((k3)b0));
                break;
            }
            case 22: {
                this.Z4(((k3)b0));
                break;
            }
            case 23: {
                this.Y4(((k3)b0));
                break;
            }
            case 24: {
                this.Q4(((k3)b0));
                break;
            }
            case 25: {
                this.a5(((k3)b0));
                break;
            }
            case 26: {
                this.g5(((k3)b0));
                break;
            }
            case 27: {
                this.l5(((k3)b0));
                break;
            }
            case 28: {
                this.N4(((k3)b0));
                break;
            }
            case 29: {
                this.U4(((k3)b0));
                break;
            }
            case 30: {
                this.W4(((k3)b0));
                break;
            }
            case 0x1F: {
                this.T4(((k3)b0));
                break;
            }
            case 0x20: {
                this.h5(((l3)b0));
                break;
            }
            case 33: {
                this.d5(((k3)b0));
                break;
            }
            case 34: {
                this.S4(((k3)b0));
                break;
            }
            case 35: {
                this.X4(((k3)b0));
                break;
            }
            case 36: {
                this.R2(v);
                break;
            }
            case 37: {
                this.z2();
                break;
            }
            case 38: {
                this.t1();
                break;
            }
            case 39: {
                this.b5();
                break;
            }
            case 40: {
                this.X3();
                return true;
            }
            case 42: {
                this.O1();
                return true;
            }
            case 43: {
                this.e4();
                return true;
            }
            case 44: 
            case 45: 
            case 46: {
                this.z3(v1);
                return true;
            }
            case 0x2F: {
                this.q4(((a.b.m)b0).K, bundle0, 0);
                break;
            }
            case 0x30: {
                this.m4(((a.b.e)b0).K, ((a.b.e)b0).L, bundle0, 0);
                break;
            }
            case 53: {
                this.k2(((a.b.y0)b0), false);
                break;
            }
            case 55: {
                this.L3();
                return true;
            }
            case 59: {
                this.D3();
                return false;
            }
            case 61: {
                this.K3(((a.b.b0)b0).A());
                return true;
            }
            case 62: {
                return this.l1(((d0)b0).I, false) ? this.y3(((d0)b0).J, v, bundle0) : this.y3(((d0)b0).K, v, bundle0);
            }
            case 0x40: {
                this.J1(((a.b.k0)b0).I);
                break;
            }
            case 65: {
                this.f4(((x2)b0).I);
                return true;
            }
            case 66: {
                this.C3(v);
                break;
            }
            case 67: {
                this.Y2(v);
                break;
            }
            case 70: {
                this.z4(((g2)b0).I);
                break;
            }
            case 71: {
                this.c4();
                return true;
            }
            case 72: {
                b b1 = ((d)b0).K;
                return b1 != null && this.y3(b1, v, bundle0);
            }
            case 73: {
                this.V4(((k3)b0));
                break;
            }
            case 74: {
                this.J3(((h2)b0).I);
                break;
            }
            case 75: {
                this.x1();
                return true;
            }
            case 76: {
                this.l4(((a.b.g)b0).K, bundle0, 0);
                break;
            }
            case 77: {
                this.y2(((f1)b0).I);
                break;
            }
            case 78: {
                this.s4(((s2)b0).K, bundle0);
                break;
            }
            case 81: {
                this.F3();
                break;
            }
            case 82: {
                this.h1(((a.b.o)b0).I);
                return true;
            }
            case 83: {
                this.v1(((a.b.f0)b0));
                return true;
            }
            case 85: {
                this.e5(((a3)b0).I);
                break;
            }
            case 86: {
                this.D1(((i3)b0));
                break;
            }
            case 87: {
                this.A1(((a.b.e0)b0).I);
                break;
            }
            case 88: {
                this.U3(((a.b.p2)b0));
                return true;
            }
            case 89: {
                this.c1(((a.b.i)b0));
                break;
            }
            case 90: {
                this.Z.p0();
                return true;
            }
            case 92: {
                this.q5(((n3)b0).I);
                return false;
            }
            case 93: {
                this.B3(((y1)b0));
                return false;
            }
            case 94: {
                this.b4(((a.b.m0)b0));
                return true;
            }
            case 0x5F: {
                this.C4(((a.b.n2)b0).I);
                return true;
            }
            case 0x60: {
                this.f5();
                return true;
            }
            case 97: {
                this.R4(((k3)b0));
                break;
            }
            case 98: {
                this.u4();
                break;
            }
            case 99: {
                this.M1();
                break;
            }
            case 100: {
                this.K3(((a.b.a0)b0));
                return true;
            }
            case 0x3F: 
            case 84: 
            case 101: {
                this.z1(b0);
                break;
            }
            case 0: 
            case 1: 
            case 102: {
                return false;
            }
            case 104: {
                this.C1(((j1)b0).I);
                break;
            }
            case 105: {
                this.n2(((z0)b0));
                break;
            }
            case 51: 
            case 68: 
            case 106: 
            case 107: {
                v.c("Pie/slide adjust/gesture pointer... not supports this trigger.");
                this.K4(0x7F060179);  // string:pie_slide_adjust_support "Pie/slide adjust/gesture pointer/sub gesture… only 
                                      // for gesture long press/double click, or swipes if not enabled swipe triggers after 
                                      // finger upped."
                return false;
            }
            case 108: {
                this.o2(((b1)b0).I, true);
                break;
            }
            case 109: {
                this.Z2(((u1)b0));
                return false;
            }
            case 110: {
                this.k4(((c3)b0));
                return false;
            }
            case 0x6F: {
                this.g4(((u2)b0).I);
                return false;
            }
            case 0x70: {
                this.v4();
                break;
            }
            case 0x71: {
                this.m4(((a.b.f)b0).K, ((a.b.f)b0).L, bundle0, ((a.b.f)b0).M);
                break;
            }
            case 0x72: {
                this.l4(((a.b.h)b0).K, bundle0, ((a.b.h)b0).L);
                break;
            }
            case 0x73: {
                this.o4(((a.b.l)b0), bundle0);
                break;
            }
            case 0xFFFF: {
                this.B1(((a.b.s0)b0));
                return false;
            }
            default: {
                v.c(("Unknown action: " + b0));
                return false;
            }
        }
        if(!(b0 instanceof k3)) {
            this.M4(b0);
        }
        return true;
    }

    b y4(b b0, boolean z) {
        try {
            switch(b0.z) {
                case 62: {
                    return this.y4((this.l1(((d0)b0).I, z) ? ((d0)b0).J : ((d0)b0).K), z);
                }
                case 72: {
                    return this.y4(((d)b0).K, z);
                }
                default: {
                    return b0;
                }
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return b.e();
        }
    }

    private void z1(b b0) {
        if(!l.r) {
            throw new RuntimeException("!");
        }
        new r0(this, b0, SystemClock.uptimeMillis()).c();
    }

    private void z2() {
        if(this.t2()) {
            return;
        }
        this.y1();
        this.P.g0();
    }

    private void z3(int v) {
        switch(v) {
            case 44: {
                this.E3();
                return;
            }
            case 45: {
                this.j4();
                return;
            }
            case 46: {
                this.h4();
                return;
            }
            default: {
                v.c(("Unknown id: " + v));
            }
        }
    }

    private void z4(int v) {
        int v1 = this.V.d();
        int v2 = 0;
        switch(v) {
            case 1: {
                break;
            }
            case 2: {
                v2 = 1;
                break;
            }
            case 3: {
                v2 = 2;
                break;
            }
            default: {
                if(v1 + 1 <= 2) {
                    v2 = v1 + 1;
                }
                this.V.j(v2);
                if(v2 == 1 && this.V.d() == v1) {
                    v.c("Can not set ringer mode to vibrate. Skip it.");
                    this.V.j(2);
                }
                return;
            }
        }
        if(v1 != v2) {
            this.V.j(v2);
        }
    }
}


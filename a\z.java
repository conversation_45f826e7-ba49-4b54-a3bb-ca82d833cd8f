package a;

import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.database.ContentObserver;
import android.os.Build.VERSION;
import android.os.Handler;
import android.provider.Settings.System;
import android.widget.Toast;
import com.jozein.xedgepro.ApplicationMain;
import f.h0;
import f.l;
import f.m;
import f.r;
import f.v;
import java.io.File;

public class z extends p {
    public interface b {
        void a(boolean arg1);

        void b(boolean arg1);
    }

    private ContentObserver R;

    public z() {
        this.R = null;
    }

    @Override  // a.p
    public void X(Context context0, int v, boolean z) {
        e1 b$e10;
        switch(v) {
            case 0: {
                b$e10 = new e1((z ? 1 : 2));
                break;
            }
            case 1: {
                b$e10 = new p0((z ? 1 : 2));
                break;
            }
            case 2: {
                b$e10 = new m2((z ? 1 : 2));
                break;
            }
            default: {
                b$e10 = null;
            }
        }
        if(b$e10 != null) {
            a.b.t(context0, b$e10);
        }
    }

    @Override  // a.p
    protected void b(Intent intent0) {
        v.d(new UnsupportedOperationException("Should not used!"));
    }

    @Override  // a.p
    public void c0(Context context0, int v) {
        v.d(new UnsupportedOperationException("Use clearScreenFilterProgress(context) instead."));
    }

    @Override  // a.p
    public void d(Context context0, Handler handler0) {
        v.d(new UnsupportedOperationException("Use startReceive(context, listener) instead."));
    }

    public void d0(Context context0, int v) {
        this.E.c(v);
        try {
            this.j0();
        }
        catch(Throwable throwable0) {
            this.x0(context0, throwable0);
        }
    }

    // 去混淆评级： 低(30)
    public static boolean e0() {
        return Build.VERSION.SDK_INT >= 24 && (!new File(p.P).exists() && new File(p.P.replace("user_de/0", "data")).exists() && z.h0(true));
    }

    public boolean f0(Context context0, String s) {
        String s1 = s.trim();
        int v = s1.length();
        if(v < 4) {
            return false;
        }
        if(s1.charAt(0) == 35) {
            if(s1.endsWith(" 1")) {
                int v1 = p.D(s1.substring(1, v - 2));
                if(v1 > 0) {
                    this.o0(context0, v1, true);
                    return true;
                }
            }
            else if(s1.endsWith(" 0")) {
                int v2 = p.D(s1.substring(1, v - 2));
                if(v2 > 0) {
                    this.o0(context0, v2, false);
                    return true;
                }
            }
        }
        return false;
    }

    public boolean g0(int v) {
        return (1 << v & this.u(24)) != 0;
    }

    private static boolean h0(boolean z) {
        try {
            String[] arr_s = j.F;
            for(int v = 0; v < arr_s.length; ++v) {
                String s = arr_s[v];
                String s1 = s.replace("user_de/0", "data");
                String s2 = z ? s1 : s;
                if(!z) {
                    s = s1;
                }
                r.j(s2, s);
            }
            return true;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return false;
        }
    }

    public void i0(Context context0, int v) {
        this.a0(v, false);
        this.P(1, v, 0, this.B);
        this.P(1, v, 1, this.B);
        this.P(1, v, 2, this.B);
        this.E.n(v);
        try {
            this.j0();
            Intent intent0 = new Intent();
            new m(intent0).j().o(6).o(v).o(0).l();
            this.c(context0, intent0);
            Intent intent1 = new Intent();
            new m(intent1).j().o(1).o(v).o(0).p(this.B).l();
            this.c(context0, intent1);
            Intent intent2 = new Intent();
            new m(intent2).j().o(1).o(v).o(1).p(this.B).l();
            this.c(context0, intent2);
            Intent intent3 = new Intent();
            new m(intent3).j().o(1).o(v).o(2).p(this.B).l();
            this.c(context0, intent3);
        }
        catch(Throwable throwable0) {
            this.x0(context0, throwable0);
        }
    }

    private void j0() {
        if(!this.N) {
            return;
        }
        f.p p0 = this.A();
        p0.F();
        p0.w(4).w(this.K).C();
        p0.w(6);
        int[] arr_v = this.E.b;
        for(int v = 0; v < arr_v.length; ++v) {
            p0.w(arr_v[v]);
        }
        p0.C();
        p0.w(7);
        int[] arr_v1 = this.L;
        for(int v1 = 0; v1 < arr_v1.length; ++v1) {
            p0.w(arr_v1[v1]);
        }
        p0.C();
        p0.w(10).w(this.M.length);
        int[] arr_v2 = this.M;
        for(int v2 = 0; v2 < arr_v2.length; ++v2) {
            p0.w(arr_v2[v2]);
        }
        p0.C();
        this.O(p0, 0, this.D);
        this.O(p0, 1, this.E.a);
        this.O(p0, 2, this.F);
        this.O(p0, 3, this.G);
        for(int v3 = 0; true; ++v3) {
            a.b[] arr_b = this.H;
            if(v3 >= arr_b.length) {
                break;
            }
            a.b b0 = arr_b[v3];
            if(b0 != null && b0.z != 0) {
                p0.w(8).w(v3).x(b0).C();
            }
        }
        for(int v4 = 0; true; ++v4) {
            q1[] arr_b$q1 = this.I;
            if(v4 >= arr_b$q1.length) {
                break;
            }
            if(arr_b$q1[v4] != null) {
                p0.w(9).w(v4).x(this.I[v4]).C();
            }
        }
        this.E.o(p0);
        p0.n();
        this.N = false;
    }

    private void k0(Context context0, int v, int v1) {
        try {
            this.j0();
            Intent intent0 = new Intent();
            new m(intent0).j().o(7).o(v).o(v1).l();
            this.c(context0, intent0);
        }
        catch(Throwable throwable0) {
            this.x0(context0, throwable0);
        }
    }

    public void l0(Context context0, int v, int v1, int v2, a.b b0) {
        if(b0.z == 1 && (v == 2 || v == 3 || v == 8)) {
            b0 = this.B;
        }
        this.P(v, v1, v2, b0);
        try {
            this.j0();
            Intent intent0 = new Intent();
            new m(intent0).j().o(v).o(v1).o(v2).p(b0).l();
            this.c(context0, intent0);
        }
        catch(Throwable throwable0) {
            this.x0(context0, throwable0);
        }
    }

    public void m0(Context context0, int v, a.b b0) {
        this.R(v, b0);
        try {
            this.j0();
            Intent intent0 = new Intent();
            new m(intent0).j().o(8).o(v).p(b0).l();
            this.c(context0, intent0);
        }
        catch(Throwable throwable0) {
            this.x0(context0, throwable0);
        }
    }

    public void n0(Context context0, int v, q1 b$q10) {
        this.U(v, b$q10);
        try {
            this.j0();
            Intent intent0 = new Intent();
            new m(intent0).j().o(9).o(v).p(b$q10).l();
            this.c(context0, intent0);
        }
        catch(Throwable throwable0) {
            this.x0(context0, throwable0);
        }
    }

    public void o0(Context context0, int v, boolean z) {
        int v1 = 1;
        if(v < 0x20) {
            this.V(v, z);
            try {
                this.j0();
                Intent intent0 = new Intent();
                m m0 = new m(intent0).j().o(4).o(v);
                if(!z) {
                    v1 = 0;
                }
                m0.o(v1).l();
                this.c(context0, intent0);
            }
            catch(Throwable throwable0) {
                this.x0(context0, throwable0);
            }
            return;
        }
        int v2 = this.u(21);
        int v3 = 1 << v - 0x20;
        this.s0(context0, 21, (z ? v3 | v2 : ~v3 & v2));
    }

    public void p0(Context context0, int v) {
        if(v == 0) {
            v = -1;
        }
        this.s0(context0, 18, v);
    }

    public void q0(Context context0, int v, boolean z) {
        int v1 = this.u(24);
        this.s0(context0, 24, (z ? 1 << v | v1 : ~(1 << v) & v1));
    }

    public void r0(Context context0, int v, float f) {
        this.Y(v, f);
        this.k0(context0, v, q.a(f));
    }

    public void s0(Context context0, int v, int v1) {
        this.Z(v, v1);
        this.k0(context0, v, v1);
    }

    public void t0(Context context0, int v, boolean z) {
        this.a0(v, z);
        try {
            this.j0();
            Intent intent0 = new Intent();
            new m(intent0).j().o(6).o(v).o((z ? 1 : 0)).l();
            this.c(context0, intent0);
        }
        catch(Throwable throwable0) {
            this.x0(context0, throwable0);
        }
    }

    public boolean u0(Context context0, int[] arr_v) {
        if(arr_v == null) {
            v.c("colors == null");
            return false;
        }
        this.b0(arr_v);
        try {
            this.j0();
            Intent intent0 = new Intent();
            m m0 = new m(intent0).j().o(10).o(this.M.length);
            int[] arr_v1 = this.M;
            for(int v = 0; v < arr_v1.length; ++v) {
                m0.o(arr_v1[v]);
            }
            m0.l();
            this.c(context0, intent0);
            return true;
        }
        catch(Throwable throwable0) {
            this.x0(context0, throwable0);
            return false;
        }
    }

    public void v0(Context context0, int v) {
        if(ApplicationMain.isModuleActivated()) {
            this.s0(context0, 1, v);
            s0.B(context0, 11);
            this.J &= 0xFFFFFF0F;
            this.N = true;
            return;
        }
        v.c("Module not activated.");
    }

    public static boolean w0() {
        boolean z;
        int v = 0;
        try {
            String s = l.l;
            z = false;
            if((r.k(s) & 1) == 0) {
                new File(s).setExecutable(true, false);
                z = true;
            }
        }
        catch(Throwable unused_ex) {
        }
        String s1 = p.P;
        if(new File(s1).exists()) {
            try {
                if((r.k(s1) & 4) == 0) {
                    String[] arr_s = j.F;
                    while(v < arr_s.length) {
                        String s2 = arr_s[v];
                        if(r.h(s2)) {
                            ++v;
                            z = true;
                        }
                        else {
                            v.c(("Failed to set world readable: " + s2));
                            if(true) {
                                break;
                            }
                        }
                    }
                    v.c("Set readable.");
                    return z;
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        return z;
    }

    private void x0(Context context0, Throwable throwable0) {
        v.d(throwable0);
        Toast.makeText(context0, "Failed to save file!" + throwable0.getMessage(), 0).show();
    }

    public void y0(Context context0, b z$b0) {
        class a extends ContentObserver {
            final ContentResolver a;
            final b b;
            final z c;

            a(Handler handler0, ContentResolver contentResolver0, b z$b0) {
                this.a = contentResolver0;
                this.b = z$b0;
                super(handler0);
            }

            @Override  // android.database.ContentObserver
            public void onChange(boolean z) {
                int v = z.this.J;
                z.this.J = Settings.System.getInt(this.a, p.Q, v);
                z z1 = z.this;
                int v1 = v ^ z1.J;
                if(v1 == 0) {
                    return;
                }
                if((v1 & 2) != 0) {
                    boolean z2 = z1.p(1);
                    this.b.a(z2);
                    return;
                }
                if((v1 & 1) != 0) {
                    boolean z3 = z1.p(0);
                    this.b.b(z3);
                }
            }
        }

        ContentResolver contentResolver0 = context0.getContentResolver();
        String s = p.Q;
        this.J = Settings.System.getInt(contentResolver0, s, this.J);
        if(z$b0 == null) {
            return;
        }
        this.R = new a(this, h0.d(), contentResolver0, z$b0);
        contentResolver0.registerContentObserver(Settings.System.getUriFor(s), true, this.R);
    }
}


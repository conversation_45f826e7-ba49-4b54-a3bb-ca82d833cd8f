package e;

import android.app.Activity;
import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.widget.LinearLayout;
import android.widget.SeekBar.OnSeekBarChangeListener;
import android.widget.SeekBar;
import android.widget.TextView;
import g.x;

public class g0 extends b implements SeekBar.OnSeekBarChangeListener {
    private TextView C;
    private SeekBar D;
    private boolean E;

    public g0() {
        this.C = null;
        this.E = false;
    }

    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        Activity activity0 = this.getActivity();
        Bundle bundle1 = this.e();
        x x0 = this.g();
        int v = x0.f;
        int v1 = bundle1.getInt("min", 0);
        int v2 = bundle1.getInt("max");
        int v3 = bundle1.getInt("progress", v1);
        CharSequence charSequence0 = bundle1.getCharSequence("title");
        LinearLayout linearLayout0 = new LinearLayout(activity0);
        linearLayout0.setOrientation(1);
        linearLayout0.setPadding(v, v, v, v);
        CharSequence charSequence1 = bundle1.getCharSequence("description", null);
        this.E = charSequence1 == null;
        TextView textView0 = new TextView(activity0);
        this.C = textView0;
        textView0.setTextSize(0, ((float)x0.c));
        this.C.setPadding(v, v, v, 0);
        TextView textView1 = this.C;
        if(this.E) {
            charSequence1 = Integer.toString((v3 + v1) * bundle1.getInt("scale", 1));
        }
        textView1.setText(charSequence1);
        linearLayout0.addView(this.C);
        SeekBar seekBar0 = new SeekBar(activity0);
        this.D = seekBar0;
        seekBar0.setMax(v2 - v1);
        this.D.setProgress(v3);
        this.D.setPadding(v * 3, v, v * 3, v);
        this.D.setOnSeekBarChangeListener(this);
        linearLayout0.addView(this.D, -1, -2);
        return new AlertDialog.Builder(activity0).setTitle(charSequence0).setView(linearLayout0).setNegativeButton(0x1040000, b.B).setPositiveButton(0x104000A, (DialogInterface dialogInterface0, int v) -> this.p("result", this.D.getProgress() + this.e().getInt("min", 0))).create();
    }

    @Override  // android.widget.SeekBar$OnSeekBarChangeListener
    public void onProgressChanged(SeekBar seekBar0, int v, boolean z) {
        if(this.E) {
            Bundle bundle0 = this.e();
            this.C.setText(Integer.toString((v + bundle0.getInt("min", 0)) * bundle0.getInt("scale", 1)));
        }
    }

    @Override  // e.j0$b
    public void onSaveInstanceState(Bundle bundle0) {
        if(this.D != null) {
            this.e().putInt("progress", this.D.getProgress() + this.e().getInt("min", 0));
        }
        super.onSaveInstanceState(bundle0);
    }

    @Override  // android.widget.SeekBar$OnSeekBarChangeListener
    public void onStartTrackingTouch(SeekBar seekBar0) {
    }

    @Override  // android.widget.SeekBar$OnSeekBarChangeListener
    public void onStopTrackingTouch(SeekBar seekBar0) {
    }

    // 检测为 Lambda 实现
    private void v(DialogInterface dialogInterface0, int v) [...]

    public g0 w(CharSequence charSequence0, CharSequence charSequence1, int v, int v1) {
        return this.y(charSequence0, charSequence1, 0, v, v1, 1);
    }

    public g0 x(CharSequence charSequence0, CharSequence charSequence1, int v, int v1, int v2) {
        return this.y(charSequence0, charSequence1, v, v1, v2, 1);
    }

    public g0 y(CharSequence charSequence0, CharSequence charSequence1, int v, int v1, int v2, int v3) {
        Bundle bundle0 = this.e();
        bundle0.putCharSequence("title", charSequence0);
        bundle0.putCharSequence("description", charSequence1);
        bundle0.putInt("progress", v1 - v);
        bundle0.putInt("min", v);
        bundle0.putInt("max", v2);
        bundle0.putInt("scale", v3);
        return this;
    }

    public void z(CharSequence charSequence0) {
        TextView textView0 = this.C;
        if(textView0 != null) {
            textView0.setText(charSequence0);
        }
    }
}


package e;

import android.content.DialogInterface.OnKeyListener;
import android.content.DialogInterface;
import android.view.KeyEvent;

public final class d0 implements DialogInterface.OnKeyListener {
    public static final d0 a;

    static {
        d0.a = new d0();
    }

    @Override  // android.content.DialogInterface$OnKeyListener
    public final boolean onKey(DialogInterface dialogInterface0, int v, KeyEvent keyEvent0) {
        return e0.u(dialogInterface0, v, keyEvent0);
    }
}


package f;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.net.Uri;
import android.view.KeyEvent;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.InvalidParameterException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class p0 implements l {
    private static final char[] A;
    private static final byte[] B;
    private static int C;
    public static final Charset z;

    static {
        p0.z = StandardCharsets.UTF_8;
        p0.A = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        p0.B = new byte[0];
        p0.C = -1;
    }

    public static int a(int[] arr_v, int v) {
        for(int v1 = 0; v1 < arr_v.length; ++v1) {
            if(arr_v[v1] == v) {
                return v1;
            }
        }
        return -1;
    }

    public static int b(Object[] arr_object, Object object0, int v) {
        int v1 = 0;
        if(object0 != null) {
            while(v1 < v) {
                if(object0.equals(arr_object[v1])) {
                    return v1;
                }
                ++v1;
            }
            return -1;
        }
        while(v1 < v) {
            if(arr_object[v1] == null) {
                return v1;
            }
            ++v1;
        }
        return -1;
    }

    public static void c(Object[] arr_object, int v, int v1) {
        if(v == v1) {
            return;
        }
        Object object0 = arr_object[v];
        if(v > v1) {
            System.arraycopy(arr_object, v1, arr_object, v1 + 1, v - v1);
        }
        else {
            System.arraycopy(arr_object, v + 1, arr_object, v, v1 - v);
        }
        arr_object[v1] = object0;
    }

    public static int d(int[] arr_v, int v) {
        int v1 = arr_v.length - 1;
        int v2 = 0;
        while(v2 <= v1) {
            int v3 = v2 + v1 >>> 1;
            int v4 = arr_v[v3] - v;
            if(v4 < 0) {
                v2 = v3 + 1;
                continue;
            }
            if(v4 > 0) {
                v1 = v3 - 1;
                continue;
            }
            return v3;
        }
        return ~v2;
    }

    public static int e(int v, int v1, int v2) {
        if(v < v1) {
            return v1;
        }
        return v <= v2 ? v : v2;
    }

    public static String f() {
        return new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
    }

    public static byte[] g(String s) {
        int v5;
        int v3;
        if(s != null && s.length() != 0) {
            int v = s.length();
            byte[] arr_b = new byte[v / 2];
            for(int v1 = 0; v1 < v / 2; ++v1) {
                int v2 = s.charAt(v1 * 2);
                if(v2 < 0x30 || v2 > 57) {
                    if(v2 < 65 || v2 > 70) {
                        throw new InvalidParameterException("Invalid data: " + s);
                    }
                    v3 = v2 - 55;
                }
                else {
                    v3 = v2 - 0x30;
                }
                int v4 = s.charAt(v1 * 2 + 1);
                if(v4 < 0x30 || v4 > 57) {
                    if(v4 < 65 || v4 > 70) {
                        throw new InvalidParameterException("Invalid data: " + s);
                    }
                    v5 = v4 - 55;
                }
                else {
                    v5 = v4 - 0x30;
                }
                arr_b[v1] = (byte)(v5 << 4 | v3);
            }
            return arr_b;
        }
        return p0.B;
    }

    public static String h(String s) {
        return s == null ? null : Integer.toString(Integer.parseInt(s) - 1);
    }

    public static String i(byte[] arr_b) {
        if(arr_b != null && arr_b.length != 0) {
            StringBuilder stringBuilder0 = new StringBuilder(arr_b.length);
            for(int v = 0; v < arr_b.length; ++v) {
                int v1 = arr_b[v];
                stringBuilder0.append(p0.A[v1 & 15]);
                stringBuilder0.append(p0.A[v1 >> 4 & 15]);
            }
            return stringBuilder0.toString();
        }
        return "";
    }

    public static String j(Context context0, int v) {
        switch(v) {
            case 0: {
                return "Click";
            }
            case 1: {
                return "Double click";
            }
            case 2: {
                return "Long press";
            }
            default: {
                return null;
            }
        }
    }

    public static String k(Context context0, int v) {
        switch(v) {
            case 3: {
                return "Home";
            }
            case 4: {
                return "Back";
            }
            case 24: {
                return "Volume up";
            }
            case 25: {
                return "Volume down";
            }
            case 26: {
                return "Power";
            }
            case 0x4F: {
                return "Headset hook";
            }
            case 82: {
                return "Menu";
            }
            case 0xBB: {
                return "Recent apps";
            }
            default: {
                return KeyEvent.keyCodeToString(v);
            }
        }
    }

    public static int l(String s) {
        Resources resources0 = Resources.getSystem();
        int v = resources0.getIdentifier(s, "dimen", "android");
        return v <= 0 ? 0 : resources0.getDimensionPixelSize(v);
    }

    public static int m(String s, int v) {
        Resources resources0 = Resources.getSystem();
        int v1 = resources0.getIdentifier(s, "integer", "android");
        return v1 <= 0 ? v : resources0.getInteger(v1);
    }

    public static String n(String s) {
        Resources resources0 = Resources.getSystem();
        int v = resources0.getIdentifier(s, "string", "android");
        return v <= 0 ? null : resources0.getString(v);
    }

    public static String o(String s) {
        return s == null ? null : Integer.toString(Integer.parseInt(s) + 1);
    }

    public static int p() {
        int v1;
        if(p0.C == -1) {
            StackTraceElement[] arr_stackTraceElement = new Throwable().getStackTrace();
            int v = 0;
            boolean z = false;
            while(v < arr_stackTraceElement.length) {
                String s = arr_stackTraceElement[v].getClassName();
                if(!s.contains("LspHooker") && !s.contains("lsposed")) {
                    if(s.contains("elderdrivers")) {
                        v1 = 2;
                        p0.C = v1;
                        return v1;
                    }
                    if(!z && s.startsWith("de.robv.android.xposed")) {
                        z = true;
                    }
                    ++v;
                    continue;
                }
                v1 = 3;
                p0.C = v1;
                return v1;
            }
            if(z) {
                p0.C = 1;
                return 1;
            }
            p0.C = 0;
        }
        return p0.C;
    }

    public static boolean q(Context context0, String s, String s1) {
        int v;
        if(context0 != null && s != null && s1 != null) {
            try {
                String[] arr_s = context0.getPackageManager().getPackageInfo(s, 0x1000).requestedPermissions;
                for(v = 0; true; ++v) {
                label_3:
                    if(v >= arr_s.length) {
                        return false;
                    }
                    if(s1.equals(arr_s[v])) {
                        break;
                    }
                }
                return true;
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return false;
            }
            ++v;
            goto label_3;
        }
        return false;
    }

    public static int r() {
        return (int)(System.currentTimeMillis() / 1000L);
    }

    public static void s(Context context0, Intent intent0) {
        intent0.addFlags(0x30000000);
        try {
            context0.startActivity(intent0);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    public static void t(Context context0, String s) {
        Intent intent0 = context0.getPackageManager().getLaunchIntentForPackage(s);
        if(intent0 != null) {
            p0.s(context0, intent0);
            return;
        }
        v.c(("Main activity not found: " + s));
    }

    public static void u(Context context0, String s) {
        p0.s(context0, new Intent("android.settings.APPLICATION_DETAILS_SETTINGS", Uri.fromParts("package", s, null)));
    }

    public static void v(Activity activity0, String s) {
        Intent intent0 = new Intent("android.intent.action.VIEW", Uri.parse(("market://details?id=" + s)));
        intent0.addFlags(0x48080000);
        if(l.w.equals(s)) {
            intent0.setPackage("com.android.vending");
        }
        try {
            activity0.startActivity(intent0);
        }
        catch(Throwable unused_ex) {
            try {
                Intent intent1 = new Intent("android.intent.action.VIEW", Uri.parse(("http://play.google.com/store/apps/details?id=" + s)));
                intent1.addFlags(0x48080000);
                activity0.startActivity(intent1);
            }
            catch(Throwable unused_ex) {
            }
        }
    }
}


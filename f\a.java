package f;

import android.app.AlarmManager.OnAlarmListener;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build.VERSION;
import android.os.Handler;

public abstract class a {
    static class f.a.a extends a {
        private final Context e;
        private final Handler f;
        private final c g;
        private final int h;
        private BroadcastReceiver i;

        f.a.a(Context context0, Handler handler0, c a$c0, int v) {
            this.e = context0;
            this.f = handler0;
            this.g = a$c0;
            this.h = v;
        }

        @Override  // f.a
        public void a() {
            this.b = 0L;
            BroadcastReceiver broadcastReceiver0 = this.i;
            if(broadcastReceiver0 != null) {
                try {
                    this.e.unregisterReceiver(broadcastReceiver0);
                    this.i = null;
                    AlarmManager alarmManager0 = (AlarmManager)this.e.getSystemService("alarm");
                    Intent intent0 = new Intent(this.a);
                    alarmManager0.cancel(PendingIntent.getBroadcast(this.e, 0, intent0, (Build.VERSION.SDK_INT < 0x1F ? 0x10000000 : 0x12000000)));
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        @Override  // f.a
        public void d(long v) {
            class f.a.a.a extends BroadcastReceiver {
                final f.a.a a;

                @Override  // android.content.BroadcastReceiver
                public void onReceive(Context context0, Intent intent0) {
                    try {
                        long v = f.a.a.this.b;
                        f.a.a.this.b = 0L;
                        f.a.a.this.g.a(f.a.a.this, v);
                        f.a.a a$a0 = f.a.a.this;
                        if(a$a0.b <= 0L) {
                            context0.unregisterReceiver(a$a0.i);
                            f.a.a.this.i = null;
                        }
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }

            if(this.i == null) {
                f.a.a.a a$a$a0 = new f.a.a.a(this);
                this.i = a$a$a0;
                IntentFilter intentFilter0 = new IntentFilter(this.a);
                this.e.registerReceiver(a$a$a0, intentFilter0, null, this.f);
            }
            AlarmManager alarmManager0 = (AlarmManager)this.e.getSystemService("alarm");
            Intent intent0 = new Intent(this.a);
            intent0.setPackage("android");
            PendingIntent pendingIntent0 = PendingIntent.getBroadcast(this.e, 0, intent0, (Build.VERSION.SDK_INT < 0x1F ? 0x8000000 : 0xA000000));
            try {
                alarmManager0.setExact(this.h, v, pendingIntent0);
                this.b = v;
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    static class b extends a implements AlarmManager.OnAlarmListener {
        private final Context e;
        private final Handler f;
        private final c g;
        private final int h;
        private f.a.a i;

        b(Context context0, Handler handler0, c a$c0, int v) {
            this.i = null;
            this.e = context0;
            this.f = handler0;
            this.g = a$c0;
            this.h = v;
        }

        @Override  // f.a
        public void a() {
            f.a.a a$a0 = this.i;
            if(a$a0 != null) {
                a$a0.a();
                return;
            }
            try {
                this.b = 0L;
                ((AlarmManager)this.e.getSystemService("alarm")).cancel(this);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        @Override  // f.a
        public void d(long v) {
            f.a.a a$a0 = this.i;
            if(a$a0 != null) {
                a$a0.d(v);
                return;
            }
            AlarmManager alarmManager0 = (AlarmManager)this.e.getSystemService("alarm");
            if(this.b > 0L) {
                try {
                    alarmManager0.cancel(this);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
            try {
                alarmManager0.setExact(this.h, v, this.a, this, this.f);
                this.b = v;
            }
            catch(Throwable throwable1) {
                v.d(throwable1);
                f.a.a a$a1 = new f.a.a(this.e, this.f, this.g, this.h);
                this.i = a$a1;
                a$a1.d(v);
            }
        }

        @Override  // android.app.AlarmManager$OnAlarmListener
        public void onAlarm() {
            try {
                long v = this.b;
                this.b = 0L;
                this.g.a(this, v);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    public interface c {
        void a(a arg1, long arg2);
    }

    protected final String a;
    long b;
    private static final String c;
    private static int d;

    static {
        a.c = l.k + "SCHEDULE-";
        a.d = 0;
    }

    a() {
        this.b = 0L;
        int v = a.d + 1;
        a.d = v;
        this.a = a.c + v;
    }

    public abstract void a();

    public long b() {
        return this.b;
    }

    public static a c(Context context0, Handler handler0, c a$c0, int v) {
        return Build.VERSION.SDK_INT >= 24 ? new b(context0, handler0, a$c0, v) : new f.a.a(context0, handler0, a$c0, v);
    }

    public abstract void d(long arg1);
}


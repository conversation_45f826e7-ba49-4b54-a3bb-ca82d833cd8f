package d;

import a.b.q1;
import a.b;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import e.j.g;
import e.j.k;
import e.m;
import e.z;
import java.util.ArrayList;
import java.util.Collections;

public class s0 extends c implements a {
    private ArrayList M;
    private static final int[] N;

    static {
        s0.N = new int[]{0x7F06012D, 0x7F060159, 0x7F060158, 0x7F0600CD};  // string:insert "Insert"
    }

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.R1();
    }

    @Override  // e.j
    protected int B0() {
        this.M = this.J1();
        return this.h().getBoolean("editable") ? this.M.size() + 1 : this.M.size();
    }

    @Override  // d.c
    protected void C1(Bundle bundle0, int v) {
        int v1;
        b b0;
        if(v == 1) {
            int v2 = this.E0();
            int v3 = bundle0.getInt("result", -1);
            if(v3 >= 0) {
                switch(s0.N[v3]) {
                    case 0x7F0600CD: {  // string:delete "Delete"
                        this.P1(v2);
                        return;
                    }
                    case 0x7F06012D: {  // string:insert "Insert"
                        this.E1(this.K1(), 3);
                        return;
                    }
                    case 0x7F060158: {  // string:move_down "Move down"
                        if(v2 < this.M.size() - 1) {
                            this.O1(v2, false);
                            return;
                        }
                        break;
                    }
                    case 0x7F060159: {  // string:move_up "Move up"
                        if(v2 > 0) {
                            this.O1(v2, true);
                            return;
                        }
                        break;
                    }
                }
            }
        }
        else {
            switch(v) {
                case 2: {
                    b0 = (b)bundle0.getParcelable("result");
                    if(b0 != null) {
                        v1 = this.M.size();
                        this.M1(v1, b0);
                        return;
                    }
                    break;
                }
                case 3: {
                    b0 = (b)bundle0.getParcelable("result");
                    if(b0 != null) {
                        v1 = this.E0();
                        this.M1(v1, b0);
                        return;
                    }
                    break;
                }
                case 4: {
                    b b1 = (b)bundle0.getParcelable("result");
                    if(b1 != null) {
                        this.Q1(this.E0(), b1);
                        return;
                    }
                    break;
                }
                case 5: {
                    if(bundle0.getBoolean("result", false)) {
                        this.W("result", new q1(this.M));
                    }
                    this.L();
                }
            }
        }
    }

    protected final ArrayList J1() {
        ArrayList arrayList0 = this.M;
        if(arrayList0 != null) {
            return arrayList0;
        }
        ArrayList arrayList1 = this.h().getParcelableArrayList("actions");
        return arrayList1 == null ? new ArrayList() : arrayList1;
    }

    protected int K1() {
        return 4;
    }

    // 检测为 Lambda 实现
    private void L1(View view0) [...]

    protected void M1(int v, b b0) {
        this.M.add(v, b0);
        this.x0(v);
        this.h().putBoolean("changed", true);
    }

    protected k N1(int v) {
        Context context0 = this.M0();
        if(v < this.M.size()) {
            b b0 = (b)this.M.get(v);
            return new g(this, this.D0(b0), b0.n(context0), null);
        }
        return this.b1();
    }

    protected void O1(int v, boolean z) {
        int v1 = z ? v - 1 : v + 1;
        this.M.add(v1, ((b)this.M.remove(v)));
        this.a1(v, v1);
        this.h().putBoolean("changed", true);
    }

    protected void P1(int v) {
        this.M.remove(v);
        this.u1(v);
        this.h().putBoolean("changed", true);
    }

    protected void Q1(int v, b b0) {
        this.M.set(v, b0);
        g j$g0 = (g)this.L0(v);
        j$g0.setImageDrawable(this.D0(b0));
        j$g0.setText(b0.n(this.M0()));
        this.h().putBoolean("changed", true);
    }

    protected void R1() {
        this.c0(0x7F06003C);  // string:action_multi_action "Multi-action"
        this.S(0x7F040071, (View view0) -> {
            if(this.M != null) {
                this.W("result", new q1(this.M));
            }
            this.L();
        });
    }

    public s0 S1(b[] arr_b, boolean z) {
        ArrayList arrayList0 = new ArrayList(arr_b.length);
        this.M = arrayList0;
        Collections.addAll(arrayList0, arr_b);
        Bundle bundle0 = this.h();
        bundle0.putParcelableArrayList("actions", this.M);
        bundle0.putBoolean("editable", z);
        return this;
    }

    @Override  // e.j
    protected View h1(int v) {
        return this.N1(v);
    }

    @Override  // e.j
    protected void k1(int v) {
        if(v == this.M.size()) {
            this.E1(this.K1(), 2);
            return;
        }
        if(this.h().getBoolean("editable")) {
            CharSequence[] arr_charSequence = new CharSequence[s0.N.length];
            for(int v1 = 0; true; ++v1) {
                int[] arr_v = s0.N;
                if(v1 >= arr_v.length) {
                    break;
                }
                arr_charSequence[v1] = this.u(arr_v[v1]);
            }
            this.N(new z().u(arr_charSequence), 1);
        }
    }

    @Override  // e.j
    protected boolean l1(int v) {
        if(v < this.M.size()) {
            b b0 = (b)this.M.get(v);
            boolean z = this.h().getBoolean("editable");
            this.G1(b0, 4, this.K1(), z);
            return true;
        }
        return super.l1(v);
    }

    @Override  // e.j
    public void onSaveInstanceState(Bundle bundle0) {
        this.h().putParcelableArrayList("actions", this.M);
        super.onSaveInstanceState(bundle0);
    }

    @Override  // e.j0$c
    protected void y() {
        if(this.h().getBoolean("changed", false)) {
            this.N(new m().u(this.u(0x7F06008F)), 5);  // string:check_save_message "Do you want to save changes?"
            return;
        }
        super.y();
    }
}


package a;

import android.content.Context;
import android.content.Intent;
import f.l;
import f.m;
import f.p;
import f.t;
import java.util.ArrayList;
import java.util.HashMap;

public class v extends r {
    public static class a extends r {
        private final p B;
        private final ArrayList C;
        private static a D;

        static {
        }

        private a() {
            super(v.D);
            p p0 = new p(v.C);
            this.B = p0;
            this.C = new ArrayList();
            if(p0.E()) {
                while(true) {
                    String s = this.B.a();
                    this.C.add(s);
                    if(!this.B.t()) {
                        break;
                    }
                }
            }
            this.B.m();
        }

        @Override  // a.r
        protected void b(Intent intent0) {
        }

        public boolean e(Context context0, String s) {
            if(this.C.contains(s)) {
                return false;
            }
            this.C.add(s);
            this.j();
            Intent intent0 = new Intent();
            new m(intent0).j().o(0).q(s).l();
            this.c(context0, intent0);
            return true;
        }

        public String f(int v) {
            return (String)this.C.get(v);
        }

        public static a g() {
            synchronized(a.class) {
                if(a.D == null) {
                    a.D = new a();
                }
                return a.D;
            }
        }

        public static void h() {
            synchronized(a.class) {
                a.D = null;
            }
        }

        public void i(Context context0, String s) {
            this.C.remove(s);
            this.j();
            Intent intent0 = new Intent();
            new m(intent0).j().o(1).q(s).l();
            this.c(context0, intent0);
        }

        private void j() {
            this.B.F();
            int v = this.C.size();
            for(int v1 = 0; v1 < v; ++v1) {
                String s = (String)this.C.get(v1);
                this.B.y(s).C();
            }
            this.B.n();
        }

        public int k() {
            return this.C.size();
        }
    }

    private HashMap B;
    private static final String C;
    private static final String D;

    static {
        v.C = l.m + "variables";
        v.D = l.k + "VARIABLES_CHANGED";
    }

    public v() {
        super(v.D);
        this.h();
    }

    @Override  // a.r
    protected void b(Intent intent0) {
        HashMap hashMap0;
        m m0 = new m(intent0);
        if(m0.i()) {
            switch(m0.h()) {
                case 0: {
                    String s = m0.a();
                    if(s != null) {
                        hashMap0 = new HashMap(this.B);
                        hashMap0.put(s, new t(0));
                        this.B = hashMap0;
                    }
                    break;
                }
                case 1: {
                    String s1 = m0.a();
                    if(s1 != null) {
                        hashMap0 = new HashMap(this.B);
                        hashMap0.remove(s1);
                        this.B = hashMap0;
                    }
                }
            }
        }
        m0.k();
    }

    public t g(String s) {
        return (t)this.B.get(s);
    }

    public void h() {
        HashMap hashMap0 = new HashMap();
        p p0 = new p(v.C);
        if(p0.E()) {
            while(true) {
                hashMap0.put(p0.a(), new t(0));
                if(!p0.t()) {
                    break;
                }
            }
        }
        p0.m();
        this.B = hashMap0;
    }
}


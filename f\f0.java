package f;

import android.app.Activity;
import android.app.Application.OnProvideAssistDataListener;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.util.Pair;
import java.lang.reflect.AccessibleObject;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.util.function.BiFunction;

public class f0 {
    static class a {
    }

    static final class b extends f0 {
        private final BiFunction c;

        b(BiFunction biFunction0) {
            this.c = biFunction0;
            e.w(biFunction0);
        }

        @Override  // f.f0
        public Class d(String s, ClassLoader classLoader0) {
            Class class0 = (Class)this.v(2, new Object[]{s, classLoader0});
            return class0 == null ? super.d(s, classLoader0) : class0;
        }

        @Override  // f.f0
        public Constructor e(Class class0, Class[] arr_class) {
            Constructor constructor0 = (Constructor)this.v(5, new Object[]{class0, arr_class});
            return constructor0 == null ? super.e(class0, arr_class) : constructor0;
        }

        @Override  // f.f0
        public Field g(Class class0, String s) {
            Field field0 = (Field)this.v(4, new Object[]{class0, s});
            return field0 == null ? super.g(class0, s) : field0;
        }

        @Override  // f.f0
        public Method h(Class class0, String s, Class[] arr_class) {
            Method method0 = (Method)this.v(8, new Object[]{class0, s, arr_class});
            return method0 == null ? super.h(class0, s, arr_class) : method0;
        }

        @Override  // f.f0
        public Method j(Class class0, String s, Class[] arr_class) {
            Method method0 = (Method)this.v(7, new Object[]{class0, s, arr_class});
            return method0 == null ? super.j(class0, s, arr_class) : method0;
        }

        @Override  // f.f0
        public Method[] m(Class class0) {
            Method[] arr_method = (Method[])this.v(12, new Object[]{class0});
            return arr_method == null ? super.m(class0) : arr_method;
        }

        @Override  // f.f0
        public void s(AccessibleObject accessibleObject0) {
            if(this.v(1, new Object[]{accessibleObject0}) == null) {
                super.s(accessibleObject0);
            }
        }

        private Object v(int v, Object[] arr_object) {
            return this.c.apply(v, arr_object);
        }
    }

    static class c extends Pair implements Application.OnProvideAssistDataListener {
        static final String a;

        static {
            c.a = l.j + ".REFLECT_ADAPTER";
        }

        public c(Object object0) {
            super(c.a, object0);
        }

        @Override  // android.app.Application$OnProvideAssistDataListener
        public void onProvideAssistData(Activity activity0, Bundle bundle0) {
            v.c("Unused method!!!");
        }
    }

    public static abstract class d implements InvocationHandler {
        protected abstract Object a(String arg1, Object[] arg2);

        @Override
        public final Object invoke(Object object0, Method method0, Object[] arr_object) {
            String s = method0.getName();
            s.hashCode();
            boolean z = true;
            switch(s) {
                case "clone": {
                    throw new CloneNotSupportedException("InterfaceHandler " + this.getClass().getName() + " doesn\'t cloneable");
                }
                case "equals": {
                    if(this != arr_object[0]) {
                        z = false;
                    }
                    return Boolean.valueOf(z);
                }
                case "hashCode": {
                    return this.hashCode();
                }
                case "toString": {
                    return this.toString();
                }
                default: {
                    return this.a(s, arr_object);
                }
            }
        }
    }

    static final class e extends f0 implements BiFunction {
        private e() {
        }

        e(a f0$a0) {
        }

        @Override
        public Object apply(Object object0, Object object1) {
            return this.v(((Integer)object0), ((Object[])object1));
        }

        public Object v(Integer integer0, Object[] arr_object) {
            try {
                switch(((int)integer0)) {
                    case 0: {
                        return c.a;
                    }
                    case 1: {
                        this.s(((AccessibleObject)arr_object[0]));
                        return true;
                    }
                    case 2: {
                        return this.d(((String)arr_object[0]), ((ClassLoader)arr_object[1]));
                    }
                    case 3: {
                        return this.i(((Class)arr_object[0]), ((String)arr_object[1]));
                    }
                    case 4: {
                        return this.g(((Class)arr_object[0]), ((String)arr_object[1]));
                    }
                    case 5: {
                        return this.e(((Class)arr_object[0]), ((Class[])arr_object[1]));
                    }
                    case 6: {
                        return this.f(((Class)arr_object[0]), ((Class[])arr_object[1]));
                    }
                    case 7: {
                        return this.j(((Class)arr_object[0]), ((String)arr_object[1]), ((Class[])arr_object[2]));
                    }
                    case 8: {
                        return this.h(((Class)arr_object[0]), ((String)arr_object[1]), ((Class[])arr_object[2]));
                    }
                    case 9: {
                        return this.k(((Class)arr_object[0]));
                    }
                    case 10: {
                        return this.l(((Class)arr_object[0]));
                    }
                    case 11: {
                        return this.o(((Class)arr_object[0]));
                    }
                    case 12: {
                        return this.m(((Class)arr_object[0]));
                    }
                    default: {
                        return null;
                    }
                }
            }
            catch(Throwable unused_ex) {
                return null;
            }
        }

        static void w(BiFunction biFunction0) {
            if(biFunction0.getClass().getClassLoader() != f0.class.getClassLoader()) {
                Object object0 = biFunction0.apply(0, null);
                if(c.a.equals(object0)) {
                    return;
                }
            }
            throw new IllegalArgumentException("Not available function: " + biFunction0);
        }
    }

    private static final f0 a;
    private static f0 b;

    static {
        f0 f00 = new f0();
        f0.a = f00;
        f0.b = f00;
    }

    private static boolean a(Class[] arr_class, Class[] arr_class1) {
        if(arr_class == arr_class1) {
            return true;
        }
        if(arr_class == null || arr_class1 == null || arr_class1.length != arr_class.length) {
            return false;
        }
        for(int v = 0; v < arr_class.length; ++v) {
            if(arr_class[v] != arr_class1[v]) {
                return false;
            }
        }
        return true;
    }

    public final Method b(Class class0, Class[] arr_class) {
        Method[] arr_method = this.m(class0);
        for(int v = 0; v < arr_method.length; ++v) {
            Method method0 = arr_method[v];
            if(f0.a(method0.getParameterTypes(), arr_class)) {
                this.s(method0);
                return method0;
            }
        }
        throw new NoSuchMethodException(class0.getName() + "#(unnamed)");
    }

    public final Class c(String s) {
        return this.d(s, this.getClass().getClassLoader());
    }

    public Class d(String s, ClassLoader classLoader0) {
        return Class.forName(s, true, classLoader0);
    }

    public Constructor e(Class class0, Class[] arr_class) {
        Constructor constructor0 = class0.getConstructor(arr_class);
        constructor0.setAccessible(true);
        return constructor0;
    }

    public Constructor f(Class class0, Class[] arr_class) {
        Constructor constructor0 = class0.getDeclaredConstructor(arr_class);
        constructor0.setAccessible(true);
        return constructor0;
    }

    public Field g(Class class0, String s) {
        Field field0 = class0.getDeclaredField(s);
        field0.setAccessible(true);
        return field0;
    }

    public Method h(Class class0, String s, Class[] arr_class) {
        Method method0 = class0.getDeclaredMethod(s, arr_class);
        method0.setAccessible(true);
        return method0;
    }

    public Field i(Class class0, String s) {
        Field field0 = class0.getField(s);
        field0.setAccessible(true);
        return field0;
    }

    public Method j(Class class0, String s, Class[] arr_class) {
        Method method0 = class0.getMethod(s, arr_class);
        method0.setAccessible(true);
        return method0;
    }

    public Constructor[] k(Class class0) {
        return class0.getConstructors();
    }

    public Constructor[] l(Class class0) {
        return class0.getDeclaredConstructors();
    }

    public Method[] m(Class class0) {
        return class0.getDeclaredMethods();
    }

    public static f0 n() {
        return f0.b;
    }

    public Method[] o(Class class0) {
        return class0.getMethods();
    }

    public static Application.OnProvideAssistDataListener p() {
        return Build.VERSION.SDK_INT >= 29 ? new c(new e(null)) : null;
    }

    private static void q(BiFunction biFunction0) {
        if(biFunction0 != null) {
            f0.t(new b(biFunction0));
        }
    }

    public static void r(Application.OnProvideAssistDataListener application$OnProvideAssistDataListener0) {
        if(Build.VERSION.SDK_INT >= 29 && f0.b == f0.a && application$OnProvideAssistDataListener0 instanceof Pair && c.a.equals(((Pair)application$OnProvideAssistDataListener0).first)) {
            Object object0 = ((Pair)application$OnProvideAssistDataListener0).second;
            if(object0 instanceof BiFunction) {
                f0.q(((BiFunction)object0));
            }
        }
    }

    public void s(AccessibleObject accessibleObject0) {
        if(accessibleObject0 != null) {
            accessibleObject0.setAccessible(true);
        }
    }

    private static void t(f0 f00) {
        if(f00 == null) {
            f00 = f0.a;
        }
        f0.b = f00;
    }

    public static boolean u(ClassLoader classLoader0) {
        if(Build.VERSION.SDK_INT >= 29) {
            try {
                f0 f00 = f0.n();
                f00.b(f00.d(f0.class.getName(), classLoader0), new Class[]{BiFunction.class}).invoke(null, new e(null));
                return true;
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return false;
            }
        }
        return true;
    }
}


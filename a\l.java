package a;

import android.content.Context;
import android.content.Intent;
import f.m;
import f.p0;
import f.p;
import f.s;
import f.u;
import java.util.ArrayList;

public abstract class l extends r {
    private final p B;
    private u C;
    private final boolean D;

    l(String s, String s1) {
        super(s);
        this.D = s != null;
        this.B = new p(s1);
        this.o();
    }

    @Override  // a.r
    protected void b(Intent intent0) {
        m m0 = new m(intent0);
        if(m0.i()) {
            int v = m0.h();
            if(v == 1) {
                this.C.put(m0.h(), this.g(m0));
            }
            else if(v == 2) {
                this.C.remove(m0.h());
            }
        }
        m0.k();
    }

    public int e(Context context0, Object object0) {
        int v = this.n();
        this.C.put(v, object0);
        this.r();
        this.s(context0, v, object0);
        return v;
    }

    public int f(Context context0, int v, Object object0) {
        int v1 = this.n();
        this.C.put(v1, object0);
        int v2 = this.C.d(v1);
        if(v2 < v) {
            while(v2 < v) {
                this.C.g(v2);
                ++v2;
            }
        }
        else if(v2 > v) {
            while(v2 > v) {
                this.C.h(v2);
                --v2;
            }
        }
        this.r();
        this.s(context0, v1, object0);
        return v1;
    }

    protected abstract Object g(s arg1);

    public Object h(int v) {
        return this.C.get(v);
    }

    public ArrayList i() {
        return this.C.f();
    }

    public void j(int v) {
        int v1 = this.C.d(v);
        if(v < 0) {
            return;
        }
        this.C.g(v1);
        this.r();
    }

    public void k(int v) {
        this.C.g(v);
        this.r();
    }

    public void l(int v) {
        int v1 = this.C.d(v);
        if(v < 0) {
            return;
        }
        this.C.h(v1);
        this.r();
    }

    public void m(int v) {
        this.C.h(v);
        this.r();
    }

    private int n() {
        int v;
        u u0 = this.C;
        do {
            v = p0.r();
        }
        while(u0.containsKey(v));
        return v;
    }

    public void o() {
        u u0 = new u();
        if(this.B.E()) {
            while(true) {
                u0.put(this.B.h(), this.g(this.B));
                if(!this.B.t()) {
                    break;
                }
            }
        }
        this.B.m();
        this.C = u0;
    }

    public void p(Context context0, int v) {
        this.q(context0, this.C.d(v));
    }

    public void q(Context context0, int v) {
        if(v < 0) {
            return;
        }
        int v1 = (int)(((Integer)this.C.e(v)));
        this.C.i(v);
        this.r();
        if(this.D) {
            Intent intent0 = new Intent();
            new m(intent0).j().o(2).o(v1).l();
            this.c(context0, intent0);
        }
    }

    private void r() {
        this.B.F();
        int v = this.C.size();
        for(int v1 = 0; v1 < v; ++v1) {
            int v2 = (int)(((Integer)this.C.e(v1)));
            this.B.w(v2);
            Object object0 = this.C.k(v1);
            this.x(this.B, object0);
            this.B.C();
        }
        this.B.n();
    }

    private void s(Context context0, int v, Object object0) {
        if(this.D) {
            Intent intent0 = new Intent();
            m m0 = new m(intent0).j();
            m0.o(1).o(v);
            this.x(m0, object0);
            m0.l();
            this.c(context0, intent0);
        }
    }

    public void t(Context context0, int v, Object object0) {
        this.C.put(v, object0);
        this.r();
        this.s(context0, v, object0);
    }

    public void u(Context context0, int v, Object object0) {
        this.C.j(v, object0);
        this.r();
        this.s(context0, ((int)(((Integer)this.C.e(v)))), object0);
    }

    public int v() {
        return this.C.size();
    }

    public Object w(int v) {
        return this.C.k(v);
    }

    protected abstract void x(s arg1, Object arg2);
}


package g;

import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Outline;
import android.graphics.Paint.Style;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import f.v;

public final class z extends Drawable {
    private final Drawable a;
    private final int b;
    private final int c;
    private Bitmap d;
    private static final Rect e;
    private static final Rect f;
    private static final Paint g;

    static {
        z.e = new Rect();
        z.f = new Rect();
        Paint paint0 = new Paint(1);
        z.g = paint0;
        paint0.setStyle(Paint.Style.FILL);
    }

    public z(Drawable drawable0, int v, int v1) {
        this.a = drawable0;
        this.b = v;
        this.c = v1;
    }

    @Override  // android.graphics.drawable.Drawable
    public void draw(Canvas canvas0) {
        Rect rect0 = this.getBounds();
        int v = rect0.width();
        int v1 = Math.min(v, rect0.height());
        int v2 = rect0.centerX();
        int v3 = rect0.centerY();
        int v4 = this.c;
        if(v4 != 0) {
            z.g.setColor(v4);
            canvas0.drawCircle(((float)v2), ((float)v3), ((float)(v1 / 2)), z.g);
        }
        if(this.a == null) {
            return;
        }
        int v5 = (int)(((float)v1) * (v4 == 0 ? 0.6f : 0.8f) / 2.0f);
        Rect rect1 = z.f;
        rect1.set(v2 - v5, v3 - v5, v2 + v5, v3 + v5);
        if(this.b != -1 && this.b != 0) {
            if(this.d == null || this.d.getWidth() * 2 < v) {
                h.a().e(this.a, this.b);
                int v6 = rect1.width();
                int v7 = rect1.height();
                this.d = a0.i(this.a, v6, v7);
                h.a().b(this.a);
            }
            int v8 = this.d.getWidth();
            int v9 = this.d.getHeight();
            z.e.set(0, 0, v8, v9);
            canvas0.drawBitmap(this.d, z.e, rect1, z.g);
            return;
        }
        this.a.setBounds(rect1);
        this.a.draw(canvas0);
    }

    @Override  // android.graphics.drawable.Drawable
    public int getOpacity() {
        return -2;
    }

    @Override  // android.graphics.drawable.Drawable
    public void getOutline(Outline outline0) {
        if(this.c != 0) {
            Rect rect0 = this.getBounds();
            outline0.setRoundRect(rect0, ((float)(Math.min(rect0.width(), rect0.height()) / 2)));
            return;
        }
        super.getOutline(outline0);
    }

    @Override  // android.graphics.drawable.Drawable
    public void setAlpha(int v) {
    }

    @Override  // android.graphics.drawable.Drawable
    public void setColorFilter(ColorFilter colorFilter0) {
        v.d(new UnsupportedOperationException("set color filter!"));
    }

    @Override  // android.graphics.drawable.Drawable
    public void setTintList(ColorStateList colorStateList0) {
        v.d(new UnsupportedOperationException("set tint list!"));
    }
}


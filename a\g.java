package a;

import android.content.ComponentName;
import android.content.Context;
import android.content.ServiceConnection;
import android.os.Binder;
import android.os.Handler;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.Pair;
import com.jozein.xedgepro.service.BinderService;
import f.h0;
import f.l;
import f.r;
import f.v;
import java.util.ArrayList;
import java.util.List;

public interface g extends IInterface {
    public static final class a implements ServiceConnection, Runnable {
        private final Handler A;
        private final List B;
        private final List C;
        private boolean D;
        private g E;
        private static volatile a F;
        private final Context z;

        static {
        }

        public a(Context context0) {
            this.A = h0.b();
            this.B = new ArrayList();
            this.C = new ArrayList();
            this.D = false;
            this.E = null;
            this.z = context0;
        }

        private boolean a() {
            boolean z;
            synchronized(this) {
                this.A.removeCallbacks(this);
                if(this.D) {
                    return true;
                }
                z = com.jozein.xedgepro.service.BinderService.a.g(this.z, this);
                this.D = z;
            }
            if(!z) {
                v.c("Failed to bind service!");
                Pair[] arr_pair = this.c();
                BinderService.y(this.z, arr_pair);
            }
            return z;
        }

        private Runnable[] b() {
            synchronized(this.B) {
                int v1 = this.B.size();
                Runnable[] arr_runnable = v1 <= 0 ? null : ((Runnable[])this.B.toArray(new Runnable[v1]));
                this.B.clear();
                return arr_runnable;
            }
        }

        private Pair[] c() {
            synchronized(this.C) {
                int v1 = this.C.size();
                Pair[] arr_pair = v1 <= 0 ? null : ((Pair[])this.C.toArray(new Pair[v1]));
                this.C.clear();
                return arr_pair;
            }
        }

        public boolean d(String s, Parcelable parcelable0) {
            g g0 = this.e();
            if(g0 != null) {
                g0.b(s, parcelable0);
                this.i();
                return true;
            }
            synchronized(this.C) {
                Pair pair0 = new Pair(s, parcelable0);
                this.C.add(pair0);
            }
            return this.a();
        }

        private g e() {
            synchronized(this) {
                this.A.removeCallbacks(this);
                g g0 = this.E;
                if(g0 != null) {
                    IBinder iBinder0 = g0.asBinder();
                    if(iBinder0 != null && iBinder0.isBinderAlive()) {
                        return this.E;
                    }
                }
                this.E = null;
                this.D = false;
                return null;
            }
        }

        public static a f(Context context0) {
            if(a.F == null) {
                Class class0 = a.class;
                synchronized(class0) {
                    if(a.F == null) {
                        a.F = new a(context0);
                    }
                    return a.F;
                }
            }
            return a.F;
        }

        public Parcelable g(String s) {
            g g0 = this.e();
            Parcelable parcelable0 = g0 == null ? null : g0.a(s);
            this.i();
            return parcelable0;
        }

        public void h(Runnable runnable0) {
            if(runnable0 == null) {
                return;
            }
            if(this.e() != null) {
                try {
                    runnable0.run();
                }
                finally {
                    this.i();
                }
                return;
            }
            synchronized(this.B) {
                this.B.add(runnable0);
            }
            this.a();
        }

        private void i() {
            this.A.postDelayed(this, 120000L);
        }

        @Override  // android.content.ServiceConnection
        public void onBindingDied(ComponentName componentName0) {
            synchronized(this) {
                this.E = null;
                this.D = false;
            }
        }

        @Override  // android.content.ServiceConnection
        public void onNullBinding(ComponentName componentName0) {
            synchronized(this) {
                this.E = null;
                this.D = false;
            }
        }

        @Override  // android.content.ServiceConnection
        public void onServiceConnected(ComponentName componentName0, IBinder iBinder0) {
            int v;
            try {
                g g0 = b.d(iBinder0);
                this.E = g0;
                if(g0 != null) {
                    Pair[] arr_pair = this.c();
                    v = 0;
                    if(arr_pair != null) {
                        for(int v1 = 0; v1 < arr_pair.length; ++v1) {
                            Pair pair0 = arr_pair[v1];
                            g0.b(((String)pair0.first), ((Parcelable)pair0.second));
                        }
                    }
                    Runnable[] arr_runnable = this.b();
                    if(arr_runnable != null) {
                        while(true) {
                            if(v >= arr_runnable.length) {
                                break;
                            }
                            Runnable runnable0 = arr_runnable[v];
                            try {
                                runnable0.run();
                            }
                            catch(Throwable throwable1) {
                                v.d(throwable1);
                            }
                            ++v;
                        }
                    }
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            this.i();
        }

        @Override  // android.content.ServiceConnection
        public void onServiceDisconnected(ComponentName componentName0) {
            synchronized(this) {
                this.E = null;
                this.D = false;
            }
        }

        @Override
        public void run() {
            synchronized(this) {
                try {
                    if(this.D) {
                        this.D = false;
                        this.E = null;
                        this.z.unbindService(this);
                    }
                }
                catch(Throwable throwable0) {
                    v.c(throwable0.toString());
                }
            }
        }
    }

    public static abstract class b extends Binder implements g {
        static final class a.g.b.a implements g {
            private final IBinder a;

            a.g.b.a(IBinder iBinder0) {
                this.a = iBinder0;
            }

            @Override  // a.g
            public Parcelable a(String s) {
                Parcel parcel0 = Parcel.obtain();
                Parcel parcel1 = Parcel.obtain();
                try {
                    parcel0.writeInterfaceToken(b.a);
                    parcel0.writeString(s);
                    if(this.a.transact(1, parcel0, parcel1, 0)) {
                        parcel1.readException();
                        return parcel1.readParcelable(a.g.b.a.class.getClassLoader());
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                    return null;
                }
                finally {
                    parcel1.recycle();
                    parcel0.recycle();
                }
                try {
                    v.c(("Failed to read " + s));
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
                return null;
            }

            @Override  // android.os.IInterface
            public IBinder asBinder() {
                return this.a;
            }

            @Override  // a.g
            public void b(String s, Parcelable parcelable0) {
                Parcel parcel0 = Parcel.obtain();
                try {
                    parcel0.writeInterfaceToken(b.a);
                    parcel0.writeString(s);
                    parcel0.writeParcelable(parcelable0, 0);
                    if(!this.a.transact(2, parcel0, null, 1)) {
                        v.c(("Failed to commit: " + s));
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
                finally {
                    parcel0.recycle();
                }
            }
        }

        private static final String a;
        private static int b;

        static {
            b.a = l.j + ".BinderInterface";
            b.b = -1;
        }

        protected b() {
            this.attachInterface(this, b.a);
        }

        @Override  // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        public static g d(IBinder iBinder0) {
            if(iBinder0 == null) {
                return null;
            }
            IInterface iInterface0 = iBinder0.queryLocalInterface(b.a);
            return iInterface0 instanceof g ? ((g)iInterface0) : new a.g.b.a(iBinder0);
        }

        protected int e() {
            if(b.b < 0) {
                try {
                    b.b = r.l(l.l);
                    return b.b;
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                    b.b = 0;
                }
            }
            return b.b;
        }

        @Override  // android.os.Binder
        protected boolean onTransact(int v, Parcel parcel0, Parcel parcel1, int v1) {
            Parcelable parcelable0;
            int v2 = Binder.getCallingUid();
            int v3 = this.e();
            if(v2 >= 10000 && v3 > 0 && v3 != v2) {
                v.c(("Unsupported caller uid: " + v2));
                return false;
            }
            switch(v) {
                case 1: {
                    parcel0.enforceInterface(b.a);
                    try {
                        parcelable0 = this.a(parcel0.readString());
                        parcel1.writeNoException();
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                        return false;
                    }
                    parcel1.writeParcelable(parcelable0, 0);
                    return true;
                }
                case 2: {
                    parcel0.enforceInterface(b.a);
                    try {
                        this.b(parcel0.readString(), parcel0.readParcelable(this.getClass().getClassLoader()));
                        if(parcel1 != null) {
                            parcel1.writeNoException();
                        }
                        return true;
                    }
                    catch(Throwable throwable1) {
                        v.d(throwable1);
                        return false;
                    }
                }
                case 0x5F4E5446: {
                    parcel1.writeString(b.a);
                    return true;
                }
                default: {
                    return super.onTransact(v, parcel0, parcel1, v1);
                }
            }
        }
    }

    Parcelable a(String arg1);

    void b(String arg1, Parcelable arg2);
}


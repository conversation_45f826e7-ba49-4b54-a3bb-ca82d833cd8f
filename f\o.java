package f;

import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;

public class o extends q implements Parcelable {
    class a implements Parcelable.Creator {
        a() {
            super();
        }

        public o a(Parcel parcel0) {
            return new o(parcel0);
        }

        public o[] b(int v) {
            return new o[v];
        }

        @Override  // android.os.Parcelable$Creator
        public Object createFromParcel(Parcel parcel0) {
            return this.a(parcel0);
        }

        @Override  // android.os.Parcelable$Creator
        public Object[] newArray(int v) {
            return this.b(v);
        }
    }

    public static final Parcelable.Creator CREATOR;
    public final String E;
    public final long F;
    public final byte[] G;

    static {
        o.CREATOR = new a();
    }

    o(Parcel parcel0) {
        this.E = parcel0.readString();
        this.F = parcel0.readLong();
        this.G = parcel0.createByteArray();
    }

    public o(String s, long v, byte[] arr_b) {
        this.E = s;
        this.F = v;
        this.G = arr_b;
    }

    @Override  // f.q
    public boolean d() {
        return this.G != null;
    }

    @Override  // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    @Override  // f.q
    public long f() {
        return this.F;
    }

    @Override  // f.q
    public byte[] j() {
        return this.G == null ? q.z : this.G;
    }

    @Override  // android.os.Parcelable
    public void writeToParcel(Parcel parcel0, int v) {
        parcel0.writeString(this.E);
        parcel0.writeLong(this.F);
        parcel0.writeByteArray(this.G);
    }
}


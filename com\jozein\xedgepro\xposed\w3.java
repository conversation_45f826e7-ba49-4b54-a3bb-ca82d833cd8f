package com.jozein.xedgepro.xposed;

import android.os.IBinder.DeathRecipient;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import java.io.FileDescriptor;

class w3 implements IBinder {
    @Override  // android.os.IBinder
    public void dump(FileDescriptor fileDescriptor0, String[] arr_s) {
    }

    @Override  // android.os.IBinder
    public void dumpAsync(FileDescriptor fileDescriptor0, String[] arr_s) {
    }

    @Override  // android.os.IBinder
    public String getInterfaceDescriptor() {
        return "";
    }

    @Override  // android.os.IBinder
    public boolean isBinderAlive() {
        return true;
    }

    @Override  // android.os.IBinder
    public void linkToDeath(IBinder.DeathRecipient iBinder$DeathRecipient0, int v) {
    }

    @Override  // android.os.IBinder
    public boolean pingBinder() {
        return true;
    }

    @Override  // android.os.IBinder
    public IInterface queryLocalInterface(String s) {
        return null;
    }

    @Override  // android.os.IBinder
    public boolean transact(int v, Parcel parcel0, Parcel parcel1, int v1) {
        return true;
    }

    @Override  // android.os.IBinder
    public boolean unlinkToDeath(IBinder.DeathRecipient iBinder$DeathRecipient0, int v) {
        return true;
    }
}


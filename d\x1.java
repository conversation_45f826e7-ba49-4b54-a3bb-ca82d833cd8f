package d;

import a.b.q1;
import a.b;
import a.p;
import a.z;
import java.util.ArrayList;

public class x1 extends s0 {
    @Override  // d.s0
    protected int K1() {
        return 6;
    }

    @Override  // d.s0
    protected void M1(int v, b b0) {
        super.M1(v, b0);
        this.T1();
    }

    @Override  // d.s0
    protected void O1(int v, boolean z) {
        super.O1(v, z);
        this.T1();
    }

    @Override  // d.s0
    protected void P1(int v) {
        super.P1(v);
        this.T1();
    }

    @Override  // d.s0
    protected void Q1(int v, b b0) {
        super.Q1(v, b0);
        this.T1();
    }

    @Override  // d.s0
    protected void R1() {
        this.c0((this.h().getInt("side", 0) == 0 ? 0x7F060147 : 0x7F060185));  // string:left_side_bar "Left side bar"
    }

    private void T1() {
        z z0 = this.g().h();
        if(z0 != null) {
            ArrayList arrayList0 = this.J1();
            z0.n0(this.getActivity(), this.h().getInt("side"), new q1(arrayList0));
        }
    }

    public x1 U1(p p0, int v) {
        this.h().putInt("side", v);
        this.S1(p0.n(v).I, true);
        return this;
    }

    @Override  // d.s0
    protected void y() {
        this.L();
    }
}


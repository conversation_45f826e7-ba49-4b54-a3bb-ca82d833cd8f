package f;

import a.b;
import android.content.Intent;

public class k0 implements s {
    private int A;
    private final Intent z;

    public k0(Intent intent0) {
        this.A = 0;
        this.z = intent0;
    }

    @Override  // f.s
    public String a() {
        int v = this.A;
        this.A = v + 1;
        return this.z.getStringExtra("__" + v);
    }

    @Override  // f.s
    public s b(String s) {
        return this.p(s);
    }

    @Override  // f.s
    public s c(String s) {
        return this.o(s);
    }

    @Override  // f.s
    public s d(int v) {
        return this.m(v);
    }

    @Override  // f.s
    public String e() {
        return this.a();
    }

    @Override  // f.s
    public s f(b b0) {
        return this.n(b0);
    }

    @Override  // f.s
    public b g() {
        return b.s(this);
    }

    @Override  // f.s
    public int h() {
        int v = this.A;
        this.A = v + 1;
        return this.z.getIntExtra("__" + v, 0);
    }

    public boolean i() {
        this.A = 0;
        return true;
    }

    public k0 j() {
        return this;
    }

    public void k() {
    }

    public void l() {
    }

    public k0 m(int v) {
        int v1 = this.A;
        this.A = v1 + 1;
        this.z.putExtra("__" + v1, v);
        return this;
    }

    public k0 n(b b0) {
        b0.y(this);
        return this;
    }

    public k0 o(String s) {
        int v = this.A;
        this.A = v + 1;
        this.z.putExtra("__" + v, s);
        return this;
    }

    public k0 p(String s) {
        return this.o(s);
    }
}


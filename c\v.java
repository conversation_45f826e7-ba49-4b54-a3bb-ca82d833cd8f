package c;

import a.b.p2;
import a.v.a;
import android.app.Activity;
import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.InputFilter.LengthFilter;
import android.text.InputFilter;
import android.text.Spanned;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.Spinner;
import android.widget.TextView;
import e.j0.b;
import e.y;
import f.i;
import g.x;

public class v extends b {
    private String[] C;
    private Spinner D;
    private Spinner E;
    private EditText F;
    private static final String[] G;

    static {
        v.G = new String[]{" = ", " += ", " -= "};
    }

    public v A(String s, String s1, String s2) {
        Bundle bundle0 = this.e();
        bundle0.putString("variable", s);
        for(int v = 0; true; ++v) {
            String[] arr_s = v.G;
            if(v >= arr_s.length) {
                break;
            }
            if(arr_s[v].contains(s1)) {
                bundle0.putInt("operator", v);
                break;
            }
        }
        bundle0.putCharSequence("expression", s2);
        return this;
    }

    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        Activity activity0 = this.getActivity();
        Bundle bundle1 = this.e();
        LinearLayout linearLayout0 = new LinearLayout(activity0);
        linearLayout0.setOrientation(1);
        x x0 = this.g();
        int v = x0.f;
        linearLayout0.setPadding(v, v, v, v);
        a v$a0 = a.g();
        int v1 = v$a0.k();
        this.C = new String[v$a0.k()];
        String s = bundle1.getString("variable");
        int v3 = -1;
        for(int v2 = 0; v2 < v1; ++v2) {
            String[] arr_s = this.C;
            arr_s[v2] = v$a0.f(v2);
            if(v3 == -1 && s != null && s.equals(this.C[v2])) {
                v3 = v2;
            }
        }
        this.D = new Spinner(activity0);
        ArrayAdapter arrayAdapter0 = new ArrayAdapter(activity0, 0x1090008, this.C);
        arrayAdapter0.setDropDownViewResource(0x1090009);
        this.D.setAdapter(arrayAdapter0);
        if(v3 != -1) {
            this.D.setSelection(v3);
        }
        linearLayout0.addView(this.D);
        this.E = new Spinner(activity0);
        ArrayAdapter arrayAdapter1 = new ArrayAdapter(activity0, 0x1090008, v.G);
        arrayAdapter1.setDropDownViewResource(0x1090009);
        this.E.setAdapter(arrayAdapter1);
        int v4 = bundle1.getInt("operator", -1);
        if(v4 > 0) {
            this.E.setSelection(v4);
        }
        linearLayout0.addView(this.E, -2, -2);
        EditText editText0 = v.y(activity0, bundle1.getCharSequence("expression", "1"));
        this.F = editText0;
        this.r(editText0);
        linearLayout0.addView(this.F, -1, -2);
        TextView textView0 = new TextView(activity0);
        textView0.setText(0x7F060214);  // string:variable_expression_hint "Variable:\n 32 bit signed integer.\nSupported operators:\n 
                                        // +, -, *, /, %, <<, >>, &, ^, |, ~, ++, --.\n Same as C/C++/Java…\nExample:\n (var 
                                        // + 1) % 2"
        textView0.setTextSize(0, ((float)x0.e));
        textView0.setPadding(v, v, v, v);
        linearLayout0.addView(textView0);
        y.L(x0, textView0, this.F);
        ScrollView scrollView0 = new ScrollView(activity0);
        scrollView0.setFillViewport(true);
        scrollView0.addView(linearLayout0);
        return new AlertDialog.Builder(activity0).setView(scrollView0).setNegativeButton(0x1040000, b.B).setPositiveButton(0x104000A, (DialogInterface dialogInterface0, int v) -> {
            String s = this.F.getText().toString();
            String[] arr_s = this.C;
            String s1 = arr_s[this.D.getSelectedItemPosition()];
            try {
                i.d(s);
                this.q("result", new p2(s1, v.G[this.E.getSelectedItemPosition()].trim(), s));
            }
            catch(Throwable throwable0) {
                this.t(throwable0.getMessage());
                f.v.d(throwable0);
            }
        }).create();
    }

    @Override  // e.j0$b
    public void onSaveInstanceState(Bundle bundle0) {
        Bundle bundle1 = this.e();
        String[] arr_s = this.C;
        bundle1.putString("variable", arr_s[this.D.getSelectedItemPosition()]);
        bundle1.putInt("operator", this.E.getSelectedItemPosition());
        bundle1.putCharSequence("expression", this.F.getText());
        super.onSaveInstanceState(bundle0);
    }

    // 检测为 Lambda 实现
    private static CharSequence w(CharSequence charSequence0, int v, int v1, Spanned spanned0, int v2, int v3) [...]

    // 检测为 Lambda 实现
    private void x(DialogInterface dialogInterface0, int v) [...]

    public static EditText y(Context context0, CharSequence charSequence0) {
        EditText editText0 = new EditText(context0);
        editText0.setInputType(1);
        editText0.setText(charSequence0);
        if(charSequence0 != null) {
            editText0.setSelection(charSequence0.length());
        }
        editText0.setFilters(new InputFilter[]{(CharSequence charSequence0, int v, int v1, Spanned spanned0, int v2, int v3) -> {
            if(v == v1) {
                return null;
            }
            while(v < v1) {
                int v4 = charSequence0.charAt(v);
                if((v4 < 65 || v4 > 90) && (v4 < 97 || v4 > 0x7A) && (v4 < 0x30 || v4 > 57) && " _$+-*/%()<>&^|~".indexOf(v4) < 0) {
                    return "";
                }
                ++v;
            }
            return null;
        }, new InputFilter.LengthFilter(0x20)});
        return editText0;
    }

    public v z(String s) {
        this.e().putString("variable", s);
        return this;
    }
}


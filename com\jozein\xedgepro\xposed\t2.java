package com.jozein.xedgepro.xposed;

import a.p;
import android.content.Context;
import android.graphics.Point;
import android.graphics.drawable.Drawable;
import android.text.TextUtils.TruncateAt;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.AnimationSet;
import android.view.animation.TranslateAnimation;
import android.widget.AbsListView.LayoutParams;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.AdapterView.OnItemLongClickListener;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.FrameLayout.LayoutParams;
import android.widget.FrameLayout;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.TextView;
import f.v;
import g.a0;
import g.x;

class t2 {
    class c extends BaseAdapter implements AdapterView.OnItemClickListener, AdapterView.OnItemLongClickListener {
        private final Drawable[] A;
        private final CharSequence[] B;
        private boolean C;
        final t2 D;
        private final e z;

        c(e t2$e0, boolean z) {
            int v = 0;
            this.C = false;
            this.z = t2$e0;
            try {
                v = t2$e0.getCount();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            this.A = new Drawable[v];
            this.B = z ? new CharSequence[v] : null;
        }

        void b() {
            if(this.C) {
                try {
                    this.C = false;
                    t2.this.j();
                    this.z.f();
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        @Override  // android.widget.Adapter
        public int getCount() {
            return this.A.length;
        }

        @Override  // android.widget.Adapter
        public Object getItem(int v) {
            return null;
        }

        @Override  // android.widget.Adapter
        public long getItemId(int v) {
            return (long)v;
        }

        @Override  // android.widget.Adapter
        public View getView(int v, View view0, ViewGroup viewGroup0) {
            View view1;
            boolean z = true;
            boolean z1 = this.B != null;
            Drawable[] arr_drawable = this.A;
            if(arr_drawable[v] == null) {
                try {
                    arr_drawable[v] = this.z.c(v);
                    if(z1) {
                        this.B[v] = this.z.d(v);
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
            if(view0 == null) {
                t2 t20 = t2.this;
                Context context0 = viewGroup0.getContext();
                if(this.B == null) {
                    z = false;
                }
                view1 = new d(t20, context0, z);
                ((FrameLayout)view1).setLayoutParams((z1 ? t2.this.g : t2.this.f));
            }
            else {
                view1 = (d)view0;
            }
            view1.z.setImageDrawable(this.A[v]);
            if(z1) {
                view1.A.setText(this.B[v]);
            }
            return view1;
        }

        @Override  // android.widget.AdapterView$OnItemClickListener
        public void onItemClick(AdapterView adapterView0, View view0, int v, long v1) {
            try {
                this.z.e(v, view0);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            t2.this.j();
        }

        @Override  // android.widget.AdapterView$OnItemLongClickListener
        public boolean onItemLongClick(AdapterView adapterView0, View view0, int v, long v1) {
            try {
                if(this.z.b(v)) {
                    this.C = true;
                    return true;
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            t2.this.j();
            return false;
        }
    }

    class d extends FrameLayout {
        final TextView A;
        final t2 B;
        final ImageView z;

        d(Context context0, boolean z) {
            super(context0);
            if(z) {
                ImageView imageView0 = new ImageView(context0);
                this.z = imageView0;
                this.addView(imageView0, t20.i);
                TextView textView0 = new TextView(context0);
                this.A = textView0;
                textView0.setTextSize(0, ((float)t20.e));
                textView0.setTextColor(t20.l);
                textView0.setPadding(t20.e / 4, 0, t20.e / 4, 0);
                textView0.setSingleLine(true);
                textView0.setEllipsize(TextUtils.TruncateAt.END);
                this.addView(textView0, t20.j);
                return;
            }
            ImageView imageView1 = new ImageView(context0);
            this.z = imageView1;
            this.addView(imageView1, t20.h);
            this.A = null;
        }
    }

    interface e {
        boolean a();

        boolean b(int arg1);

        Drawable c(int arg1);

        CharSequence d(int arg1);

        void e(int arg1, View arg2);

        void f();

        int getCount();
    }

    private final p a;
    private final Context b;
    private int c;
    private int d;
    private int e;
    private AbsListView.LayoutParams f;
    private AbsListView.LayoutParams g;
    private FrameLayout.LayoutParams h;
    private FrameLayout.LayoutParams i;
    private FrameLayout.LayoutParams j;
    private int k;
    private int l;

    t2(w1 w10) {
        this.k = -1;
        this.l = -1;
        this.a = w10.b2();
        this.b = w10.T1();
    }

    void j() {
        t3.n().t(18);
        this.k = -1;
    }

    private void k() {
        x x0 = x.b(this.b);
        float f = ((float)this.a.v(28, 100)) / 100.0f;
        int v = (int)(((float)x0.a) * f + 0.5f);
        this.c = v;
        this.e = (int)(((float)x0.d) * f + 0.5f);
        this.d = v;
        int v1 = (int)(((float)x0.b) * f + 0.5f);
        this.f = new AbsListView.LayoutParams(-1, this.c);
        this.h = new FrameLayout.LayoutParams(v1, v1, 17);
        int v2 = (int)(((float)(this.c - v1 - this.e)) * 0.3f);
        this.g = new AbsListView.LayoutParams(-1, this.d);
        FrameLayout.LayoutParams frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(v1, v1, 49);
        this.i = frameLayout$LayoutParams0;
        frameLayout$LayoutParams0.topMargin = v2;
        FrameLayout.LayoutParams frameLayout$LayoutParams1 = new FrameLayout.LayoutParams(-2, -2, 81);
        this.j = frameLayout$LayoutParams1;
        frameLayout$LayoutParams1.bottomMargin = v2;
    }

    boolean l(int v) {
        return v == this.k && t3.n().p(18);
    }

    // 检测为 Lambda 实现
    private void m(GridView gridView0) [...]

    void n(e t2$e0, int v, int v1, int v2) {
        class a extends FrameLayout {
            final t2 A;
            final c z;

            a(Context context0, c t2$c0) {
                this.z = t2$c0;
                super(context0);
            }

            @Override  // android.view.ViewGroup
            public boolean dispatchTouchEvent(MotionEvent motionEvent0) {
                if(motionEvent0.getActionMasked() == 1) {
                    this.z.b();
                }
                return super.dispatchTouchEvent(motionEvent0);
            }

            @Override  // android.view.View
            public boolean onGenericMotionEvent(MotionEvent motionEvent0) {
                View view0 = this.getChildAt(0);
                return view0 == null ? super.onGenericMotionEvent(motionEvent0) : view0.onGenericMotionEvent(motionEvent0);
            }
        }


        class b extends GridView {
            final t2 A;
            final o2 z;

            b(Context context0, o2 o20) {
                this.z = o20;
                super(context0);
            }

            @Override  // android.view.View
            public boolean hasWindowFocus() {
                return true;
            }

            @Override  // android.widget.AbsListView
            public boolean onTouchEvent(MotionEvent motionEvent0) {
                if(this.z.c()) {
                    return true;
                }
                try {
                    return super.onTouchEvent(motionEvent0);
                }
                catch(Throwable throwable0) {
                    this.z.b(throwable0);
                    t2.this.k = -1;
                    return true;
                }
            }
        }

        long v15;
        FrameLayout.LayoutParams frameLayout$LayoutParams0;
        TranslateAnimation translateAnimation1;
        FrameLayout.LayoutParams frameLayout$LayoutParams2;
        float f;
        t3 t30 = t3.n();
        t30.t(18);
        this.k();
        int v3 = this.a.v((v == 2 ? 33 : 2), 0xFF003037);
        this.l = a0.r(v3) ? -1 : 0xFF000000;
        boolean z = t2$e0.a();
        c t2$c0 = new c(this, t2$e0, z);
        a t2$a0 = new a(this, this.b, t2$c0);
        o2 o20 = new o2(this.b, 18);
        b t2$b0 = new b(this, this.b, o20);
        o20.a(t2$b0);
        t2$b0.setSelector(a0.l());
        Point point0 = new Point();
        Point point1 = a0.n(this.b, point0);
        int v4 = v1 <= 0 ? point1.x / this.c : v1;
        t2$b0.setNumColumns(v4);
        this.k = v2;
        t2$b0.setAdapter(t2$c0);
        t2$b0.setOnItemClickListener(t2$c0);
        t2$b0.setOnItemLongClickListener(t2$c0);
        int v5 = 3;
        if(v == 1) {
            f = (float)this.e;
        }
        else {
            f = v == 3 || v == 4 ? ((float)this.e) * 0.5f : 0.0f;
        }
        a0.w(t2$a0, v3, f);
        t2$a0.addView(t2$b0, new FrameLayout.LayoutParams(-1, -1));
        Animation animation0 = null;
        long v6 = 200L;
        if(v == 1) {
            frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(this.c * v4, -2, 81);
            int v16 = point1.x;
            int v17 = point1.y;
            if(v16 >= v17) {
                v16 = v17;
            }
            int v18 = (t2$c0.A.length + v4 - 1) / v4;
            if(v18 == 0) {
                v18 = 1;
            }
            frameLayout$LayoutParams0.bottomMargin = (v16 - (z ? this.d : this.c) * v18) / 2;
        }
        else {
            switch(v) {
                case 2: {
                    int v7 = (t2$c0.A.length + v4 - 1) / v4;
                    if(point1.x >= point1.y) {
                        v5 = 2;
                    }
                    if(v7 > v5) {
                        v7 = v5;
                    }
                    else if(v7 == 0) {
                        v7 = 1;
                    }
                    int v8 = (z ? this.d : this.c) * v7 + 2;
                    FrameLayout.LayoutParams frameLayout$LayoutParams1 = new FrameLayout.LayoutParams(-1, v8, 80);
                    TranslateAnimation translateAnimation0 = new TranslateAnimation(0.0f, 0.0f, ((float)v8) / 2.0f, 0.0f);
                    v6 = ((long)v7) * 66L;
                    t2$b0.post(() -> {
                        int v = t2$b0.getWidth() / this.c;
                        if(v > 0) {
                            t2$b0.setNumColumns(v);
                        }
                    });
                    frameLayout$LayoutParams0 = frameLayout$LayoutParams1;
                    animation0 = translateAnimation0;
                    goto label_91;
                }
                case 3: {
                    int v9 = (z ? this.d : this.c) * t2$c0.A.length;
                    int v10 = a0.p();
                    if(v9 != 0) {
                        int v11 = point1.y;
                        if(v9 > v11 - v10) {
                            v9 = v11 - v10;
                        }
                    }
                    else if(z) {
                        v9 = this.d;
                    }
                    else {
                        v9 = this.c;
                    }
                    frameLayout$LayoutParams2 = new FrameLayout.LayoutParams(this.c, v9, 83);
                    frameLayout$LayoutParams2.leftMargin = v10;
                    translateAnimation1 = new TranslateAnimation(((float)(-v10 - this.c)) / 2.0f, 0.0f, 0.0f, 0.0f);
                    break;
                }
                case 4: {
                    int v12 = (z ? this.d : this.c) * t2$c0.A.length;
                    int v13 = a0.p();
                    if(v12 != 0) {
                        int v14 = point1.y;
                        if(v12 > v14 - v13) {
                            v12 = v14 - v13;
                        }
                    }
                    else if(z) {
                        v12 = this.d;
                    }
                    else {
                        v12 = this.c;
                    }
                    frameLayout$LayoutParams2 = new FrameLayout.LayoutParams(this.c, v12, 85);
                    frameLayout$LayoutParams2.rightMargin = v13;
                    translateAnimation1 = new TranslateAnimation(((float)(v13 + this.c)) / 2.0f, 0.0f, 0.0f, 0.0f);
                    break;
                }
                default: {
                    frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(-2, -2, 17);
                    goto label_91;
                }
            }
            animation0 = translateAnimation1;
            frameLayout$LayoutParams0 = frameLayout$LayoutParams2;
            v15 = 82L;
            goto label_92;
        }
    label_91:
        v15 = v6;
    label_92:
        t30.k(t2$a0, 18, frameLayout$LayoutParams0);
        AnimationSet animationSet0 = new AnimationSet(true);
        animationSet0.addAnimation(new AlphaAnimation(0.0f, 1.0f));
        if(animation0 != null) {
            animationSet0.addAnimation(animation0);
        }
        a0.z(t2$a0, animationSet0, v15);
    }

    void o(e t2$e0, int v) {
        this.n(t2$e0, 2, 0, v);
    }
}


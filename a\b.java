package a;

import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProviderInfo;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent.ShortcutIconResource;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Bitmap.CompressFormat;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build.VERSION;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import android.os.Process;
import android.util.Pair;
import android.view.KeyEvent;
import f.d0;
import f.e0;
import f.l;
import f.q0;
import f.q;
import f.s;
import f.v;
import f.z;
import g.n;
import g.t;
import g.w;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.StringTokenizer;

public abstract class b implements Parcelable, l {
    public static class a0 extends c0 {
        public final boolean J;
        public final String K;
        private String[] L;

        private a0() {
            this(false, false, null);
        }

        private a0(int v, boolean z, boolean z1, String s) {
            super(v, z);
            this.L = null;
            this.J = z1;
            this.K = s;
        }

        a0(a b$a0) {
        }

        private a0(s s0) {
            boolean z = true;
            boolean z1 = s0.h() != 0;
            if(s0.h() == 0) {
                z = false;
            }
            this(z1, z, s0.e());
        }

        a0(s s0, a b$a0) {
            this(s0);
        }

        public a0(boolean z, boolean z1, String s) {
            this(100, z, z1, s);
        }

        public String[] A() {
            if(this.K != null && this.L == null) {
                StringTokenizer stringTokenizer0 = new StringTokenizer(this.K, "\r\n");
                int v = stringTokenizer0.countTokens();
                this.L = new String[v];
                for(int v1 = 0; v1 < v; ++v1) {
                    String[] arr_s = this.L;
                    arr_s[v1] = stringTokenizer0.nextToken();
                }
            }
            return this.L;
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            String[] arr_s = this.A();
            stringBuilder0.append(s);
            stringBuilder0.append(context0.getText(this.A));
            if(arr_s != null && arr_s.length > 0) {
                stringBuilder0.append((this.J ? " #: " : " $: "));
                stringBuilder0.append(arr_s[0]);
                if(arr_s.length > 1) {
                    for(int v = 1; v < arr_s.length; ++v) {
                        stringBuilder0.append('\n');
                        stringBuilder0.append(s);
                        stringBuilder0.append("    ");
                        stringBuilder0.append(arr_s[v]);
                    }
                }
            }
            return stringBuilder0;
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.K == null ? super.n(context0) : super.n(context0) + ": " + this.K;
        }

        @Override  // a.b$c0
        public void y(s s0) {
            super.y(s0);
            s0.d(((int)this.J)).b(this.K);
        }

        public String z() {
            return this.K;
        }
    }

    public static final class a1 extends b {
        public final int I;
        public final int J;

        public a1(int v, int v1) {
            super(52, 0x7F06002C, 0x7F040053);  // string:action_inject_key_event "Inject key event"
            this.I = v;
            this.J = v1;
        }

        private a1(s s0) {
            this(s0.h(), s0.h());
        }

        a1(s s0, a b$a0) {
            this(s0);
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            int v = this.I;
            if(v != 0) {
                String s = KeyEvent.keyCodeToString(v).substring(8);
                int v1 = this.J;
                switch(v1) {
                    case 0: {
                        return context0.getString(0x7F060134, new Object[]{s});  // string:key_click_f "Click %1$s"
                    }
                    case 1: {
                        return context0.getString(0x7F060136, new Object[]{s});  // string:key_double_click_f "Double click %1$s"
                    label_8:
                        if(v1 == 2) {
                            return context0.getString(0x7F06013C, new Object[]{s});  // string:key_long_press_f "Long press %1$s"
                        }
                        break;
                    }
                    default: {
                        goto label_8;
                    }
                }
            }
            return super.n(context0);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I).d(this.J);
        }

        z0 z() {
            return new z0(this.I, this.J, 0);
        }
    }

    public static final class a2 extends b {
        private a2() {
            super(66, 0x7F060045, 0x7F040089);  // string:action_previous_app "Previous app"
        }

        a2(a b$a0) {
        }
    }

    public static final class a3 extends b implements u0 {
        public final int I;

        public a3(int v) {
            super(85, 0x7F060058, 0x7F0400AE);  // string:action_soft_keyboard "Soft keyboard"
            this.I = v;
        }

        @Override  // a.b$u0
        public CharSequence a(Context context0, int v) {
            switch(v) {
                case 1: {
                    return context0.getText(0x7F0601B5);  // string:show_soft_keyboard "Show soft keyboard"
                }
                case 2: {
                    return context0.getText(0x7F060125);  // string:hide_soft_keyboard "Hide soft keyboard"
                }
                case 3: {
                    return context0.getText(0x7F06012C);  // string:input_method_menu "Input method menu"
                }
                case 4: {
                    return context0.getText(0x7F060145);  // string:last_input_method "Last input method"
                }
                default: {
                    return context0.getText(0x7F060208);  // string:toggle "Toggle"
                }
            }
        }

        @Override  // a.b$u0
        public int b() {
            return 5;
        }

        @Override  // a.b$u0
        public b c(int v) {
            return new a3(v);
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            switch(this.I) {
                case 1: {
                    return context0.getResources().getDrawable(0x7F0400AB);  // drawable:ic_show_soft_keyboard
                }
                case 2: {
                    return context0.getResources().getDrawable(0x7F04004F);  // drawable:ic_hide_soft_keyboard
                }
                case 3: {
                    return context0.getResources().getDrawable(0x7F040055);  // drawable:ic_input_method_menu
                }
                case 4: {
                    return context0.getResources().getDrawable(0x7F04005C);  // drawable:ic_last_input_method
                }
                default: {
                    return super.m(context0);
                }
            }
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            switch(this.I) {
                case 1: {
                    return context0.getText(0x7F0601B5);  // string:show_soft_keyboard "Show soft keyboard"
                }
                case 2: {
                    return context0.getText(0x7F060125);  // string:hide_soft_keyboard "Hide soft keyboard"
                }
                case 3: {
                    return context0.getText(0x7F06012C);  // string:input_method_menu "Input method menu"
                }
                case 4: {
                    return context0.getText(0x7F060145);  // string:last_input_method "Last input method"
                }
                default: {
                    return super.n(context0);
                }
            }
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }
    }

    class a implements Parcelable.Creator {
        a() {
            super();
        }

        public b a(Parcel parcel0) {
            return b.s(new d0(parcel0));
        }

        public b[] b(int v) {
            return new b[v];
        }

        @Override  // android.os.Parcelable$Creator
        public Object createFromParcel(Parcel parcel0) {
            return this.a(parcel0);
        }

        @Override  // android.os.Parcelable$Creator
        public Object[] newArray(int v) {
            return this.b(v);
        }
    }

    public static final class b0 extends c0 {
        public final String J;
        private String[] K;

        private b0(s s0) {
            this(s0.h() != 0, Uri.decode(s0.a()));
        }

        b0(s s0, a b$a0) {
            this(s0);
        }

        private b0(boolean z, String s) {
            super(61, z);
            this.K = null;
            this.J = s;
        }

        public a0 A() {
            return new a0(this.I, false, this.J);
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            String[] arr_s = this.z();
            stringBuilder0.append(s);
            stringBuilder0.append(context0.getText(this.A));
            if(arr_s != null && arr_s.length > 0) {
                stringBuilder0.append(": ");
                stringBuilder0.append(arr_s[0]);
                if(arr_s.length > 1) {
                    for(int v = 1; v < arr_s.length; ++v) {
                        stringBuilder0.append('\n');
                        stringBuilder0.append(s);
                        stringBuilder0.append("    ");
                        stringBuilder0.append(arr_s[v]);
                    }
                }
            }
            return stringBuilder0;
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.J == null ? super.n(context0) : super.n(context0) + ": " + this.J;
        }

        @Override  // a.b$c0
        public void y(s s0) {
            super.y(s0);
            s0.c(Uri.encode(this.J));
        }

        String[] z() {
            if(this.J != null && this.K == null) {
                StringTokenizer stringTokenizer0 = new StringTokenizer(this.J, "\r\n");
                int v = stringTokenizer0.countTokens();
                this.K = new String[v];
                for(int v1 = 0; v1 < v; ++v1) {
                    String[] arr_s = this.K;
                    arr_s[v1] = stringTokenizer0.nextToken().trim();
                }
            }
            return this.K;
        }
    }

    public static final class b1 extends b {
        public final String I;

        private b1() {
            this(null);
        }

        b1(a b$a0) {
        }

        private b1(s s0) {
            this(s0.e());
        }

        b1(s s0, a b$a0) {
            this(s0);
        }

        public b1(String s) {
            super(108, 0x7F06002D, 0x7F040054);  // string:action_inject_text "Input text"
            this.I = s;
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.I != null ? super.n(context0) + ": " + this.I : super.n(context0);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.b(this.I);
        }
    }

    public static final class b2 extends b {
        public b2() {
            super(59, 0x7F060046, 0x7F04008D);  // string:action_read_clipboard "Read clipboard text"
        }
    }

    public static final class b3 extends b {
        private b3() {
            super(45, 0x7F060059, 0x7F0400AF);  // string:action_soft_reboot "Soft reboot"
        }

        b3(a b$a0) {
        }
    }

    public static final class a.b.b extends b implements e0 {
        private a.b.b() {
            super(102, 0x7F060003, 0x7F040000);  // string:action_abort "Abort"
        }

        a.b.b(a b$a0) {
        }
    }

    public static abstract class c0 extends b implements e0 {
        public final boolean I;

        protected c0(int v, boolean z) {
            super(v, 0x7F060016, 0x7F040020);  // string:action_command "Shell command"
            this.I = z;
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(((int)this.I));
        }
    }

    static final class c1 extends b {
        public final String I;

        private c1(s s0) {
            this(Uri.decode(s0.a()));
        }

        c1(s s0, a b$a0) {
            this(s0);
        }

        private c1(String s) {
            super(56, 0x7F06002D, 0x7F040054);  // string:action_inject_text "Input text"
            this.I = s;
        }

        private b1 A() {
            return new b1(this.I);
        }
    }

    public static final class c2 extends b {
        private c2() {
            super(44, 0x7F060047, 0x7F04008E);  // string:action_reboot "Reboot"
        }

        c2(a b$a0) {
        }
    }

    public static final class c3 extends b {
        public final String I;

        private c3() {
            this(null);
        }

        c3(a b$a0) {
        }

        private c3(s s0) {
            this(s0.e());
        }

        c3(s s0, a b$a0) {
            this(s0);
        }

        public c3(String s) {
            super(110, 0x7F06005A, 0x7F0400B0);  // string:action_speech "Speech"
            this.I = s;
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            return this.I == null || this.I.length() != 0 ? super.m(context0) : context0.getResources().getDrawable(0x7F0400B4);  // drawable:ic_stop_speech
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            String s = this.I;
            if(s == null) {
                return super.n(context0);
            }
            return s.length() == 0 ? context0.getText(0x7F0601E9) : super.n(context0) + ": " + this.I;  // string:stop_speech "Stop speech"
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.b(this.I);
        }
    }

    static final class c extends b {
        private c() {
            super(0x4F, 0x7F060004, 0x7F040003);  // string:action_action_collection "Action collection"
        }

        c(a b$a0) {
        }
    }

    public static final class a.b.d0 extends b implements e0 {
        public final h I;
        public final b J;
        public final b K;
        private static final o L;

        static {
            a.b.d0.L = new o();
        }

        private a.b.d0() {
            this(null, null, null);
        }

        a.b.d0(a b$a0) {
        }

        public a.b.d0(h h0, b b0, b b1) {
            super(62, 0x7F060017, 0x7F040021);  // string:action_conditional "Conditional"
            if(h0 == null) {
                h0 = a.b.d0.L;
            }
            this.I = h0;
            if(b0 == null) {
                b0 = b.r();
            }
            this.J = b0;
            if(b1 == null) {
                b1 = b.r();
            }
            this.K = b1;
        }

        private a.b.d0(s s0) {
            this(h.d(s0), s0.g(), s0.g());
        }

        a.b.d0(s s0, a b$a0) {
            this(s0);
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            stringBuilder0.append(s);
            stringBuilder0.append("if ");
            stringBuilder0.append(this.I.b(context0));
            stringBuilder0.append('\n');
            stringBuilder0.append(s);
            stringBuilder0.append("{\n");
            this.J.l(stringBuilder0, context0, s + "    ").append('\n');
            stringBuilder0.append(s);
            stringBuilder0.append('}');
            if(this.K.z != 1) {
                stringBuilder0.append('\n');
                stringBuilder0.append(s);
                stringBuilder0.append("else\n");
                stringBuilder0.append(s);
                stringBuilder0.append("{\n");
                this.K.l(stringBuilder0, context0, s + "    ").append('\n');
                stringBuilder0.append(s);
                stringBuilder0.append('}');
            }
            return stringBuilder0;
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            if(this.J.z != 1) {
                return this.K.z == 1 ? new n(null, this.J.o(context0), null, null, context0.getResources().getDrawable(0x7F040022)) : new n(null, this.J.o(context0), null, this.K.o(context0), context0.getResources().getDrawable(0x7F040022));  // drawable:ic_conditional_half
            }
            return this.K.z != 1 ? new n(null, null, null, this.K.o(context0), context0.getResources().getDrawable(0x7F040022)) : super.m(context0);  // drawable:ic_conditional_half
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            b b0;
            StringBuilder stringBuilder0;
            if(this.I.z != 0 && (this.J.z != 1 || this.K.z != 1)) {
                if(this.K.z == 1) {
                    stringBuilder0 = new StringBuilder();
                    stringBuilder0.append("if ");
                    stringBuilder0.append(this.I.b(context0));
                    stringBuilder0.append(" { ");
                    b0 = this.J;
                }
                else {
                    stringBuilder0 = new StringBuilder();
                    stringBuilder0.append("if ");
                    stringBuilder0.append(this.I.b(context0));
                    stringBuilder0.append(" { ");
                    stringBuilder0.append(this.J.n(context0));
                    stringBuilder0.append(" } else { ");
                    b0 = this.K;
                }
                stringBuilder0.append(b0.n(context0));
                stringBuilder0.append(" }");
                return stringBuilder0.toString();
            }
            return super.n(context0);
        }

        @Override  // a.b
        public Drawable o(Context context0) {
            return super.m(context0);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            this.I.e(s0);
            s0.f(this.J).f(this.K);
        }
    }

    public static final class d1 extends k3 {
        public d1(int v) {
            super(30, 0x7F06002E, 0x7F040056, v);  // string:action_keep_screen_on "Stay awake"
        }
    }

    public static final class d2 extends b {
        private d2() {
            super(4, 0x7F060048, 0x7F04008F);  // string:action_recent_apps "Recent apps"
        }

        d2(a b$a0) {
        }
    }

    static final class d3 extends b {
        public final String I;

        private d3(s s0) {
            this(d3.A(s0));
        }

        d3(s s0, a b$a0) {
            this(s0);
        }

        private d3(String s) {
            super(58, 0x7F06005A, 0x7F0400B0);  // string:action_speech "Speech"
            this.I = s;
        }

        private static String A(s s0) {
            String s1 = s0.a();
            return s1 == null ? "" : Uri.decode(s1);
        }

        private c3 B() {
            return new c3(this.I);
        }
    }

    public static final class d extends x implements e0 {
        public final b K;
        private final String L;
        private final String M;
        private int N;
        private final String O;
        private static final String P;

        static {
            d.P = v0.a;
        }

        private d() {
            this(null, null, null, null);
        }

        d(a b$a0) {
        }

        public d(b b0) {
            this(b0, null, null, null);
        }

        private d(b b0, String s, String s1, String s2) {
            super(72, 0x7F06001C, 0x7F04003E);  // string:action_editable_action "Select and edit"
            this.N = 0;
            this.K = b0;
            this.L = s;
            this.M = s1;
            this.O = s2;
            v0.b(s2);
        }

        private d(s s0) {
            this(s0.g(), Uri.decode(s0.a()), s0.a(), s0.a());
        }

        d(s s0, a b$a0) {
            this(s0);
        }

        @Override  // a.b$x
        protected CharSequence A(Context context0) {
            return this.K == null ? context0.getText(this.A) : this.K.n(context0);
        }

        public boolean B() {
            return this.M != null || this.O != null;
        }

        public boolean C() {
            return this.L != null;
        }

        public d D(b b0) {
            if(b0.z == 72) {
                return this.M != null || this.O != null ? new d(((d)b0).K, (this.L == null ? ((d)b0).L : this.L), this.M, this.O) : new d(((d)b0).K, (this.L == null ? ((d)b0).L : this.L), ((d)b0).M, ((d)b0).O);
            }
            return new d(b0, this.L, this.M, this.O);
        }

        public d E() {
            return new d(this.K, this.L, null, null);
        }

        public d F(Bitmap bitmap0, Context context0) {
            String s = v0.e(bitmap0);
            d b$d0 = new d(this.K, this.L, null, s);
            if(b$d0.O != null) {
                j.k(context0, d.P + b$d0.O);
            }
            return b$d0;
        }

        public d G(String s) {
            return new d(this.K, this.L, null, s);
        }

        public d H(String s) {
            return new d(this.K, this.L, s, null);
        }

        public d I(String s) {
            return new d(this.K, s, this.M, this.O);
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            return this.K == null ? super.l(stringBuilder0, context0, s) : this.K.l(stringBuilder0, context0, s);
        }

        @Override  // a.b$x
        public CharSequence n(Context context0) {
            return this.L == null ? this.A(context0) : this.L;
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.f(this.K).c(Uri.encode(this.L)).c(this.M).c(this.O);
        }

        @Override  // a.b$x
        protected Drawable z(Context context0) {
            try {
                if(this.M != null) {
                    Resources resources0 = context0.getResources();
                    int v = this.N;
                    if(v == 0) {
                        v = resources0.getIdentifier(this.M, "drawable", l.j);
                        this.N = v;
                    }
                    if(v > 0) {
                        Drawable drawable0 = resources0.getDrawable(v);
                        if(drawable0 != null) {
                            return drawable0;
                        }
                    }
                }
                if(this.O != null) {
                    Drawable drawable1 = q.h((d.P + this.O));
                    if(drawable1 != null) {
                        return drawable1;
                    }
                }
            }
            catch(Throwable unused_ex) {
            }
            return this.K == null ? null : this.K.m(context0);
        }
    }

    public static final class a.b.e0 extends b implements u0 {
        public final int I;
        private static final int J;

        static {
            a.b.e0.J = Build.VERSION.SDK_INT < 23 ? 4 : 6;
        }

        private a.b.e0(int v) {
            super(87, 0x7F060018, 0x7F040023);  // string:action_content "Edit"
            this.I = v;
        }

        a.b.e0(int v, a b$a0) {
            this(v);
        }

        @Override  // a.b$u0
        public CharSequence a(Context context0, int v) {
            switch(v) {
                case 0: {
                    return context0.getText(0x1040003);
                }
                case 1: {
                    return context0.getText(0x1040001);
                }
                case 2: {
                    return context0.getText(0x104000B);
                }
                case 3: {
                    return context0.getText(0x104000D);
                }
                case 4: {
                    return context0.getText(0x7F06020F);  // string:undo "Undo"
                }
                case 5: {
                    return context0.getText(0x7F06017F);  // string:redo "Redo"
                }
                default: {
                    return super.n(context0);
                }
            }
        }

        @Override  // a.b$u0
        public int b() {
            return a.b.e0.J;
        }

        @Override  // a.b$u0
        public b c(int v) {
            return new a.b.e0(v);
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            switch(this.I) {
                case 0: {
                    return context0.getResources().getDrawable(0x7F040025);  // drawable:ic_content_cut
                }
                case 1: {
                    return context0.getResources().getDrawable(0x7F040024);  // drawable:ic_content_copy
                }
                case 2: {
                    return context0.getResources().getDrawable(0x7F040026);  // drawable:ic_content_paste
                }
                case 3: {
                    return context0.getResources().getDrawable(0x7F040028);  // drawable:ic_content_select_all
                }
                case 4: {
                    return context0.getResources().getDrawable(0x7F040029);  // drawable:ic_content_undo
                }
                case 5: {
                    return context0.getResources().getDrawable(0x7F040027);  // drawable:ic_content_redo
                }
                default: {
                    return super.m(context0);
                }
            }
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.a(context0, this.I);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }
    }

    public static final class e1 extends k3 {
        public e1(int v) {
            super(35, 0x7F06002F, 0x7F040057, v);  // string:action_key_enabled "Key control"
        }
    }

    public static final class e2 extends b {
        private e2() {
            super(81, 0x7F060049, 0x7F040090);  // string:action_refreeze "Refreeze apps in freezer"
        }

        e2(a b$a0) {
        }
    }

    public static final class e3 extends b {
        public e3() {
            super(0x60, 0x7F06005B, 0x7F0400B1);  // string:action_split_screen "Split screen"
        }
    }

    public static class e extends x {
        public final String K;
        public final String L;

        private e() {
            this(null, null);
        }

        e(a b$a0) {
        }

        private e(s s0) {
            this(s0.a(), s0.a());
        }

        e(s s0, a b$a0) {
            this(s0);
        }

        public e(String s, String s1) {
            super(0x30, 0x7F060005, 0x7F040004);  // string:action_activity "Activity"
            this.K = s;
            this.L = s1;
        }

        @Override  // a.b$x
        protected CharSequence A(Context context0) {
            if(this.L != null) {
                try {
                    return z.K(context0).z(this.K, this.L);
                }
                catch(Throwable unused_ex) {
                    return f.c0.C(this.K, this.L);
                }
            }
            return null;
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            String s1 = this.L;
            if(s1 == null) {
                return super.l(stringBuilder0, context0, s);
            }
            stringBuilder0.append(s);
            stringBuilder0.append(this.n(context0));
            stringBuilder0.append('\n');
            stringBuilder0.append(s);
            stringBuilder0.append("    ");
            stringBuilder0.append(this.K);
            stringBuilder0.append('/');
            stringBuilder0.append(f.c0.A(this.K, s1));
            return stringBuilder0;
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.c(this.K).c(this.L);
        }

        @Override  // a.b$x
        protected Drawable z(Context context0) {
            return this.L == null ? null : z.K(context0).s(this.K, this.L);
        }
    }

    public static final class f0 extends b {
        public final int I;
        private static a.n J;

        private f0() {
            this(-1);
        }

        public f0(int v) {
            super(83, 0x7F060019, 0x7F04002A);  // string:action_custom_panel "Custom panel"
            this.I = v;
        }

        f0(a b$a0) {
        }

        public static a.n A() {
            synchronized(f0.class) {
                if(f0.J == null) {
                    f0.J = new a.n(l.k + "CUSTOM_PANELS", l.m + "panels");
                }
                return f0.J;
            }
        }

        public static void B() {
            synchronized(f0.class) {
                f0.J = null;
            }
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            if(this.I >= 0) {
                a.o o0 = (a.o)f0.A().h(this.I);
                if(o0 != null) {
                    stringBuilder0.append(s);
                    stringBuilder0.append(this.n(context0));
                    stringBuilder0.append('(');
                    stringBuilder0.append(super.n(context0));
                    stringBuilder0.append("): ");
                    if(s.length() > q1.J) {
                        stringBuilder0.append('\n');
                        stringBuilder0.append(s);
                        stringBuilder0.append("    ");
                        stringBuilder0.append("...");
                        return stringBuilder0;
                    }
                    b[] arr_b = o0.b;
                    if(arr_b != null) {
                        for(int v = 0; v < arr_b.length; ++v) {
                            b b0 = arr_b[v];
                            stringBuilder0.append('\n');
                            b0.l(stringBuilder0, context0, s + "    ");
                        }
                    }
                }
                return stringBuilder0;
            }
            return super.l(stringBuilder0, context0, s);
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            if(this.I >= 0) {
                a.o o0 = (a.o)f0.A().h(this.I);
                if(o0 != null && o0.b != null) {
                    return new n(context0, o0.b, null);
                }
            }
            return super.m(context0);
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            CharSequence charSequence0 = super.n(context0);
            if(this.I >= 0) {
                a.o o0 = (a.o)f0.A().h(this.I);
                if(o0 != null) {
                    return o0.a;
                }
                v.c(("Panel id not found: " + this.I));
            }
            return charSequence0;
        }

        @Override  // a.b
        public Drawable o(Context context0) {
            return super.m(context0);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }

        public b[] z() {
            a.o o0 = (a.o)f0.A().h(this.I);
            if(o0 == null) {
                throw new RuntimeException("Panel not exists!");
            }
            return o0.b;
        }
    }

    public static final class f1 extends b {
        public final String I;

        public f1(String s) {
            super(77, 0x7F060030, 0x7F04005A);  // string:action_kill_app "Kill app"
            this.I = s;
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            if(this.I != null) {
                try {
                    return new t(z.K(context0).C(this.I), context0.getResources().getDrawable(0x7F040058), 0.7f, true);  // drawable:ic_kill
                }
                catch(Throwable unused_ex) {
                }
            }
            return super.m(context0);
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            if(this.I != null) {
                try {
                    return super.n(context0) + ": " + z.K(context0).G(this.I);
                }
                catch(Throwable unused_ex) {
                    return super.n(context0) + ": " + this.I;
                }
            }
            return super.n(context0);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.c(this.I);
        }
    }

    public static final class f2 extends b implements e0 {
        public final int I;
        public final b J;

        private f2() {
            this(-1, b.r());
        }

        public f2(int v, b b0) {
            super(101, 0x7F06004A, 0x7F040091);  // string:action_repeat "Repeat"
            this.I = v;
            this.J = b0;
        }

        f2(a b$a0) {
        }

        private f2(s s0) {
            this(s0.h(), s0.g());
        }

        f2(s s0, a b$a0) {
            this(s0);
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            stringBuilder0.append(s);
            stringBuilder0.append(super.n(context0));
            stringBuilder0.append('(');
            stringBuilder0.append(this.I);
            stringBuilder0.append(")\n");
            stringBuilder0.append(s);
            stringBuilder0.append("{\n");
            this.J.l(stringBuilder0, context0, s + "    ").append('\n');
            stringBuilder0.append(s);
            stringBuilder0.append('}');
            return stringBuilder0;
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            return this.I <= 0 ? super.m(context0) : new t(this.J.m(context0), context0.getResources().getDrawable(0x7F040092), 0.8f, true);  // drawable:ic_repeat_corner
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.I == -1 ? super.n(context0) : super.n(context0) + ": " + this.J.n(context0);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I).f(this.J);
        }
    }

    public static final class f3 extends b {
        public f3() {
            super(0x10001, 0, 0);
        }
    }

    public static final class f extends x {
        public final String K;
        public final String L;
        public final int M;

        private f(s s0) {
            this(s0.a(), s0.a(), s0.h());
        }

        f(s s0, a b$a0) {
            this(s0);
        }

        public f(String s, String s1, int v) {
            super(0x71, 0x7F060005, 0x7F040004);  // string:action_activity "Activity"
            this.K = s;
            this.L = s1;
            this.M = v;
        }

        @Override  // a.b$x
        protected CharSequence A(Context context0) {
            if(this.L != null) {
                try {
                    return z.K(context0).A(this.K, this.L, this.M);
                }
                catch(Throwable unused_ex) {
                    return this.K + '/' + f.c0.A(this.K, this.L) + f.c0.z + this.M + ")";
                }
            }
            return null;
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            String s1 = this.L;
            if(s1 == null) {
                return super.l(stringBuilder0, context0, s);
            }
            stringBuilder0.append(s);
            stringBuilder0.append(this.n(context0));
            stringBuilder0.append('\n');
            stringBuilder0.append(s);
            stringBuilder0.append("    ");
            stringBuilder0.append(this.K);
            stringBuilder0.append('/');
            stringBuilder0.append(f.c0.A(this.K, s1));
            stringBuilder0.append(f.c0.z);
            stringBuilder0.append(this.M);
            stringBuilder0.append(')');
            return stringBuilder0;
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.c(this.K).c(this.L).d(this.M);
        }

        @Override  // a.b$x
        protected Drawable z(Context context0) {
            return this.L == null ? null : z.K(context0).t(this.K, this.L, this.M);
        }
    }

    public static final class g0 extends b implements e0 {
        public final int I;

        private g0() {
            this(-1);
        }

        public g0(int v) {
            super(54, 0x7F06001A, 0x7F04002B);  // string:action_delay "Delay"
            this.I = v;
        }

        g0(a b$a0) {
        }

        private g0(s s0) {
            this(s0.h());
        }

        g0(s s0, a b$a0) {
            this(s0);
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.I == -1 ? super.n(context0) : super.n(context0) + ": " + this.I + "ms";
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }
    }

    public static final class g1 extends b {
        private g1() {
            super(37, 0x7F060031, 0x7F040059);  // string:action_kill_foreground_app "Kill foreground app"
        }

        g1(a b$a0) {
        }
    }

    public static final class g2 extends b implements u0 {
        public final int I;

        private g2() {
            this(0);
        }

        private g2(int v) {
            super(70, 0x7F06004B, 0x7F040094);  // string:action_ringer_mode "Ringer mode"
            this.I = v;
        }

        g2(int v, a b$a0) {
            this(v);
        }

        g2(a b$a0) {
        }

        @Override  // a.b$u0
        public CharSequence a(Context context0, int v) {
            switch(v) {
                case 0: {
                    return context0.getText(0x7F0601EC);  // string:sub_action_switch "Switch"
                }
                case 1: {
                    return context0.getText(0x7F060187);  // string:ringer_mode_silent "Silent"
                }
                case 2: {
                    return context0.getText(0x7F060188);  // string:ringer_mode_vibrate "Vibrate"
                }
                case 3: {
                    return context0.getText(0x7F060186);  // string:ringer_mode_normal "Normal"
                }
                default: {
                    return null;
                }
            }
        }

        @Override  // a.b$u0
        public int b() {
            return 4;
        }

        @Override  // a.b$u0
        public b c(int v) {
            return new g2(v);
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            switch(this.I) {
                case 1: {
                    return context0.getResources().getDrawable(0x7F040095);  // drawable:ic_ringer_mode_silent
                }
                case 2: {
                    return context0.getResources().getDrawable(0x7F040096);  // drawable:ic_ringer_mode_vibrate
                }
                default: {
                    return super.m(context0);
                }
            }
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.I == 0 ? super.n(context0) : super.n(context0) + ": " + this.a(context0, this.I);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }
    }

    public static final class g3 extends b {
        private g3() {
            super(0x70, 0x7F06005C, 0x7F0400B2);  // string:action_stop_all "Stop all"
        }

        g3(a b$a0) {
        }
    }

    public static class g extends x {
        public final Intent K;

        public g(Intent intent0) {
            super(76, 0x7F060005, 0x7F040004);  // string:action_activity "Activity"
            this.K = intent0;
        }

        private g(s s0) {
            this(r2.E(s0));
        }

        g(s s0, a b$a0) {
            this(s0);
        }

        @Override  // a.b$x
        protected CharSequence A(Context context0) {
            if(this.K != null) {
                try {
                    return z.K(context0).x(this.K);
                }
                catch(Throwable unused_ex) {
                    return f.c0.B(this.K);
                }
            }
            return null;
        }

        public b B() {
            Intent intent0 = this.K;
            if(intent0 != null) {
                ComponentName componentName0 = intent0.getComponent();
                if(componentName0 != null) {
                    return new e(componentName0.getPackageName(), componentName0.getClassName());
                }
            }
            return null;
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            if(this.K == null) {
                return super.l(stringBuilder0, context0, s);
            }
            stringBuilder0.append(s);
            stringBuilder0.append(this.n(context0));
            stringBuilder0.append('\n');
            stringBuilder0.append(s);
            stringBuilder0.append("    ");
            stringBuilder0.append(this.K);
            return stringBuilder0;
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.b((this.K == null ? null : this.K.toUri(0)));
        }

        @Override  // a.b$x
        protected Drawable z(Context context0) {
            return this.K == null ? null : z.K(context0).q(this.K);
        }
    }

    public static final class h0 extends b {
        public h0() {
            super(75, 0x7F06001B, 0x7F04002D);  // string:action_dismiss_keyguard "Unlock keyguard"
        }
    }

    public static final class h1 extends b {
        private h1() {
            super(36, 0x7F060032, 0x7F04005B);  // string:action_last_app "Last app"
        }

        h1(a b$a0) {
        }
    }

    public static final class h2 extends b implements u0 {
        public final int I;

        private h2() {
            this(0);
        }

        private h2(int v) {
            super(74, 0x7F06004C, 0x7F040099);  // string:action_rotate_screen "Rotate screen"
            this.I = v;
        }

        h2(int v, a b$a0) {
            this(v);
        }

        h2(a b$a0) {
        }

        @Override  // a.b$u0
        public CharSequence a(Context context0, int v) {
            switch(v) {
                case 1: {
                    return context0.getText(0x7F06018E);  // string:rotate_portrait "Rotate to portrait"
                }
                case 2: {
                    return context0.getText(0x7F06018D);  // string:rotate_landscape "Rotate to landscape"
                }
                case 3: {
                    return context0.getText(0x7F060190);  // string:rotate_upside_down "Rotate to upside down"
                }
                case 4: {
                    return context0.getText(0x7F06018F);  // string:rotate_seascape "Rotate to seascape"
                }
                case 5: {
                    return context0.getText(0x7F06018C);  // string:rotate_90 "Rotate 90°"
                }
                case 6: {
                    return context0.getText(0x7F06018A);  // string:rotate_180 "Rotate 180°"
                }
                case 7: {
                    return context0.getText(0x7F06018B);  // string:rotate_270 "Rotate -90°"
                }
                default: {
                    return super.n(context0);
                }
            }
        }

        @Override  // a.b$u0
        public int b() {
            return 8;
        }

        @Override  // a.b$u0
        public b c(int v) {
            return new h2(v);
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            switch(this.I) {
                case 1: 
                case 3: {
                    return context0.getResources().getDrawable(0x7F040098);  // drawable:ic_rotate_portrait
                }
                case 2: 
                case 4: {
                    return context0.getResources().getDrawable(0x7F040097);  // drawable:ic_rotate_landscape
                }
                default: {
                    return super.m(context0);
                }
            }
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.a(context0, this.I);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }
    }

    public static final class h3 extends b implements e0 {
        public final b[] I;
        public static final int[] J;
        public static final b[] K;

        static {
            h3.J = new int[]{0x7F060107, 0x7F060112, 0x7F060113, 0x7F060114, 0x7F060111};  // string:gesture_hold "Hold"
            h3.K = new b[]{b.r(), b.r(), b.r(), b.r(), b.r()};
        }

        public h3() {
            this(h3.K);
        }

        private h3(s s0) {
            this(new b[]{s0.g(), s0.g(), s0.g(), s0.g(), s0.g()});
        }

        h3(s s0, a b$a0) {
            this(s0);
        }

        public h3(b[] arr_b) {
            super(107, 0x7F06005D, 0x7F0400B5);  // string:action_sub_gestures "Sub gestures"
            this.I = arr_b;
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            b[] arr_b = this.I;
            for(int v = 0; v < arr_b.length; ++v) {
                arr_b[v].y(s0);
            }
        }
    }

    public static final class a.b.h extends x {
        public final Intent K;
        public final int L;

        public a.b.h(Intent intent0, int v) {
            super(0x72, 0x7F060005, 0x7F040004);  // string:action_activity "Activity"
            this.K = intent0;
            this.L = v;
        }

        private a.b.h(s s0) {
            this(r2.E(s0), s0.h());
        }

        a.b.h(s s0, a b$a0) {
            this(s0);
        }

        @Override  // a.b$x
        protected CharSequence A(Context context0) {
            if(this.K != null) {
                try {
                    return z.K(context0).y(this.K, this.L);
                }
                catch(Throwable unused_ex) {
                    return f.c0.B(this.K) + f.c0.z + this.L + ")";
                }
            }
            return null;
        }

        public b B() {
            Intent intent0 = this.K;
            if(intent0 != null) {
                ComponentName componentName0 = intent0.getComponent();
                if(componentName0 != null) {
                    return new f(componentName0.getPackageName(), componentName0.getClassName(), this.L);
                }
            }
            return null;
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            if(this.K == null) {
                return super.l(stringBuilder0, context0, s);
            }
            stringBuilder0.append(s);
            stringBuilder0.append(this.n(context0));
            stringBuilder0.append('\n');
            stringBuilder0.append(s);
            stringBuilder0.append("    ");
            stringBuilder0.append(this.K);
            stringBuilder0.append(f.c0.z);
            stringBuilder0.append(this.L);
            stringBuilder0.append(')');
            return stringBuilder0;
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.b((this.K == null ? null : this.K.toUri(0))).d(this.L);
        }

        @Override  // a.b$x
        protected Drawable z(Context context0) {
            return this.K == null ? null : z.K(context0).r(this.K, this.L);
        }
    }

    public static final class i0 extends b {
        private i0() {
            super(15, 0x7F06001D, 0x7F04006E);  // string:action_expand_notifications_panel "Expand notifications panel"
        }

        i0(a b$a0) {
        }
    }

    public static final class i1 extends k3 {
        public i1(int v) {
            super(23, 0x7F060033, 0x7F040060, v);  // string:action_location "Location mode"
        }
    }

    public static final class i2 extends b {
        private i2() {
            super(55, 0x7F06004D, 0x7F04009A);  // string:action_running_apps_drawer "Running apps drawer"
        }

        i2(a b$a0) {
        }
    }

    public static final class i3 extends b {
        public final int I;
        public final boolean J;

        private i3() {
            this(-1, false);
        }

        public i3(int v, boolean z) {
            super(86, 0x7F06005E, 0x7F0400B6);  // string:action_successive_adjust "Successive adjust"
            this.I = v;
            this.J = z;
        }

        i3(a b$a0) {
        }

        public static CharSequence A(Context context0, int v, boolean z) {
            switch(v) {
                case 13: {
                    return z ? context0.getText(0x7F0601A0) : context0.getText(0x7F06019C);  // string:scroll_up "Scroll up"
                }
                case 14: {
                    return z ? context0.getText(0x7F06008B) : context0.getText(0x7F06008C);  // string:caret_left "Cursor left shift"
                }
                default: {
                    return z ? context0.getText(0x7F060072) : context0.getText(0x7F06006F);  // string:adjust_up "up"
                }
            }
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            int v = this.I;
            switch(v) {
                case 0: 
                case 1: 
                case 2: 
                case 3: 
                case 4: 
                case 5: 
                case 6: 
                case 7: 
                case 8: 
                case 9: 
                case 10: 
                case 11: 
                case 12: {
                    return this.J ? context0.getString(0x7F060212, new Object[]{i3.z(context0, v)}) : context0.getString(0x7F0600D8, new Object[]{i3.z(context0, v)});  // string:up_f "%1$s up"
                }
                case 13: {
                    return this.J ? context0.getText(0x7F0601A0) : context0.getText(0x7F06019C);  // string:scroll_up "Scroll up"
                }
                case 14: {
                    return this.J ? context0.getText(0x7F06008B) : context0.getText(0x7F06008C);  // string:caret_left "Cursor left shift"
                }
                default: {
                    return super.n(context0);
                }
            }
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I).d(((int)this.J));
        }

        public static CharSequence z(Context context0, int v) {
            switch(v) {
                case 0: {
                    return context0.getText(0x7F060070);  // string:adjust_screen_filter_alpha "Screen filter alpha"
                }
                case 1: {
                    return context0.getText(0x7F06006D);  // string:adjust_brightness "Brightness"
                }
                case 13: {
                    return context0.getText(0x7F060071);  // string:adjust_scroll "Scroll"
                }
                case 14: {
                    return context0.getText(0x7F06006E);  // string:adjust_caret "Cursor"
                }
                default: {
                    return q0.d(context0, v - 3);
                }
            }
        }
    }

    public static final class i extends b {
        public final int I;
        public final int J;

        private i() {
            this(-1, 0);
        }

        public i(int v, int v1) {
            super(89, 0x7F060006, 0x7F040007);  // string:action_adjust "Adjust"
            this.I = v;
            this.J = v1;
        }

        i(a b$a0) {
        }

        public static int A(Context context0, int v) {
            switch(v) {
                case 0: {
                    return 15;
                }
                case 1: {
                    return f.g.b;
                }
                case 2: {
                    return 0;
                }
                default: {
                    return q0.a(context0, v - 3);
                }
            }
        }

        public static int B(int v) {
            switch(v) {
                case 0: {
                    return 1;
                }
                case 1: {
                    return f.g.a;
                }
                default: {
                    return 0;
                }
            }
        }

        public static CharSequence C(Context context0, int v) {
            switch(v) {
                case 0: {
                    return context0.getText(0x7F060070);  // string:adjust_screen_filter_alpha "Screen filter alpha"
                }
                case 1: {
                    return context0.getText(0x7F06006D);  // string:adjust_brightness "Brightness"
                }
                default: {
                    return q0.d(context0, v - 3);
                }
            }
        }

        public static int D(int v) {
            return v == 2 ? 2 : 3;
        }

        public static CharSequence a(Context context0, int v) {
            switch(v) {
                case 0: {
                    return context0.getText(0x7F060072);  // string:adjust_up "up"
                }
                case 1: {
                    return context0.getText(0x7F06006F);  // string:adjust_down "down"
                }
                default: {
                    return context0.getText(0x7F0601AD);  // string:set_level "set level"
                }
            }
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            int v = this.I;
            if(v < 0) {
                return super.m(context0);
            }
            switch(v) {
                case 0: {
                    return context0.getResources().getDrawable(0x7F04009F);  // drawable:ic_screen_filter
                }
                case 1: {
                    Resources resources0 = context0.getResources();
                    return this.J == -2 ? resources0.getDrawable(0x7F040014) : resources0.getDrawable(0x7F040015);  // drawable:ic_brightness_down
                }
                case 2: 
                case 3: 
                case 4: 
                case 5: 
                case 6: 
                case 7: 
                case 8: 
                case 9: 
                case 10: 
                case 11: 
                case 12: {
                    Resources resources1 = context0.getResources();
                    switch(this.J) {
                        case -2: {
                            return resources1.getDrawable(0x7F0400C5);  // drawable:ic_volume_down
                        }
                        case 0: {
                            return resources1.getDrawable(0x7F0400C6);  // drawable:ic_volume_mute
                        }
                        default: {
                            return resources1.getDrawable(0x7F0400C7);  // drawable:ic_volume_up
                        }
                    }
                }
                default: {
                    return super.m(context0);
                }
            }
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            int v = this.I;
            if(v < 0) {
                return super.n(context0);
            }
            switch(this.J) {
                case -2: {
                    return context0.getString(0x7F0600D8, new Object[]{i.C(context0, v)});  // string:down_f "%1$s down"
                }
                case -1: {
                    return context0.getString(0x7F060212, new Object[]{i.C(context0, v)});  // string:up_f "%1$s up"
                }
                default: {
                    return i.C(context0, this.I) + ": " + this.J;
                }
            }
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I).d(this.J);
        }

        public static int z(Context context0, int v) {
            switch(v) {
                case 1: {
                    return f.g.a(context0);
                }
                case 0: 
                case 2: {
                    return -1;
                }
                default: {
                    return q0.b(context0, v - 3);
                }
            }
        }
    }

    public static final class j0 extends b {
        private j0() {
            super(16, 0x7F06001E, 0x7F04008A);  // string:action_expand_quick_settings_panel "Expand quick settings panel"
        }

        j0(a b$a0) {
        }
    }

    public static final class j1 extends b implements u0 {
        public final int I;

        private j1(int v) {
            super(104, 0x7F060034, 0x7F040063);  // string:action_media_control "Media control"
            this.I = v;
        }

        j1(int v, a b$a0) {
            this(v);
        }

        @Override  // a.b$u0
        public CharSequence a(Context context0, int v) {
            switch(v) {
                case 0: {
                    return context0.getText(0x7F060036);  // string:action_media_play "Media play"
                }
                case 1: {
                    return context0.getText(0x7F060037);  // string:action_media_play_pause "Media play/pause"
                }
                case 2: {
                    return context0.getText(0x7F060039);  // string:action_media_stop "Media stop"
                }
                case 3: {
                    return context0.getText(0x7F060038);  // string:action_media_previous "Media previous"
                }
                case 4: {
                    return context0.getText(0x7F060035);  // string:action_media_next "Media next"
                }
                default: {
                    return super.n(context0);
                }
            }
        }

        @Override  // a.b$u0
        public int b() {
            return 5;
        }

        @Override  // a.b$u0
        public b c(int v) {
            return new j1(v);
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            switch(this.I) {
                case 0: {
                    return context0.getResources().getDrawable(0x7F040062);  // drawable:ic_media_play
                }
                case 1: {
                    return context0.getResources().getDrawable(0x7F040063);  // drawable:ic_media_play_pause
                }
                case 2: {
                    return context0.getResources().getDrawable(0x7F040065);  // drawable:ic_media_stop
                }
                case 3: {
                    return context0.getResources().getDrawable(0x7F040064);  // drawable:ic_media_previous
                }
                case 4: {
                    return context0.getResources().getDrawable(0x7F040061);  // drawable:ic_media_next
                }
                default: {
                    return super.m(context0);
                }
            }
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.a(context0, this.I);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }
    }

    public static final class j2 {
        private final String[] a;
        private static final String b;

        static {
            j2.b = v0.a;
        }

        public j2() {
            File file0 = new File(j2.b);
            if(file0.exists()) {
                this.a = file0.list();
                return;
            }
            this.a = new String[0];
        }

        public int a() {
            return this.a.length;
        }

        public Drawable b(int v) {
            return q.h((j2.b + this.a[v]));
        }

        public static Drawable c(String s) {
            return q.h((j2.b + s));
        }

        public String d(int v) {
            return this.a[v];
        }
    }

    public static final class j3 extends k3 {
        public j3(int v) {
            super(26, 0x7F06005F, 0x7F0400BC, v);  // string:action_sync "Sync"
        }
    }

    public static final class a.b.j extends b {
        public final int I;
        public final int J;

        public a.b.j(int v, int v1) {
            super(60, 0x7F060007, 0x7F0400C7);  // string:action_adjust_volume "Adjust volume"
            this.I = v;
            this.J = v1;
        }

        private a.b.j(s s0) {
            this(s0.h(), s0.h());
        }

        a.b.j(s s0, a b$a0) {
            this(s0);
        }

        public i A() {
            return new i(this.I + 2, this.J);
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            if(this.I != -2) {
                int v = this.J;
                if(v == -2) {
                    return context0.getResources().getDrawable(0x7F0400C5);  // drawable:ic_volume_down
                }
                return v == 0 ? context0.getResources().getDrawable(0x7F0400C6) : super.m(context0);  // drawable:ic_volume_mute
            }
            return super.m(context0);
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.I == -2 ? super.n(context0) : q0.d(context0, this.I) + " " + a.b.j.z(context0, this.J);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I).d(this.J);
        }

        public static CharSequence z(Context context0, int v) {
            if(v != -2) {
                switch(v) {
                    case -1: {
                        return "up";
                    }
                    case 0: {
                        return "mute";
                    }
                    default: {
                        return Integer.toString(v);
                    }
                }
            }
            return "down";
        }
    }

    public static final class k0 extends b implements u0 {
        public final int I;

        private k0() {
            this(-1);
        }

        public k0(int v) {
            super(0x40, 0x7F06001F, 0x7F040041);  // string:action_fast_scroll "Fast scroll"
            this.I = v;
        }

        k0(a b$a0) {
        }

        @Override  // a.b$u0
        public CharSequence a(Context context0, int v) {
            return v == 0 ? context0.getText(0x7F06019F) : context0.getText(0x7F06019E);  // string:scroll_to_top "Scroll to top"
        }

        @Override  // a.b$u0
        public int b() {
            return 2;
        }

        @Override  // a.b$u0
        public b c(int v) {
            return v == 0 ? new k0(0) : new k0(1);
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            switch(this.I) {
                case 0: {
                    return context0.getResources().getDrawable(0x7F0400A3);  // drawable:ic_scroll_to_top
                }
                case 1: {
                    return context0.getResources().getDrawable(0x7F0400A2);  // drawable:ic_scroll_to_bottom
                }
                default: {
                    return super.m(context0);
                }
            }
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            switch(this.I) {
                case 0: {
                    return context0.getText(0x7F06019F);  // string:scroll_to_top "Scroll to top"
                }
                case 1: {
                    return context0.getText(0x7F06019E);  // string:scroll_to_bottom "Scroll to bottom"
                }
                default: {
                    return super.n(context0);
                }
            }
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }
    }

    public static final class k1 extends b {
        private k1() {
            super(12, 0x7F060035, 0x7F040061);  // string:action_media_next "Media next"
        }

        k1(a b$a0) {
        }
    }

    public static final class k2 extends b implements e0 {
        public final int I;
        private static a.n J;

        private k2() {
            this(-1);
        }

        public k2(int v) {
            super(84, 0x7F06004E, 0x7F04009D);  // string:action_saved_multi_action "Saved multi-action"
            this.I = v;
        }

        k2(a b$a0) {
        }

        public static a.n A() {
            synchronized(k2.class) {
                if(k2.J == null) {
                    k2.J = new a.n(l.k + "MULTI_ACTION", l.m + "multi_actions");
                }
                return k2.J;
            }
        }

        public static void B() {
            synchronized(k2.class) {
                k2.J = null;
            }
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            if(this.I >= 0) {
                a.o o0 = (a.o)k2.A().h(this.I);
                if(o0 != null) {
                    stringBuilder0.append(s);
                    stringBuilder0.append(this.n(context0));
                    stringBuilder0.append('(');
                    stringBuilder0.append(super.n(context0));
                    stringBuilder0.append("): ");
                    if(s.length() > q1.J) {
                        stringBuilder0.append('\n');
                        stringBuilder0.append(s);
                        stringBuilder0.append("    ");
                        stringBuilder0.append("...");
                        return stringBuilder0;
                    }
                    b[] arr_b = o0.b;
                    if(arr_b != null) {
                        for(int v = 0; v < arr_b.length; ++v) {
                            b b0 = arr_b[v];
                            stringBuilder0.append('\n');
                            b0.l(stringBuilder0, context0, s + "    ");
                        }
                    }
                    return stringBuilder0;
                }
            }
            return super.l(stringBuilder0, context0, s);
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            if(this.I >= 0) {
                a.o o0 = (a.o)k2.A().h(this.I);
                if(o0 != null && o0.b != null) {
                    Drawable drawable0 = context0.getResources().getDrawable(0x7F04009C);  // drawable:ic_save_small
                    return new w(context0, o0.b, drawable0);
                }
            }
            return super.m(context0);
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            if(this.I >= 0) {
                a.o o0 = (a.o)k2.A().h(this.I);
                if(o0 != null) {
                    return o0.a;
                }
                v.c(("Saved multi-action id not found: " + this.I));
            }
            return super.n(context0);
        }

        @Override  // a.b
        public Drawable o(Context context0) {
            return super.m(context0);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }

        public b[] z() {
            a.o o0 = (a.o)k2.A().h(this.I);
            if(o0 == null) {
                throw new RuntimeException("Multi-action not exists!");
            }
            return o0.b;
        }
    }

    public static abstract class k3 extends b implements u0, Cloneable {
        public final int I;

        protected k3(int v, int v1, int v2, int v3) {
            super(v, v1, v2);
            this.I = v3;
        }

        @Override  // a.b$u0
        public CharSequence a(Context context0, int v) {
            switch(v) {
                case 1: {
                    return context0.getText(0x7F06020B);  // string:turn_on "Turn on"
                }
                case 2: {
                    return context0.getText(0x7F060209);  // string:turn_off "Turn off"
                }
                default: {
                    return context0.getText(0x7F060208);  // string:toggle "Toggle"
                }
            }
        }

        @Override  // a.b$u0
        public int b() {
            return 3;
        }

        @Override  // a.b$u0
        public b c(int v) {
            return this.z(v);
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            Drawable drawable0 = super.m(context0);
            int v = this.I;
            if(v == 1) {
                return new t(drawable0, context0.getResources().getDrawable(0x7F040072), 0.8f, true);  // drawable:ic_on_corner
            }
            return v == 2 ? new t(drawable0, context0.getResources().getDrawable(0x7F040070), 0.8f, true) : drawable0;  // drawable:ic_off_corner
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            switch(this.I) {
                case 1: {
                    return context0.getString(0x7F06020C, new Object[]{super.n(context0)});  // string:turn_on_f "Turn on %1$s"
                }
                case 2: {
                    return context0.getString(0x7F06020A, new Object[]{super.n(context0)});  // string:turn_off_f "Turn off %1$s"
                }
                default: {
                    return super.n(context0);
                }
            }
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }

        public k3 z(int v) {
            try {
                return (k3)this.getClass().getConstructor(Integer.TYPE).newInstance(v);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return null;
            }
        }
    }

    public static final class k extends k3 {
        public k(int v) {
            super(28, 0x7F060008, 0x7F040008, v);  // string:action_airplane_mode "Airplane mode"
        }
    }

    public static final class l0 extends b {
        private l0() {
            super(99, 0x7F060020, 0x7F040042);  // string:action_finish_activity "Finish activity"
        }

        l0(a b$a0) {
        }
    }

    public static final class l1 extends b {
        private l1() {
            super(9, 0x7F060037, 0x7F040063);  // string:action_media_play_pause "Media play/pause"
        }

        l1(a b$a0) {
        }
    }

    public static final class l2 extends b implements e0 {
        private final String I;
        private static final String J;

        static {
            l2.J = l.l + "lists/";
        }

        private l2(String s) {
            super(50, 0x7F06004E, 0x7F04009D);  // string:action_saved_multi_action "Saved multi-action"
            this.I = s;
        }

        l2(String s, a b$a0) {
            this(s);
        }

        b z() {
            String s = this.I;
            return s != null ? new k2(Integer.parseInt(s, 16)) : b.r();
        }
    }

    public static final class l3 extends k3 {
        public l3(int v) {
            super(0x20, 0x7F060061, 0x7F0400BE, v);  // string:action_torch "Torch"
        }
    }

    public static final class a.b.l extends x {
        public final String K;
        public final String L;
        private final int M;

        private a.b.l() {
            this(null, null, 0);
        }

        a.b.l(a b$a0) {
        }

        private a.b.l(s s0) {
            this(s0.a(), s0.a(), s0.h());
        }

        a.b.l(s s0, a b$a0) {
            this(s0);
        }

        public a.b.l(String s, String s1, int v) {
            super(0x73, 0x7F060009, 0x7F040009);  // string:action_app "App"
            this.K = s;
            this.L = s1;
            this.M = v;
        }

        @Override  // a.b$x
        protected CharSequence A(Context context0) {
            if(this.K != null) {
                int v = this.B();
                try {
                    return this.L == null ? z.K(context0).H(this.K, v) : z.K(context0).A(this.K, this.L, v);
                }
                catch(Throwable unused_ex) {
                    return v == 0 ? this.K : this.K + f.c0.z + v + ")";
                }
            }
            return null;
        }

        // 去混淆评级： 低(20)
        public int B() {
            return this.M == 0 ? 0 : this.M;
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            stringBuilder0.append(s);
            stringBuilder0.append(this.n(context0));
            stringBuilder0.append('\n');
            stringBuilder0.append(s);
            stringBuilder0.append("    ");
            stringBuilder0.append(this.K);
            if(this.M != 0) {
                stringBuilder0.append(f.c0.z);
                stringBuilder0.append(this.M);
                stringBuilder0.append(')');
            }
            return stringBuilder0;
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.c(this.K);
            s0.c(this.L);
            s0.d(this.M);
        }

        @Override  // a.b$x
        protected Drawable z(Context context0) {
            if(this.K != null) {
                int v = this.B();
                z z0 = z.K(context0);
                return this.L == null ? z0.D(this.K, v) : z0.t(this.K, this.L, v);
            }
            return null;
        }
    }

    public static final class m0 extends b {
        public final int I;

        public m0() {
            this(0);
        }

        public m0(int v) {
            super(94, 0x7F060021, 0x7F040043);  // string:action_floating_widget "Floating widget"
            this.I = v;
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            if(this.I != 0) {
                try {
                    AppWidgetProviderInfo appWidgetProviderInfo0 = AppWidgetManager.getInstance(context0).getAppWidgetInfo(this.I);
                    if(appWidgetProviderInfo0 != null) {
                        return Build.VERSION.SDK_INT < 21 ? super.n(context0) + ": " + appWidgetProviderInfo0.label : super.n(context0) + ": " + appWidgetProviderInfo0.loadLabel(context0.getPackageManager());
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
            return super.n(context0);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }
    }

    public static final class m1 extends b {
        private m1() {
            super(11, 0x7F060038, 0x7F040064);  // string:action_media_previous "Media previous"
        }

        m1(a b$a0) {
        }
    }

    public static final class m2 extends k3 {
        public m2(int v) {
            super(33, 0x7F06004F, 0x7F04009F, v);  // string:action_screen_filter "Screen filter"
        }
    }

    public static final class m3 extends b {
        public m3() {
            super(98, 0x7F060062, 0x7F0400C2);  // string:action_universal_copy "Universal copy"
        }
    }

    public static final class m extends x {
        public final String K;

        private m() {
            this(null);
        }

        private m(s s0) {
            this(s0.a());
        }

        m(s s0, a b$a0) {
            this(s0);
        }

        public m(String s) {
            super(0x2F, 0x7F060009, 0x7F040009);  // string:action_app "App"
            this.K = s;
        }

        @Override  // a.b$x
        protected CharSequence A(Context context0) {
            if(this.K != null) {
                try {
                    return z.K(context0).G(this.K);
                }
                catch(Throwable unused_ex) {
                    return this.K;
                }
            }
            return null;
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            stringBuilder0.append(s);
            stringBuilder0.append(this.n(context0));
            stringBuilder0.append('\n');
            stringBuilder0.append(s);
            stringBuilder0.append("    ");
            stringBuilder0.append(this.K);
            return stringBuilder0;
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.c(this.K);
        }

        @Override  // a.b$x
        protected Drawable z(Context context0) {
            return this.K == null ? null : z.K(context0).C(this.K);
        }
    }

    public static final class n0 extends b {
        private n0() {
            super(42, 0x7F060022, 0x7F040045);  // string:action_freezer_drawer "Freezer drawer"
        }

        n0(a b$a0) {
        }
    }

    public static final class n1 extends b {
        private n1() {
            super(10, 0x7F060039, 0x7F040065);  // string:action_media_stop "Media stop"
        }

        n1(a b$a0) {
        }
    }

    public static final class n2 extends b implements u0 {
        public final int I;

        public n2(int v) {
            super(0x5F, 0x7F060050, 0x7F0400A0);  // string:action_screenshot "Screenshot"
            this.I = v;
        }

        @Override  // a.b$u0
        public CharSequence a(Context context0, int v) {
            return v == 1 ? context0.getText(0x7F06019B) : context0.getText(0x7F06019A);  // string:screenshot_selected_region "Screenshot selected region"
        }

        @Override  // a.b$u0
        public int b() {
            return 2;
        }

        @Override  // a.b$u0
        public b c(int v) {
            return v == 0 ? new n2(1) : new n2(2);
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            return this.I == 2 ? context0.getResources().getDrawable(0x7F0400A1) : super.m(context0);  // drawable:ic_screenshot_selected_region
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.I == 2 ? context0.getText(0x7F06019B) : super.n(context0);  // string:screenshot_selected_region "Screenshot selected region"
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }
    }

    public static final class n3 extends b {
        public final int I;

        private n3() {
            this(0);
        }

        public n3(int v) {
            super(92, 0x7F060063, 0x7F0400C3);  // string:action_vibrate "Vibrate"
            this.I = v;
        }

        n3(a b$a0) {
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            if(this.I <= 0) {
                return super.l(stringBuilder0, context0, s);
            }
            stringBuilder0.append(s);
            stringBuilder0.append(this.n(context0));
            stringBuilder0.append(": ");
            stringBuilder0.append(this.I);
            return stringBuilder0;
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }
    }

    public static final class a.b.n extends b {
        private a.b.n() {
            super(71, 0x7F06000A, 0x7F04000A);  // string:action_app_option_menu "App option menu"
        }

        a.b.n(a b$a0) {
        }
    }

    public static final class o0 extends k3 {
        public o0(int v) {
            super(97, 0x7F060023, 0x7F040046, v);  // string:action_game_mode "Game mode"
        }
    }

    public static final class o1 extends b {
        private o1() {
            super(5, 0x7F06003A, 0x7F040066);  // string:action_menu "Menu"
        }

        o1(a b$a0) {
        }
    }

    public static final class o2 extends b {
        public o2() {
            super(17, 0x7F060050, 0x7F0400A0);  // string:action_screenshot "Screenshot"
        }
    }

    public static final class o3 extends k3 {
        public o3(int v) {
            super(21, 0x7F060064, 0x7F0400C8, v);  // string:action_wifi "WIFI"
        }
    }

    public static final class a.b.o extends b implements u0 {
        public final int I;

        private a.b.o() {
            this(0);
        }

        private a.b.o(int v) {
            super(82, 0x7F06000B, 0x7F04000B);  // string:action_apps_drawer "Apps drawer"
            this.I = v;
        }

        a.b.o(int v, a b$a0) {
            this(v);
        }

        a.b.o(a b$a0) {
        }

        @Override  // a.b$u0
        public CharSequence a(Context context0, int v) {
            switch(v) {
                case 1: {
                    return context0.getText(0x7F0601F0);  // string:system_apps_drawer "System apps drawer"
                }
                case 2: {
                    return context0.getText(0x7F0600D9);  // string:download_apps_drawer "Download apps drawer"
                }
                default: {
                    return super.n(context0);
                }
            }
        }

        @Override  // a.b$u0
        public int b() {
            return 3;
        }

        @Override  // a.b$u0
        public b c(int v) {
            return new a.b.o(v);
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.a(context0, this.I);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }
    }

    public static final class p0 extends k3 {
        public p0(int v) {
            super(34, 0x7F060024, 0x7F040048, v);  // string:action_gesture_enabled "Gesture control"
        }
    }

    public static final class p1 extends k3 {
        public p1(int v) {
            super(22, 0x7F06003B, 0x7F040067, v);  // string:action_mobile_data "Mobile data"
        }
    }

    public static final class p2 extends b implements e0 {
        public final String I;
        public final String J;
        public final String K;
        private f.i L;

        private p2() {
            this(null, null, null);
        }

        p2(a b$a0) {
        }

        private p2(s s0) {
            this(s0.a(), s0.a(), s0.e());
        }

        p2(s s0, a b$a0) {
            this(s0);
        }

        public p2(String s, String s1, String s2) {
            super(88, 0x7F060051, 0x7F0400A5);  // string:action_set_variable "Set variable"
            this.L = null;
            this.I = s;
            this.J = s1;
            this.K = s2;
        }

        public int A(f.i.f i$f0) {
            int v1;
            f.t t0 = i$f0.b(this.I);
            if(t0 == null) {
                throw new RuntimeException("Variable not exists: " + this.I);
            }
            this.J.hashCode();
            switch(this.J) {
                case "+=": {
                    v1 = t0.a + this.z(i$f0);
                    break;
                }
                case "-=": {
                    v1 = t0.a - this.z(i$f0);
                    break;
                }
                case "=": {
                    int v = this.z(i$f0);
                    t0.a = v;
                    return v;
                }
                default: {
                    throw new RuntimeException("Invalid operator: " + this.J);
                }
            }
            t0.a = v1;
            return v1;
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.I != null ? this.I + ' ' + this.J + ' ' + this.K : super.n(context0);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.c(this.I).c(this.J).b(this.K);
        }

        private int z(f.i.f i$f0) {
            if(this.L == null) {
                this.L = f.i.d(this.K);
            }
            return this.L.a(i$f0);
        }
    }

    public static final class p3 extends k3 {
        public p3(int v) {
            super(27, 0x7F060065, 0x7F0400C9, v);  // string:action_wifi_ap "Wifi AP"
        }
    }

    static final class p extends b {
        private p() {
            super(41, 0x7F06000B, 0x7F04000B);  // string:action_apps_drawer "Apps drawer"
        }

        p(a b$a0) {
        }

        public a.b.o z() {
            return new a.b.o(null);
        }
    }

    public static final class a.b.q0 extends b implements u0 {
        public final int I;

        private a.b.q0(int v) {
            super(106, 0x7F060025, 0x7F040049);  // string:action_gesture_pointer "Gesture pointer"
            this.I = v;
        }

        a.b.q0(int v, a b$a0) {
            this(v);
        }

        @Override  // a.b$u0
        public CharSequence a(Context context0, int v) {
            return a.b.q0.z(context0, v);
        }

        @Override  // a.b$u0
        public int b() {
            return a.p.f.i.length;
        }

        @Override  // a.b$u0
        public b c(int v) {
            return new a.b.q0(v);
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.I == 0 ? super.n(context0) : super.n(context0) + ": " + a.b.q0.z(context0, this.I);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }

        public static CharSequence z(Context context0, int v) {
            int[] arr_v = a.p.f.i;
            if(v > arr_v.length) {
                v = 0;
            }
            return context0.getText(arr_v[v]);
        }
    }

    public static final class q1 extends b implements e0 {
        public final b[] I;
        static final int J = 40;

        static {
        }

        private q1() {
            this(null);
        }

        q1(a b$a0) {
        }

        private q1(s s0) {
            this(q1.z(s0));
        }

        q1(s s0, a b$a0) {
            this(s0);
        }

        public q1(List list0) {
            this(((b[])list0.toArray(new b[list0.size()])));
        }

        public q1(b[] arr_b) {
            super(0x3F, 0x7F06003C, 0x7F04006A);  // string:action_multi_action "Multi-action"
            this.I = arr_b;
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            if(this.I != null) {
                if(s.length() > q1.J) {
                    stringBuilder0.append(s);
                    stringBuilder0.append(super.n(context0));
                    return stringBuilder0;
                }
                for(int v = 0; v < this.I.length; ++v) {
                    if(v != 0) {
                        stringBuilder0.append('\n');
                    }
                    this.I[v].l(stringBuilder0, context0, s);
                }
            }
            return stringBuilder0;
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            return this.I != null && this.I.length != 0 ? new w(context0, this.I) : super.m(context0);
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            if(this.I != null && this.I.length != 0) {
                CharSequence charSequence0 = new StringBuilder();
                ((StringBuilder)charSequence0).append('[');
                b[] arr_b = this.I;
                for(int v = 0; v < arr_b.length; ++v) {
                    b b0 = arr_b[v];
                    ((StringBuilder)charSequence0).append(' ');
                    ((StringBuilder)charSequence0).append(b0.n(context0));
                }
                ((StringBuilder)charSequence0).append(" ]");
                return charSequence0;
            }
            return super.n(context0);
        }

        @Override  // a.b
        public Drawable o(Context context0) {
            return super.m(context0);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            b[] arr_b = this.I;
            if(arr_b != null) {
                s0.d(arr_b.length);
                b[] arr_b1 = this.I;
                for(int v = 0; v < arr_b1.length; ++v) {
                    s0.f(arr_b1[v]);
                }
                return;
            }
            s0.d(0);
        }

        private static b[] z(s s0) {
            int v = s0.h();
            b[] arr_b = new b[v];
            for(int v1 = 0; v1 < v; ++v1) {
                arr_b[v1] = s0.g();
            }
            return arr_b;
        }
    }

    public static final class q2 extends s2 {
        private q2(Intent intent0, String s, String s1, String s2) {
            super(69, intent0, s, s1, s2);
        }

        private q2(s s0) {
            this(q2.D(s0), Uri.decode(s0.a()), s0.a(), s0.a());
        }

        q2(s s0, a b$a0) {
            this(s0);
        }

        private static Intent D(s s0) {
            try {
                if(s0 instanceof f.h) {
                    int v = s0.h();
                    StringBuilder stringBuilder0 = new StringBuilder(s0.a());
                    for(int v1 = 0; v1 < v; ++v1) {
                        stringBuilder0.append(' ');
                        stringBuilder0.append(s0.a());
                    }
                    return Intent.parseUri(stringBuilder0.toString(), 0);
                }
                return Intent.parseUri(s0.a(), 0);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return null;
            }
        }

        @Override  // a.b$s2
        public void y(s s0) {
            String s3;
            s s2;
            try {
                String s1 = this.K.toUri(0);
                if(s0 instanceof f.h) {
                    int v1 = 0;
                    int v2;
                    for(int v = 0; (v2 = s1.indexOf(0x20, v)) != -1; v = v2 + 1) {
                        ++v1;
                    }
                    s2 = s0.d(this.z).d(v1).c(s1).c(Uri.encode(this.L)).c(this.M);
                }
                else {
                    s2 = s0.d(this.z).c(s1).c(Uri.encode(this.L)).c(this.M);
                }
                s3 = this.N;
                s2.c(s3);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    public static final class a.b.q extends b {
        public a.b.q() {
            super(0, 0x7F06000C, 0x7F04000E);  // string:action_as_default "Default"
        }
    }

    public static final class r0 extends k3 {
        public r0(int v) {
            super(0x1F, 0x7F060026, 0x7F04004A, v);  // string:action_gesture_record "Gesture record"
        }
    }

    public static final class r1 extends b {
        private r1() {
            super(67, 0x7F06003D, 0x7F04006B);  // string:action_next_app "Next app"
        }

        r1(a b$a0) {
        }
    }

    public static final class r2 extends s2 {
        private r2() {
            this(null, null, null, null);
        }

        r2(a b$a0) {
        }

        public r2(Intent intent0, String s, Intent.ShortcutIconResource intent$ShortcutIconResource0) {
            this(intent0, s, intent$ShortcutIconResource0.resourceName, intent$ShortcutIconResource0.packageName);
        }

        public r2(Intent intent0, String s, Bitmap bitmap0, Context context0) {
            this(intent0, s, v0.e(bitmap0), null);
            if(this.M != null) {
                j.k(context0, s2.O + this.M);
            }
        }

        public r2(Intent intent0, String s, String s1) {
            this(intent0, s, null, s1);
        }

        private r2(Intent intent0, String s, String s1, String s2) {
            super(78, intent0, s, s1, s2);
        }

        r2(Intent intent0, String s, String s1, String s2, a b$a0) {
            this(intent0, s, s1, s2);
        }

        private r2(s s0) {
            this(r2.E(s0), Uri.decode(s0.a()), s0.a(), s0.a());
        }

        r2(s s0, a b$a0) {
            this(s0);
        }

        private static Intent E(s s0) {
            try {
                return Intent.parseUri(s0.e(), 0);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return null;
            }
        }

        @Override  // a.b$s2
        public void y(s s0) {
            try {
                s0.d(this.z).b((this.K == null ? null : this.K.toUri(0))).c(Uri.encode(this.L)).c(this.M).c(this.N);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    public static final class r extends k3 {
        public r(int v) {
            super(19, 0x7F06000D, 0x7F04000F, v);  // string:action_auto_brightness "Auto brightness"
        }
    }

    public static final class s0 extends b {
        public final int I;
        public final String J;
        public final int K;
        public static final String L;
        public static final String M;
        public static final String N;
        public static final String O;

        static {
            s0.L = l.k + "KEYCODE";
            s0.M = l.k + "WIFI_LIST";
            s0.N = l.k + "VOLUME_STREAM_ALIAS";
            s0.O = l.k + "LOGS";
        }

        private s0(int v, String s, int v1) {
            super(0xFFFF, 0, 0);
            this.I = v;
            this.J = s;
            this.K = v1;
        }

        private s0(s s0) {
            this(s0.h(), s0.a(), s0.h());
        }

        s0(s s0, a b$a0) {
            this(s0);
        }

        public static void A(Context context0, String s) {
            s0.E(context0, 27, "[u" + Process.myUid() + "]" + s, 0);
        }

        public static void B(Context context0, int v) {
            s0.E(context0, v, null, 0);
        }

        public static void C(Context context0, int v, int v1) {
            s0.E(context0, v, null, v1);
        }

        public static void D(Context context0, int v, String s) {
            s0.E(context0, v, s, 0);
        }

        public static void E(Context context0, int v, String s, int v1) {
            b.t(context0, new s0(v, s, v1));
        }

        public static void F(Context context0, int v, boolean z) {
            s0.E(context0, v, null, ((int)z));
        }

        public static void G(Context context0, int v, String s) {
            b.u(context0, new s0(v, s, 0), f.o0.m());
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return "";
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I).c(this.J).d(this.K);
        }

        public boolean z() {
            return this.K != 0;
        }
    }

    public static final class s1 extends k3 {
        public s1(int v) {
            super(25, 0x7F06003E, 0x7F04006C, v);  // string:action_nfc "NFC"
        }
    }

    public static class s2 extends x {
        public final Intent K;
        protected final String L;
        protected String M;
        protected String N;
        protected static final String O;

        static {
            s2.O = v0.a;
        }

        protected s2(int v, Intent intent0, String s, String s1, String s2) {
            super(v, 0x7F060052, 0x7F0400A7);  // string:action_shortcut "Shortcut"
            this.K = intent0;
            this.L = s;
            this.M = s1;
            this.N = s2;
            if(s2 == null && s1 != null) {
                v0.b(s1);
            }
        }

        private s2(Intent intent0, String s, String s1, String s2) {
            this(49, intent0, s, s1, s2);
        }

        private s2(s s0) {
            this(s2.B(s0), Uri.decode(s0.a()), s0.a(), s0.a());
        }

        s2(s s0, a b$a0) {
            this(s0);
        }

        @Override  // a.b$x
        protected CharSequence A(Context context0) {
            Intent intent0 = this.K;
            if(intent0 != null) {
                ComponentName componentName0 = intent0.getComponent();
                if(componentName0 != null) {
                    try {
                        return z.K(context0).u(componentName0);
                    }
                    catch(Throwable unused_ex) {
                        return componentName0.flattenToShortString();
                    }
                }
            }
            CharSequence charSequence0 = this.N;
            if(charSequence0 == null) {
                Intent intent1 = this.K;
                charSequence0 = intent1 == null ? null : intent1.getPackage();
            }
            if(charSequence0 != null) {
                try {
                    return z.K(context0).G(((String)charSequence0));
                }
                catch(Throwable unused_ex) {
                }
            }
            return charSequence0;
        }

        protected static Intent B(s s0) {
            try {
                return Intent.parseUri(s0.a(), 0);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return null;
            }
        }

        public r2 C() {
            return new r2(this.K, this.L, this.M, this.N, null);
        }

        @Override  // a.b
        protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
            if(this.K == null) {
                return super.l(stringBuilder0, context0, s);
            }
            stringBuilder0.append(s);
            stringBuilder0.append(this.n(context0));
            stringBuilder0.append('\n');
            stringBuilder0.append(s);
            stringBuilder0.append("    ");
            stringBuilder0.append(this.K);
            return stringBuilder0;
        }

        @Override  // a.b$x
        public CharSequence n(Context context0) {
            return this.L == null ? super.n(context0) : this.L;
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            try {
                s0.c(this.K.toUri(0)).c(Uri.encode(this.L)).c(this.M).c(this.N);
            }
            catch(Throwable throwable0) {
                Intent intent0 = this.K;
                if(intent0 != null) {
                    v.e(new Object[]{intent0});
                }
                v.d(throwable0);
            }
        }

        @Override  // a.b$x
        protected Drawable z(Context context0) {
            if(this.N != null) {
                if(this.M != null) {
                    try {
                        Resources resources0 = context0.getPackageManager().getResourcesForApplication(this.N);
                        int v = resources0.getIdentifier(this.M, "drawable", null);
                        if(v != 0) {
                            Drawable drawable0 = resources0.getDrawable(v);
                            if(drawable0 != null) {
                                return new g.g(drawable0);
                            }
                        }
                    }
                    catch(Throwable unused_ex) {
                    }
                }
                return z.K(context0).C(this.N);
            }
            if(this.M != null) {
                Drawable drawable1 = q.h((s2.O + this.M));
                if(drawable1 != null) {
                    return drawable1;
                }
            }
            return this.K == null ? null : z.K(context0).q(this.K);
        }
    }

    public static final class a.b.s extends k3 {
        public a.b.s(int v) {
            super(20, 0x7F06000E, 0x7F040010, v);  // string:action_auto_rotation "Auto rotation"
        }
    }

    public static final class t0 extends b {
        private t0() {
            super(3, 0x7F060027, 0x7F040050);  // string:action_home "Home"
        }

        t0(a b$a0) {
        }
    }

    public static final class t1 extends b {
        public t1() {
            super(1, 0x7F06003F, 0x7F04006D);  // string:action_none "Noop"
        }
    }

    public static final class t2 extends b {
        private t2() {
            super(40, 0x7F060053, 0x7F0400AA);  // string:action_shortcuts_panel "Shortcuts panel"
        }

        t2(a b$a0) {
        }
    }

    public static final class a.b.t extends b {
        private a.b.t() {
            super(2, 0x7F06000F, 0x7F040011);  // string:action_back "Back"
        }

        a.b.t(a b$a0) {
        }
    }

    public interface u0 {
        CharSequence a(Context arg1, int arg2);

        int b();

        b c(int arg1);
    }

    public static final class u1 extends b {
        public final int I;
        public final String J;

        private u1() {
            this(0, null);
        }

        public u1(int v, String s) {
            super(109, 0x7F060040, 0x7F04006F);  // string:action_notify "Notification"
            this.I = v;
            this.J = s;
        }

        u1(a b$a0) {
        }

        private u1(s s0) {
            this(s0.h(), s0.e());
        }

        u1(s s0, a b$a0) {
            this(s0);
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.J == null ? super.n(context0) : super.n(context0) + ": " + this.J;
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I).b(this.J);
        }
    }

    public static final class u2 extends b {
        public final String I;

        private u2() {
            this(null);
        }

        u2(a b$a0) {
        }

        private u2(s s0) {
            this(s0.e());
        }

        u2(s s0, a b$a0) {
            this(s0);
        }

        public u2(String s) {
            super(0x6F, 0x7F060060, 0x7F0400BD);  // string:action_toast "Toast"
            this.I = s;
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.I == null ? super.n(context0) : super.n(context0) + ": " + this.I;
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.b(this.I);
        }
    }

    public static final class u extends k3 {
        public u(int v) {
            super(24, 0x7F060010, 0x7F040013, v);  // string:action_bluetooth "Bluetooth"
        }
    }

    public static class v0 {
        static final String a;
        private static String[] b;

        static {
            v0.a = l.l + "icons/";
            v0.b = null;
        }

        public static void a() {
            String[] arr_s = new File(v0.a).list();
            if(arr_s != null && arr_s.length > 0) {
                v0.b = arr_s;
            }
        }

        static void b(String s) {
            if(s == null) {
                return;
            }
            String[] arr_s = v0.b;
            if(arr_s != null) {
                for(int v = 0; v < arr_s.length; ++v) {
                    String s1 = arr_s[v];
                    if(s1 != null && s1.equals(s)) {
                        arr_s[v] = null;
                        return;
                    }
                }
            }
        }

        private static String c() {
            String s;
            do {
                s = Integer.toHexString(f.p0.r()) + ".png";
            }
            while(new File(v0.a + s).exists());
            return s;
        }

        public static void d() {
            String[] arr_s = v0.b;
            if(arr_s != null) {
                v0.b = null;
                for(int v = 0; v < arr_s.length; ++v) {
                    String s = arr_s[v];
                    if(s != null) {
                        new File(v0.a + s).delete();
                    }
                }
            }
        }

        static String e(Bitmap bitmap0) {
            String s = v0.a;
            File file0 = new File(s);
            if(!file0.exists() && (!file0.mkdir() || !file0.setExecutable(true, false))) {
                return null;
            }
            String s1 = v0.c();
            File file1 = new File(s + s1);
            try(FileOutputStream fileOutputStream0 = new FileOutputStream(file1)) {
                bitmap0.compress(Bitmap.CompressFormat.PNG, 100, fileOutputStream0);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return null;
            }
            file1.setReadable(true, false);
            return s1;
        }

        public static void f() {
            v0.b = null;
        }
    }

    static final class v1 extends b {
        public final int I;
        public final String J;

        private v1(int v, String s) {
            super(57, 0x7F060040, 0x7F04006F);  // string:action_notify "Notification"
            this.I = v;
            this.J = s;
        }

        private v1(s s0) {
            this(s0.h(), v1.A(s0));
        }

        v1(s s0, a b$a0) {
            this(s0);
        }

        private static String A(s s0) {
            String s1 = s0.a();
            return s1 == null ? "" : Uri.decode(s1);
        }

        private u1 B() {
            return new u1(this.I, this.J);
        }
    }

    static final class v2 extends b {
        public final String I;

        private v2(s s0) {
            this(v2.A(s0));
        }

        v2(s s0, a b$a0) {
            this(s0);
        }

        private v2(String s) {
            super(91, 0x7F060060, 0x7F0400BD);  // string:action_toast "Toast"
            this.I = s;
        }

        private static String A(s s0) {
            String s1 = s0.a();
            return s1 == null ? "" : Uri.decode(s1);
        }

        private u2 B() {
            return new u2(this.I);
        }
    }

    public static final class a.b.v extends b {
        private a.b.v() {
            super(14, 0x7F060011, 0x7F040014);  // string:action_brightness_down "Brightness down"
        }

        a.b.v(a b$a0) {
        }
    }

    public static final class w0 extends k3 {
        public w0(int v) {
            super(29, 0x7F060028, 0x7F040051, v);  // string:action_immersive_nav_bar "Hide navigation bar"
        }
    }

    public static final class w1 extends b {
        public w1() {
            super(39, 0x7F060041, 0x7F040081);  // string:action_pen "Pen"
        }
    }

    public static final class w2 extends b {
        private w2() {
            super(46, 0x7F060054, 0x7F0400AC);  // string:action_shutdown "Shutdown"
        }

        w2(a b$a0) {
        }
    }

    public static final class a.b.w extends b {
        private a.b.w() {
            super(13, 0x7F060012, 0x7F040015);  // string:action_brightness_up "Brightness up"
        }

        a.b.w(a b$a0) {
        }
    }

    public static final class x0 extends k3 {
        public x0(int v) {
            super(73, 0x7F060029, 0x7F040052, v);  // string:action_immersive_status_bar "Hide status bar"
        }
    }

    public static final class x1 extends b {
        private x1() {
            super(51, 0x7F060042, 0x7F040082);  // string:action_pie "Pie"
        }

        x1(a b$a0) {
        }
    }

    public static final class x2 extends b implements u0 {
        public final int I;

        private x2(int v) {
            super(65, 0x7F060055, 0x7F04005F);  // string:action_side_bar "Side bar"
            this.I = v;
        }

        x2(int v, a b$a0) {
            this(v);
        }

        @Override  // a.b$u0
        public CharSequence a(Context context0, int v) {
            return v == 1 ? context0.getText(0x7F0601BB) : context0.getText(0x7F0601BA);  // string:side_right "Right side"
        }

        @Override  // a.b$u0
        public int b() {
            return 2;
        }

        @Override  // a.b$u0
        public b c(int v) {
            return new x2(v);
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            switch(this.I) {
                case 0: {
                    return context0.getResources().getDrawable(0x7F04005F);  // drawable:ic_left_side_bar
                }
                case 1: {
                    return context0.getResources().getDrawable(0x7F040093);  // drawable:ic_right_side_bar
                }
                default: {
                    return super.m(context0);
                }
            }
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            switch(this.I) {
                case 0: {
                    return context0.getText(0x7F060147);  // string:left_side_bar "Left side bar"
                }
                case 1: {
                    return context0.getText(0x7F060185);  // string:right_side_bar "Right side bar"
                }
                default: {
                    return super.n(context0);
                }
            }
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }
    }

    public static abstract class x extends b {
        private CharSequence I;
        private Locale J;

        x(int v, int v1, int v2) {
            super(v, v1, v2);
        }

        protected abstract CharSequence A(Context arg1);

        @Override  // a.b
        public Drawable m(Context context0) {
            try {
                Drawable drawable0 = this.z(context0);
                if(drawable0 != null) {
                    return drawable0;
                }
            }
            catch(Throwable unused_ex) {
            }
            return super.m(context0);
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            CharSequence charSequence0 = this.I;
            Locale locale0 = this.J;
            Locale locale1 = Locale.getDefault();
            if(charSequence0 != null && locale1.equals(locale0)) {
                return charSequence0;
            }
            try {
                charSequence0 = this.A(context0);
                this.I = charSequence0;
                this.J = locale1;
            }
            catch(Throwable unused_ex) {
            }
            return charSequence0 == null ? super.n(context0) : charSequence0;
        }

        protected abstract Drawable z(Context arg1);
    }

    public static final class y0 extends b {
        private final String I;
        private final boolean J;
        public static final String K;
        public static final String L;
        private static f.n M;
        private static boolean N;
        private static List O;

        static {
            y0.K = l.j + ".GESTURE_RECORDS_CHANGES";
            y0.L = l.n;
            y0.M = null;
            y0.N = false;
        }

        private y0() {
            this(null, false);
        }

        y0(a b$a0) {
        }

        public y0(Context context0, List list0, boolean z) {
            this(y0.N(context0, list0, z), z);
        }

        private y0(s s0) {
            this(s0.a(), (s0.h() & 1) != 0);
        }

        y0(s s0, a b$a0) {
            this(s0);
        }

        private y0(String s, boolean z) {
            super(53, 0x7F06002B, 0x7F040047);  // string:action_inject_gesture "Inject gesture"
            this.J = z;
            this.I = s;
        }

        public String A() {
            String s = y0.B().b(this.I);
            return s == null ? "-" : s.substring(1);
        }

        private static f.n B() {
            synchronized(y0.class) {
                if(y0.M == null) {
                    y0.M = new f.n(y0.L);
                }
                return y0.M;
            }
        }

        public int C() {
            List list0 = this.D();
            return list0 == null ? 0 : ((g.q)list0.get(list0.size() - 1)).z;
        }

        public List D() {
            if(this.I == null) {
                v.c("File of gesture: null");
                return null;
            }
            return q.g((y0.L + this.I));
        }

        public static List E() {
            f.n n0 = y0.B();
            Set set0 = n0.c();
            List list0 = new ArrayList(set0.size());
            for(Object object0: set0) {
                ((ArrayList)list0).add(new y0(((String)object0), n0.b(((String)object0)).charAt(0) == 108));
            }
            return list0;
        }

        public boolean F() {
            return this.J;
        }

        // 检测为 Lambda 实现
        private static void G(Context context0) [...]

        public static void H() {
            y0.N = true;
        }

        public static void I(y0 b$y00) {
            if(b$y00 != null && b$y00.I != null) {
                y0.B().d(b$y00.I);
            }
        }

        public static void J(y0 b$y00) {
            if(b$y00 != null && b$y00.I != null) {
                y0.B().e(b$y00.I);
            }
        }

        public static void K() {
            synchronized(y0.class) {
                y0.M = null;
            }
        }

        public static void L() {
            y0.B().f();
        }

        public static void M(y0 b$y00) {
            if(b$y00 != null && b$y00.I != null) {
                y0.B().g(b$y00.I);
            }
        }

        private static String N(Context context0, List list0, boolean z) {
            String s = y0.B().a(((char)(z ? 108 : 0x70)) + f.p0.f());
            String s1 = y0.L + s;
            f.p p0 = new f.p(s1);
            p0.F();
            for(Object object0: list0) {
                ((g.q)object0).b(p0);
                p0.C();
            }
            p0.n();
            j.k(context0, s1);
            return s;
        }

        public static void O(Context context0, List list0, boolean z) {
            if(y0.N) {
                if(y0.O == null) {
                    y0.O = new ArrayList();
                }
                y0.O.add(new Pair(list0, Boolean.valueOf(z)));
                return;
            }
            y0.N(context0, list0, z);
        }

        public void P(String s) {
            y0.B().i(this.I, ((char)(this.J ? 108 : 0x70)) + s);
        }

        public static void Q(Context context0) {
            y0.N = false;
            if(y0.O == null) {
                return;
            }
            f.h0.a().post(() -> if(!y0.N) {
                List list0 = y0.O;
                if(list0 != null) {
                    int v = list0.size();
                    for(int v1 = 0; v1 < v; ++v1) {
                        try {
                            Pair pair0 = (Pair)y0.O.get(v1);
                            y0.N(context0, ((List)pair0.first), ((Boolean)pair0.second).booleanValue());
                        }
                        catch(Throwable throwable0) {
                            v.d(throwable0);
                        }
                    }
                    y0.O = null;
                }
            });
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.I != null ? super.n(context0) + ": " + this.A() : super.n(context0);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.c(this.I).d(((int)this.J));
        }
    }

    public static final class y1 extends b {
        public final String I;
        public final int J;
        public final boolean K;
        private CharSequence L;

        private y1() {
            this(null, 0, false);
        }

        y1(a b$a0) {
        }

        private y1(s s0) {
            this(s0.e(), s0.h(), s0.h() == 1);
        }

        y1(s s0, a b$a0) {
            this(s0);
        }

        public y1(String s, int v, boolean z) {
            super(93, 0x7F060043, 0x7F040084);  // string:action_play_sound "Play sound"
            this.L = null;
            this.I = s;
            this.J = v;
            this.K = z;
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            if(this.I != null && this.I.length() == 0) {
                return context0.getResources().getDrawable(0x7F0400B3);  // drawable:ic_stop_play_sound
            }
            return this.K ? context0.getResources().getDrawable(0x7F040085) : super.m(context0);  // drawable:ic_play_sound_repeat
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            String s = this.I;
            if(s == null) {
                return super.n(context0);
            }
            if(s.length() == 0) {
                return context0.getText(0x7F0601E8);  // string:stop_play_sound "Stop play sound"
            }
            if(this.L == null) {
                try {
                    this.L = RingtoneManager.getRingtone(context0, Uri.parse(this.I)).getTitle(context0);
                    return super.n(context0) + ": " + this.L;
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                    this.L = "-";
                    return super.n(context0) + ": " + this.L;
                }
            }
            return super.n(context0) + ": " + this.L;
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.b(this.I).d(this.J).d(((int)this.K));
        }
    }

    public static final class y2 extends b implements u0 {
        public final int I;

        private y2() {
            this(0);
        }

        public y2(int v) {
            super(6, 0x7F060056, 0x7F0400AD);  // string:action_sleep_wake "Sleep/wake up"
            this.I = v;
        }

        y2(a b$a0) {
        }

        @Override  // a.b$u0
        public CharSequence a(Context context0, int v) {
            switch(v) {
                case 1: {
                    return context0.getText(0x7F0601EB);  // string:sub_action_sleep "Sleep"
                }
                case 2: {
                    return context0.getText(0x7F0601ED);  // string:sub_action_wake_up "Wake up"
                }
                default: {
                    return context0.getText(0x7F0601EC);  // string:sub_action_switch "Switch"
                }
            }
        }

        @Override  // a.b$u0
        public int b() {
            return 3;
        }

        @Override  // a.b$u0
        public b c(int v) {
            return new y2(v);
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            int v = this.I;
            if(v == 1) {
                return context0.getText(0x7F0601EB);  // string:sub_action_sleep "Sleep"
            }
            return v == 2 ? context0.getText(0x7F0601ED) : context0.getText(0x7F060056);  // string:sub_action_wake_up "Wake up"
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }
    }

    public static final class y extends b {
        private y() {
            super(38, 0x7F060013, 0x7F040017);  // string:action_clear_background_apps "Kill background apps"
        }

        y(a b$a0) {
        }
    }

    public static final class z0 extends b {
        public final int I;
        public final int J;
        public final int K;

        private z0() {
            this(0, 0, 0);
        }

        public z0(int v, int v1, int v2) {
            super(105, 0x7F06002C, 0x7F040053);  // string:action_inject_key_event "Inject key event"
            this.I = v;
            this.J = v1;
            this.K = v2;
        }

        z0(a b$a0) {
        }

        private z0(s s0) {
            this(s0.h(), s0.h(), s0.h());
        }

        z0(s s0, a b$a0) {
            this(s0);
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            CharSequence charSequence0;
            int v = this.I;
            if(v != 0) {
                String s = KeyEvent.keyCodeToString(v).substring(8);
                switch(this.J) {
                    case 0: {
                        charSequence0 = context0.getString(0x7F060134, new Object[]{s});  // string:key_click_f "Click %1$s"
                        break;
                    }
                    case 1: {
                        charSequence0 = context0.getString(0x7F060136, new Object[]{s});  // string:key_double_click_f "Double click %1$s"
                        break;
                    }
                    case 2: {
                        charSequence0 = context0.getString(0x7F06013C, new Object[]{s});  // string:key_long_press_f "Long press %1$s"
                        break;
                    }
                    default: {
                        return super.n(context0);
                    }
                }
                if(this.K == 0) {
                    return charSequence0;
                }
                StringBuilder stringBuilder0 = new StringBuilder(charSequence0);
                if((this.K & 1) != 0) {
                    stringBuilder0.append(" + Shift");
                }
                if((this.K & 2) != 0) {
                    stringBuilder0.append(" + Alt");
                }
                if((this.K & 0x1000) != 0) {
                    stringBuilder0.append(" + Ctrl");
                }
                if((this.K & 0x10000) != 0) {
                    stringBuilder0.append(" + Meta");
                }
                if((this.K & 8) != 0) {
                    stringBuilder0.append(" + Fn");
                }
                if((this.K & 4) != 0) {
                    stringBuilder0.append(" + Sym");
                }
                return stringBuilder0.toString();
            }
            return super.n(context0);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I).d(this.J).d(this.K);
        }
    }

    public static final class z1 extends b {
        private z1() {
            super(43, 0x7F060044, 0x7F040087);  // string:action_power_menu "Power menu"
        }

        z1(a b$a0) {
        }
    }

    public static final class z2 extends b implements u0 {
        public final int I;

        private z2(int v) {
            super(68, 0x7F060057, 0x7F0400B8);  // string:action_slide_adjust "Slide adjust"
            this.I = v;
        }

        z2(int v, a b$a0) {
            this(v);
        }

        @Override  // a.b$u0
        public CharSequence a(Context context0, int v) {
            switch(v) {
                case 0: {
                    return context0.getText(0x7F060070);  // string:adjust_screen_filter_alpha "Screen filter alpha"
                }
                case 1: {
                    return context0.getText(0x7F06006D);  // string:adjust_brightness "Brightness"
                }
                case 13: {
                    return context0.getText(0x7F06006E);  // string:adjust_caret "Cursor"
                }
                default: {
                    return q0.d(context0, v - 3);
                }
            }
        }

        @Override  // a.b$u0
        public int b() {
            return 14;
        }

        @Override  // a.b$u0
        public b c(int v) {
            return new z2(v);
        }

        @Override  // a.b
        public Drawable m(Context context0) {
            switch(this.I) {
                case 0: {
                    return context0.getResources().getDrawable(0x7F0400BA);  // drawable:ic_swipe_adjust_screen_filter
                }
                case 1: {
                    return context0.getResources().getDrawable(0x7F0400B9);  // drawable:ic_swipe_adjust_brightness
                }
                case 2: 
                case 3: 
                case 4: 
                case 5: 
                case 6: 
                case 7: 
                case 8: 
                case 9: 
                case 10: 
                case 11: 
                case 12: {
                    return context0.getResources().getDrawable(0x7F0400BB);  // drawable:ic_swipe_adjust_volume
                }
                case 13: {
                    return context0.getResources().getDrawable(0x7F040016);  // drawable:ic_caret
                }
                default: {
                    return super.m(context0);
                }
            }
        }

        @Override  // a.b
        public CharSequence n(Context context0) {
            return this.I == -1 ? super.n(context0) : super.n(context0) + " " + this.a(context0, this.I);
        }

        @Override  // a.b
        public void y(s s0) {
            super.y(s0);
            s0.d(this.I);
        }
    }

    public static final class a.b.z extends b {
        private a.b.z() {
            super(90, 0x7F060014, 0x7F040018);  // string:action_clipboard "Clipboard"
        }

        a.b.z(a b$a0) {
        }
    }

    final int A;
    public final int B;
    public static final String C;
    public static final Parcelable.Creator CREATOR;
    public static final String D;
    private static a.b.q E;
    private static t1 F;
    private static final b[] G;
    private static b[] H;
    public final int z;

    static {
        b.C = l.k + "PERFORM_ACTION";
        b.D = l.k + "PERFORM";
        b.G = new b[0];
        b.H = null;
        b.CREATOR = new a();
    }

    protected b(int v, int v1, int v2) {
        this.z = v;
        this.A = v1;
        this.B = v2;
    }

    @Override  // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    public static a.b.q e() {
        if(b.E == null) {
            b.E = new a.b.q();
        }
        return b.E;
    }

    public static b f(String s) {
        if(s == null) {
            return b.e();
        }
        byte[] arr_b = f.p0.g(s);
        f.h h0 = new f.h();
        h0.i(arr_b);
        b b0 = h0.g();
        h0.m();
        return b0;
    }

    public static String g(b b0) {
        String s = b.h(b0);
        return "am broadcast -a " + b.D + " -e " + "data" + ' ' + s;
    }

    public static String h(b b0) {
        return f.p0.i(new f.h().j().x(b0).o());
    }

    public static b[] i(int v) {
        switch(v) {
            case 1: {
                return b.w(new int[]{51, 68, 106, 107, 54, 102});
            }
            case 2: {
                return b.w(new int[]{54, 102});
            }
            case 3: {
                return b.w(new int[]{0, 1, 51, 68, 106, 107, 86, 78, 54, 102});
            }
            case 4: {
                return b.w(new int[]{0, 1, 51, 68, 106, 107, 86});
            }
            case 0: 
            case 5: {
                return b.w(null);
            }
            case 6: {
                return b.w(new int[]{0, 51, 68, 106, 107, 54, 102});
            }
            case 7: {
                return b.w(new int[]{0, 1, 0x4F});
            }
            default: {
                v.c("Unknown flag!");
                return b.G;
            }
        }
    }

    private static b[] j() {
        if(b.H == null) {
            b[] arr_b = new b[89];
            arr_b[0] = b.e();
            arr_b[1] = b.r();
            arr_b[2] = new h3();
            arr_b[3] = new a.b.t(null);
            arr_b[4] = new t0(null);
            arr_b[5] = new d2(null);
            arr_b[6] = new o1(null);
            arr_b[7] = new y2(null);
            arr_b[8] = new h0();
            arr_b[9] = new j1(-1, null);
            arr_b[10] = new i0(null);
            arr_b[11] = new j0(null);
            int v = Build.VERSION.SDK_INT;
            arr_b[12] = v < 24 ? null : new e3();
            n2 b$n20 = v >= 24 ? new n2(0) : new o2();
            arr_b[13] = b$n20;
            arr_b[14] = new k0(null);
            arr_b[15] = new h2(null);
            arr_b[16] = new a3(0);
            arr_b[17] = new a.b.e0(-1, null);
            arr_b[18] = new a.b.z(null);
            arr_b[19] = new m3();
            arr_b[20] = new g2(null);
            arr_b[21] = new r(0);
            arr_b[22] = new a.b.s(0);
            arr_b[23] = new o3(0);
            arr_b[24] = new p1(0);
            arr_b[25] = new i1(0);
            arr_b[26] = new u(0);
            arr_b[27] = new s1(0);
            arr_b[28] = new j3(0);
            arr_b[29] = new p3(0);
            arr_b[30] = new k(0);
            arr_b[0x1F] = new x0(0);
            arr_b[0x20] = new w0(0);
            arr_b[33] = new d1(0);
            arr_b[34] = new l3(0);
            arr_b[35] = new m2(0);
            arr_b[36] = new r0(0);
            arr_b[37] = new p0(0);
            arr_b[38] = new e1(0);
            arr_b[39] = new o0(0);
            arr_b[40] = new h1(null);
            arr_b[41] = new a2(null);
            arr_b[42] = new r1(null);
            arr_b[43] = new l0(null);
            arr_b[44] = new g1(null);
            arr_b[45] = new f1(null);
            arr_b[46] = new a.b.n(null);
            arr_b[0x2F] = new y(null);
            arr_b[0x30] = new e2(null);
            arr_b[49] = new z1(null);
            arr_b[50] = new c2(null);
            arr_b[51] = new b3(null);
            arr_b[52] = new w2(null);
            arr_b[53] = new t2(null);
            arr_b[54] = new f0(null);
            arr_b[55] = new a.b.o(null);
            arr_b[56] = new n0(null);
            arr_b[57] = new i2(null);
            arr_b[58] = new x2(-1, null);
            arr_b[59] = new m0();
            arr_b[60] = new z0(null);
            arr_b[61] = new y0(null);
            arr_b[62] = new b1(null);
            arr_b[0x3F] = new n3(null);
            arr_b[0x40] = new y1(null);
            arr_b[65] = new u1(null);
            arr_b[66] = new u2(null);
            arr_b[67] = new c3(null);
            arr_b[68] = new b2();
            arr_b[69] = new w1();
            arr_b[70] = new x1(null);
            arr_b[71] = new z2(-1, null);
            arr_b[72] = new a.b.q0(0, null);
            arr_b[73] = new i3(null);
            arr_b[74] = new i(null);
            arr_b[75] = new a.b.l(null);
            arr_b[76] = new e(null);
            arr_b[77] = new r2(null);
            arr_b[78] = new g0(null);
            arr_b[0x4F] = new a0(null);
            arr_b[80] = new p2(null);
            arr_b[81] = new a.b.b(null);
            arr_b[82] = new g3(null);
            arr_b[83] = new f2(null);
            arr_b[84] = new a.b.d0(null);
            arr_b[85] = new q1(null);
            arr_b[86] = new k2(null);
            arr_b[87] = new c(null);
            arr_b[88] = new d(null);
            b.H = arr_b;
        }
        return b.H;
    }

    public final StringBuilder k(Context context0) {
        return this.l(new StringBuilder(), context0, "");
    }

    protected StringBuilder l(StringBuilder stringBuilder0, Context context0, String s) {
        stringBuilder0.append(s);
        stringBuilder0.append(this.n(context0));
        return stringBuilder0;
    }

    public Drawable m(Context context0) {
        return context0.getResources().getDrawable(this.B);
    }

    public CharSequence n(Context context0) {
        return context0.getText(this.A);
    }

    public Drawable o(Context context0) {
        return this.m(context0);
    }

    public CharSequence p(Context context0) {
        return context0.getText(this.A);
    }

    public static t1 r() {
        if(b.F == null) {
            b.F = new t1();
        }
        return b.F;
    }

    public static b s(s s0) {
        int v = s0.h();
        if(v != 0xFFFF) {
            switch(v) {
                case 0: {
                    return b.e();
                }
                case 1: {
                    return b.r();
                }
                case 2: {
                    return new a.b.t(null);
                }
                case 3: {
                    return new t0(null);
                }
                case 4: {
                    return new d2(null);
                }
                case 5: {
                    return new o1(null);
                }
                case 6: {
                    return new y2(s0.h());
                }
                case 7: {
                    return new a.b.j(-1, -1);
                }
                case 8: {
                    return new a.b.j(-1, -2);
                }
                case 9: {
                    return new l1(null);
                }
                case 10: {
                    return new n1(null);
                }
                case 11: {
                    return new m1(null);
                }
                case 12: {
                    return new k1(null);
                }
                case 13: {
                    return new a.b.w(null);
                }
                case 14: {
                    return new a.b.v(null);
                }
                case 15: {
                    return new i0(null);
                }
                case 16: {
                    return new j0(null);
                }
                case 17: {
                    return new o2();
                }
                case 18: {
                    return new g2(null);
                }
                case 19: {
                    return new r(s0.h());
                }
                case 20: {
                    return new a.b.s(s0.h());
                }
                case 21: {
                    return new o3(s0.h());
                }
                case 22: {
                    return new p1(s0.h());
                }
                case 23: {
                    return new i1(s0.h());
                }
                case 24: {
                    return new u(s0.h());
                }
                case 25: {
                    return new s1(s0.h());
                }
                case 26: {
                    return new j3(s0.h());
                }
                case 27: {
                    return new p3(s0.h());
                }
                case 28: {
                    return new k(s0.h());
                }
                case 29: {
                    return new w0(s0.h());
                }
                case 30: {
                    return new d1(s0.h());
                }
                case 0x1F: {
                    return new r0(s0.h());
                }
                case 0x20: {
                    return new l3(s0.h());
                }
                case 33: {
                    return new m2(s0.h());
                }
                case 34: {
                    return new p0(s0.h());
                }
                case 35: {
                    return new e1(s0.h());
                }
                case 36: {
                    return new h1(null);
                }
                case 37: {
                    return new g1(null);
                }
                case 38: {
                    return new y(null);
                }
                case 39: {
                    return new w1();
                }
                case 40: {
                    return new t2(null);
                }
                case 41: {
                    return new p(null).z();
                }
                case 42: {
                    return new n0(null);
                }
                case 43: {
                    return new z1(null);
                }
                case 44: {
                    return new c2(null);
                }
                case 45: {
                    return new b3(null);
                }
                case 46: {
                    return new w2(null);
                }
                case 0x2F: {
                    return new m(s0, null);
                }
                case 0x30: {
                    return new e(s0, null);
                }
                case 49: {
                    return new s2(s0, null).C();
                }
                case 50: {
                    return new l2(s0.a(), null).z();
                }
                case 51: {
                    return new x1(null);
                }
                case 52: {
                    return new a1(s0, null).z();
                }
                case 53: {
                    return new y0(s0, null);
                }
                case 54: {
                    return new g0(s0, null);
                }
                case 55: {
                    return new i2(null);
                }
                case 56: {
                    return new c1(s0, null).A();
                }
                case 57: {
                    return new v1(s0, null).B();
                }
                case 58: {
                    return new d3(s0, null).B();
                }
                case 59: {
                    return new b2();
                }
                case 60: {
                    return new a.b.j(s0, null).A();
                }
                case 61: {
                    return new b0(s0, null);
                }
                case 62: {
                    return new a.b.d0(s0, null);
                }
                case 0x3F: {
                    return new q1(s0, null);
                }
                case 0x40: {
                    return new k0(s0.h());
                }
                case 65: {
                    return new x2(s0.h(), null);
                }
                case 66: {
                    return new a2(null);
                }
                case 67: {
                    return new r1(null);
                }
                case 68: {
                    return new z2(s0.h(), null);
                }
                case 69: {
                    return new q2(s0, null).C();
                }
                case 70: {
                    return new g2(s0.h(), null);
                }
                case 71: {
                    return new a.b.n(null);
                }
                case 72: {
                    return new d(s0, null);
                }
                case 73: {
                    return new x0(s0.h());
                }
                case 74: {
                    return new h2(s0.h(), null);
                }
                case 75: {
                    return new h0();
                }
                case 76: {
                    return new g(s0, null);
                }
                case 77: {
                    return new f1(s0.a());
                }
                case 78: {
                    return new r2(s0, null);
                }
                default: {
                    boolean z = false;
                    switch(v) {
                        case 80: {
                            return new a.b.q0(0, null);
                        }
                        case 81: {
                            return new e2(null);
                        }
                        case 82: {
                            return new a.b.o(s0.h(), null);
                        }
                        case 83: {
                            return new f0(s0.h());
                        }
                        case 84: {
                            return new k2(s0.h());
                        }
                        case 85: {
                            return new a3(s0.h());
                        }
                        case 86: {
                            int v1 = s0.h();
                            if(s0.h() != 0) {
                                z = true;
                            }
                            return new i3(v1, z);
                        }
                        case 87: {
                            return new a.b.e0(s0.h(), null);
                        }
                        case 88: {
                            return new p2(s0, null);
                        }
                        case 89: {
                            return new i(s0.h(), s0.h());
                        }
                        case 90: {
                            return new a.b.z(null);
                        }
                        case 91: {
                            return new v2(s0, null).B();
                        }
                        case 92: {
                            return new n3(s0.h());
                        }
                        case 93: {
                            return new y1(s0, null);
                        }
                        case 94: {
                            return new m0(s0.h());
                        }
                        case 0x5F: {
                            return new n2(s0.h());
                        }
                        case 0x60: {
                            return new e3();
                        }
                        case 97: {
                            return new o0(s0.h());
                        }
                        case 98: {
                            return new m3();
                        }
                        case 99: {
                            return new l0(null);
                        }
                        case 100: {
                            return new a0(s0, null);
                        }
                        case 101: {
                            return new f2(s0, null);
                        }
                        case 102: {
                            return new a.b.b(null);
                        }
                        case 104: {
                            return new j1(s0.h(), null);
                        }
                        case 105: {
                            return new z0(s0, null);
                        }
                        case 106: {
                            return new a.b.q0(s0.h(), null);
                        }
                        case 107: {
                            return new h3(s0, null);
                        }
                        case 108: {
                            return new b1(s0, null);
                        }
                        case 109: {
                            return new u1(s0, null);
                        }
                        case 110: {
                            return new c3(s0, null);
                        }
                        case 0x6F: {
                            return new u2(s0, null);
                        }
                        case 0x70: {
                            return new g3(null);
                        }
                        case 0x71: {
                            return new f(s0, null);
                        }
                        case 0x72: {
                            return new a.b.h(s0, null);
                        }
                        case 0x73: {
                            return new a.b.l(s0, null);
                        }
                        default: {
                            v.c(("Unknown action id: " + v));
                            return b.e();
                        }
                    }
                }
            }
        }
        return new s0(s0, null);
    }

    // 检测为 Lambda 实现
    public static void t(Context context0, b b0) [...]

    @Override
    public String toString() {
        return "Action<" + this.z + ">";
    }

    static void u(Context context0, b b0, int v) {
        switch(b0.z) {
            case 0: 
            case 1: 
            case 51: 
            case 54: 
            case 68: 
            case 106: 
            case 107: {
                break;
            }
            default: {
                Intent intent0 = new Intent(b.C).setPackage("android");
                new f.m(intent0).j().p(b0).l();
                if(v != 0) {
                    try {
                        context0.sendBroadcastAsUser(intent0, f.o0.r());
                        return;
                    }
                    catch(Throwable throwable0) {
                        v.c(throwable0.toString());
                    }
                }
                context0.sendBroadcast(intent0);
            }
        }
    }

    public static void v(Context context0, b b0, long v) {
        f.h0.a().postDelayed(() -> b.u(context0, b0, 0), v);
    }

    private static b[] w(int[] arr_v) {
        b[] arr_b = b.j();
        ArrayList arrayList0 = new ArrayList(arr_b.length);
        int v = 0;
        if(arr_v != null && arr_v.length != 0) {
            while(v < arr_b.length) {
                b b0 = arr_b[v];
                if(b0 != null && f.p0.a(arr_v, b0.z) == -1) {
                    arrayList0.add(b0);
                }
                ++v;
            }
            return (b[])arrayList0.toArray(new b[arrayList0.size()]);
        }
        while(v < arr_b.length) {
            b b1 = arr_b[v];
            if(b1 != null) {
                arrayList0.add(b1);
            }
            ++v;
        }
        return (b[])arrayList0.toArray(new b[arrayList0.size()]);
    }

    @Override  // android.os.Parcelable
    public void writeToParcel(Parcel parcel0, int v) {
        this.y(new d0(parcel0).j());
    }

    public static b x(b b0) {
        while(b0.z == 72) {
            b0 = ((d)b0).K;
        }
        return b0;
    }

    public void y(s s0) {
        s0.d(this.z);
    }
}


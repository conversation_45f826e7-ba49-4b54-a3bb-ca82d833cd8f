package d;

import a.b.s0;
import a.z.b;
import a.z;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.ListView;
import com.jozein.xedgepro.ApplicationMain;
import e.j.f;
import e.j.g;
import e.j.k;
import e.j;
import f.g0;
import f.l;
import f.o0;
import f.v;
import java.io.File;

public class p0 extends j {
    private g0 M;
    private Runnable N;
    private z O;
    private b P;
    private int[] Q;
    private BroadcastReceiver R;
    boolean S;
    private static final int[] T;
    private static final int[] U;
    private static boolean V;

    static {
        p0.T = new int[]{0x7F040048, 0x7F040057, 0x7F040082, 0x7F0400AA, 0x7F04002A, 0x7F04005F, 0x7F040093, 0x7F04008B, 0x7F04009E, 0x7F040009, 0x7F040069, 0x7F04009D, 0x7F04004B, 0x7F040003, 0x7F040044, 0x7F04004E, 0x7F040088, 0x7F040068};  // drawable:ic_gesture_enabled
        p0.U = new int[]{0x7F060116, 0x7F060142, 0x7F060173, 0x7F0601B2, 0x7F0600CA, 0x7F060147, 0x7F060185, 0x7F060202, 0x7F060198, 0x7F06007D, 0x7F060157, 0x7F060197, 0x7F060110, 0x7F060015, 0x7F060102, 0x7F060124, 0x7F06017B, 0x7F060156};  // string:gestures "Gestures"
        p0.V = false;
    }

    public p0() {
        this.N = null;
        this.P = null;
        this.R = null;
        this.S = false;
    }

    @Override  // e.j0$c
    protected void B() {
        super.B();
        CharSequence charSequence0 = this.u(l.y);
        int v = o0.m();
        if(v != 0) {
            charSequence0 = charSequence0 + " (" + v + ")";
        }
        this.d0(charSequence0);
        if(this.h().getBoolean("init", false)) {
            this.S(0x7F040002, (View view0) -> this.P(new e0(), 5));
        }
    }

    @Override  // e.j
    protected int B0() {
        return p0.U.length;
    }

    @Override  // e.j0$c
    protected void F(Intent intent0) {
        super.F(intent0);
        int v = intent0.getIntExtra("clicked", -1);
        if(v >= 0) {
            p0.V = true;
            this.k1(v);
        }
    }

    @Override  // e.j0$c
    protected void J(Bundle bundle0, int v) {
        e.j.j j$j0;
        boolean z = false;
        if(bundle0 == null) {
            return;
        }
        switch(v) {
            case 2: {
                if(bundle0.getBoolean("result", false)) {
                    this.a2();
                    return;
                }
                break;
            }
            case 3: {
                int v1 = bundle0.getInt("result");
                if(v1 <= 1) {
                    j$j0 = (e.j.j)this.L0(0);
                    if(v1 != 0) {
                        j$j0.setChecked(z);
                        return;
                    }
                    z = true;
                    j$j0.setChecked(z);
                    return;
                }
                break;
            }
            case 4: {
                int v2 = bundle0.getInt("result");
                if(v2 <= 1) {
                    j$j0 = (e.j.j)this.L0(1);
                    if(v2 == 0) {
                        z = true;
                    }
                    j$j0.setChecked(z);
                    return;
                }
                break;
            }
            case 5: {
                ListView listView0 = this.G0();
                if(listView0 != null) {
                    listView0.setSelectionFromTop(0, 0);
                    this.x1();
                    return;
                }
                break;
            }
        }
    }

    @Override  // e.j
    protected boolean N0(int v) {
        return v == 17;
    }

    private int[] Q1() {
        if(this.Q == null) {
            if(ApplicationMain.isModuleActivated()) {
                this.Q = l.r ? new int[]{0x7F060085, 0x7F06017E, 0x7F0601AF, 0x7F06011B, 0x7F060000, 0x7F06014B} : new int[]{0x7F060085, 0x7F06017E, 0x7F0601AF, 0x7F06011A, 0x7F06011B, 0x7F060000, 0x7F06014B};  // string:backup_restore "Backup/import"
                return this.Q;
            }
            this.Q = l.r ? new int[]{0x7F060085, 0x7F06017E, 0x7F0601AF, 0x7F06011B, 0x7F060000} : new int[]{0x7F060085, 0x7F06017E, 0x7F0601AF, 0x7F06011A, 0x7F06011B, 0x7F060000};  // string:backup_restore "Backup/import"
        }
        return this.Q;
    }

    // 检测为 Lambda 实现
    private void R1(CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void S1(CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void T1(View view0) [...]

    private void U1(CompoundButton compoundButton0, boolean z, int v) {
        if(!this.S) {
            this.S = true;
            if(!ApplicationMain.isModuleActivated()) {
                this.e0(0x7F060155);  // string:module_not_activated "Module not activated yet(or not updated)! Please activate 
                                      // this module in Xposed installer, and reboot."
                compoundButton0.setChecked(this.O.p(v));
                return;
            }
        }
        boolean z1 = this.O.p(v);
        if(z1 != z) {
            this.O.X(compoundButton0.getContext(), v, z);
        }
        compoundButton0.setChecked(z1);
        if(z1 && z && v == 1) {
            s0.C(compoundButton0.getContext(), 8, -1);
        }
    }

    protected k V1(int v) {
        int v1;
        switch(v) {
            case 0: {
                k j$k0 = new f(this, this.C0(p0.T[0]), this.u(0x7F060116), null, this.O.p(1));  // string:gestures "Gestures"
                ((e.j.j)j$k0).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> this.U1(compoundButton0, z, 1));
                return j$k0;
            }
            case 1: {
                k j$k1 = new f(this, this.C0(p0.T[1]), this.u(0x7F060142), null, this.O.p(0));  // string:keys "Keys"
                ((e.j.j)j$k1).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> this.U1(compoundButton0, z, 0));
                return j$k1;
            }
            case 7: {
                if(Build.VERSION.SDK_INT < 24) {
                    k j$k2 = new g(this, this.C0(p0.T[7]), this.u(p0.U[7]), this.u(0x7F0601BC));  // string:since_nougat "Since Android N"
                    j$k2.setEnabled(false);
                    return j$k2;
                }
                v1 = 0;
                break;
            }
            case 8: 
            case 9: 
            case 10: 
            case 11: {
                v1 = true ^ l.r;
                break;
            }
            default: {
                v1 = 0;
                break;
            }
        }
        if(v1 != 0) {
            k j$k3 = new g(this, this.C0(p0.T[v]), this.u(p0.U[v]), this.u(0x7F06017C));  // string:pro_only "Pro only"
            j$k3.setEnabled(false);
            return j$k3;
        }
        return new g(this, this.C0(p0.T[v]), this.u(p0.U[v]), null);
    }

    private void W1() {
        Context context0 = this.M0();
        int v = Build.VERSION.SDK_INT;
        if(v < 30 || Environment.isExternalStorageLegacy()) {
            if(v >= 23 && (context0.checkSelfPermission("android.permission.READ_EXTERNAL_STORAGE") != 0 || context0.checkSelfPermission("android.permission.WRITE_EXTERNAL_STORAGE") != 0)) {
                this.requestPermissions(new String[]{"android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"}, 1);
                return;
            }
        }
        else if(!Environment.isExternalStorageManager()) {
            String s = l.j;
            if(f.p0.q(context0, s, "android.permission.MANAGE_EXTERNAL_STORAGE")) {
                if(this.Y1()) {
                    try {
                        Intent intent0 = new Intent("android.settings.MANAGE_APP_ALL_FILES_ACCESS_PERMISSION");
                        intent0.setData(Uri.fromParts("package", s, null));
                        this.startActivityForResult(intent0, 2);
                    }
                    catch(Throwable throwable0) {
                        this.g0(throwable0);
                        this.P(new m(), 1);
                    }
                    return;
                }
                this.e0(0x7F060172);  // string:permission_denied "Permission denied!"
            }
        }
        this.P(new m(), 1);
    }

    public p0 X1(int v) {
        this.h().putInt("clicked", v);
        return this;
    }

    private boolean Y1() {
        try {
            int v = (int)new File(this.f().getApplicationInfo().sourceDir).lastModified();
            if(a.m.b().a(5) != v) {
                a.m.b().f(5, v);
                return true;
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        return false;
    }

    private void Z1(CharSequence charSequence0, CharSequence charSequence1, CharSequence charSequence2) {
        Intent intent0 = new Intent("android.intent.action.SEND");
        intent0.setType("text/plain");
        intent0.putExtra("android.intent.extra.SUBJECT", charSequence1);
        intent0.putExtra("android.intent.extra.TEXT", charSequence2);
        try {
            this.startActivity(Intent.createChooser(intent0, charSequence0));
        }
        catch(Throwable unused_ex) {
        }
    }

    private void a2() {
        if(Build.VERSION.SDK_INT >= 26) {
            return;
        }
        if(this.b2("de.robv.android.xposed.installer", 1)) {
            return;
        }
        if(this.b2("org.meowcat.edxposed.manager", 3)) {
            return;
        }
        this.b2("com.solohsu.android.edxp.manager", 1);
    }

    private boolean b2(String s, int v) {
        try {
            Intent intent0 = this.f().getPackageManager().getLaunchIntentForPackage(s);
            if(intent0 != null) {
                intent0.putExtra("fragment", v);
                intent0.putExtra("section", "modules");
                intent0.setFlags(0x30000000);
                this.startActivity(intent0);
                return true;
            }
        }
        catch(Throwable unused_ex) {
        }
        return false;
    }

    @Override  // e.j
    protected View h1(int v) {
        return this.V1(v);
    }

    @Override  // e.j
    protected View i1(int v) {
        int[] arr_v = this.Q1();
        CharSequence charSequence0 = this.u(arr_v[v]);
        switch(arr_v[v]) {
            case 0x7F060000: {  // string:about "About"
                return new g(this, this.C0(0x7F040001), charSequence0, null);  // drawable:ic_about
            }
            case 0x7F060085: {  // string:backup_restore "Backup/import"
                if(l.r) {
                    return new g(this, this.C0(0x7F040012), charSequence0, null);  // drawable:ic_backup_restore
                }
                View view0 = new g(this, this.C0(0x7F040012), charSequence0, this.u(0x7F06017C));  // drawable:ic_backup_restore
                ((k)view0).setEnabled(false);
                return view0;
            }
            case 0x7F06011A: {  // string:get_pro_version "Get pro version"
                return new g(this, this.C0(0x7F04004C), charSequence0, null);  // drawable:ic_get_pro_version
            }
            case 0x7F06011B: {  // string:help_and_support "Help and support"
                return new g(this, this.C0(0x7F04004D), charSequence0, null);  // drawable:ic_help
            }
            case 0x7F06014B: {  // string:logs "Logs"
                return new g(this, this.C0(0x7F040004), charSequence0, null);  // drawable:ic_activity
            }
            case 0x7F06017E: {  // string:rate "Rate on play store"
                return new g(this, this.C0(0x7F04008C), charSequence0, null);  // drawable:ic_rate
            }
            case 0x7F0601AF: {  // string:share "Share"
                return new g(this, this.C0(0x7F0400A6), charSequence0, null);  // drawable:ic_share
            }
            default: {
                return null;
            }
        }
    }

    @Override  // e.j
    protected void k1(int v) {
        int v1 = 0;
        switch(v) {
            case 0: {
                this.O(new c0());
                return;
            }
            case 1: {
                this.O(new j0());
                return;
            }
            case 2: {
                this.O(new w0());
                return;
            }
            case 3: {
                this.O(new w1());
                return;
            }
            case 4: {
                this.O(new r());
                return;
            }
            case 5: 
            case 6: {
                x1 x10 = new x1();
                z z0 = this.g().h();
                if(v != 5) {
                    v1 = 1;
                }
                this.O(x10.U1(z0, v1));
                return;
            }
            case 7: {
                if(Build.VERSION.SDK_INT >= 24) {
                    this.O(new c2());
                    return;
                }
                break;
            }
            case 8: {
                if(l.r) {
                    this.O(new u1());
                    return;
                }
                break;
            }
            case 9: {
                if(l.r) {
                    this.O(new d.j());
                    return;
                }
                break;
            }
            case 10: {
                if(l.r) {
                    this.O(new q0());
                    return;
                }
                break;
            }
            case 11: {
                if(l.r) {
                    this.O(new r1().I1(false));
                    return;
                }
                break;
            }
            case 12: {
                this.O(new a0().C1(false));
                return;
            }
            case 13: {
                this.O(new d.b().T1(false));
                return;
            }
            case 14: {
                this.O(new y());
                return;
            }
            case 15: {
                this.O(new h0());
                return;
            }
            case 16: {
                this.O(new o1());
                break;
            }
            case 17: {
                this.y1(17, this.Q1().length);
            }
        }
    }

    @Override  // e.j
    protected boolean l1(int v) {
        int v1;
        e.z z0;
        switch(v) {
            case 0: {
                z0 = new e.z().v(new CharSequence[]{this.u(0x7F0600F4), this.u(0x7F0600D0)}, !this.O.p(1));  // string:enable "Enable"
                v1 = 3;
                break;
            }
            case 1: {
                z0 = new e.z().v(new CharSequence[]{this.u(0x7F0600F4), this.u(0x7F0600D0)}, !this.O.p(0));  // string:enable "Enable"
                v1 = 4;
                break;
            }
            default: {
                return false;
            }
        }
        this.N(z0, v1);
        return true;
    }

    @Override  // e.j
    protected void o1(int v) {
        class c extends BroadcastReceiver {
            final p0 a;

            @Override  // android.content.BroadcastReceiver
            public void onReceive(Context context0, Intent intent0) {
                String s = intent0.getStringExtra("logs");
                String s1 = ApplicationMain.i();
                String s2 = ApplicationMain.j();
                boolean z = s2 == null || s2.equals(l.t);
                if(s1 != null && !s1.equals(l.s) || !z) {
                    StringBuilder stringBuilder0 = new StringBuilder();
                    stringBuilder0.append("Please reboot to activate new version: ");
                    stringBuilder0.append(l.u);
                    if(z) {
                        stringBuilder0.append('-');
                        stringBuilder0.append(l.s);
                        stringBuilder0.append('(');
                        stringBuilder0.append(s1);
                    }
                    else {
                        stringBuilder0.append('(');
                        stringBuilder0.append(s2);
                    }
                    stringBuilder0.append(')');
                    stringBuilder0.append("\n\n");
                    stringBuilder0.append(s);
                    s = stringBuilder0.toString();
                }
                l0 l00 = new l0().j0(s);
                p0.this.O(l00);
            }
        }

        String s2;
        Activity activity0;
        switch(this.Q1()[v]) {
            case 0x7F060000: {  // string:about "About"
                StringBuilder stringBuilder0 = new StringBuilder(0x100);
                stringBuilder0.append(l.u);
                String s = ApplicationMain.i();
                String s1 = ApplicationMain.j();
                boolean z = s1 == null || s1.equals(l.t);
                if(s != null && !s.equals(l.s) || !z) {
                    if(z) {
                        stringBuilder0.append('-');
                        stringBuilder0.append(l.s);
                        stringBuilder0.append('(');
                        stringBuilder0.append(s);
                    }
                    else {
                        stringBuilder0.append('(');
                        stringBuilder0.append(s1);
                    }
                    stringBuilder0.append(')');
                }
                stringBuilder0.append("\n\n");
                stringBuilder0.append(this.u(0x7F060001));  // string:about_description "Author: jozein"
                this.N(new e.a0().u(this.u(0x7F060000), stringBuilder0.toString()), 1);  // string:about "About"
                return;
            }
            case 0x7F060085: {  // string:backup_restore "Backup/import"
                if(l.r) {
                    this.W1();
                    return;
                }
                return;
            }
            case 0x7F06011A: {  // string:get_pro_version "Get pro version"
                activity0 = this.getActivity();
                s2 = l.w;
                break;
            }
            case 0x7F06011B: {  // string:help_and_support "Help and support"
                this.P(new e0(), 5);
                return;
            }
            case 0x7F06014B: {  // string:logs "Logs"
                if(this.R == null) {
                    this.R = new c(this);
                    this.f().registerReceiver(this.R, new IntentFilter(s0.O));
                }
                s0.B(this.f(), 26);
                return;
            }
            case 0x7F06017E: {  // string:rate "Rate on play store"
                activity0 = this.getActivity();
                s2 = l.j;
                break;
            }
            case 0x7F0601AF: {  // string:share "Share"
                CharSequence charSequence0 = this.u(l.y);
                this.Z1(charSequence0, charSequence0, this.u(0x7F0600CF) + "\nhttp://play.google.com/store/apps/details?id=" + l.j);  // string:description "This module provides you Gesture control, Key control and other 
                                                                                                                                      // smart triggers. To trigger actions you want."
                return;
            }
            default: {
                return;
            }
        }
        f.p0.v(activity0, s2);
    }

    @Override  // android.app.Fragment
    public void onActivityResult(int v, int v1, Intent intent0) {
        if(v == 2) {
            if(Build.VERSION.SDK_INT >= 30 && !Environment.isExternalStorageManager()) {
                this.e0(0x7F060172);  // string:permission_denied "Permission denied!"
            }
            this.P(new m(), 1);
        }
    }

    @Override  // e.j
    public View onCreateView(LayoutInflater layoutInflater0, ViewGroup viewGroup0, Bundle bundle0) {
        class a implements b {
            final p0 a;

            @Override  // a.z$b
            public void a(boolean z) {
                ((f)p0.this.L0(0)).setChecked(z);
            }

            @Override  // a.z$b
            public void b(boolean z) {
                ((f)p0.this.L0(1)).setChecked(z);
            }
        }

        this.O = this.g().h();
        this.P = new a(this);
        this.g().e(this.P);
        return super.onCreateView(layoutInflater0, viewGroup0, bundle0);
    }

    @Override  // android.app.Fragment
    public void onDestroyView() {
        super.onDestroyView();
        if(this.P != null) {
            this.g().o(this.P);
            this.P = null;
        }
        if(this.R != null) {
            this.f().unregisterReceiver(this.R);
            this.R = null;
        }
    }

    @Override  // android.app.Fragment
    public void onPause() {
        super.onPause();
        g0 g00 = this.M;
        if(g00 != null) {
            g00.f();
        }
    }

    @Override  // android.app.Fragment
    public void onRequestPermissionsResult(int v, String[] arr_s, int[] arr_v) {
        if(v == 1) {
            for(int v1 = 0; v1 < arr_v.length; ++v1) {
                if(arr_v[v1] != 0) {
                    this.e0(0x7F060172);  // string:permission_denied "Permission denied!"
                    break;
                }
            }
            this.P(new m(), 1);
        }
    }

    @Override  // android.app.Fragment
    public void onResume() {
        super.onResume();
        g0 g00 = this.M;
        if(g00 != null) {
            g00.d();
        }
    }

    @Override  // e.j
    public void onSaveInstanceState(Bundle bundle0) {
        super.onSaveInstanceState(bundle0);
        View view0 = this.getView();
        if(view0 != null) {
            view0.removeCallbacks(this.N);
        }
        this.N = null;
    }

    @Override  // android.app.Fragment
    public void onViewCreated(View view0, Bundle bundle0) {
        class d.p0.b extends g0 {
            final p0 D;

            d.p0.b(Handler handler0, int v, long v1) {
                super(handler0, v, v1);
            }

            @Override  // f.g0
            protected boolean a(int v) {
                if(o0.m() != 0) {
                    p0.V = true;
                    e.m m0 = new e.m().u(p0.this.u(0x7F060213));  // string:user_not_supported "Current user is not supported!!!"
                    p0.this.N(m0, 1);
                    return false;
                }
                if(Build.VERSION.SDK_INT < 26 && !ApplicationMain.l()) {
                    p0.V = true;
                    e.m m1 = new e.m().u(p0.this.u(0x7F060236));  // string:xposed_not_installed "Xposed not installed!!!"
                    p0.this.N(m1, 1);
                    return false;
                }
                if(ApplicationMain.isModuleActivated()) {
                    p0.V = true;
                    return false;
                }
                return true;
            }

            @Override  // f.g0
            protected void b() {
                p0.this.M = null;
                if(!p0.V) {
                    p0.V = true;
                    e.m m0 = new e.m().u(p0.this.u(0x7F060155));  // string:module_not_activated "Module not activated yet(or not updated)! Please activate 
                                                                  // this module in Xposed installer, and reboot."
                    p0.this.N(m0, 2);
                }
            }
        }

        super.onViewCreated(view0, bundle0);
        if(view0 == null) {
            return;
        }
        if(!p0.V) {
            this.M = new d.p0.b(this, f.h0.a(), 2, 1500L);
        }
        Bundle bundle1 = this.h();
        int v = bundle1.getInt("clicked", -1);
        if(v >= 0) {
            bundle1.putInt("clicked", -1);
            this.k1(v);
        }
    }
}


package com.jozein.xedgepro.xposed;

import a.p;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Point;
import android.graphics.Rect;
import android.os.Build.VERSION;
import android.os.Handler;
import android.os.IBinder;
import de.robv.android.xposed.XC_MethodHook.MethodHookParam;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import f.p0;
import f.v;
import java.lang.reflect.Field;

class n0 extends j2 {
    class q extends XC_MethodHook {
        int a;
        final n0 b;

        private q() {
            this.a = -1;
        }

        q(h n0$h0) {
        }

        protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
            if(this.a != -1) {
                try {
                    Object object0 = n0.this.u("mFocusedWindow");
                    if(object0 == null) {
                        return;
                    }
                    XposedHelpers.setIntField(object0, "mSystemUiVisibility", this.a);
                    this.a = -1;
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        protected void beforeHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
            try {
                this.a = -1;
                if(!n0.this.L) {
                    return;
                }
                Object object0 = n0.this.u("mFocusedWindow");
                if(object0 == null) {
                    return;
                }
                int v = XposedHelpers.getIntField(object0, "mSystemUiVisibility");
                int v1 = n0.this.W(v);
                if(v != v1) {
                    XposedHelpers.setIntField(object0, "mSystemUiVisibility", v1);
                    this.a = v;
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                this.a = -1;
            }
        }
    }

    private final w1 H;
    private final m2 I;
    private final n3 J;
    private Handler K;
    private boolean L;
    private boolean M;
    private boolean N;
    private boolean O;
    private boolean P;
    private boolean Q;
    private boolean R;
    private boolean S;
    private int T;
    private int U;
    private int V;
    private int W;
    private volatile boolean X;
    private int Y;
    private boolean Z;
    private boolean a0;
    private int b0;
    private int c0;
    private int d0;
    private Field e0;
    private int f0;
    private final Runnable g0;
    private static final String h0;
    private static final String i0;
    private static final boolean j0;

    static {
        n0.h0 = Build.VERSION.SDK_INT < 24 ? "mNavigationBarWidthForRotation" : "mNavigationBarWidthForRotationDefault";
        n0.i0 = Build.VERSION.SDK_INT < 24 ? "mNavigationBarHeightForRotation" : "mNavigationBarHeightForRotationDefault";
        n0.j0 = Build.VERSION.SDK_INT >= 30;
    }

    n0(Class class0, ClassLoader classLoader0, w1 w10, p p0) {
        class a extends XC_MethodHook {
            final n0 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                n0.this.I.A(xC_MethodHook$MethodHookParam0.thisObject);
                if(n0.this.Z) {
                    return;
                }
                int v = n0.this.c0();
                n0.this.A0(v);
            }
        }


        class b extends XC_MethodHook {
            final n0 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                n0.this.I.A(xC_MethodHook$MethodHookParam0.thisObject);
                if(n0.this.Z && ((Boolean)xC_MethodHook$MethodHookParam0.getResult()).booleanValue() && ((int)(((Integer)xC_MethodHook$MethodHookParam0.args[1]))) == 0) {
                    int v = n0.this.c0();
                    n0.this.A0(v);
                }
            }
        }


        class c extends XC_MethodHook {
            final n0 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                n0.this.I.A(xC_MethodHook$MethodHookParam0.thisObject);
                if(n0.this.Z && ((Boolean)xC_MethodHook$MethodHookParam0.getResult()).booleanValue() && ((int)(((Integer)xC_MethodHook$MethodHookParam0.args[0]))) == 0) {
                    int v = n0.this.c0();
                    n0.this.A0(v);
                }
            }
        }


        class d extends XC_MethodHook {
            final n0 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                n0.this.I.A(xC_MethodHook$MethodHookParam0.thisObject);
                int v = XposedHelpers.getIntField(xC_MethodHook$MethodHookParam0.thisObject, "mRotation");
                n0.this.A0(v);
            }
        }


        class e extends XC_MethodHook {
            final n0 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                n0.this.A0(((int)(((Integer)xC_MethodHook$MethodHookParam0.args[0]))));
            }
        }


        class f extends XC_MethodHook {
            final n0 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                int[] arr_v = (int[])n0.this.u("mNavigationBarWidthForRotation");
                int[] arr_v1 = (int[])n0.this.u("mNavigationBarHeightForRotation");
                boolean z = false;
                if(!n0.this.X) {
                    if(arr_v1[n0.this.U] == 0 || arr_v[n0.this.U] == 0) {
                        z = true;
                    }
                }
                else if(arr_v1[n0.this.T] == 0 || arr_v[n0.this.T] == 0) {
                    z = true;
                }
                if(n0.this.O != z) {
                    n0.this.O = z;
                    n0.this.y0(n0.this.Q);
                }
            }
        }


        class h extends XC_MethodHook {
            final String a;
            final n0 b;

            h(String s) {
                this.a = s;
                super();
            }

            protected void beforeHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                if("qemu.hw.mainkeys".equals(xC_MethodHook$MethodHookParam0.args[0])) {
                    xC_MethodHook$MethodHookParam0.setResult(this.a);
                }
            }
        }


        class i extends XC_MethodHook {
            final n0 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                if(n0.this.L || n0.this.M && !n0.this.N) {
                    int v = (int)(((Integer)xC_MethodHook$MethodHookParam0.getResult()));
                    int v1 = n0.this.W(v);
                    if(v != v1) {
                        xC_MethodHook$MethodHookParam0.setResult(v1);
                    }
                }
            }
        }


        class j extends XC_MethodHook {
            final n0 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                if(n0.this.L || n0.this.M && !n0.this.N) {
                    int v = (int)(((Integer)xC_MethodHook$MethodHookParam0.getResult()));
                    int v1 = n0.this.X(v);
                    if(v != v1) {
                        xC_MethodHook$MethodHookParam0.setResult(v1);
                    }
                }
            }
        }


        class k extends XC_MethodHook {
            final n0 a;

            protected void beforeHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                if(n0.this.L) {
                    xC_MethodHook$MethodHookParam0.setResult(Boolean.TRUE);
                }
            }
        }


        class l extends XC_MethodHook {
            final n0 a;

            protected void beforeHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                if(n0.this.M) {
                    xC_MethodHook$MethodHookParam0.setResult(Boolean.TRUE);
                }
            }
        }


        class m extends XC_MethodHook {
            Field a;
            Field b;
            Field c;
            Field d;
            final Class e;
            final n0 f;

            m(Class class0) {
                this.e = class0;
                super();
            }

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                try {
                    if(this.a == null) {
                        this.a = XposedHelpers.findField(this.e, "mDisplayContent");
                    }
                    if(this.a.get(xC_MethodHook$MethodHookParam0.thisObject) != n0.this.d0().G()) {
                        return;
                    }
                    if(this.b == null) {
                        this.b = XposedHelpers.findField(this.e, "mSource");
                    }
                    Object object0 = this.b.get(xC_MethodHook$MethodHookParam0.thisObject);
                    if(this.d == null) {
                        this.d = XposedHelpers.findField(object0.getClass(), "mVisible");
                    }
                    if(this.c == null) {
                        this.c = XposedHelpers.findField(this.b.getType(), "mType");
                    }
                    int v = this.c.getInt(object0);
                    if(v == 0) {
                        boolean z = this.d.getBoolean(object0);
                        n0.this.B0(z);
                        return;
                    }
                    if(v == 1) {
                        boolean z1 = this.d.getBoolean(object0);
                        n0.this.y0(z1);
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }


        class n extends XC_MethodHook {
            final Field a;
            final n0 b;

            n(Field field0) {
                this.a = field0;
                super();
            }

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                try {
                    String s = (String)this.a.get(xC_MethodHook$MethodHookParam0.thisObject);
                    n0.this.C0(s, xC_MethodHook$MethodHookParam0.args[0] != null);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }


        class o extends XC_MethodHook {
            final Field a;
            final n0 b;

            o(Field field0) {
                this.a = field0;
                super();
            }

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                if(((Boolean)xC_MethodHook$MethodHookParam0.getResult()).booleanValue()) {
                    try {
                        String s = (String)this.a.get(xC_MethodHook$MethodHookParam0.thisObject);
                        n0.this.C0(s, ((Boolean)xC_MethodHook$MethodHookParam0.args[0]).booleanValue());
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }
        }


        class com.jozein.xedgepro.xposed.n0.p extends XC_MethodHook {
            final n0 a;

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                n0.this.Z = true;
                n0.this.A0(((int)(((Integer)xC_MethodHook$MethodHookParam0.args[0]))));
            }
        }

        super(class0);
        String s1;
        String s;
        Class class1;
        this.J = new n3();
        this.L = false;
        this.M = false;
        this.N = Build.VERSION.SDK_INT < 30;
        this.O = false;
        this.P = true;
        this.Q = false;
        this.R = false;
        this.S = false;
        this.T = 0;
        this.U = 1;
        this.V = 2;
        this.W = 3;
        this.X = false;
        this.Y = 0;
        this.Z = false;
        this.a0 = false;
        this.e0 = null;
        this.f0 = -1;
        this.g0 = () -> {
            int v = this.f0;
            this.f0 = -1;
            if(v >= 0 && v != this.c0()) {
                try {
                    this.d0().D(-1);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        };
        this.H = w10;
        this.I = new m2(classLoader0, w10);
        int v = this.a0(p0);
        if(v == 1 || v == 2) {
            try {
                Object[] arr_object = {String.class, new h(this, (v == 1 ? "1" : "0"))};
                XposedHelpers.findAndHookMethod(o3.a, "get", arr_object);
            }
            catch(Throwable throwable0) {
                p2.g(throwable0);
            }
        }
        int v1 = Build.VERSION.SDK_INT;
        if(v1 < 30) {
            if(v1 >= 21) {
                try {
                    class1 = XposedHelpers.findClass("android.view.WindowManagerPolicyControl", classLoader0);
                    this.N = false;
                    goto label_45;
                }
                catch(Throwable unused_ex) {
                    try {
                        int v2 = Build.VERSION.SDK_INT;
                        if(v2 < 23) {
                            s = "com.android.internal.policy.impl.PolicyControl";
                        }
                        else {
                            s = v2 > 28 ? "com.android.server.wm.PolicyControl" : "com.android.server.policy.PolicyControl";
                        }
                        Class class2 = XposedHelpers.findClass(s, classLoader0);
                        if(v2 >= 24) {
                            this.N = false;
                        }
                        class1 = class2;
                    label_45:
                        XposedBridge.hookAllMethods(class1, "getSystemUiVisibility", new i(this));
                        XposedBridge.hookAllMethods(class1, "getWindowFlags", new j(this));
                    }
                    catch(Throwable throwable1) {
                        p2.g(throwable1);
                    }
                }
            }
            else {
                try {
                    this.p("expandedDesktopHidesStatusBar", new Object[]{new k(this)});
                    this.p("expandedDesktopHidesNavigationBar", new Object[]{new l(this)});
                    this.N = false;
                    goto label_57;
                }
                catch(Throwable unused_ex) {
                    try {
                        this.p("updateSystemUiVisibilityLw", new Object[]{new q(this, null)});
                        this.o("layoutWindowLw", new q(this, null));
                        goto label_57;
                    }
                    catch(Throwable throwable1) {
                    }
                }
                p2.g(throwable1);
            }
        }
        try {
        label_57:
            if(!n0.j0) {
                int v3 = Build.VERSION.SDK_INT;
                if(v3 < 23) {
                    s1 = "com.android.internal.policy.impl.BarController";
                }
                else {
                    s1 = v3 > 28 ? "com.android.server.wm.BarController" : "com.android.server.policy.BarController";
                }
                Class class3 = XposedHelpers.findClass(s1, classLoader0);
                Field field0 = XposedHelpers.findField(class3, "mTag");
                field0.setAccessible(true);
                XposedBridge.hookAllMethods(class3, "setWindow", new n(this, field0));
                XposedHelpers.findAndHookMethod(class3, "setBarShowingLw", new Object[]{Boolean.TYPE, new o(this, field0)});
            }
            else if(Build.VERSION.SDK_INT >= 30) {
                Class class4 = XposedHelpers.findClass("com.android.server.wm.InsetsSourceProvider", classLoader0);
                XposedHelpers.findAndHookMethod(class4, "updateVisibility", new Object[]{new m(this, class4)});
            }
        }
        catch(Throwable throwable2) {
            p2.g(throwable2);
        }
        int v4 = Build.VERSION.SDK_INT;
        if(v4 < 26) {
            try {
                Object[] arr_object4 = {Boolean.TYPE, new d(this)};
                this.I.p("updateRotationUncheckedLocked", arr_object4);
                goto label_101;
            }
            catch(Throwable throwable4) {
                p2.g(throwable4);
                try {
                    this.p("setRotationLw", new Object[]{Integer.TYPE, new e(this)});
                    goto label_101;
                }
                catch(Throwable throwable3) {
                }
            }
        label_100:
            p2.g(throwable3);
        }
        else if(v4 <= 27) {
            try {
                Object[] arr_object1 = new Object[2];
                Class class5 = Integer.TYPE;
                arr_object1[0] = class5;
                arr_object1[1] = new com.jozein.xedgepro.xposed.n0.p(this);
                this.p("setRotationLw", arr_object1);
                Object[] arr_object2 = new Object[3];
                Class class6 = Boolean.TYPE;
                arr_object2[0] = class6;
                arr_object2[1] = class6;
                arr_object2[2] = new a(this);
                this.I.p("updateRotationUnchecked", arr_object2);
                Object[] arr_object3 = {class6, class5, new b(this)};
                this.I.p("updateOrientationFromAppTokensLocked", arr_object3);
            }
            catch(Throwable throwable3) {
                goto label_100;
            }
        }
    label_101:
        if(Build.VERSION.SDK_INT < 24) {
            try {
                this.p("updateRotation", new Object[]{Boolean.TYPE, new f(this)});
            }
            catch(Throwable throwable5) {
                p2.g(throwable5);
            }
        }
    }

    private void A0(int v) {
        this.Y = v;
        boolean z = v == this.U || v == this.W;
        if(this.X != z) {
            this.X = z;
            this.H.j3(z);
        }
    }

    private void B0(boolean z) {
        if(this.P != z) {
            this.P = z;
            this.H.o3(z);
            boolean z1 = !this.R && !z;
            if(this.S != z1) {
                this.S = z1;
                this.H.g3(z1);
            }
        }
    }

    private void C0(String s, boolean z) {
        if(s.endsWith("StatusBar")) {
            this.B0(z);
            return;
        }
        if(s.endsWith("NavigationBar")) {
            this.y0(z);
            return;
        }
        v.c(("Unknown tag: " + s));
    }

    private void V() {
        class g extends f.f0.d {
            final IBinder a;
            final n0 b;

            g() {
                this.a = new w3();
            }

            @Override  // f.f0$d
            public Object a(String s, Object[] arr_object) {
                s.hashCode();
                if(!s.equals("asBinder")) {
                    if(!s.equals("onRotationChanged")) {
                        v.c(("IRotationWatcher." + s));
                        return null;
                    }
                    n0.this.A0(((int)(((Integer)arr_object[0]))));
                    return null;
                }
                return this.a;
            }
        }

        try {
            g n0$g0 = new g(this);
            this.I.z("watchRotation", n0$g0, new Object[]{0});
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private int W(int v) {
        if(this.L) {
            if(Build.VERSION.SDK_INT >= 21) {
                v = (v | 0x1000) & 0xBFFFFFFF;
            }
            v = (v | 0x404) & 0xFFFFFEFF;
        }
        if(this.M && !this.N) {
            if(Build.VERSION.SDK_INT >= 21) {
                v = (v | 0x1000) & 0x7FFFFFFF;
            }
            return (v | 0x202) & 0xFFFFFEFF;
        }
        return v;
    }

    private int X(int v) {
        if(this.L) {
            v = (v | 0x400) & 0xFBFFF7FF;
        }
        return !this.M || this.N ? v : v & 0xF7FFFFFF;
    }

    Object Y(boolean z) {
        Object object0 = XposedHelpers.getObjectField(this.d0().H(), (z ? "mStatusBar" : "mNavigationBar"));
        if(object0 == null) {
            v.c(((z ? "mStatusBar" : "mNavigationBar") + " not exists."));
        }
        return object0;
    }

    Point Z() {
        try {
            Object object0 = this.d0().M();
            if(object0 != null) {
                Rect rect0 = u3.g(object0, new Rect());
                return new Point((rect0.left + rect0.right) / 2, (rect0.top + rect0.bottom) / 2);
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        Point point0 = new Point();
        Point point1 = this.I.I(point0);
        point1.x /= 2;
        point1.y /= 2;
        return point1;
    }

    // 去混淆评级： 低(20)
    private int a0(p p0) {
        return p0.J() ? p0.u(14) : this.J.a(0);
    }

    private Resources b0() {
        Context context0 = this.H.T1();
        return context0 == null ? Resources.getSystem() : context0.getResources();
    }

    private int c0() {
        try {
            int v = this.d0().O();
            this.Y = v;
            return v;
        }
        catch(Throwable unused_ex) {
            return this.Y;
        }
    }

    m2 d0() {
        return this.I;
    }

    void e0(Handler handler0, Object object0) {
        try {
            this.K = handler0;
            if(this.c0 == 0) {
                this.f0();
            }
            this.I.A(object0);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private void f0() {
        try {
            Resources resources0 = this.b0();
            int v = resources0.getIdentifier("navigation_bar_width", "dimen", "android");
            int v1 = resources0.getIdentifier("navigation_bar_height", "dimen", "android");
            int v2 = resources0.getIdentifier("navigation_bar_height_landscape", "dimen", "android");
            this.b0 = (int)resources0.getDimension(v);
            this.c0 = (int)resources0.getDimension(v1);
            this.d0 = (int)resources0.getDimension(v2);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private void g0() {
        boolean z = false;
        this.f0();
        try {
            if(Build.VERSION.SDK_INT >= 29) {
                Resources resources0 = this.b0();
                int v = resources0.getIdentifier("config_reverseDefaultRotation", "boolean", "android");
                boolean z1 = v > 0 && resources0.getBoolean(v);
                Point point0 = new Point();
                Point point1 = this.I.I(point0);
                if(point1.x > point1.y) {
                    this.U = 0;
                    this.W = 2;
                    if(z1) {
                        this.T = 1;
                        this.V = 3;
                    }
                    else {
                        this.T = 3;
                        this.V = 1;
                    }
                }
                else {
                    this.T = 0;
                    this.V = 2;
                    if(z1) {
                        this.U = 3;
                        this.W = 1;
                    }
                    else {
                        this.U = 1;
                        this.W = 3;
                    }
                }
            }
            else {
                int v1 = this.t("mPortraitRotation");
                int v2 = this.t("mLandscapeRotation");
                if(v1 == 0 && v2 == 0) {
                    return;
                }
                this.T = v1;
                this.U = v2;
                this.V = this.t("mUpsideDownRotation");
                int v3 = this.t("mSeascapeRotation");
                this.W = v3;
                if(this.U == 0 || v3 == 0) {
                    z = true;
                }
                this.X = z;
            }
            this.a0 = true;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    boolean h0() {
        return this.M;
    }

    boolean i0() {
        return this.L;
    }

    boolean j0() {
        return this.X;
    }

    boolean k0() {
        return this.R;
    }

    boolean l0() {
        return this.P;
    }

    // 检测为 Lambda 实现
    private void m0() [...]

    void n0() {
        if(this.K == null) {
            this.K = this.H.U1();
        }
        this.I.U();
    }

    void o0() {
        this.g0();
    }

    void p0() {
        if(!this.a0) {
            this.g0();
        }
        if(Build.VERSION.SDK_INT > 27 || p0.p() != 1) {
            this.V();
        }
    }

    boolean q0() {
        if(Build.VERSION.SDK_INT < 29) {
            try {
                this.r("requestTransientBars", new Object[]{this.u("mStatusBar")});
                return true;
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        return false;
    }

    void r0(int v) {
        int v2;
        int v1 = this.c0();
        switch(v) {
            case 1: {
                v2 = this.T;
                break;
            }
            case 2: {
                v2 = this.U;
                break;
            }
            case 3: {
                v2 = this.V;
                break;
            }
            case 4: {
                v2 = this.W;
                break;
            }
            case 5: {
                v2 = v1 + 1 & 3;
                break;
            }
            case 6: {
                v2 = v1 + 2 & 3;
                break;
            }
            case 7: {
                v2 = v1 + 3 & 3;
                break;
            }
            default: {
                v2 = this.T;
                if(v1 == v2 || v1 == this.V) {
                    v2 = this.U;
                }
            }
        }
        this.d0().D(v2);
        this.K.removeCallbacks(this.g0);
        this.f0 = v2;
        this.K.postDelayed(this.g0, 500L);
    }

    private void s0(Object object0, boolean z) {
        if(object0 == null) {
            return;
        }
        if(Build.VERSION.SDK_INT >= 0x1F) {
            XposedHelpers.callMethod(object0, (z ? "show" : "hide"), new Object[]{Boolean.TRUE, Boolean.TRUE});
            return;
        }
        XposedHelpers.callMethod(object0, (z ? "showLw" : "hideLw"), new Object[]{Boolean.TRUE});
        this.z0();
    }

    void t0(boolean z) {
        if(this.N) {
            try {
                if(z) {
                    this.w0(0, 0, 0);
                }
                else {
                    this.w0(this.b0, this.c0, this.d0);
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        this.M = z;
        if(n0.j0) {
            this.u0(false, z);
            return;
        }
        this.z0();
    }

    private void u0(boolean z, boolean z1) {
        this.s0(this.Y(z), !z1);
    }

    void v0(boolean z) {
        this.L = z;
        if(n0.j0) {
            this.u0(true, z);
            return;
        }
        this.z0();
    }

    private void w0(int v, int v1, int v2) {
        Object object1;
        int[] arr_v;
        if(Build.VERSION.SDK_INT >= 29) {
            Object object0 = this.d0().H();
            arr_v = (int[])XposedHelpers.getObjectField(object0, n0.h0);
            object1 = XposedHelpers.getObjectField(object0, n0.i0);
        }
        else {
            arr_v = (int[])this.u(n0.h0);
            object1 = this.u(n0.i0);
        }
        int v3 = this.T;
        if(((int[])object1)[v3] == v1 && (((int[])object1)[this.U] == v2 && arr_v[v3] == v && arr_v[this.U] == v)) {
            return;
        }
        int v4 = this.V;
        ((int[])object1)[v4] = v1;
        ((int[])object1)[v3] = v1;
        int v5 = this.U;
        int v6 = this.W;
        ((int[])object1)[v6] = v2;
        ((int[])object1)[v5] = v2;
        arr_v[v6] = v;
        arr_v[v5] = v;
        arr_v[v4] = v;
        arr_v[v3] = v;
    }

    @Override  // com.jozein.xedgepro.xposed.j2
    protected Object x() {
        return this.H.v();
    }

    void x0() {
        int v = this.H.b2().u(14);
        if(v != this.J.a(0)) {
            this.J.c(0, v);
        }
    }

    private void y0(boolean z) {
        this.Q = z;
        boolean z1 = true;
        boolean z2 = z && !this.O;
        if(this.R != z2) {
            this.R = z2;
            this.H.i3(z2);
            if(this.P || z2) {
                z1 = false;
            }
            if(this.S != z1) {
                this.S = z1;
                this.H.g3(z1);
            }
        }
    }

    void z0() {
        this.r("updateRotation", new Object[]{Boolean.FALSE});
    }
}


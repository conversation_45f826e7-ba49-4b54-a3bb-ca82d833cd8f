package d;

import android.content.ClipboardManager;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.HorizontalScrollView;
import android.widget.ScrollView;
import android.widget.TextView;
import e.j0.c;

public class l0 extends c {
    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F06014B);  // string:logs "Logs"
        this.S(0x7F040024, (View view0) -> {
            CharSequence charSequence0 = this.h().getCharSequence("logs", "");
            if(charSequence0.length() > 0) {
                ClipboardManager clipboardManager0 = (ClipboardManager)this.getActivity().getSystemService("clipboard");
                if(clipboardManager0 != null) {
                    clipboardManager0.setText(charSequence0);
                    this.e0(0x7F0601F2);  // string:text_copied "Text copied."
                }
            }
        });
    }

    // 检测为 Lambda 实现
    private void i0(View view0) [...]

    public l0 j0(CharSequence charSequence0) {
        this.h().putCharSequence("logs", charSequence0);
        return this;
    }

    @Override  // android.app.Fragment
    public View onCreateView(LayoutInflater layoutInflater0, ViewGroup viewGroup0, Bundle bundle0) {
        Context context0 = viewGroup0 == null ? this.f() : viewGroup0.getContext();
        View view0 = new ScrollView(context0);
        ((ScrollView)view0).setFillViewport(true);
        HorizontalScrollView horizontalScrollView0 = new HorizontalScrollView(context0);
        ((ScrollView)view0).addView(horizontalScrollView0);
        TextView textView0 = new TextView(context0);
        this.Y(textView0);
        textView0.setText(this.h().getCharSequence("logs", ""));
        textView0.setTextIsSelectable(true);
        horizontalScrollView0.addView(textView0);
        return view0;
    }
}


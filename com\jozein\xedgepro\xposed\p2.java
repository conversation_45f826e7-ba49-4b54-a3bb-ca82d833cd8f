package com.jozein.xedgepro.xposed;

import android.content.Context;
import android.os.Environment;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import f.l;
import f.v;
import java.lang.reflect.Method;

class p2 implements l {
    static final String A;
    private static final String B;
    private static Context C;
    private static ClassLoader D;
    static final String z;

    static {
        String s;
        p2.A = Environment.getDataDirectory() + "/system/xedge/";
        p2.B = '[' + l.x + ']';
        try {
            s = System.getenv("ANDROID_CACHE");
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            s = null;
        }
        p2.z = s == null ? "/cache/" : s + '/';
        p2.C = null;
        p2.D = null;
    }

    static Object a(Object object0, String s, Object[] arr_object) {
        Class class0 = object0.getClass();
        NoSuchMethodError noSuchMethodError0 = null;
        do {
            try {
                return XposedHelpers.findMethodBestMatch(class0, s, arr_object).invoke(object0, arr_object);
            }
            catch(NoSuchMethodError noSuchMethodError1) {
            }
            if(noSuchMethodError0 == null) {
                noSuchMethodError0 = noSuchMethodError1;
            }
            class0 = class0.getSuperclass();
        }
        while(class0 != Object.class && class0 != null);
        throw noSuchMethodError0;
    }

    static Method b(Class class0, String s, Class[] arr_class) {
        NoSuchMethodError noSuchMethodError0 = null;
        do {
            try {
                return XposedHelpers.findMethodExact(class0, s, arr_class);
            }
            catch(NoSuchMethodError noSuchMethodError1) {
            }
            if(noSuchMethodError0 == null) {
                noSuchMethodError0 = noSuchMethodError1;
            }
            class0 = class0.getSuperclass();
        }
        while(class0 != Object.class && class0 != null);
        throw noSuchMethodError0;
    }

    static ClassLoader c() {
        return p2.D == null ? p2.class.getClassLoader() : p2.D;
    }

    static Context d(Context context0) {
        if(p2.C == null && p2.D == null) {
            p2.e(context0.getClassLoader());
        }
        try {
            p2.C = context0.createPackageContext(l.j, 2);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        return p2.C;
    }

    static void e(ClassLoader classLoader0) {
        p2.D = classLoader0;
    }

    static void f(String s) {
        XposedBridge.log((p2.B + ' ' + s));
        v.f(s);
    }

    static void g(Throwable throwable0) {
        String s = throwable0.getClass().getName() + ": " + throwable0.getMessage();
        XposedBridge.log((p2.B + ' ' + s));
        v.f(s);
    }
}


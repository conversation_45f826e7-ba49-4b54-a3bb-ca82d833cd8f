package d;

import a.b.i;
import a.z;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import e.g0;
import e.j.k;

public class g extends e {
    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F060006);  // string:action_adjust "Adjust"
    }

    @Override  // e.j
    protected int B0() {
        return 13;
    }

    @Override  // d.e
    protected void B1(Bundle bundle0, int v) {
        int v1 = bundle0.getInt("result", -1);
        if(v1 != -1) {
            this.C1(new i(this.I0(), v1));
        }
    }

    protected int E1(Context context0, int v) {
        if(v == 0) {
            z z0 = this.g().h();
            return z0 == null ? -1 : (z0.B() >>> 24) / 16;
        }
        return i.z(context0, v);
    }

    @Override  // e.j
    protected boolean N0(int v) {
        return true;
    }

    @Override  // e.j
    protected View h1(int v) {
        return new k(this, i.C(this.M0(), v));
    }

    @Override  // e.j
    protected View i1(int v) {
        return new k(this, i.a(this.M0(), v));
    }

    @Override  // e.j
    protected void k1(int v) {
        this.y1(v, i.D(v));
    }

    @Override  // e.j
    protected void o1(int v) {
        i b$i0;
        int v1 = this.I0();
        switch(v) {
            case 0: {
                b$i0 = new i(v1, -1);
                break;
            }
            case 1: {
                b$i0 = new i(v1, -2);
                break;
            }
            default: {
                Context context0 = this.M0();
                int v2 = i.B(v1);
                int v3 = i.A(context0, v1);
                int v4 = this.E1(context0, v1);
                if(v4 < 0) {
                    v4 = (v2 + v3) / 2;
                }
                this.M(new g0().x(i.C(context0, v1), null, v2, v4, v3));
                return;
            }
        }
        this.C1(b$i0);
    }

    @Override  // e.j
    protected boolean p1(int v) {
        if(v < 2) {
            this.D1(new i(this.I0(), (v == 0 ? -1 : -2)));
            return true;
        }
        return super.p1(v);
    }
}


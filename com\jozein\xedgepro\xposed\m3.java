package com.jozein.xedgepro.xposed;

import a.b.h3;
import a.b;
import android.os.Handler;

class m3 implements a, Runnable {
    private final w1 A;
    private final Handler B;
    protected final int C;
    protected final long D;
    protected final long E;
    protected final float F;
    protected final float G;
    private final boolean H;
    protected final float[] I;
    protected final float[] J;
    protected final long[] K;
    protected int L;
    protected int M;
    private volatile long N;
    private volatile boolean O;
    private long P;
    private final b[] z;

    m3(h3 b$h30, w1 w10, int v, long v1, float f, boolean z) {
        this.I = new float[0x80];
        this.J = new float[0x80];
        this.K = new long[0x80];
        this.L = 0;
        this.M = 0;
        this.N = 0L;
        this.O = false;
        this.P = 0L;
        this.z = b$h30.I;
        this.A = w10;
        this.B = w10.U1();
        this.C = this.l(v);
        this.D = v1;
        this.E = v1 + 200L;
        this.G = f;
        this.F = f * 6.25f;
        this.H = z;
    }

    @Override  // com.jozein.xedgepro.xposed.b0$a
    public void b(float f, float f1, long v) {
        if(this.O) {
            return;
        }
        int v1 = this.h(f, f1, v);
        if(v1 == -1) {
            v1 = this.C;
        }
        this.k(v1);
    }

    @Override  // com.jozein.xedgepro.xposed.b0$a
    public void c() {
        this.O = true;
    }

    @Override  // com.jozein.xedgepro.xposed.b0$a
    public void d(float f, float f1, long v) {
        if(this.O) {
            return;
        }
        if(this.H) {
            int v1 = this.h(f, f1, v);
            if(v1 != -1) {
                this.k(v1);
                return;
            }
        }
        this.f(f, f1, v);
    }

    @Override  // com.jozein.xedgepro.xposed.b0$a
    public void e(int v, float f, float f1, long v1) {
        this.f(f, f1, v1);
        this.P = v1;
        this.N = v1;
        if(this.H && this.C != 0) {
            this.B.postAtTime(this, v1 + this.D);
        }
    }

    private void f(float f, float f1, long v) {
        if(this.L >= 0x80) {
            this.L = 0;
        }
        int v1 = this.M;
        if(v1 < 0x80) {
            this.M = v1 + 1;
        }
        int v2 = this.L;
        this.I[v2] = f;
        this.J[v2] = f1;
        this.K[v2] = v;
        this.L = v2 + 1;
    }

    private static int g(float f, float f1, float f2, float f3, float f4, float f5) {
        float f6 = f2 - f;
        float f7 = f3 - f1;
        float f8 = f6 * f6;
        float f9 = f7 * f7;
        float f10 = f8 + f9;
        if(f10 < f4) {
            return 0;
        }
        if(f10 < f5) {
            return -1;
        }
        if(f8 > f9) {
            return f6 < 0.0f ? -2 : 2;
        }
        return f7 < 0.0f ? -3 : 3;
    }

    private int h(float f, float f1, long v) {
        int v1 = m3.g(this.I[this.L - 1], this.J[this.L - 1], f, f1, this.G * 0.01f, 0.0f);
        long v2 = this.D;
        if(v1 != 0) {
            int v3 = this.C;
            if(v1 == v3) {
                v2 -= 50L;
            }
            else if(v1 == -v3) {
                v2 += 200L;
            }
        }
        long v4 = v;
        int v5 = 0;
        boolean z = false;
        boolean z1 = true;
        while(v5 < this.M) {
            int v6 = this.L - 1 - v5;
            v6 = v6 >= 0 ? this.L - 1 - v5 : v6 + 0x80;
            int v7 = m3.g(this.I[v6], this.J[v6], f, f1, this.G, this.F);
            int v8 = this.C;
            if(v8 == 0) {
                if(v7 != 0 && v7 != -1) {
                    return v7;
                }
            }
            else if(v7 == 0) {
                long[] arr_v = this.K;
                long v9 = v - arr_v[v6];
                if(v9 > this.E) {
                    this.N = v4;
                    return 0;
                }
                if(v9 > v2) {
                    z = true;
                }
                if(z1) {
                    v4 = arr_v[v6];
                }
            }
            else {
                if(v7 != -1) {
                    goto label_39;
                }
                if(v - this.K[v6] > this.E) {
                    this.N = v4;
                    return 0;
                }
                z1 = false;
            }
            ++v5;
            continue;
        label_39:
            this.N = v4;
            if(v7 == v8) {
                return z ? 0 : -1;
            }
            return v7;
        }
        this.N = v4;
        return -1;
    }

    // 检测为 Lambda 实现
    private void i(int v) [...]

    private void j(int v) {
        if(this.O) {
            return;
        }
        int v1 = 1;
        this.O = true;
        int v2 = 0;
        if(v == -3) {
            v1 = 3;
            v2 = 3;
        }
        else {
            switch(v) {
                case -2: {
                    v2 = 1;
                    break;
                }
                case 2: {
                    v1 = 2;
                    break;
                }
                case 3: {
                    v1 = 4;
                    v2 = 2;
                    break;
                }
                default: {
                    v1 = 0;
                    v2 = -1;
                }
            }
        }
        this.A.v3(this.z[v1], v2, null);
    }

    private void k(int v) {
        if(this.H) {
            this.B.removeCallbacks(this);
            l3 l30 = () -> this.j(v);
            this.B.post(l30);
            return;
        }
        this.j(v);
    }

    private int l(int v) {
        switch(v) {
            case 1: {
                return -2;
            }
            case 2: {
                return 2;
            }
            case 3: {
                return -3;
            }
            case 4: {
                return 3;
            }
            default: {
                return 0;
            }
        }
    }

    @Override
    public void run() {
        if(this.O) {
            return;
        }
        long v = this.N;
        if(v != this.P) {
            this.P = v;
            this.B.postAtTime(this, v + this.D);
            return;
        }
        this.j(0);
    }
}


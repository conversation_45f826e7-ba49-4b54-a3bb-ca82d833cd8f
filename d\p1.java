package d;

import a.b.f2;
import a.b;
import android.content.Context;
import android.os.Bundle;
import android.view.View.OnClickListener;
import android.view.View;
import e.j.g;
import e.j.k;
import e.m;
import e.y;
import f.v;

public class p1 extends c implements a {
    private int M;
    private b N;

    public p1() {
        this.M = 0;
        this.N = b.r();
    }

    @Override  // e.j0$c
    protected void B() {
        class d.p1.a implements View.OnClickListener {
            final p1 z;

            @Override  // android.view.View$OnClickListener
            public void onClick(View view0) {
                f2 b$f20 = new f2(p1.this.M, p1.this.N);
                p1.this.W("result", b$f20);
                p1.this.L();
            }
        }

        super.B();
        this.c0(0x7F06004A);  // string:action_repeat "Repeat"
        this.S(0x7F040071, new d.p1.a(this));  // drawable:ic_ok
    }

    @Override  // e.j
    protected int B0() {
        Bundle bundle0 = this.h();
        this.M = bundle0.getInt("count", 0);
        b b0 = (b)bundle0.getParcelable("action");
        this.N = b0;
        if(b0 == null) {
            this.N = b.r();
        }
        return 2;
    }

    @Override  // d.c
    protected void C1(Bundle bundle0, int v) {
        switch(v) {
            case 1: {
                CharSequence charSequence0 = bundle0.getCharSequence("result");
                if(charSequence0 != null) {
                    try {
                        this.M = Integer.parseInt(charSequence0.toString());
                        ((k)this.L0(0)).setSubText(Integer.toString(this.M));
                        this.h().putBoolean("changed", true);
                    }
                    catch(NumberFormatException numberFormatException0) {
                        v.d(numberFormatException0);
                    }
                    return;
                }
                break;
            }
            case 2: {
                b b0 = (b)bundle0.getParcelable("result");
                if(b0 != null) {
                    this.N = b0;
                    Context context0 = this.M0();
                    g j$g0 = (g)this.L0(1);
                    j$g0.setSubText(b0.n(context0));
                    j$g0.setImageDrawable(b0.m(context0));
                    this.h().putBoolean("changed", true);
                    return;
                }
                break;
            }
            case 3: {
                if(bundle0.getBoolean("result", false)) {
                    this.W("result", new f2(this.M, this.N));
                }
                this.L();
            }
        }
    }

    public p1 M1(int v, b b0) {
        Bundle bundle0 = this.h();
        bundle0.putInt("count", v);
        bundle0.putParcelable("action", b0);
        return this;
    }

    @Override  // e.j
    protected View h1(int v) {
        if(v == 0) {
            return new k(this, this.u(0x7F0600C9), Integer.toString(this.M));  // string:count "Count:"
        }
        Context context0 = this.M0();
        return new g(this, this.u(0x7F060002), this.N.n(context0), this.N.m(context0));  // string:action "Action:"
    }

    @Override  // e.j
    protected void k1(int v) {
        if(v == 0) {
            y y0 = new y();
            y0.J(this.u(0x7F0600F6), null, Integer.toString(this.M), 7, 9);  // string:enter_count "Enter repeat count"
            this.N(y0, 1);
            return;
        }
        this.E1(3, 2);
    }

    @Override  // e.j
    protected boolean l1(int v) {
        if(v == 0) {
            return super.l1(0);
        }
        this.F1(this.N, 2, 3);
        return true;
    }

    @Override  // e.j
    public void onSaveInstanceState(Bundle bundle0) {
        bundle0.putInt("count", this.M);
        bundle0.putParcelable("action", this.N);
        super.onSaveInstanceState(bundle0);
    }

    @Override  // e.j0$c
    protected void y() {
        if(this.h().getBoolean("changed", false)) {
            this.N(new m().u(this.u(0x7F06008F)), 3);  // string:check_save_message "Do you want to save changes?"
            return;
        }
        super.y();
    }
}


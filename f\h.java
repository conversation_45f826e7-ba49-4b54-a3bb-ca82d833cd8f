package f;

import a.b;

public class h implements l, s {
    private int A;
    private char[] B;
    private static final byte[] C;
    protected static final byte[] D;
    private byte[] z;

    static {
        h.C = new byte[]{0x30, 49, 50, 51, 52, 53, 54, 55, 56, 57, 65, 66, 67, 68, 69, 70};
        h.D = new byte[0];
    }

    public h() {
        this.z = null;
        this.A = 0;
        this.B = null;
    }

    private void A(int v) {
        int v1;
        for(v1 = 28; v1 > 0 && (v >>> v1 & 15) == 0; v1 -= 4) {
        }
        while(v1 >= 0) {
            int v2 = this.A;
            this.A = v2 + 1;
            this.z[v2] = h.C[v >>> v1 & 15];
            v1 -= 4;
        }
    }

    public h B(String s) {
        if(this.z == null) {
            return this;
        }
        this.y(s);
        this.z[this.A - 1] = 10;
        return this;
    }

    public h C() {
        byte[] arr_b = this.z;
        if(arr_b == null) {
            return this;
        }
        int v = this.A;
        if(v != 0 && arr_b[v - 1] == 0x20) {
            arr_b[v - 1] = 10;
        }
        return this;
    }

    private void D(String s) {
        if(this.B == null) {
            this.B = new char[0x2000];
        }
        int v = s.length();
        for(int v1 = 0; v1 < v; ++v1) {
            int v2 = this.A;
            this.A = v2 + 1;
            this.z[v2] = (byte)this.B[v1];
        }
    }

    @Override  // f.s
    public String a() {
        int v = this.A;
        byte[] arr_b = this.z;
        if(v >= arr_b.length) {
            return null;
        }
        if(arr_b[v] == 0x20) {
            this.A = v + 1;
        }
        if(this.B == null) {
            this.B = new char[0x2000];
        }
        int v1;
        for(v1 = 0; true; ++v1) {
            int v2 = this.A;
            byte[] arr_b1 = this.z;
            if(v2 >= arr_b1.length) {
                break;
            }
            char c = (char)arr_b1[v2];
            if(c == 10 || c == 0x20) {
                break;
            }
            this.B[v1] = c;
            this.A = v2 + 1;
        }
        return v1 == 0 || v1 == 1 && this.B[0] == 0x60 ? null : String.valueOf(this.B, 0, v1);
    }

    @Override  // f.s
    public s b(String s) {
        return this.z(s);
    }

    @Override  // f.s
    public s c(String s) {
        return this.y(s);
    }

    @Override  // f.s
    public s d(int v) {
        return this.w(v);
    }

    @Override  // f.s
    public String e() {
        int v = this.h();
        if(v == -1) {
            return null;
        }
        if(v != 0) {
            int v1 = this.A + 1;
            this.A = v1;
            byte[] arr_b = this.z;
            if(v1 < arr_b.length) {
                int v2 = v1 + v;
                this.A = v2;
                if(v2 > v1 + v) {
                    this.A = arr_b.length;
                    v = arr_b.length - v1;
                }
                return new String(arr_b, v1, v, p0.z);
            }
        }
        return "";
    }

    @Override  // f.s
    public s f(b b0) {
        return this.x(b0);
    }

    @Override  // f.s
    public b g() {
        return b.s(this);
    }

    @Override  // f.s
    public int h() {
        int v = this.A;
        byte[] arr_b = this.z;
        if(v >= arr_b.length) {
            return 0;
        }
        if(arr_b[v] == 0x20) {
            this.A = v + 1;
        }
        int v1 = this.A;
        if(v1 < arr_b.length && arr_b[v1] == 0x20) {
            this.A = v1 + 1;
        }
        return this.r();
    }

    public void i(byte[] arr_b) {
        if(arr_b == null) {
            arr_b = h.D;
        }
        this.z = arr_b;
        this.A = 0;
    }

    public h j() {
        this.z = new byte[0x4000];
        this.A = 0;
        return this;
    }

    private void k(int v) {
        int v1 = this.A;
        if(v1 + v >= this.z.length) {
            this.z = this.u(v1 + v);
        }
    }

    protected void l() {
        this.z = h.D;
        this.A = 0;
    }

    public void m() {
        this.z = null;
    }

    public void n() {
        v.d(new Throwable("Should not be used."));
    }

    public byte[] o() {
        int v = this.A;
        byte[] arr_b = new byte[v];
        System.arraycopy(this.z, 0, arr_b, 0, v);
        this.z = null;
        this.A = 0;
        return arr_b;
    }

    protected byte[] p() {
        return this.z;
    }

    protected int q() {
        return this.A;
    }

    private int r() {
        int v3;
        int v = 0;
        while(true) {
            int v1 = this.A;
            byte[] arr_b = this.z;
            if(v1 >= arr_b.length) {
                break;
            }
            int v2 = arr_b[v1];
            if(v2 < 0x30 || v2 > 57) {
                if(v2 < 65 || v2 > 70) {
                    break;
                }
                v3 = v2 - 55;
            }
            else {
                v3 = v2 - 0x30;
            }
            v = v << 4 | v3;
            this.A = v1 + 1;
        }
        return v;
    }

    public String s() {
        int v = this.A;
        byte[] arr_b = this.z;
        if(v >= arr_b.length) {
            return null;
        }
        if(arr_b[v] == 0x20) {
            this.A = v + 1;
        }
        if(this.B == null) {
            this.B = new char[0x2000];
        }
        int v1;
        for(v1 = 0; true; ++v1) {
            int v2 = this.A;
            byte[] arr_b1 = this.z;
            if(v2 >= arr_b1.length) {
                break;
            }
            this.A = v2 + 1;
            char c = (char)arr_b1[v2];
            if(c == 10) {
                break;
            }
            this.B[v1] = c;
        }
        return v1 == 0 ? null : String.valueOf(this.B, 0, v1);
    }

    public boolean t() {
        byte[] arr_b;
        do {
            int v = this.A;
            arr_b = this.z;
            if(v >= arr_b.length) {
                break;
            }
            this.A = v + 1;
        }
        while(arr_b[v] != 10);
        return this.A < arr_b.length;
    }

    protected byte[] u(int v) {
        byte[] arr_b = new byte[(v + 0x4000) / 0x4000 * 0x4000];
        System.arraycopy(this.z, 0, arr_b, 0, this.A);
        return arr_b;
    }

    protected void v(int v) {
        this.A = v;
    }

    public h w(int v) {
        this.k(9);
        if(this.z == null) {
            return this;
        }
        this.A(v);
        int v1 = this.A;
        this.A = v1 + 1;
        this.z[v1] = 0x20;
        return this;
    }

    public h x(b b0) {
        if(this.z == null) {
            return this;
        }
        b0.y(this);
        return this;
    }

    public h y(String s) {
        String s1 = "`";
        if(this.z == null) {
            return this;
        }
        if(s == null || s.length() == 0) {
            s = "`";
        }
        int v = s.length();
        if(v > 0x2000) {
            v.c("String length > MAX_STRING_LENGTH");
            v = 1;
        }
        else {
            s1 = s;
        }
        this.k(v + 1);
        this.D(s1);
        int v1 = this.A;
        this.A = v1 + 1;
        this.z[v1] = 0x20;
        return this;
    }

    public h z(String s) {
        if(this.z == null) {
            return this;
        }
        if(s == null) {
            this.w(-1);
            return this;
        }
        if(s.length() > 0) {
            byte[] arr_b = s.getBytes(p0.z);
            this.w(arr_b.length);
            this.k(arr_b.length + 1);
            for(int v = 0; v < arr_b.length; ++v) {
                int v1 = this.A;
                this.A = v1 + 1;
                this.z[v1] = arr_b[v];
            }
            int v2 = this.A;
            this.A = v2 + 1;
            this.z[v2] = 0x20;
            return this;
        }
        this.w(0);
        return this;
    }
}


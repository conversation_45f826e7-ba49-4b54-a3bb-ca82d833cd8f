package com.jozein.xedgepro.xposed;

import android.app.ActivityOptions;
import android.content.Context;
import android.graphics.Point;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.view.View;

class a {
    private static Bundle[] a;
    private static Bundle[] b;
    private static Bundle c;
    private static Point d;

    static {
    }

    private static void a(Bundle[] arr_bundle, Context context0, int v, boolean z) {
        int v1 = 0x7F010000;  // anim:fade_out
        arr_bundle[v] = a.g(context0, 0x7F010002, (z ? 0x7F010007 : 0x7F010000));  // anim:slide_in_left
        arr_bundle[v + 1] = a.g(context0, 0x7F010003, (z ? 0x7F010006 : 0x7F010000));  // anim:slide_in_right
        arr_bundle[v + 2] = a.g(context0, 0x7F010004, (z ? 0x7F010005 : 0x7F010000));  // anim:slide_in_top
        if(z) {
            v1 = 0x7F010008;  // anim:slide_out_top
        }
        arr_bundle[v + 3] = a.g(context0, 0x7F010001, v1);  // anim:slide_in_bottom
    }

    private static void b(Bundle[] arr_bundle, int v, int v1, int v2) {
        int v3 = (int)(((float)v1) * 0.15f);
        int v4 = (int)(0.15f * ((float)v2));
        int v5 = (int)(((float)v1) * 0.7f);
        int v6 = (int)(((float)v2) * 0.7f);
        arr_bundle[v] = a.h(-v1, v4, v5, v6);
        arr_bundle[v + 1] = a.h(v1, v4, v5, v6);
        arr_bundle[v + 2] = a.h(v3, -v2, v5, v6);
        arr_bundle[v + 3] = a.h(v3, v2, v5, v6);
    }

    static Bundle c(View view0) {
        return ActivityOptions.makeScaleUpAnimation(view0, 0, 0, view0.getWidth(), view0.getHeight()).toBundle();
    }

    static Bundle d(l0 l00, int v) {
        if(v >= 0 && v <= 3) {
            if(a.a == null) {
                a.a = new Bundle[8];
                Context context0 = p2.d(l00.m0());
                a.a(a.a, context0, 0, false);
                a.a(a.a, context0, 4, true);
            }
            return l00.G0() ? a.a[v] : a.a[v + 4];
        }
        if(a.c == null) {
            a.c = a.g(l00.m0(), 0x10A0000, 0x10A0001);
        }
        return a.c;
    }

    static Bundle e(Bundle bundle0) {
        return a.f(bundle0, 0);
    }

    static Bundle f(Bundle bundle0, int v) {
        if(Build.VERSION.SDK_INT >= 26 && v.f) {
            if(bundle0 == null) {
                bundle0 = new Bundle();
            }
            bundle0.putInt("android.activity.launchDisplayId", v);
        }
        return bundle0;
    }

    private static Bundle g(Context context0, int v, int v1) {
        Bundle bundle0 = ActivityOptions.makeCustomAnimation(context0, v, v1).toBundle();
        if(Build.VERSION.SDK_INT >= 0x1F) {
            bundle0.putBoolean("android:activity.overrideTaskTransition", true);
        }
        return bundle0;
    }

    private static Bundle h(int v, int v1, int v2, int v3) {
        Bundle bundle0 = new Bundle();
        bundle0.putString("android:activity.packageName", "android");
        bundle0.putInt("android:activity.animType", 2);
        bundle0.putInt("android:activity.animStartX", v);
        bundle0.putInt("android:activity.animStartY", v1);
        bundle0.putInt("android:activity.animWidth", v2);
        bundle0.putInt("android:activity.animHeight", v3);
        return bundle0;
    }

    static Bundle i(Bundle bundle0, int v) {
        if(Build.VERSION.SDK_INT >= 28) {
            if(bundle0 == null) {
                bundle0 = new Bundle();
            }
            bundle0.putInt("android.activity.windowingMode", v);
        }
        return bundle0;
    }
}


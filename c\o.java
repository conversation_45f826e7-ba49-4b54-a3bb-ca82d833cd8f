package c;

import a.b.b1;
import android.os.Bundle;
import e.y;

public class o extends y {
    public o M(CharSequence charSequence0) {
        this.e().putCharSequence("text", charSequence0);
        return this;
    }

    @Override  // e.j0$b
    protected void o(Bundle bundle0) {
        CharSequence charSequence0 = bundle0.getCharSequence("result");
        if(charSequence0 != null) {
            bundle0.remove("result");
            bundle0.putParcelable("result", new b1(charSequence0.toString()));
        }
        super.o(bundle0);
    }

    @Override  // e.j0$b
    public void onCreate(Bundle bundle0) {
        super.onCreate(bundle0);
        if(bundle0 == null) {
            this.J(this.k(0x7F06002D), this.k(0x7F060206), this.e().getCharSequence("text"), 5, 0x1000);  // string:action_inject_text "Input text"
        }
    }
}


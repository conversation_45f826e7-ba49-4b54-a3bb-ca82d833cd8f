package e;

import android.app.Activity;
import android.app.AlertDialog.Builder;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.KeyEvent;
import android.widget.ProgressBar;

public class e0 extends b {
    @Override  // e.j0$b
    protected void n() {
    }

    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        Activity activity0 = this.getActivity();
        Dialog dialog0 = new AlertDialog.Builder(activity0).setView(new ProgressBar(activity0)).setTitle(this.e().getCharSequence("title")).setOnKeyListener((DialogInterface dialogInterface0, int v, KeyEvent keyEvent0) -> true).create();
        ((AlertDialog)dialog0).setCanceledOnTouchOutside(false);
        ((AlertDialog)dialog0).setCancelable(false);
        return dialog0;
    }

    // 检测为 Lambda 实现
    public static boolean u(DialogInterface dialogInterface0, int v, KeyEvent keyEvent0) [...]

    private static boolean v(DialogInterface dialogInterface0, int v, KeyEvent keyEvent0) [...] // Inlined contents
}


package e;

import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface;
import android.os.Bundle;

public class z extends b {
    static class a {
    }

    class e.z.b implements DialogInterface.OnClickListener {
        final z z;

        private e.z.b() {
        }

        e.z.b(a z$a0) {
        }

        @Override  // android.content.DialogInterface$OnClickListener
        public void onClick(DialogInterface dialogInterface0, int v) {
            if(v != -2) {
                if(v != z.this.e().getInt("checked", -1)) {
                    z.this.p("result", v);
                }
                z.this.dismiss();
            }
        }
    }

    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        Bundle bundle1 = this.e();
        int v = bundle1.getInt("checked", -1);
        e.z.b z$b0 = new e.z.b(this, null);
        AlertDialog.Builder alertDialog$Builder0 = new AlertDialog.Builder(this.getActivity());
        CharSequence[] arr_charSequence = bundle1.getCharSequenceArray("items");
        if(v == -1) {
            alertDialog$Builder0.setItems(arr_charSequence, z$b0);
            return alertDialog$Builder0.setNegativeButton(0x1040000, z$b0).create();
        }
        alertDialog$Builder0.setSingleChoiceItems(arr_charSequence, v, z$b0);
        return alertDialog$Builder0.setNegativeButton(0x1040000, z$b0).create();
    }

    public z u(CharSequence[] arr_charSequence) {
        this.e().putCharSequenceArray("items", arr_charSequence);
        return this;
    }

    public z v(CharSequence[] arr_charSequence, int v) {
        Bundle bundle0 = this.e();
        bundle0.putInt("checked", v);
        bundle0.putCharSequenceArray("items", arr_charSequence);
        return this;
    }
}


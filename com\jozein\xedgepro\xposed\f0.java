package com.jozein.xedgepro.xposed;

import android.content.Context;
import android.os.Handler;
import g.q;
import g.r;

class f0 {
    private final Context a;
    private final Handler b;
    private r c;
    private final int d;
    private final int e;

    f0(Context context0, Handler handler0, boolean z) {
        this.c = null;
        this.a = context0;
        this.b = handler0;
        this.d = z ? 0xFF0097A7 : 0xFFFFA000;
        this.e = z ? 3 : 4;
    }

    public void d(q q0) {
        e0 e00 = () -> {
            r r0 = this.c;
            if(r0 != null) {
                r0.e(q0);
            }
        };
        this.b.post(e00);
    }

    public void e() {
        c0 c00 = () -> {
            t3.n().t(this.e);
            this.c = null;
        };
        this.b.post(c00);
    }

    // 检测为 Lambda 实现
    private void f(q q0) [...]

    // 检测为 Lambda 实现
    private void g() [...]

    // 检测为 Lambda 实现
    private void h() [...]

    public void i() {
        d0 d00 = () -> if(this.c == null) {
            r r0 = new r(this.a, this.d);
            this.c = r0;
            r0.l(1, 0, false);
            t3.n().j(this.c, this.e);
        };
        this.b.post(d00);
    }
}


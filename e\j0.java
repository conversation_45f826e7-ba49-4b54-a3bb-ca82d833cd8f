package e;

import a.m;
import android.app.ActionBar.LayoutParams;
import android.app.ActionBar;
import android.app.Activity;
import android.app.DialogFragment;
import android.app.Fragment;
import android.app.FragmentManager;
import android.content.Context;
import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.Parcelable;
import android.os.SystemClock;
import android.util.SparseArray;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.View.OnClickListener;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager.LayoutParams;
import android.view.WindowManager;
import android.view.animation.TranslateAnimation;
import android.view.inputmethod.InputMethodManager;
import android.widget.AbsListView.LayoutParams;
import android.widget.FrameLayout.LayoutParams;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout.LayoutParams;
import android.widget.TextView;
import android.widget.Toast;
import com.jozein.xedgepro.ApplicationMain;
import f.h0;
import f.p0;
import f.v;
import g.a0;
import g.h;
import g.p;
import g.x;
import java.util.List;

public abstract class j0 extends Activity {
    public static class b extends DialogFragment {
        private d A;
        protected static final DialogInterface.OnClickListener B;
        private Bundle z;

        static {
            b.B = k0.z;
        }

        public static void a(DialogInterface dialogInterface0, int v) {
        }

        protected Context c() {
            return j0.I;
        }

        protected ApplicationMain d() {
            return (ApplicationMain)this.getActivity().getApplication();
        }

        protected Bundle e() {
            if(this.z == null) {
                this.z = new Bundle();
            }
            return this.z;
        }

        protected final Drawable f(int v) {
            Drawable drawable0 = j0.J.getDrawable(v);
            h.a().h(drawable0, j0.J);
            return drawable0;
        }

        protected final x g() {
            return this.j().a;
        }

        protected c h() {
            Fragment fragment0 = this.getFragmentManager().findFragmentByTag(p0.h(this.getTag()));
            return fragment0 instanceof c ? ((c)fragment0) : null;
        }

        protected j0 i() {
            return (j0)this.getActivity();
        }

        final d j() {
            if(this.A == null) {
                this.A = this.i().D;
            }
            return this.A;
        }

        protected final CharSequence k(int v) {
            return j0.J.getText(v);
        }

        private static void l(DialogInterface dialogInterface0, int v) {
        }

        protected p m(Drawable drawable0) {
            Context context0 = this.getActivity();
            if(context0 == null) {
                context0 = j0.I;
            }
            p p0 = new p(context0);
            p0.setImageDrawable(drawable0);
            return p0;
        }

        protected void n() {
            this.dismiss();
        }

        protected void o(Bundle bundle0) {
            c j0$c0 = this.h();
            if(j0$c0 != null) {
                j0$c0.K(bundle0);
            }
        }

        @Override  // android.app.DialogFragment
        public void onCreate(Bundle bundle0) {
            super.onCreate(bundle0);
            if(bundle0 != null && this.z == null) {
                this.z = bundle0.getBundle("ElemDialogFragment_bundle");
            }
        }

        @Override  // android.app.DialogFragment
        public void onSaveInstanceState(Bundle bundle0) {
            Bundle bundle1 = this.z;
            if(bundle1 != null) {
                bundle0.putBundle("ElemDialogFragment_bundle", bundle1);
            }
            super.onSaveInstanceState(bundle0);
        }

        protected void p(String s, int v) {
            Bundle bundle0 = new Bundle(1);
            bundle0.putInt(s, v);
            this.o(bundle0);
        }

        protected void q(String s, Parcelable parcelable0) {
            Bundle bundle0 = new Bundle(1);
            bundle0.putParcelable(s, parcelable0);
            this.o(bundle0);
        }

        protected final void r(View view0) {
            j0.x(view0, this.e());
        }

        public void s(int v) {
            this.t(this.k(v));
        }

        public void t(CharSequence charSequence0) {
            j0.z(charSequence0);
        }
    }

    public static class c extends Fragment {
        private boolean A;
        private boolean B;
        private d C;
        private View D;
        private Bundle z;

        public c() {
            this.z = null;
            this.A = false;
            this.B = false;
            this.D = null;
        }

        protected void A() {
            if("0".equals(this.getTag())) {
                return;
            }
            View view0 = this.getView();
            if(view0 != null) {
                a0.y(view0, new TranslateAnimation(0.0f, ((float)view0.getWidth()), 0.0f, 0.0f));
            }
        }

        protected void B() {
            this.a0(this.h().getCharSequence("ElemFragment_subtitle"));
            this.T(null);
        }

        protected void C() {
        }

        protected boolean D(int v, KeyEvent keyEvent0) {
            return false;
        }

        protected boolean E(int v, KeyEvent keyEvent0) {
            if(this.D != null && (v == 20 || v == 82) && !keyEvent0.isCanceled()) {
                if(!this.w()) {
                    return v == 82 ? this.R() : false;
                }
                if(v == 20) {
                    j0 j00 = this.r();
                    if(j00 != null) {
                        j00.B.requestFocus(130);
                        return true;
                    }
                }
            }
            return false;
        }

        protected void F(Intent intent0) {
        }

        protected void G() {
            if(Build.VERSION.SDK_INT < 28) {
                this.A();
            }
            this.I();
            ActionBar actionBar0 = this.getActivity().getActionBar();
            if(actionBar0 != null && "1".equals(this.getTag())) {
                actionBar0.setDisplayHomeAsUpEnabled(false);
            }
            this.Z(null);
        }

        protected void H() {
            ActionBar actionBar0 = this.getActivity().getActionBar();
            if(actionBar0 != null && "1".equals(this.getTag())) {
                actionBar0.setDisplayHomeAsUpEnabled(true);
            }
            this.z(this.getView());
            c j0$c0 = this.p();
            if(j0$c0 != null) {
                j0$c0.I();
            }
        }

        protected void I() {
            if(this.D != null) {
                try {
                    ActionBar actionBar0 = this.getActivity().getActionBar();
                    if(actionBar0 != null && actionBar0.getCustomView() == this.D) {
                        actionBar0.setCustomView(null);
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
                this.D = null;
            }
        }

        protected void J(Bundle bundle0, int v) {
            v.c("Unhandled result.");
        }

        private void K(Bundle bundle0) {
            View view0 = this.getView();
            l0 l00 = () -> this.J(bundle0, this.h().getInt("ElemFragment_request_code"));
            if(view0 != null) {
                view0.post(l00);
                return;
            }
            h0.a().post(l00);
        }

        protected final void L() {
            try {
                this.B = true;
                if("0".equals(this.getTag())) {
                    this.getActivity().finish();
                }
                else {
                    this.getFragmentManager().beginTransaction().remove(this).show(this.p()).commit();
                }
                if(this.D != null) {
                    InputMethodManager inputMethodManager0 = (InputMethodManager)j0.I.getSystemService("input_method");
                    if(inputMethodManager0 != null) {
                        inputMethodManager0.hideSoftInputFromWindow(this.D.getWindowToken(), 0);
                    }
                }
            }
            catch(Throwable throwable0) {
                this.g0(throwable0);
            }
        }

        protected final void M(b j0$b0) {
            this.N(j0$b0, -1);
        }

        protected final void N(b j0$b0, int v) {
            d j0$d0 = this.C;
            if(j0$d0 != null) {
                j0$b0.A = j0$d0;
            }
            try {
                if(!this.e()) {
                    return;
                }
                this.h().putInt("ElemFragment_request_code", v);
                FragmentManager fragmentManager0 = this.getFragmentManager();
                if(fragmentManager0 == null) {
                    return;
                }
                String s = p0.o(this.getTag());
                fragmentManager0.beginTransaction().add(j0$b0, s).commit();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        protected final void O(c j0$c0) {
            this.P(j0$c0, -1);
        }

        protected final void P(c j0$c0, int v) {
            d j0$d0 = this.C;
            if(j0$d0 != null) {
                j0$c0.C = j0$d0;
            }
            try {
                if(!this.e()) {
                    return;
                }
                this.h().putInt("ElemFragment_request_code", v);
                FragmentManager fragmentManager0 = this.getFragmentManager();
                if(fragmentManager0 == null) {
                    return;
                }
                fragmentManager0.beginTransaction().add(0, j0$c0, p0.o(this.getTag())).hide(this).commit();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        private boolean Q(View view0) {
            if(view0.requestFocus(33)) {
                return true;
            }
            if(view0.isFocusableInTouchMode()) {
                return false;
            }
            view0.setFocusableInTouchMode(true);
            boolean z = view0.requestFocus(33);
            view0.setFocusableInTouchMode(false);
            return z;
        }

        private boolean R() {
            if(this.D != null && !this.D.isFocused()) {
                if(this.D.isFocusable()) {
                    return this.Q(this.D);
                }
                View view0 = this.D;
                if(view0 instanceof ViewGroup) {
                    for(int v = ((ViewGroup)view0).getChildCount() - 1; v >= 0; --v) {
                        View view1 = ((ViewGroup)view0).getChildAt(v);
                        if(view1.isFocusable()) {
                            return this.Q(view1);
                        }
                    }
                }
            }
            return false;
        }

        protected void S(int v, View.OnClickListener view$OnClickListener0) {
            if(this.B) {
                return;
            }
            Context context0 = this.getActivity();
            if(context0 == null) {
                context0 = j0.I;
            }
            this.T(this.v(context0, v, view$OnClickListener0));
        }

        protected final void T(View view0) {
            ActionBar actionBar0 = this.getActivity().getActionBar();
            if(actionBar0 != null) {
                if(view0 == null) {
                    actionBar0.setCustomView(null);
                }
                else {
                    x x0 = this.l();
                    ActionBar.LayoutParams actionBar$LayoutParams0 = new ActionBar.LayoutParams(-2, -1, 0x800015);
                    actionBar$LayoutParams0.rightMargin = x0.f;
                    actionBar0.setCustomView(view0, actionBar$LayoutParams0);
                    this.D = view0;
                }
                actionBar0.setDisplayShowCustomEnabled(true);
            }
        }

        protected void U(Bundle bundle0) {
            c j0$c0 = this.p();
            if(j0$c0 != null) {
                j0$c0.K(bundle0);
            }
        }

        protected void V(String s, int v) {
            Bundle bundle0 = new Bundle(1);
            bundle0.putInt(s, v);
            this.U(bundle0);
        }

        protected void W(String s, Parcelable parcelable0) {
            Bundle bundle0 = new Bundle(1);
            bundle0.putParcelable(s, parcelable0);
            this.U(bundle0);
        }

        protected void X(String s, boolean z) {
            Bundle bundle0 = new Bundle(1);
            bundle0.putBoolean(s, z);
            this.U(bundle0);
        }

        protected final void Y(View view0) {
            j0.x(view0, this.h());
        }

        protected final void Z(Object object0) {
            if(object0 == null) {
                Bundle bundle0 = this.z;
                if(bundle0 != null) {
                    int v = bundle0.getInt("BaseActivity_fragment_id", 0);
                    if(v != 0) {
                        j0.L.remove(v);
                    }
                }
            }
            else {
                Bundle bundle1 = this.h();
                int v1 = bundle1.getInt("BaseActivity_fragment_id", 0);
                if(v1 == 0) {
                    v1 = this.hashCode();
                    bundle1.putInt("BaseActivity_fragment_id", v1);
                }
                j0.L.append(v1, object0);
            }
        }

        protected final void a0(CharSequence charSequence0) {
            ActionBar actionBar0 = this.getActivity().getActionBar();
            if(actionBar0 != null) {
                actionBar0.setSubtitle(charSequence0);
            }
        }

        public final c b0(CharSequence charSequence0) {
            this.h().putCharSequence("ElemFragment_subtitle", charSequence0);
            return this;
        }

        protected final void c0(int v) {
            this.d0(this.u(v));
        }

        protected final void d0(CharSequence charSequence0) {
            Activity activity0 = this.getActivity();
            if(activity0 != null) {
                activity0.setTitle(charSequence0);
            }
        }

        private boolean e() {
            j0 j00 = this.r();
            long v = SystemClock.uptimeMillis();
            j00.C = v;
            return v - j00.C > 500L;
        }

        public final void e0(int v) {
            j0.z(this.u(v));
        }

        protected final Context f() {
            return j0.I;
        }

        public final void f0(CharSequence charSequence0) {
            j0.z(charSequence0);
        }

        protected ApplicationMain g() {
            return (ApplicationMain)this.getActivity().getApplication();
        }

        public final void g0(Throwable throwable0) {
            String s = throwable0.getMessage();
            if(s == null) {
                s = throwable0.toString();
            }
            this.f0(s);
            v.d(throwable0);
        }

        protected final Bundle h() {
            if(this.z == null) {
                this.z = new Bundle();
            }
            return this.z;
        }

        protected final Drawable i(int v) {
            Drawable drawable0 = j0.J.getDrawable(v);
            h.a().h(drawable0, j0.J);
            return drawable0;
        }

        protected final Drawable j(int v) {
            Drawable drawable0 = j0.J.getDrawable(v);
            h.a().b(drawable0);
            return drawable0;
        }

        protected final x k() {
            return this.t().b;
        }

        protected final x l() {
            return this.t().a;
        }

        protected final List m(Class class0) {
            Object object0 = this.s();
            return !(object0 instanceof List) || ((List)object0).size() <= 0 || !class0.isInstance(((List)object0).get(0)) ? null : ((List)object0);
        }

        protected final Fragment n() {
            try {
                return this.getFragmentManager().findFragmentByTag(p0.o(this.getTag()));
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return null;
            }
        }

        protected final CharSequence o() {
            return this.h().getCharSequence("ElemFragment_subtitle");
        }

        @Override  // android.app.Fragment
        public void onCreate(Bundle bundle0) {
            super.onCreate(bundle0);
            if(bundle0 != null) {
                if(this.z == null) {
                    this.z = bundle0.getBundle("ElemFragment_bundle");
                }
                ActionBar actionBar0 = this.getActivity().getActionBar();
                if(actionBar0 != null && "1".equals(this.getTag())) {
                    actionBar0.setDisplayHomeAsUpEnabled(true);
                }
                if(bundle0.getBoolean("ElemFragment_hidden", false)) {
                    this.getFragmentManager().beginTransaction().hide(this).commit();
                }
                this.A = true;
                return;
            }
            this.A = false;
        }

        @Override  // android.app.Fragment
        public void onDestroy() {
            super.onDestroy();
            if(Build.VERSION.SDK_INT >= 28 && !this.A) {
                this.G();
            }
        }

        @Override  // android.app.Fragment
        public void onHiddenChanged(boolean z) {
            super.onHiddenChanged(z);
            if(!z && !this.B) {
                this.B();
                this.C();
            }
        }

        @Override  // android.app.Fragment
        public void onSaveInstanceState(Bundle bundle0) {
            super.onSaveInstanceState(bundle0);
            Bundle bundle1 = this.z;
            if(bundle1 != null) {
                bundle0.putBundle("ElemFragment_bundle", bundle1);
            }
            this.A = true;
            if(this.isHidden()) {
                bundle0.putBoolean("ElemFragment_hidden", true);
                return;
            }
            bundle0.remove("ElemFragment_hidden");
        }

        @Override  // android.app.Fragment
        public void onStart() {
            super.onStart();
            if(!this.A) {
                this.H();
            }
            if(!this.isHidden()) {
                this.B();
                this.C();
            }
            this.A = false;
        }

        @Override  // android.app.Fragment
        public void onStop() {
            super.onStop();
            if(Build.VERSION.SDK_INT < 28 && !this.A) {
                this.G();
            }
        }

        protected final c p() {
            try {
                Fragment fragment0 = this.getFragmentManager().findFragmentByTag(p0.h(this.getTag()));
                if(fragment0 instanceof c) {
                    return (c)fragment0;
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            return null;
        }

        protected final Resources q() {
            return j0.J;
        }

        protected final j0 r() {
            return (j0)this.getActivity();
        }

        protected final Object s() {
            Bundle bundle0 = this.z;
            if(bundle0 != null) {
                int v = bundle0.getInt("BaseActivity_fragment_id", 0);
                return v == 0 ? null : j0.L.get(v);
            }
            return null;
        }

        final d t() {
            if(this.C == null) {
                this.C = this.r().D;
            }
            return this.C;
        }

        protected final CharSequence u(int v) {
            return j0.J.getText(v);
        }

        View v(Context context0, int v, View.OnClickListener view$OnClickListener0) {
            View view0 = new FrameLayout(context0);
            ((FrameLayout)view0).setFocusable(true);
            ((FrameLayout)view0).setOnClickListener(view$OnClickListener0);
            ((FrameLayout)view0).setBackground(a0.l());
            ImageView imageView0 = new ImageView(context0);
            imageView0.setImageResource(v);
            int v1 = (int)(((float)this.l().b) * 0.75f);
            FrameLayout.LayoutParams frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(v1, v1, 17);
            x x0 = this.l();
            frameLayout$LayoutParams0.rightMargin = x0.f / 2;
            frameLayout$LayoutParams0.leftMargin = x0.f / 2;
            ((FrameLayout)view0).addView(imageView0, frameLayout$LayoutParams0);
            return view0;
        }

        private boolean w() {
            if(this.D != null && this.D.isFocused()) {
                return true;
            }
            View view0 = this.D;
            if(view0 instanceof ViewGroup) {
                for(int v = ((ViewGroup)view0).getChildCount() - 1; v >= 0; --v) {
                    if(((ViewGroup)view0).getChildAt(v).isFocused()) {
                        return true;
                    }
                }
            }
            return false;
        }

        // 检测为 Lambda 实现
        private void x(Bundle bundle0) [...]

        protected void y() {
            this.L();
        }

        protected void z(View view0) {
            if(view0 != null && !"0".equals(this.getTag())) {
                a0.y(view0, new TranslateAnimation(((float)this.getActivity().getWindow().getDecorView().getWidth()), 0.0f, 0.0f, 0.0f));
            }
        }
    }

    static class d {
        final x a;
        final x b;
        final x c;
        final int d;
        final int e;
        final int f;
        final int g;
        final int h;
        final int i;
        final LinearLayout.LayoutParams j;
        final LinearLayout.LayoutParams k;
        final LinearLayout.LayoutParams l;
        final LinearLayout.LayoutParams m;
        final AbsListView.LayoutParams n;
        int o;
        int p;

        d(Context context0, boolean z) {
            int v;
            x x0 = x.c(context0);
            this.a = x0;
            this.b = x.b(context0);
            x x1 = x.a(context0);
            this.c = x1;
            if(m.b().a(4) != 2) {
                x0 = x1;
            }
            this.d = x0.f;
            this.e = x0.g;
            this.f = (int)(((float)x0.g) * 1.5f);
            this.g = x0.c;
            this.h = x0.e;
            this.i = x0.d;
            LinearLayout.LayoutParams linearLayout$LayoutParams0 = new LinearLayout.LayoutParams(x0.b, x0.b);
            this.j = linearLayout$LayoutParams0;
            linearLayout$LayoutParams0.gravity = 16;
            linearLayout$LayoutParams0.rightMargin = x0.f;
            linearLayout$LayoutParams0.leftMargin = x0.f;
            LinearLayout.LayoutParams linearLayout$LayoutParams1 = new LinearLayout.LayoutParams(-1, -2, 1.0f);
            this.k = linearLayout$LayoutParams1;
            linearLayout$LayoutParams1.gravity = 16;
            linearLayout$LayoutParams1.rightMargin = x0.f;
            linearLayout$LayoutParams1.leftMargin = x0.f;
            LinearLayout.LayoutParams linearLayout$LayoutParams2 = new LinearLayout.LayoutParams(-2, x0.a);
            this.l = linearLayout$LayoutParams2;
            linearLayout$LayoutParams2.gravity = 16;
            LinearLayout.LayoutParams linearLayout$LayoutParams3 = new LinearLayout.LayoutParams(x0.a / 2, x0.a / 2);
            this.m = linearLayout$LayoutParams3;
            linearLayout$LayoutParams3.gravity = 16;
            this.n = new AbsListView.LayoutParams(-1, x0.a);
            if(z) {
                this.o = -1;
                v = 0xFFCCCCCC;
            }
            else {
                this.o = 0xFF000000;
                v = -12303292;
            }
            this.p = v;
        }
    }

    private Bundle A;
    private FrameLayout B;
    private long C;
    private d D;
    private FrameLayout E;
    private TextView F;
    private boolean G;
    private static final int H;
    private static Context I;
    private static Resources J;
    private static Toast K;
    private static final SparseArray L;
    private int z;

    static {
        j0.H = View.generateViewId();
        j0.I = null;
        j0.J = null;
        j0.K = null;
        j0.L = new SparseArray();
    }

    public j0() {
        this.z = 0;
        this.A = null;
        this.B = null;
        this.C = 0L;
        this.D = null;
        this.E = null;
        this.F = null;
        this.G = false;
    }

    public void n() {
        if(this.E != null) {
            this.getWindowManager().removeView(this.E);
            this.E = null;
            this.F = null;
        }
    }

    public Bundle o() {
        if(this.A == null) {
            this.A = new Bundle();
        }
        return this.A;
    }

    @Override  // android.app.Activity
    public void onBackPressed() {
        Fragment fragment0 = this.getFragmentManager().findFragmentById(j0.H);
        if(fragment0 instanceof c) {
            ((c)fragment0).y();
            return;
        }
        if(fragment0 instanceof b) {
            ((b)fragment0).n();
            return;
        }
        if(fragment0 == null) {
            v.d(new RuntimeException("Null pointer of fragment."));
        }
        else {
            v.d(new RuntimeException("Invalid fragment type: " + fragment0.getClass().getName()));
        }
        super.onBackPressed();
    }

    @Override  // android.app.Activity
    protected void onCreate(Bundle bundle0) {
        boolean z;
        if(j0.I == null) {
            Context context0 = this.getApplicationContext();
            j0.I = context0;
            j0.J = context0.getResources();
        }
        if(this.z == 0) {
            int v = this.q();
            this.z = v;
            super.setTheme(v);
        }
        switch(this.z) {
            case 0x7F070002: {  // style:AppThemeAuto
                z = this.s();
                break;
            }
            case 0x7F070003:   // style:AppThemeBlack
            case 0x7F070004: {  // style:AppThemeDark
                z = true;
                break;
            }
            default: {
                z = false;
            }
        }
        this.D = new d(this, this.r(z));
        if(this.A == null && bundle0 != null) {
            this.A = bundle0.getBundle("BaseActivity_bundle");
        }
        super.onCreate(bundle0);
        FrameLayout frameLayout0 = new FrameLayout(this);
        this.B = frameLayout0;
        int v1 = j0.H;
        frameLayout0.setId(v1);
        this.setContentView(this.B);
        FragmentManager fragmentManager0 = this.getFragmentManager();
        if(fragmentManager0.findFragmentById(v1) == null) {
            c j0$c0 = this.v();
            if(j0$c0 != null) {
                j0$c0.C = this.D;
                fragmentManager0.beginTransaction().add(v1, j0$c0, "0").commit();
                return;
            }
            this.finish();
        }
    }

    @Override  // android.app.Activity
    protected void onDestroy() {
        super.onDestroy();
        this.n();
    }

    @Override  // android.app.Activity
    public boolean onKeyDown(int v, KeyEvent keyEvent0) {
        Fragment fragment0 = this.getFragmentManager().findFragmentById(j0.H);
        return fragment0 instanceof c && ((c)fragment0).D(v, keyEvent0) || super.onKeyDown(v, keyEvent0);
    }

    @Override  // android.app.Activity
    public boolean onKeyUp(int v, KeyEvent keyEvent0) {
        Fragment fragment0 = this.getFragmentManager().findFragmentById(j0.H);
        if(fragment0 instanceof c && ((c)fragment0).E(v, keyEvent0)) {
            return true;
        }
        if(this.E != null && (v == 23 || v == 66)) {
            this.w();
            return true;
        }
        return super.onKeyUp(v, keyEvent0);
    }

    @Override  // android.app.Activity
    protected void onNewIntent(Intent intent0) {
        super.onNewIntent(intent0);
        Fragment fragment0 = this.getFragmentManager().findFragmentById(j0.H);
        if(fragment0 instanceof c && "0".equals(fragment0.getTag())) {
            ((c)fragment0).F(intent0);
        }
    }

    @Override  // android.app.Activity
    public boolean onOptionsItemSelected(MenuItem menuItem0) {
        if(menuItem0.getItemId() == 0x102002C) {
            Fragment fragment0 = this.getFragmentManager().findFragmentById(j0.H);
            if(fragment0 instanceof c && !"0".equals(fragment0.getTag())) {
                ((c)fragment0).y();
            }
            return true;
        }
        return super.onOptionsItemSelected(menuItem0);
    }

    @Override  // android.app.Activity
    protected void onSaveInstanceState(Bundle bundle0) {
        super.onSaveInstanceState(bundle0);
        Bundle bundle1 = this.A;
        if(bundle1 != null) {
            bundle0.putBundle("BaseActivity_bundle", bundle1);
        }
    }

    @Override  // android.app.Activity
    protected void onStart() {
        super.onStart();
        if(Build.VERSION.SDK_INT < 21) {
            ActionBar actionBar0 = this.getActionBar();
            if(actionBar0 != null) {
                actionBar0.setBackgroundDrawable(new ColorDrawable(this.getResources().getColor(0x7F030003)));  // color:colorPrimary
            }
        }
    }

    protected final x p() {
        return this.D.a;
    }

    private int q() {
        switch(m.b().a(1)) {
            case 1: {
                return 0x7F070004;  // style:AppThemeDark
            }
            case 2: {
                return 0x7F070005;  // style:AppThemeLightGray
            }
            case 3: {
                return 0x7F070003;  // style:AppThemeBlack
            }
            case 4: {
                return this.s() ? 0x7F070004 : 0x7F070001;  // style:AppThemeDark
            }
            default: {
                return 0x7F070001;  // style:AppTheme
            }
        }
    }

    private boolean r(boolean z) {
        if(Build.VERSION.SDK_INT < 29) {
            try {
                return a0.s(this.getWindow().getDecorView().getBackground());
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        return z;
    }

    private boolean s() {
        return (this.getResources().getConfiguration().uiMode & 0x30) == 0x20;
    }

    @Override  // android.app.Activity
    public void setTheme(int v) {
        int v1 = this.q();
        this.z = v1;
        super.setTheme(v1);
    }

    // 检测为 Lambda 实现
    private void t(View view0) [...]

    // 检测为 Lambda 实现
    private void u() [...]

    protected abstract c v();

    private void w() {
        if(!this.G) {
            return;
        }
        Fragment fragment0 = this.getFragmentManager().findFragmentById(j0.H);
        if(fragment0 instanceof j) {
            ((j)fragment0).x1();
        }
        this.G = false;
    }

    private static void x(View view0, Bundle bundle0) {
        if(view0 != null && bundle0 != null) {
            int v = bundle0.getInt("BaseActivity_view_id", -1);
            if(v == -1) {
                v = View.generateViewId();
            }
            view0.setId(v);
        }
    }

    public void y(String s, long v) {
        class a extends FrameLayout {
            final j0 z;

            a(Context context0) {
                super(context0);
            }

            @Override  // android.view.ViewGroup
            public boolean dispatchKeyEvent(KeyEvent keyEvent0) {
                switch(keyEvent0.getKeyCode()) {
                    case 4: {
                        j0.this.n();
                        Fragment fragment0 = j0.this.getFragmentManager().findFragmentById(j0.H);
                        if(fragment0 instanceof j) {
                            ((j)fragment0).A0();
                        }
                        return true;
                    }
                    case 23: {
                        j0.this.w();
                        return true;
                    }
                    default: {
                        return super.dispatchKeyEvent(keyEvent0);
                    }
                }
            }
        }

        if(this.E == null) {
            a j0$a0 = new a(this, this);
            this.E = j0$a0;
            j0$a0.setKeepScreenOn(true);
            this.E.setFocusable(true);
            TextView textView0 = new TextView(this);
            this.F = textView0;
            textView0.setTextColor(-1);
            this.F.setBackgroundColor(this.getResources().getColor(0x7F030003));  // color:colorPrimary
            x x0 = this.p();
            this.F.setTextSize(0, ((float)x0.c));
            this.F.setPadding(x0.f, x0.f, x0.f, x0.f);
            this.F.setOnClickListener((View view0) -> this.w());
            this.E.addView(this.F, new FrameLayout.LayoutParams(-1, -2, 80));
            WindowManager windowManager0 = this.getWindowManager();
            WindowManager.LayoutParams windowManager$LayoutParams0 = new WindowManager.LayoutParams(1000, 0x400, -3);
            windowManager0.addView(this.E, windowManager$LayoutParams0);
        }
        if(s == null) {
            s = "";
        }
        this.F.setText(s + "\n\n" + this.getText(0x7F060093));  // string:click_to_continue "Click to continue…"
        this.G = false;
        this.F.postDelayed(() -> {
            this.G = true;
            TextView textView0 = this.F;
            if(textView0 != null) {
                textView0.requestFocus();
            }
        }, v + 100L);
    }

    private static void z(CharSequence charSequence0) {
        Toast toast0 = j0.K;
        if(toast0 == null) {
            j0.K = Toast.makeText(j0.I, charSequence0, 0);
        }
        else if(Build.VERSION.SDK_INT >= 24) {
            toast0.cancel();
            j0.K = Toast.makeText(j0.I, charSequence0, 0);
        }
        else {
            toast0.setText(charSequence0);
        }
        j0.K.show();
    }
}


package com.jozein.xedgepro.service;

import a.b.l3;
import a.b;
import android.content.ComponentName;
import android.content.Intent;
import android.widget.Toast;
import f.h0;
import f.l;
import f.m;
import f.n0;

public class ServiceTorch extends a {
    private n0 F;

    public ServiceTorch() {
        this.F = null;
    }

    @Override  // com.jozein.xedgepro.service.a
    protected void c() {
        this.a(0x7F0400BE, this.getText(0x7F060061));  // drawable:ic_torch
    }

    @Override  // com.jozein.xedgepro.service.a
    protected boolean d(Intent intent0) {
        int v1;
        m m0 = new m(intent0);
        boolean z = false;
        if(m0.i()) {
            b b0 = b.s(m0);
            if(b0.z == 0x20) {
                int v = this.F == null || !this.F.a() ? 0 : 1;
                switch(((l3)b0).I) {
                    case 1: {
                        v1 = 1;
                        break;
                    }
                    case 2: {
                        v1 = 0;
                        break;
                    }
                    default: {
                        v1 = v == 0 ? 1 : 0;
                    }
                }
                if(v != v1) {
                    if(v1 == 0) {
                        if(m0.h() != 0) {
                            z = true;
                        }
                        this.g(z);
                    }
                    else {
                        boolean z1 = m0.h() != 0;
                        if(m0.h() != 0) {
                            z = true;
                        }
                        this.h(z1, z);
                    }
                }
                m0.k();
                return true;
            }
        }
        m0.k();
        return false;
    }

    public static Intent f(l3 b$l30, boolean z, boolean z1) {
        Intent intent0 = new Intent();
        intent0.setComponent(new ComponentName(l.j, ServiceTorch.class.getName()));
        new m(intent0).j().p(b$l30).o(((int)z)).o(((int)z1)).l();
        return intent0;
    }

    private void g(boolean z) {
        if(this.F != null && this.F.a()) {
            this.F.e();
            if(z) {
                Toast.makeText(this.getApplicationContext(), new l3(2).n(this), 0).show();
            }
        }
        this.F = null;
        this.stopSelf();
    }

    private void h(boolean z, boolean z1) {
        class com.jozein.xedgepro.service.ServiceTorch.a implements f.n0.b {
            final ServiceTorch a;

            @Override  // f.n0$b
            public void a() {
                ServiceTorch.this.stopSelf();
            }

            @Override  // f.n0$b
            public void b() {
            }
        }

        if(this.F == null) {
            this.F = n0.b(h0.a(), this, new com.jozein.xedgepro.service.ServiceTorch.a(this));
        }
        if(!this.F.g()) {
            this.stopSelf();
            return;
        }
        if(z) {
            Toast.makeText(this.getApplicationContext(), new l3(1).n(this), 0).show();
        }
        if(z1) {
            this.c();
        }
    }

    @Override  // com.jozein.xedgepro.service.a
    public void onDestroy() {
        super.onDestroy();
        n0 n00 = this.F;
        if(n00 != null) {
            n00.e();
        }
    }
}


package com.jozein.xedgepro.xposed;

import a.b.i3;
import a.b.s0;
import a.b;
import a.p;
import android.content.Intent;
import android.os.Handler;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import de.robv.android.xposed.XC_MethodHook.MethodHookParam;
import f.l;
import f.m;
import f.v;

class a0 extends z {
    interface a {
        void a();

        void f();
    }

    private final p F;
    private final w1 G;
    private XC_MethodHook.MethodHookParam H;
    private XC_MethodHook.MethodHookParam I;
    private boolean J;
    private boolean K;
    private boolean L;
    private int M;
    private int N;
    private int O;
    private long P;
    private long Q;
    private long R;
    private a S;

    a0(w1 w10, y0 y00, Handler handler0) {
        super(y00, handler0);
        this.H = null;
        this.I = null;
        this.J = false;
        this.K = false;
        this.L = false;
        this.M = 0;
        this.N = 0;
        this.O = -1;
        this.P = 0L;
        this.Q = 500L;
        this.R = 300L;
        this.S = null;
        this.G = w10;
        this.F = w10.b2();
    }

    private boolean A(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, KeyEvent keyEvent0) {
        b b0 = this.q(keyEvent0.getKeyCode(), 2);
        if(b0.z == 0) {
            XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam1 = this.H;
            if(xC_MethodHook$MethodHookParam1 != null) {
                this.g(xC_MethodHook$MethodHookParam1);
                this.H = null;
            }
            return this.t(xC_MethodHook$MethodHookParam0);
        }
        this.J = true;
        this.H = null;
        this.G.u3(b0);
        return true;
    }

    private boolean B(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, KeyEvent keyEvent0) {
        a a0$a0 = this.S;
        if(a0$a0 != null) {
            a0$a0.f();
            this.S = null;
        }
        int v = this.M + 1;
        this.M = v;
        if(this.K) {
            if(this.J) {
                return true;
            }
            if(v == 2) {
                this.o();
                this.g(this.H);
                this.H = null;
                this.K = false;
            }
        }
        return this.L ? this.t(xC_MethodHook$MethodHookParam0) : false;
    }

    private boolean C(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, KeyEvent keyEvent0) {
        this.n();
        KeyEvent keyEvent1 = (KeyEvent)this.I.args[0];
        int v = keyEvent1.getKeyCode();
        if(keyEvent0.getKeyCode() == v && keyEvent0.getEventTime() - keyEvent1.getEventTime() < this.R) {
            return this.w(xC_MethodHook$MethodHookParam0, keyEvent0);
        }
        this.u();
        return this.x(xC_MethodHook$MethodHookParam0, keyEvent0);
    }

    private b D(int v) {
        b b0 = this.F.j(v, 1);
        return this.G.y4(b0, true);
    }

    @Override  // com.jozein.xedgepro.xposed.z
    protected boolean c(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, KeyEvent keyEvent0) {
        switch(keyEvent0.getAction()) {
            case 0: {
                return this.y(xC_MethodHook$MethodHookParam0, keyEvent0);
            }
            case 1: {
                return this.z(xC_MethodHook$MethodHookParam0, keyEvent0);
            }
            default: {
                return false;
            }
        }
    }

    @Override  // com.jozein.xedgepro.xposed.z
    protected void e() {
        this.u();
    }

    @Override  // com.jozein.xedgepro.xposed.z
    protected void f() {
        b b0 = this.q(((KeyEvent)this.H.args[0]).getKeyCode(), 2);
        if(b0.z == 0) {
            XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0 = this.H;
            if(xC_MethodHook$MethodHookParam0 != null) {
                this.g(xC_MethodHook$MethodHookParam0);
                this.H = null;
            }
        }
        else {
            this.J = true;
            this.p(b0);
        }
        this.H = null;
    }

    private void p(b b0) {
        if(b0.z == 86) {
            w0 w1$w00 = this.G.f2(((i3)b0));
            this.S = w1$w00;
            w1$w00.a();
            return;
        }
        this.G.u3(b0);
    }

    private b q(int v, int v1) {
        b b0 = this.F.j(v, v1);
        return this.G.y4(b0, false);
    }

    private boolean r(int v) {
        return this.F.I(v);
    }

    private XC_MethodHook.MethodHookParam s(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
        if(z.E) {
            Object[] arr_object = xC_MethodHook$MethodHookParam0.args;
            arr_object[0] = new KeyEvent(((KeyEvent)arr_object[0]));
        }
        return xC_MethodHook$MethodHookParam0;
    }

    private boolean t(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
        if(z.E) {
            this.g(this.s(xC_MethodHook$MethodHookParam0));
            return true;
        }
        return false;
    }

    private void u() {
        b b0 = this.q(((KeyEvent)this.H.args[0]).getKeyCode(), 0);
        if(b0.z == 0) {
            this.g(this.H);
            this.g(this.I);
        }
        else {
            this.J = true;
            this.G.u3(b0);
        }
        this.H = null;
        this.I = null;
    }

    private boolean v(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, KeyEvent keyEvent0) {
        int v = keyEvent0.getKeyCode();
        if(this.D(v).z == 0) {
            b b0 = this.q(v, 0);
            if(b0.z == 0) {
                XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam1 = this.H;
                if(xC_MethodHook$MethodHookParam1 != null) {
                    this.g(xC_MethodHook$MethodHookParam1);
                    this.H = null;
                }
                return this.t(xC_MethodHook$MethodHookParam0);
            }
            this.H = null;
            this.I = null;
            this.G.u3(b0);
            return true;
        }
        this.I = this.s(xC_MethodHook$MethodHookParam0);
        this.l(this.R);
        this.G.b1(this.R + 200L);
        return true;
    }

    private boolean w(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, KeyEvent keyEvent0) {
        b b0 = this.q(keyEvent0.getKeyCode(), 1);
        if(b0.z == 0) {
            this.g(this.H);
            this.g(this.I);
            this.K = false;
            this.J = false;
            this.H = null;
            this.I = null;
            return false;
        }
        this.K = true;
        this.J = true;
        this.H = null;
        this.I = null;
        this.p(b0);
        return true;
    }

    private boolean x(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, KeyEvent keyEvent0) {
        int v = keyEvent0.getKeyCode();
        this.L = false;
        if(this.G.q2() && this.F.G(v)) {
            this.K = true;
            this.J = true;
            return true;
        }
        if(this.r(v)) {
            this.P = keyEvent0.getEventTime();
            this.H = this.s(xC_MethodHook$MethodHookParam0);
            this.L = z.E;
            this.K = true;
            this.J = false;
            long v1 = (long)this.F.u(13);
            this.Q = v1;
            if(v1 == 0L) {
                this.Q = (long)ViewConfiguration.getLongPressTimeout();
            }
            long v2 = (long)this.F.u(12);
            this.R = v2;
            if(v2 == 0L) {
                this.R = (long)ViewConfiguration.getDoubleTapTimeout();
            }
            if(this.G.w2()) {
                this.m(this.Q);
            }
            return true;
        }
        this.K = false;
        this.J = false;
        return false;
    }

    private boolean y(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, KeyEvent keyEvent0) {
        if(this.M < 1) {
            this.M = 1;
            this.N = keyEvent0.getKeyCode();
            this.O = keyEvent0.getDeviceId();
            if(this.G.r2()) {
                this.G.u1();
                this.K = true;
                this.J = true;
                Intent intent0 = new Intent(s0.L);
                intent0.setPackage(l.j);
                new m(intent0).j().o(this.N).l();
                try {
                    this.G.R1().W(intent0, 0);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
                return true;
            }
            return this.I == null ? this.x(xC_MethodHook$MethodHookParam0, keyEvent0) : this.C(xC_MethodHook$MethodHookParam0, keyEvent0);
        }
        return keyEvent0.getKeyCode() != this.N || keyEvent0.getDeviceId() != this.O ? this.B(xC_MethodHook$MethodHookParam0, keyEvent0) : this.K || this.L;
    }

    private boolean z(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, KeyEvent keyEvent0) {
        a a0$a0 = this.S;
        if(a0$a0 != null) {
            a0$a0.f();
            this.S = null;
        }
        this.N = 0;
        this.O = -1;
        int v = this.M - 1;
        this.M = v;
        if(this.K) {
            if(this.J) {
                return true;
            }
            if(v == 0) {
                this.o();
                return keyEvent0.getEventTime() - this.P >= this.Q ? this.A(xC_MethodHook$MethodHookParam0, keyEvent0) : this.v(xC_MethodHook$MethodHookParam0, keyEvent0);
            }
        }
        return this.L ? this.t(xC_MethodHook$MethodHookParam0) : false;
    }
}


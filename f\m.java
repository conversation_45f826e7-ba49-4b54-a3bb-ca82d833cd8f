package f;

import a.b;
import android.content.Intent;
import android.os.Bundle;

public class m implements s {
    private final Bundle A;
    private e B;
    private static final String C;
    private final Intent z;

    static {
        m.C = l.k + "BYTES";
    }

    public m(Intent intent0) {
        this.B = null;
        this.z = intent0;
        this.A = null;
    }

    public m(Bundle bundle0) {
        this.B = null;
        this.z = null;
        this.A = bundle0;
    }

    @Override  // f.s
    public String a() {
        return this.B.a();
    }

    @Override  // f.s
    public s b(String s) {
        return this.r(s);
    }

    @Override  // f.s
    public s c(String s) {
        return this.q(s);
    }

    @Override  // f.s
    public s d(int v) {
        return this.o(v);
    }

    @Override  // f.s
    public String e() {
        return this.B.e();
    }

    @Override  // f.s
    public s f(b b0) {
        return this.p(b0);
    }

    @Override  // f.s
    public b g() {
        return b.s(this.B);
    }

    @Override  // f.s
    public int h() {
        return this.B.h();
    }

    public boolean i() {
        byte[] arr_b;
        Intent intent0 = this.z;
        if(intent0 == null) {
            arr_b = this.A == null ? null : this.A.getByteArray(m.C);
        }
        else {
            arr_b = intent0.getByteArrayExtra(m.C);
        }
        if(arr_b == null) {
            arr_b = e.B;
            v.c("No data found for action.");
        }
        e e0 = new e();
        this.B = e0;
        e0.i(arr_b);
        return true;
    }

    public m j() {
        this.B = new e().j();
        return this;
    }

    public void k() {
        this.B.m();
        this.B = null;
    }

    public void l() {
        byte[] arr_b = this.B.n();
        Intent intent0 = this.z;
        if(intent0 == null) {
            Bundle bundle0 = this.A;
            if(bundle0 == null) {
                v.d(new Throwable("intent == null && bundle == null!"));
            }
            else {
                bundle0.putByteArray(m.C, arr_b);
            }
        }
        else {
            intent0.putExtra(m.C, arr_b);
        }
        this.B = null;
    }

    public static b m(Intent intent0) {
        m m0 = new m(intent0);
        b b0 = m0.i() ? b.s(m0) : b.e();
        m0.k();
        return b0;
    }

    public static b n(Bundle bundle0) {
        m m0 = new m(bundle0);
        b b0 = m0.i() ? b.s(m0) : b.e();
        m0.k();
        return b0;
    }

    public m o(int v) {
        this.B.t(v);
        return this;
    }

    public m p(b b0) {
        b0.y(this.B);
        return this;
    }

    public m q(String s) {
        this.B.v(s);
        return this;
    }

    public m r(String s) {
        this.B.w(s);
        return this;
    }
}


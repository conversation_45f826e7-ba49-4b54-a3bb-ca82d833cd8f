package g;

import a.b;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;

public class w extends i {
    private final Drawable d;
    private final Drawable[] e;
    private final int f;
    private static final float[] g;
    private static final float[] h;

    static {
        w.g = new float[]{0.0f, 0.75f, 0.6f, 0.5f, 0.4f};
        w.h = new float[]{0.0f, 0.0f, 0.4f, 0.25f, 0.2f};
    }

    public w(Context context0, b[] arr_b) {
        this(context0, arr_b, null);
    }

    public w(Context context0, b[] arr_b, Drawable drawable0) {
        this.d = drawable0;
        int v = Math.min(arr_b.length, 4);
        this.f = v;
        this.e = new Drawable[v];
        for(int v1 = 0; v1 < this.f; ++v1) {
            this.e[v1] = arr_b[v1].o(context0);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public void draw(Canvas canvas0) {
        this.a(canvas0, this.d);
        for(int v = this.f - 1; v >= 0; --v) {
            this.a(canvas0, this.e[v]);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public int getIntrinsicHeight() {
        Drawable drawable0 = this.d;
        if(drawable0 != null) {
            drawable0.getIntrinsicHeight();
        }
        for(int v = 0; v < this.f; ++v) {
            Drawable[] arr_drawable = this.e;
            if(arr_drawable[v] != null) {
                return arr_drawable[v].getIntrinsicHeight();
            }
        }
        return super.getIntrinsicHeight();
    }

    @Override  // android.graphics.drawable.Drawable
    public int getIntrinsicWidth() {
        Drawable drawable0 = this.d;
        if(drawable0 != null) {
            return drawable0.getIntrinsicWidth();
        }
        for(int v = 0; v < this.f; ++v) {
            Drawable[] arr_drawable = this.e;
            if(arr_drawable[v] != null) {
                return arr_drawable[v].getIntrinsicWidth();
            }
        }
        return super.getIntrinsicWidth();
    }

    @Override  // android.graphics.drawable.Drawable
    public void setBounds(int v, int v1, int v2, int v3) {
        super.setBounds(v, v1, v2, v3);
        Drawable drawable0 = this.d;
        if(drawable0 != null) {
            drawable0.setBounds(v, v1, v2, v3);
        }
        int v4 = this.f;
        float[] arr_f = w.g;
        if(v4 >= arr_f.length) {
            v4 = arr_f.length - 1;
        }
        float f = (float)(v2 - v);
        int v5 = (int)(arr_f[v4] * f);
        int v6 = (int)(f * w.h[v4]);
        for(int v7 = 0; v7 < this.f; ++v7) {
            Drawable[] arr_drawable = this.e;
            if(arr_drawable[v7] != null) {
                int v8 = v6 * v7;
                arr_drawable[v7].setBounds(v + v8, v3 - v8 - v5, v + v8 + v5, v3 - v8);
            }
        }
    }
}


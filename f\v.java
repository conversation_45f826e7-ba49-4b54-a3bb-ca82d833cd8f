package f;

import android.os.SystemClock;
import android.util.Log;
import java.util.Arrays;
import java.util.Locale;

public class v implements l {
    static class a {
    }

    static class b {
        private final String[] a;
        private int b;
        private int c;
        private String d;

        private b() {
            this.a = new String[0x400];
            this.b = 0;
            this.c = 0;
            this.d = null;
        }

        b(a v$a0) {
        }

        void a(String s) {
            long v = SystemClock.elapsedRealtime();
            long v1 = v / 1000L / 60L;
            this.b(String.format(Locale.ENGLISH, "%03d-%02d:%02d:%02d.%03d  %s\n", ((int)(v1 / 60L / 24L)), ((int)(v1 / 60L % 24L)), ((int)(v1 % 60L)), ((int)(v / 1000L & 60L)), ((int)(v % 1000L)), s));
        }

        private void b(String s) {
            synchronized(this) {
                this.d = null;
                int v1 = this.b;
                this.a[v1] = s;
                ++this.c;
                this.b = v1 + 1;
                if(v1 + 1 >= this.a.length) {
                    this.b = 0;
                }
            }
        }

        String c() {
            synchronized(this) {
                if(this.c == 0) {
                    return "";
                }
                if(this.d == null) {
                    StringBuilder stringBuilder0 = new StringBuilder(0x400);
                    int v1 = this.c;
                    String[] arr_s = this.a;
                    if(v1 > arr_s.length) {
                        v1 = arr_s.length;
                        stringBuilder0.append("...\n");
                    }
                    int v2 = this.b - v1;
                    for(int v3 = 0; v3 < v1; ++v3) {
                        int v4 = v2 + v3;
                        stringBuilder0.append(this.a[(v4 >= 0 ? v2 + v3 : v4 + this.a.length)]);
                    }
                    this.d = stringBuilder0.toString();
                }
                return this.d;
            }
        }
    }

    private static b z;

    static {
    }

    public static void a() {
        if(v.z == null) {
            v.z = new b(null);
        }
    }

    public static String b() {
        return v.z == null ? "" : v.z.c();
    }

    public static void c(String s) {
        Log.i(l.x, s);
        if(v.z != null) {
            v.f(s);
        }
    }

    public static void d(Throwable throwable0) {
        Log.e(l.x, "Caught throwable:", throwable0);
        if(v.z != null) {
            try {
                StackTraceElement stackTraceElement0 = new Throwable().getStackTrace()[1];
                v.f((throwable0.getClass().getName() + ": " + throwable0.getMessage() + " - at " + stackTraceElement0.getClassName() + '.' + stackTraceElement0.getMethodName()));
            }
            catch(Throwable unused_ex) {
            }
        }
    }

    public static void e(Object[] arr_object) {
        String s;
        switch(arr_object.length) {
            case 0: {
                s = "";
                break;
            }
            case 1: {
                s = arr_object[0] == null ? "null" : arr_object[0].toString();
                break;
            }
            default: {
                s = Arrays.deepToString(arr_object);
            }
        }
        Log.i(l.x, s);
        if(v.z != null) {
            v.f(s);
        }
    }

    public static void f(String s) {
        b v$b0 = v.z;
        if(v$b0 != null) {
            v$b0.a(s);
        }
    }
}


package com.jozein.xedgepro.xposed;

import de.robv.android.xposed.XC_MethodHook.MethodHookParam;

public final class c2 implements Runnable {
    public final XC_MethodHook.MethodHookParam A;
    public final long B;
    public final g0 z;

    public c2(g0 w1$g00, XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, long v) {
        this.z = w1$g00;
        this.A = xC_MethodHook$MethodHookParam0;
        this.B = v;
    }

    @Override
    public final void run() {
        this.z.c(this.A, this.B);
    }
}


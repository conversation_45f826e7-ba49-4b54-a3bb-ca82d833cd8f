package com.jozein.xedgepro.xposed;

import android.content.res.Resources;
import android.view.View.OnClickListener;
import android.view.View;
import android.widget.FrameLayout;

public final class k implements View.OnClickListener {
    public final int A;
    public final FrameLayout B;
    public final Resources C;
    public final f D;
    public final int E;
    public final r z;

    public k(r r0, int v, FrameLayout frameLayout0, Resources resources0, f r$f0, int v1) {
        this.z = r0;
        this.A = v;
        this.B = frameLayout0;
        this.C = resources0;
        this.D = r$f0;
        this.E = v1;
    }

    @Override  // android.view.View$OnClickListener
    public final void onClick(View view0) {
        this.z.d0(this.A, this.B, this.C, this.D, this.E, view0);
    }
}


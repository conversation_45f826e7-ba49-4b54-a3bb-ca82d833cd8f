package f;

public interface l {
    public static final String j;
    public static final String k;
    public static final String l;
    public static final String m;
    public static final String n;
    public static final String o;
    public static final String p;
    public static final String q;
    public static final boolean r;
    public static final String s;
    public static final String t;
    public static final String u;
    public static final String v;
    public static final String w;
    public static final String x;
    public static final int y;

    static {
        l.j = "com.jozein.xedgepro";
        l.k = "com.jozein.xedgepro" + '.';
        String s = k.c();
        l.l = s;
        l.m = s + "prefs/";
        l.n = s + "gestures/";
        l.o = s + "icons/";
        l.p = s + "local_prefs";
        l.q = s + "widgets";
        boolean z = "com.jozein.xedgepro".contains("pro");
        l.r = z;
        l.s = "2210241503";
        l.t = 'v' + "8.0.1";
        l.u = (z ? "Xposed-edge-pro-" : "Xposed-edge-") + ('v' + "8.0.1");
        l.v = k.b();
        l.w = k.e();
        l.x = z ? "XEdgePro" : "XEdge";
        l.y = z ? 0x7F06007B : 0x7F06007A;  // string:app_name_pro "Xposed edge pro"
    }
}


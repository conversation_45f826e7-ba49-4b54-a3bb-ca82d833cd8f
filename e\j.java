package e;

import a.x;
import android.app.Fragment;
import android.appwidget.AppWidgetProviderInfo;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import android.text.TextUtils.TruncateAt;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View.OnAttachStateChangeListener;
import android.view.View.OnClickListener;
import android.view.View.OnTouchListener;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.AdapterView.OnItemLongClickListener;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.CheckBox;
import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.FrameLayout.LayoutParams;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout.LayoutParams;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.ProgressBar;
import android.widget.SearchView.OnQueryTextListener;
import android.widget.SearchView;
import android.widget.TextView;
import f.h0;
import f.l;
import f.p0;
import f.u;
import f.v;
import f.z;
import g.o;
import java.util.List;

public abstract class j extends c implements l {
    public interface d {
        String[] a();
    }

    public static class e implements Parcelable {
        class a implements Parcelable.Creator {
            a() {
                super();
            }

            public e a(Parcel parcel0) {
                return new e(parcel0);
            }

            public e[] b(int v) {
                return new e[v];
            }

            @Override  // android.os.Parcelable$Creator
            public Object createFromParcel(Parcel parcel0) {
                return this.a(parcel0);
            }

            @Override  // android.os.Parcelable$Creator
            public Object[] newArray(int v) {
                return this.b(v);
            }
        }

        public final int A;
        public final String B;
        public static final Parcelable.Creator CREATOR;
        public final int z;

        static {
            e.CREATOR = new a();
        }

        public e(int v, int v1, String s) {
            this.z = v;
            this.A = v1;
            this.B = s;
        }

        protected e(Parcel parcel0) {
            this.z = parcel0.readInt();
            this.A = parcel0.readInt();
            this.B = parcel0.readString();
        }

        @Override  // android.os.Parcelable
        public int describeContents() {
            return 0;
        }

        @Override  // android.os.Parcelable
        public void writeToParcel(Parcel parcel0, int v) {
            parcel0.writeInt(this.z);
            parcel0.writeInt(this.A);
            parcel0.writeString(this.B);
        }
    }

    public class f extends e.j.j {
        private final ImageView H;
        final j I;

        public f(Drawable drawable0, CharSequence charSequence0, CharSequence charSequence1, boolean z) {
            super(charSequence0, charSequence1, z);
            ImageView imageView0 = new ImageView(this.getContext());
            this.H = imageView0;
            imageView0.setImageDrawable(drawable0);
            this.addView(imageView0, 0, j0.t().j);
        }

        public f(CharSequence charSequence0, CharSequence charSequence1, Drawable drawable0, boolean z) {
            super(charSequence0, charSequence1, z);
            ImageView imageView0 = new ImageView(this.getContext());
            this.H = imageView0;
            imageView0.setImageDrawable(drawable0);
            this.addView(imageView0, 1, j0.t().j);
        }

        public void setImageColorFilter(int v) {
            this.H.setColorFilter(v);
        }

        public void setImageDrawable(Drawable drawable0) {
            this.H.setImageDrawable(drawable0);
        }
    }

    public class g extends k {
        public final ImageView E;
        final j F;

        public g(Drawable drawable0, CharSequence charSequence0, CharSequence charSequence1) {
            super(charSequence0, charSequence1);
            ImageView imageView0 = new ImageView(this.getContext());
            this.E = imageView0;
            imageView0.setImageDrawable(drawable0);
            this.addView(imageView0, 0, j0.t().j);
        }

        public g(CharSequence charSequence0, CharSequence charSequence1, Drawable drawable0) {
            super(charSequence0, charSequence1);
            ImageView imageView0 = new ImageView(this.getContext());
            this.E = imageView0;
            imageView0.setImageDrawable(drawable0);
            this.addView(imageView0, j0.t().j);
        }

        public void setImageDrawable(Drawable drawable0) {
            this.E.setImageDrawable(drawable0);
        }

        public void setImageResource(int v) {
            this.E.setImageResource(v);
        }
    }

    static class h implements Runnable {
        private final Context A;
        private volatile j B;
        private final i z;

        h(i j$i0, Context context0) {
            this.z = j$i0;
            this.A = context0;
        }

        // 检测为 Lambda 实现
        private void c(Object object0) [...]

        // 检测为 Lambda 实现
        private void d() [...]

        public void e(j j0) {
            this.B = j0;
        }

        @Override
        public void run() {
            try {
                Object object0 = this.z.a(this.A);
                h0.a().post(() -> {
                    j j0 = this.B;
                    if(j0 != null) {
                        try {
                            if(object0 != null) {
                                j0.r1(object0);
                                return;
                            }
                            j0.L();
                        }
                        catch(Throwable throwable0) {
                            v.d(throwable0);
                        }
                    }
                });
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                h0.a().post(() -> {
                    j j0 = this.B;
                    if(j0 != null) {
                        j0.L();
                    }
                });
            }
        }
    }

    public interface i {
        public static class e.j.i.a implements i {
            @Override  // e.j$i
            public Object a(Context context0) {
                return this.b(context0);
            }

            public final List b(Context context0) {
                return z.K(context0).R();
            }
        }

        public static class b implements i {
            @Override  // e.j$i
            public Object a(Context context0) {
                return this.b(context0);
            }

            public final List b(Context context0) {
                return z.K(context0).M();
            }
        }

        public static class e.j.i.c implements i {
            @Override  // e.j$i
            public Object a(Context context0) {
                return this.b(context0);
            }

            public final u b(Context context0) {
                return z.K(context0).B();
            }
        }

        public static class e.j.i.d implements i {
            private final boolean a;

            public e.j.i.d(boolean z) {
                this.a = z;
            }

            @Override  // e.j$i
            public Object a(Context context0) {
                return this.b(context0);
            }

            public final List b(Context context0) {
                return z.K(context0).Q(this.a);
            }
        }

        public static class e.j.i.e implements i {
            @Override  // e.j$i
            public Object a(Context context0) {
                return this.b(context0);
            }

            public List b(Context context0) {
                return z.K(context0).T();
            }
        }

        Object a(Context arg1);
    }

    public class e.j.j extends k implements View.OnTouchListener {
        private final CheckBox E;
        private CompoundButton.OnCheckedChangeListener F;
        final j G;

        public e.j.j(CharSequence charSequence0, CharSequence charSequence1, boolean z) {
            super(charSequence0, charSequence1);
            this.F = null;
            CheckBox checkBox0 = new CheckBox(this.getContext());
            this.E = checkBox0;
            checkBox0.setChecked(z);
            checkBox0.setFocusable(false);
            checkBox0.setOnTouchListener(this);
            this.addView(checkBox0, j0.t().l);
        }

        public boolean g() {
            return this.E.isChecked();
        }

        public void h() {
            this.E.toggle();
            CompoundButton.OnCheckedChangeListener compoundButton$OnCheckedChangeListener0 = this.F;
            if(compoundButton$OnCheckedChangeListener0 != null) {
                boolean z = this.E.isChecked();
                compoundButton$OnCheckedChangeListener0.onCheckedChanged(this.E, z);
            }
            j.this.z1();
        }

        @Override  // android.view.View$OnTouchListener
        public boolean onTouch(View view0, MotionEvent motionEvent0) {
            switch(motionEvent0.getActionMasked()) {
                case 0: {
                    ViewParent viewParent0 = this.getParent();
                    if(viewParent0 != null) {
                        viewParent0.requestDisallowInterceptTouchEvent(true);
                    }
                    return true;
                }
                case 1: {
                    this.E.toggle();
                    CompoundButton.OnCheckedChangeListener compoundButton$OnCheckedChangeListener0 = this.F;
                    if(compoundButton$OnCheckedChangeListener0 != null) {
                        boolean z = this.E.isChecked();
                        compoundButton$OnCheckedChangeListener0.onCheckedChanged(this.E, z);
                    }
                    j.this.z1();
                    return true;
                }
                default: {
                    return true;
                }
            }
        }

        public void setChecked(boolean z) {
            if(this.E.isChecked() != z) {
                this.E.setChecked(z);
                CompoundButton.OnCheckedChangeListener compoundButton$OnCheckedChangeListener0 = this.F;
                if(compoundButton$OnCheckedChangeListener0 != null) {
                    compoundButton$OnCheckedChangeListener0.onCheckedChanged(this.E, z);
                }
                j.this.z1();
            }
        }

        @Override  // e.j$k
        public void setEnabled(boolean z) {
            super.setEnabled(z);
            this.E.setEnabled(z);
        }

        public void setOnCheckedChangeListener(CompoundButton.OnCheckedChangeListener compoundButton$OnCheckedChangeListener0) {
            this.F = compoundButton$OnCheckedChangeListener0;
        }
    }

    public class k extends LinearLayout {
        private final TextView A;
        private ImageView B;
        private boolean C;
        final j D;
        private final TextView z;

        public k(int v, int v1) {
            this(j0.u(v), j0.u(v1));
        }

        public k(CharSequence charSequence0) {
            this(charSequence0, null);
        }

        public k(CharSequence charSequence0, CharSequence charSequence1) {
            super(j0.F.z);
            this.B = null;
            this.C = false;
            e.j0.d j0$d0 = j0.t();
            this.setLayoutParams(j0$d0.n);
            Context context0 = j0.F.z;
            this.setOrientation(0);
            TextView textView0 = j.e1(context0, charSequence0, j0$d0.g, j0$d0.o);
            this.z = textView0;
            if(charSequence1 != null) {
                LinearLayout linearLayout0 = new LinearLayout(context0);
                linearLayout0.setOrientation(1);
                linearLayout0.addView(textView0);
                TextView textView1 = j.e1(context0, charSequence1, j0$d0.i, j0$d0.p);
                this.A = textView1;
                linearLayout0.addView(textView1);
                this.addView(linearLayout0, j0$d0.k);
                return;
            }
            this.A = null;
            this.addView(textView0, j0$d0.k);
        }

        public void c() {
            e.j0.d j0$d0 = j.this.t();
            this.z.setTextColor(j0$d0.o);
            TextView textView0 = this.A;
            if(textView0 != null) {
                textView0.setTextColor(j0$d0.p);
            }
        }

        private void d() {
            if(this.B == null) {
                ImageView imageView0 = new ImageView(this.getContext());
                this.B = imageView0;
                this.addView(imageView0, j.this.t().m);
            }
            this.setDroppedUnchecked(false);
        }

        public void e() {
            this.z.setTextColor(0xFF888888);
            TextView textView0 = this.A;
            if(textView0 != null) {
                textView0.setTextColor(0xFF888888);
            }
        }

        private void setDroppedUnchecked(boolean z) {
            if(this.B != null) {
                Drawable drawable0 = j.this.i((z ? 0x7F04000D : 0x7F04000C));  // drawable:ic_arrow_up
                this.B.setImageDrawable(drawable0);
            }
        }

        @Override  // android.view.View
        public void setEnabled(boolean z) {
            if(z == this.isEnabled()) {
                return;
            }
            super.setEnabled(z);
            if(z) {
                this.c();
                return;
            }
            this.e();
        }

        public void setHighLight(boolean z) {
            if(this.C != z) {
                this.C = z;
                if(z) {
                    this.setBackgroundColor(1148680055);
                    return;
                }
                this.setBackground(null);
            }
        }

        public void setSubText(int v) {
            TextView textView0 = this.A;
            if(textView0 != null) {
                textView0.setText(v);
            }
        }

        public void setSubText(CharSequence charSequence0) {
            TextView textView0 = this.A;
            if(textView0 != null) {
                textView0.setText(charSequence0);
            }
        }

        public void setText(int v) {
            this.z.setText(v);
        }

        public void setText(CharSequence charSequence0) {
            this.z.setText(charSequence0);
        }
    }

    class e.j.l extends BaseAdapter implements AdapterView.OnItemClickListener, AdapterView.OnItemLongClickListener {
        private int A;
        private View[] B;
        private int C;
        private int D;
        private View[] E;
        private int[] F;
        final j G;
        private final Context z;

        e.j.l(Context context0, int v) {
            this.C = -1;
            this.D = -1;
            this.E = null;
            this.F = null;
            this.z = context0;
            this.s(v);
        }

        @Override  // android.widget.Adapter
        public int getCount() {
            int v = this.F == null ? this.A : this.F.length;
            return this.E == null ? v : v + this.E.length;
        }

        @Override  // android.widget.Adapter
        public Object getItem(int v) {
            return null;
        }

        @Override  // android.widget.Adapter
        public long getItemId(int v) {
            return (long)this.p(v);
        }

        @Override  // android.widget.Adapter
        public View getView(int v, View view0, ViewGroup viewGroup0) {
            try {
                int v1 = this.p(v);
                View view1 = v1 < 0 ? this.l(~v1) : this.m(v1);
                return view1 == null ? new View(this.z) : view1;
            }
            catch(Throwable unused_ex) {
                return new View(this.z);
            }
        }

        public void i(int v) {
            int v1 = this.C;
            if(v1 >= v) {
                this.C = v1 + 1;
                this.D = -1;
            }
            View[] arr_view = this.B;
            if(arr_view.length <= this.A) {
                View[] arr_view1 = new View[arr_view.length + 8];
                this.B = arr_view1;
                System.arraycopy(arr_view, 0, arr_view1, 0, v);
            }
            int v2 = this.A;
            if(v < v2) {
                System.arraycopy(arr_view, v, this.B, v + 1, v2 - v);
            }
            this.B[v] = null;
            ++this.A;
            this.o();
        }

        void j(int v, int v1, boolean z) {
            int v2;
            this.r();
            int[] arr_v = this.F;
            if(arr_v == null) {
                v2 = v;
            }
            else {
                v2 = p0.d(arr_v, v);
                if(v2 < 0) {
                    v.c(("Set sub position " + v + " not in filter indexes."));
                    return;
                }
            }
            this.C = v;
            this.D = v2;
            this.E = new View[v1];
            View view0 = this.m(v);
            if(view0 instanceof k) {
                ((k)view0).setDroppedUnchecked(true);
            }
            this.notifyDataSetChanged();
            if(z) {
                ListView listView0 = j.this.G0();
                if(listView0 != null) {
                    int v3 = listView0.getFirstVisiblePosition();
                    if(v2 <= v3) {
                        this.v(listView0, v2, 0);
                        return;
                    }
                    int v4 = listView0.getLastVisiblePosition();
                    int v5 = v1 + v2 + 1;
                    if(v5 > v4) {
                        int v6 = v5 - v4 + v3;
                        if(v6 <= v2) {
                            v2 = v6;
                        }
                        this.v(listView0, v2, 0);
                    }
                }
            }
        }

        void k(int v, int v1) {
            ListView listView0 = j.this.G0();
            if(listView0 == null) {
                return;
            }
            int v2 = listView0.getFirstVisiblePosition();
            int v3 = listView0.getLastVisiblePosition();
            if(v1 > 0) {
                v = v + v1 + 1;
            }
            if(v <= v2 || v >= v3) {
                this.v(listView0, v, 0);
            }
        }

        private View l(int v) {
            if(v >= 0) {
                View[] arr_view = this.E;
                if(arr_view != null && v <= arr_view.length) {
                    View view0 = arr_view[v];
                    if(view0 == null) {
                        if(j.this.getActivity() == null) {
                            return null;
                        }
                        View[] arr_view1 = this.E;
                        view0 = j.this.i1(v);
                        arr_view1[v] = view0;
                        e.j0.d j0$d0 = j.this.t();
                        view0.setPaddingRelative(j0$d0.f, 0, j0$d0.d, 0);
                    }
                    return view0;
                }
            }
            return null;
        }

        private View m(int v) {
            if(v >= 0) {
                View[] arr_view = this.B;
                if(v < arr_view.length) {
                    if(arr_view[v] == null) {
                        arr_view[v] = j.this.h1(v);
                        if(j.this.N0(v)) {
                            View[] arr_view1 = this.B;
                            if(arr_view1[v] instanceof k) {
                                ((k)arr_view1[v]).d();
                            }
                        }
                        e.j0.d j0$d0 = j.this.t();
                        this.B[v].setPadding(j0$d0.d, 0, j0$d0.d, 0);
                    }
                    return this.B[v];
                }
            }
            return null;
        }

        void n(int v, int v1) {
            if(v == v1) {
                return;
            }
            int v2 = this.C;
            if(v2 >= 0) {
                this.D = -1;
                if(v2 == v) {
                    this.C = v1;
                }
                else {
                    int v3 = this.C;
                    if(v3 >= Math.min(v, v1) && v3 <= Math.max(v, v1)) {
                        this.C = v >= v1 ? v3 + 1 : v3 - 1;
                    }
                }
            }
            p0.c(this.B, v, v1);
            this.o();
        }

        private void o() {
            if(this.F != null) {
                j.this.J = null;
                j.this.A1(j.this.I);
            }
            this.notifyDataSetChanged();
        }

        @Override  // android.widget.AdapterView$OnItemClickListener
        public void onItemClick(AdapterView adapterView0, View view0, int v, long v1) {
            int v2 = this.p(v);
            if(v2 >= 0) {
                this.u(v2, -1);
                try {
                    j.this.k1(v2);
                }
                catch(Throwable throwable0) {
                    j.this.g0(throwable0);
                }
                return;
            }
            this.u(this.C, ~v2);
            try {
                j.this.o1(~v2);
            }
            catch(Throwable throwable0) {
                j.this.g0(throwable0);
            }
        }

        @Override  // android.widget.AdapterView$OnItemLongClickListener
        public boolean onItemLongClick(AdapterView adapterView0, View view0, int v, long v1) {
            int v2 = this.p(v);
            if(v2 >= 0) {
                this.u(v2, -1);
                try {
                    return j.this.l1(v2);
                }
                catch(Throwable throwable0) {
                    j.this.g0(throwable0);
                    return true;
                }
            }
            this.u(this.C, ~v2);
            try {
                return j.this.p1(~v2);
            }
            catch(Throwable throwable0) {
                j.this.g0(throwable0);
                return true;
            }
        }

        private int p(int v) {
            View[] arr_view = this.E;
            if(arr_view != null) {
                int[] arr_v = this.F;
                if(arr_v != null) {
                    if(this.D < 0) {
                        int v1 = p0.d(arr_v, this.C);
                        this.D = v1;
                        if(v1 < 0) {
                            this.s(this.A);
                            this.notifyDataSetChanged();
                            throw new IllegalStateException("Set sub position " + this.C + " not in filter indexes.");
                        }
                    }
                    int v2 = this.D;
                    if(v <= v2) {
                        return this.F[v];
                    }
                    return v >= v2 + 1 + this.E.length ? this.F[v - this.E.length] : ~(v - v2 - 1);
                }
                int v3 = this.C;
                if(v > v3) {
                    return v >= v3 + 1 + arr_view.length ? v - arr_view.length : ~(v - v3 - 1);
                }
                return v;
            }
            return this.F == null ? v : this.F[v];
        }

        public View q(int v) {
            int v1 = this.C;
            if(v1 >= v) {
                this.C = v1 - 1;
                this.D = -1;
            }
            View[] arr_view = this.B;
            View view0 = arr_view[v];
            int v2 = this.A - 1;
            this.A = v2;
            System.arraycopy(arr_view, v + 1, arr_view, v, v2 - v);
            this.B[this.A] = null;
            this.o();
            return view0;
        }

        void r() {
            ListView listView0 = j.this.G0();
            if(listView0 != null) {
                listView0.setPressed(false);
                listView0.jumpDrawablesToCurrentState();
            }
            int v = this.C;
            if(v >= 0) {
                View view0 = this.m(v);
                if(view0 instanceof k) {
                    ((k)view0).setDroppedUnchecked(false);
                }
            }
            this.C = -1;
            this.D = -1;
            this.E = null;
            this.notifyDataSetChanged();
        }

        void s(int v) {
            this.A = v;
            this.B = new View[v + 7 & -8];
            this.C = -1;
            this.D = -1;
            this.E = null;
            this.F = null;
        }

        void t(int[] arr_v) {
            this.r();
            this.F = arr_v;
            this.notifyDataSetChanged();
        }

        private void u(int v, int v1) {
            Bundle bundle0 = j.this.h();
            bundle0.putInt("AdaptedListFragment_last_clicked", v);
            bundle0.putInt("AdaptedListFragment_last_clicked_sub", v1);
        }

        void v(ListView listView0, int v, int v1) {
            if(listView0 == null) {
                listView0 = j.this.G0();
            }
            if(listView0 != null && (v >= 0 && v < this.getCount())) {
                listView0.setSelectionFromTop(v, v1);
            }
        }
    }

    static class m extends FrameLayout {
        public m(Context context0) {
            super(context0);
        }

        public View a() {
            return this.getChildAt(0);
        }

        public void b() {
            this.addView(new ProgressBar(this.getContext()), new FrameLayout.LayoutParams(-2, -2, 17));
        }

        public void c(View view0) {
            this.removeAllViews();
            this.addView(view0, -1, -1);
        }
    }

    private o E;
    private e.j.l F;
    private h G;
    private boolean H;
    private String I;
    private String[] J;
    private int[] K;
    protected static final View.OnTouchListener L;

    static {
        j.L = (View view0, MotionEvent motionEvent0) -> true;
    }

    public j() {
        this.F = null;
        this.G = null;
        this.H = false;
        this.I = null;
        this.J = null;
        this.K = null;
    }

    protected final void A0() {
        j0 j00 = this.r();
        j00.n();
        Bundle bundle0 = j00.o();
        bundle0.remove("AdaptedListFragment_auto_data");
        bundle0.remove("AdaptedListFragment_auto_data_index");
    }

    private void A1(String s) {
        if(this.F == null) {
            return;
        }
        if(s.length() == 0) {
            this.F.t(null);
            return;
        }
        if(this.J == null) {
            this.v1();
            try {
                String[] arr_s = ((d)this).a();
                this.J = arr_s;
                if(arr_s != null) {
                    for(int v = 0; true; ++v) {
                        String[] arr_s1 = this.J;
                        if(v >= arr_s1.length) {
                            break;
                        }
                        arr_s1[v] = arr_s1[v].toUpperCase();
                    }
                }
            }
            catch(Throwable throwable0) {
                this.g0(throwable0);
            }
        }
        if(this.J != null) {
            String s1 = s.toUpperCase();
            if(this.K == null) {
                this.K = new int[this.J.length];
            }
            int v2 = 0;
            for(int v1 = 0; true; ++v1) {
                String[] arr_s2 = this.J;
                if(v1 >= arr_s2.length) {
                    break;
                }
                if(arr_s2[v1].contains(s1)) {
                    this.K[v2] = v1;
                    ++v2;
                }
            }
            int[] arr_v = new int[v2];
            System.arraycopy(this.K, 0, arr_v, 0, v2);
            this.F.t(arr_v);
        }
    }

    protected abstract int B0();

    @Override  // e.j0$c
    protected final void C() {
        if(this.H) {
            return;
        }
        if(this instanceof d) {
            this.T(this.P0(this.M0(), null));
        }
    }

    protected final Drawable C0(int v) {
        return this.E.a(v);
    }

    protected final Drawable D0(a.b b0) {
        return this.E.d(b0.m(this.F.z));
    }

    protected final int E0() {
        return this.h().getInt("AdaptedListFragment_last_clicked", -1);
    }

    protected final int F0() {
        return this.h().getInt("AdaptedListFragment_last_clicked_sub", -1);
    }

    protected final ListView G0() {
        View view0 = this.getView();
        if(view0 != null) {
            if(view0 instanceof m) {
                view0 = ((m)view0).a();
            }
            return view0 instanceof ListView ? ((ListView)view0) : null;
        }
        return null;
    }

    protected final int H0() {
        return this.F.A;
    }

    protected final int I0() {
        return this.F.C;
    }

    protected final int J0() {
        return this.F.E == null ? 0 : this.F.E.length;
    }

    protected final View K0(int v) {
        return this.F.l(v);
    }

    protected final View L0(int v) {
        return this.F.m(v);
    }

    protected final Context M0() {
        e.j.l j$l0 = this.F;
        if(j$l0 != null) {
            return j$l0.z;
        }
        Context context0 = this.getActivity();
        return context0 == null ? this.f() : context0;
    }

    protected boolean N0(int v) {
        return false;
    }

    private ListView O0(Context context0) {
        class e.j.a extends ListView {
            final j z;

            e.j.a(Context context0) {
                super(context0);
            }

            @Override  // android.widget.AbsListView
            public boolean onGenericMotionEvent(MotionEvent motionEvent0) {
                float f = motionEvent0.getAxisValue(9);
                if(f == 0xC7C35000 || f == 0x47C35000) {
                    try {
                        if(f == 100000.0f) {
                            this.smoothScrollToPosition(0);
                            return true;
                        }
                        this.smoothScrollToPosition(this.getCount() - 1);
                        return true;
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
                return super.onGenericMotionEvent(motionEvent0);
            }
        }

        int v = this.B0();
        this.F = new e.j.l(this, context0, v);
        if(this.I == null) {
            this.I = this.h().getString("AdaptedListFragment_filter_text");
        }
        String s = this.I;
        if(s != null) {
            this.A1(s);
        }
        ListView listView0 = new e.j.a(this, context0);
        listView0.setAdapter(this.F);
        listView0.setOnItemClickListener(this.F);
        listView0.setOnItemLongClickListener(this.F);
        listView0.setDividerHeight(0);
        int[] arr_v = this.h().getIntArray("AdaptedListFragment_view_state");
        if(arr_v != null) {
            this.F.v(listView0, arr_v[0], arr_v[1]);
            if(v == arr_v[2]) {
                this.n1(arr_v[3], arr_v[4]);
            }
        }
        return listView0;
    }

    private SearchView P0(Context context0, View view0) {
        class e.j.b implements SearchView.OnQueryTextListener {
            final Context a;
            final SearchView b;
            final j c;

            e.j.b(Context context0, SearchView searchView0) {
                this.a = context0;
                this.b = searchView0;
                super();
            }

            @Override  // android.widget.SearchView$OnQueryTextListener
            public boolean onQueryTextChange(String s) {
                if(!s.equals((j.this.I == null ? "" : j.this.I))) {
                    j.this.v1();
                    j.this.I = s;
                    j.this.A1(s);
                    if(j.this.F != null) {
                        j.this.F.v(null, 0, 0);
                    }
                }
                return false;
            }

            @Override  // android.widget.SearchView$OnQueryTextListener
            public boolean onQueryTextSubmit(String s) {
                InputMethodManager inputMethodManager0 = (InputMethodManager)this.a.getSystemService("input_method");
                if(inputMethodManager0 != null) {
                    inputMethodManager0.hideSoftInputFromWindow(this.b.getWindowToken(), 0);
                }
                return false;
            }
        }


        class e.j.c implements View.OnAttachStateChangeListener {
            final j a;

            @Override  // android.view.View$OnAttachStateChangeListener
            public void onViewAttachedToWindow(View view0) {
            }

            @Override  // android.view.View$OnAttachStateChangeListener
            public void onViewDetachedFromWindow(View view0) {
                try {
                    InputMethodManager inputMethodManager0 = (InputMethodManager)view0.getContext().getSystemService("input_method");
                    if(inputMethodManager0 != null) {
                        inputMethodManager0.hideSoftInputFromWindow(view0.getWindowToken(), 0);
                    }
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        SearchView searchView0 = new SearchView(context0);
        if(Build.VERSION.SDK_INT >= 26) {
            searchView0.setFocusedByDefault(false);
        }
        this.Y(searchView0);
        searchView0.setOnSearchClickListener((View view1) -> {
            if(view0 != null) {
                view0.setVisibility(8);
            }
            String s = this.I == null ? "" : this.I;
            if(!searchView0.getQuery().toString().equals(s)) {
                this.v1();
            }
        });
        searchView0.setOnCloseListener(() -> {
            this.I = null;
            if(view0 != null) {
                view0.setVisibility(0);
            }
            return false;
        });
        searchView0.setOnQueryTextListener(new e.j.b(this, context0, searchView0));
        searchView0.setOnFocusChangeListener((View view0, boolean z) -> if(!z) {
            try {
                InputMethodManager inputMethodManager0 = (InputMethodManager)view0.getContext().getSystemService("input_method");
                if(inputMethodManager0 != null) {
                    inputMethodManager0.hideSoftInputFromWindow(view0.getWindowToken(), 0);
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        });
        searchView0.addOnAttachStateChangeListener(new e.j.c(this));
        String s = this.h().getString("AdaptedListFragment_filter_text");
        this.I = s;
        if(s != null) {
            searchView0.setQuery(s, false);
            searchView0.setIconified(false);
        }
        if(this.t().o == 0xFF000000) {
            try {
                int v = this.getResources().getIdentifier("search_button", "id", "android");
                if(v > 0) {
                    g.h.a().e(((ImageView)searchView0.findViewById(v)).getDrawable(), -1);
                    return searchView0;
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        return searchView0;
    }

    protected final void Q0(int v) {
        e.j.l j$l0 = this.F;
        if(j$l0 != null) {
            j$l0.B[v] = null;
            this.F.notifyDataSetChanged();
        }
    }

    // 检测为 Lambda 实现
    private void R0(View view0, SearchView searchView0, View view1) [...]

    @Override  // e.j0$c
    protected void S(int v, View.OnClickListener view$OnClickListener0) {
        Context context0 = this.M0();
        View view0 = this.v(context0, v, view$OnClickListener0);
        if(this instanceof d) {
            SearchView searchView0 = this.P0(context0, view0);
            LinearLayout linearLayout0 = new LinearLayout(context0);
            linearLayout0.setOrientation(0);
            linearLayout0.addView(searchView0, -2, -1);
            linearLayout0.addView(view0, -2, -1);
            this.T(linearLayout0);
        }
        else {
            this.T(view0);
        }
        this.H = true;
    }

    // 检测为 Lambda 实现
    private boolean S0(View view0) [...]

    // 检测为 Lambda 实现
    private static void T0(View view0, boolean z) [...]

    // 检测为 Lambda 实现
    private void U0(View view0, e j$e0) [...]

    // 检测为 Lambda 实现
    private void V0(View view0, e j$e0) [...]

    // 检测为 Lambda 实现
    private void W0(View view0, e j$e0) [...]

    // 检测为 Lambda 实现
    private void X0(View view0, e j$e0) [...]

    // 检测为 Lambda 实现
    private void Y0(e.j.j j$j0, e j$e0) [...]

    private static boolean Z0(View view0, MotionEvent motionEvent0) [...] // Inlined contents

    protected final void a1(int v, int v1) {
        e.j.l j$l0 = this.F;
        if(j$l0 != null) {
            j$l0.n(v, v1);
        }
    }

    // 去混淆评级： 低(20)
    protected g b1() {
        return this.E.c() ? new g(this, this.E.b(0x7F040005, false), this.u(0x7F060066), null) : new g(this, this.E.b(0x7F040006, false), this.u(0x7F060066), null);  // drawable:ic_add
    }

    protected FrameLayout c1(CharSequence charSequence0) {
        e.j0.d j0$d0 = this.t();
        TextView textView0 = new TextView(this.F.z);
        textView0.setText(charSequence0);
        textView0.setTextSize(0, ((float)(j0$d0.g + j0$d0.i)) / 2.0f);
        textView0.setTextColor(j0$d0.o);
        textView0.setOnTouchListener(j.L);
        textView0.setPadding(j0$d0.d, j0$d0.d, j0$d0.d, j0$d0.d);
        FrameLayout frameLayout0 = new FrameLayout(this.F.z);
        FrameLayout.LayoutParams frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(-1, -2);
        frameLayout$LayoutParams0.gravity = 17;
        frameLayout0.addView(textView0, frameLayout$LayoutParams0);
        return frameLayout0;
    }

    private LinearLayout d1(Drawable drawable0, int v, int v1, CharSequence charSequence0) {
        e.j0.d j0$d0 = this.t();
        Context context0 = this.F.z;
        LinearLayout linearLayout0 = new LinearLayout(context0);
        linearLayout0.setOrientation(1);
        FrameLayout frameLayout0 = new FrameLayout(context0);
        ImageView imageView0 = new ImageView(context0);
        imageView0.setImageDrawable(drawable0);
        imageView0.setPadding(j0$d0.d, j0$d0.d, j0$d0.d, j0$d0.d);
        imageView0.setBackgroundColor(0x44444444);
        frameLayout0.addView(imageView0, v + j0$d0.d * 2, v1 + j0$d0.d * 2);
        linearLayout0.addView(frameLayout0, -1, -2);
        TextView textView0 = j.e1(context0, charSequence0, j0$d0.g, j0$d0.o);
        LinearLayout.LayoutParams linearLayout$LayoutParams0 = new LinearLayout.LayoutParams(-1, -2);
        linearLayout$LayoutParams0.bottomMargin = j0$d0.d * 2;
        linearLayout0.addView(textView0, linearLayout$LayoutParams0);
        return linearLayout0;
    }

    static TextView e1(Context context0, CharSequence charSequence0, int v, int v1) {
        TextView textView0 = new TextView(context0);
        textView0.setSingleLine(true);
        textView0.setEllipsize(TextUtils.TruncateAt.END);
        textView0.setText(charSequence0);
        textView0.setTextSize(0, ((float)v));
        textView0.setTextColor(v1);
        return textView0;
    }

    protected LinearLayout f1(AppWidgetProviderInfo appWidgetProviderInfo0) {
        Context context0 = this.F.z;
        int v = this.k().a * 2;
        return this.d1(x.k(context0, appWidgetProviderInfo0), v, v, x.j(context0, appWidgetProviderInfo0));
    }

    protected void g1(Object object0) {
    }

    protected abstract View h1(int arg1);

    protected View i1(int v) {
        throw new RuntimeException("Must be implemented if addItem sub items!");
    }

    // 检测为 Lambda 实现
    public static boolean j0(View view0, MotionEvent motionEvent0) [...]

    private void j1(e j$e0) {
        e.e e0;
        View view2;
        e.g g0;
        View view1;
        switch(j$e0.A) {
            case 0: {
                this.r().y(j$e0.B, 500L);
                return;
            }
            case 1: {
                this.z0(j$e0.z, -1);
                view1 = this.L0(j$e0.z);
                view1.setBackgroundColor(this.q().getColor(0x7F030005));  // color:colorPrimaryLight
                g0 = () -> {
                    view1.setBackground(null);
                    this.F.u(j$e0.z, -1);
                    this.k1(j$e0.z);
                    this.r().y(j$e0.B, 500L);
                };
                view1.postDelayed(g0, 500L);
                return;
            }
            case 2: {
                this.z0(j$e0.z, -1);
                view2 = this.L0(j$e0.z);
                view2.setBackgroundColor(this.q().getColor(0x7F030005));  // color:colorPrimaryLight
                e0 = () -> {
                    view2.setBackground(null);
                    this.F.u(j$e0.z, -1);
                    this.l1(j$e0.z);
                    this.r().y(j$e0.B, 1000L);
                };
                break;
            }
            case 3: {
                this.z0(this.I0(), j$e0.z);
                view1 = this.K0(j$e0.z);
                view1.setBackgroundColor(this.q().getColor(0x7F030005));  // color:colorPrimaryLight
                g0 = () -> {
                    view1.setBackground(null);
                    this.F.u(this.I0(), j$e0.z);
                    this.o1(j$e0.z);
                    this.r().y(j$e0.B, 500L);
                };
                view1.postDelayed(g0, 500L);
                return;
            }
            case 4: {
                this.z0(this.I0(), j$e0.z);
                view2 = this.K0(j$e0.z);
                view2.setBackgroundColor(this.q().getColor(0x7F030005));  // color:colorPrimaryLight
                e0 = () -> {
                    view2.setBackground(null);
                    this.F.u(this.I0(), j$e0.z);
                    this.p1(j$e0.z);
                    this.r().y(j$e0.B, 1000L);
                };
                break;
            }
            case 5: 
            case 6: {
                this.z0(j$e0.z, -1);
                View view0 = this.L0(j$e0.z);
                if(view0 instanceof e.j.j) {
                    ((e.j.j)view0).setChecked(j$e0.A == 5);
                    ((e.j.j)view0).E.setBackgroundColor(this.q().getColor(0x7F030005));  // color:colorPrimaryLight
                    view0.postDelayed(() -> {
                        ((e.j.j)view0).E.setBackground(null);
                        this.r().y(j$e0.B, 500L);
                    }, 500L);
                    return;
                }
                return;
            }
            default: {
                this.A0();
                return;
            }
        }
        view2.postDelayed(e0, 1000L);
    }

    protected abstract void k1(int arg1);

    protected boolean l1(int v) {
        return false;
    }

    protected i m1() {
        return null;
    }

    protected void n1(int v, int v1) {
        this.F.j(v, v1, false);
    }

    protected void o1(int v) {
    }

    @Override  // e.j0$c
    public void onCreate(Bundle bundle0) {
        super.onCreate(bundle0);
        this.E = new g.o.a(this.q());
    }

    @Override  // android.app.Fragment
    public View onCreateView(LayoutInflater layoutInflater0, ViewGroup viewGroup0, Bundle bundle0) {
        Context context0 = viewGroup0 == null ? this.M0() : viewGroup0.getContext();
        i j$i0 = this.m1();
        if(j$i0 == null) {
            return this.O0(context0);
        }
        View view0 = new m(context0);
        ((m)view0).b();
        h j$h0 = new h(j$i0, this.f());
        this.G = j$h0;
        j$h0.e(this);
        f.d.b(this.G);
        Bundle bundle1 = this.h();
        bundle1.remove("AdaptedListFragment_view_state");
        bundle1.remove("AdaptedListFragment_last_clicked");
        bundle1.remove("AdaptedListFragment_last_clicked_sub");
        return view0;
    }

    @Override  // e.j0$c
    public void onDestroy() {
        super.onDestroy();
        h j$h0 = this.G;
        if(j$h0 != null) {
            j$h0.e(null);
            this.G = null;
        }
    }

    @Override  // e.j0$c
    public void onHiddenChanged(boolean z) {
        super.onHiddenChanged(z);
        if(z && (this.I != null && this.I.length() > 0)) {
            this.h().putString("AdaptedListFragment_filter_text", this.I);
        }
    }

    @Override  // e.j0$c
    public void onSaveInstanceState(Bundle bundle0) {
        Bundle bundle1 = this.h();
        ListView listView0 = this.G0();
        if(listView0 != null) {
            View view0 = listView0.getChildAt(0);
            if(view0 != null) {
                bundle1.putIntArray("AdaptedListFragment_view_state", new int[]{listView0.getFirstVisiblePosition(), view0.getTop(), this.F.A, this.I0(), this.J0()});
            }
        }
        if(this.I != null && this.I.length() > 0) {
            bundle1.putString("AdaptedListFragment_filter_text", this.I);
        }
        super.onSaveInstanceState(bundle0);
    }

    protected boolean p1(int v) {
        return false;
    }

    private e q1() {
        Bundle bundle0 = this.r().o();
        e[] arr_j$e = (e[])bundle0.getParcelableArray("AdaptedListFragment_auto_data");
        if(arr_j$e != null) {
            int v = bundle0.getInt("AdaptedListFragment_auto_data_index", -1);
            if(v >= 0 && v < arr_j$e.length) {
                e j$e0 = arr_j$e[v];
                bundle0.putInt("AdaptedListFragment_auto_data_index", v + 1);
                return j$e0;
            }
            this.A0();
        }
        return null;
    }

    private void r1(Object object0) {
        View view0 = this.getView();
        if(view0 instanceof m) {
            this.g1(object0);
            ((m)view0).c(this.O0(view0.getContext()));
        }
    }

    public final void s1(e[] arr_j$e) {
        Bundle bundle0 = this.r().o();
        bundle0.putParcelableArray("AdaptedListFragment_auto_data", arr_j$e);
        bundle0.putInt("AdaptedListFragment_auto_data_index", 0);
    }

    protected final void t1() {
        View[] arr_view = this.F.B;
        for(int v = 0; v < arr_view.length; ++v) {
            View view0 = arr_view[v];
            if(view0 != null) {
                ((k)view0).setHighLight(false);
            }
        }
    }

    protected final void u1(int v) {
        e.j.l j$l0 = this.F;
        if(j$l0 != null) {
            j$l0.q(v);
        }
    }

    protected final void v1() {
        e.j.l j$l0 = this.F;
        if(j$l0 != null) {
            j$l0.r();
        }
    }

    protected final void w0() {
        e.j.l j$l0 = this.F;
        if(j$l0 != null) {
            this.x0(j$l0.A);
        }
    }

    protected final void w1() {
        Bundle bundle0 = this.h();
        bundle0.remove("AdaptedListFragment_view_state");
        bundle0.remove("AdaptedListFragment_last_clicked");
        bundle0.remove("AdaptedListFragment_last_clicked_sub");
        Fragment fragment0 = this.n();
        if(fragment0 != null) {
            if(fragment0 instanceof c) {
                ((c)fragment0).L();
            }
            else if(fragment0 instanceof e.j0.b) {
                ((e.j0.b)fragment0).dismiss();
            }
        }
        e.j.l j$l0 = this.F;
        if(j$l0 != null) {
            j$l0.s(this.B0());
            this.F.notifyDataSetChanged();
        }
        this.J = null;
        String s = this.I;
        if(s != null) {
            this.A1(s);
        }
    }

    protected final void x0(int v) {
        e.j.l j$l0 = this.F;
        if(j$l0 != null) {
            j$l0.i(v);
        }
    }

    public final void x1() {
        e j$e0 = this.q1();
        if(j$e0 != null && this.G0() != null) {
            this.j1(j$e0);
        }
    }

    protected final void y0(int v, int v1) {
        if(this.F == null) {
            return;
        }
        this.v1();
        this.F.j(v, v1, true);
    }

    protected final void y1(int v, int v1) {
        if(v != this.I0()) {
            this.y0(v, v1);
            return;
        }
        this.v1();
    }

    private void z0(int v, int v1) {
        e.j.l j$l0 = this.F;
        if(j$l0 != null) {
            j$l0.k(v, v1);
        }
    }

    protected final void z1() {
        e.j.l j$l0 = this.F;
        if(j$l0 != null) {
            j$l0.notifyDataSetChanged();
        }
    }
}


package c;

import a.h.g;
import android.app.Activity;
import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface;
import android.os.Bundle;
import android.widget.ArrayAdapter;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import e.j0.b;
import g.x;

public class e extends b {
    private static final String[] C;

    static {
        e.C = new String[101];
        for(int v = 0; v < 101; ++v) {
            e.C[v] = Integer.toString(v);
        }
    }

    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        class a implements DialogInterface.OnClickListener {
            final Spinner A;
            final e B;
            final Spinner z;

            a(Spinner spinner0, Spinner spinner1) {
                this.z = spinner0;
                this.A = spinner1;
                super();
            }

            @Override  // android.content.DialogInterface$OnClickListener
            public void onClick(DialogInterface dialogInterface0, int v) {
                g h$g0 = new g(this.z.getSelectedItemPosition(), this.A.getSelectedItemPosition());
                e.this.q("result", h$g0);
            }
        }

        Activity activity0 = this.getActivity();
        LinearLayout linearLayout0 = new LinearLayout(activity0);
        linearLayout0.setOrientation(0);
        x x0 = this.g();
        linearLayout0.setPadding(x0.g, x0.g, x0.g, x0.g);
        TextView textView0 = new TextView(activity0);
        textView0.setText(0x7F0600A3);  // string:condition_battery_level "Battery level"
        linearLayout0.addView(textView0);
        Spinner spinner0 = new Spinner(activity0);
        ArrayAdapter arrayAdapter0 = new ArrayAdapter(activity0, 0x1090008, g.E);
        arrayAdapter0.setDropDownViewResource(0x1090009);
        spinner0.setAdapter(arrayAdapter0);
        linearLayout0.addView(spinner0);
        Spinner spinner1 = new Spinner(activity0);
        ArrayAdapter arrayAdapter1 = new ArrayAdapter(activity0, 0x1090008, e.C);
        arrayAdapter1.setDropDownViewResource(0x1090009);
        spinner1.setAdapter(arrayAdapter1);
        linearLayout0.addView(spinner1);
        return new AlertDialog.Builder(activity0).setView(linearLayout0).setNegativeButton(0x1040000, b.B).setPositiveButton(0x104000A, new a(this, spinner0, spinner1)).create();
    }
}


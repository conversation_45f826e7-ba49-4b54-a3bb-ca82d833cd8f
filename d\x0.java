package d;

import a.p.f;
import a.z;
import android.os.Bundle;
import android.view.View;
import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.CompoundButton;
import e.j;
import java.util.ArrayList;

public class x0 extends j implements f {
    private z M;
    private ArrayList N;

    @Override  // e.j
    protected int B0() {
        this.c0(0x7F06010D);  // string:gesture_pointer_modes "Gesture pointer modes"
        this.M = this.g().h();
        this.N = new ArrayList(4);
        int v1 = this.M == null ? 0 : this.M.u(20);
        int v2 = 202050057;
        if(v1 != 0) {
            int v3 = 0;
            while(v3 <= 24) {
                int v4 = v1 >>> v3 & 7;
                if(v4 >= 1 && v4 <= 4 && !this.N.contains(v4)) {
                    this.N.add(v4);
                    v3 += 8;
                }
                else {
                    this.M.s0(this.f(), 20, 202050057);
                    v1 = 202050057;
                    if(true) {
                        break;
                    }
                }
            }
            this.N.clear();
            v2 = v1;
        }
        for(int v = 0; v <= 24; v += 8) {
            this.N.add(((int)(v2 >>> v & 15)));
        }
        return 4;
    }

    private CharSequence D1(int v) {
        return this.u(f.i[v]);
    }

    private void E1() {
        int v = 0;
        for(int v1 = 0; v1 < 4; ++v1) {
            v |= ((int)(((Integer)this.N.get(v1)))) << v1 * 8;
        }
        this.M.s0(this.f(), 20, v);
    }

    @Override  // e.j0$c
    protected void J(Bundle bundle0, int v) {
        int v3;
        ArrayList arrayList0;
        if(bundle0 == null) {
            return;
        }
        if(v == 1) {
            int v1 = bundle0.getInt("result", -1);
            int v2 = this.E0();
            switch(v1) {
                case 0: {
                    e.j.j j$j0 = (e.j.j)this.L0(v2);
                    j$j0.setChecked(!j$j0.g());
                    return;
                label_10:
                    if(v1 == 2 && v2 < 3) {
                        arrayList0 = this.N;
                        v3 = v2 + 1;
                        arrayList0.add(v3, ((Integer)arrayList0.remove(v2)));
                        this.a1(v2, v3);
                        this.E1();
                        return;
                    }
                    break;
                }
                case 1: {
                    if(v2 > 0) {
                        arrayList0 = this.N;
                        v3 = v2 - 1;
                        arrayList0.add(v3, ((Integer)arrayList0.remove(v2)));
                        this.a1(v2, v3);
                        this.E1();
                        return;
                    }
                    break;
                }
                default: {
                    goto label_10;
                }
            }
        }
    }

    @Override  // e.j
    protected View h1(int v) {
        class a implements CompoundButton.OnCheckedChangeListener {
            final int a;
            final x0 b;

            a(int v) {
                this.a = v;
                super();
            }

            @Override  // android.widget.CompoundButton$OnCheckedChangeListener
            public void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
                for(int v = 0; v < 4; ++v) {
                    int v1 = (int)(((Integer)x0.this.N.get(v)));
                    int v2 = this.a;
                    if((v1 & 7) == v2) {
                        if(z) {
                            v2 |= 8;
                        }
                        x0.this.N.set(v, v2);
                        x0.this.E1();
                        return;
                    }
                }
            }
        }

        int v1 = (int)(((Integer)this.N.get(v)));
        View view0 = new e.j.j(this, this.D1(v1 & 7), null, (v1 & 8) != 0);
        ((e.j.j)view0).setOnCheckedChangeListener(new a(this, v1 & 7));
        return view0;
    }

    @Override  // e.j
    protected void k1(int v) {
        CharSequence[] arr_charSequence = {this.u(((((int)(((Integer)this.N.get(v)))) & 8) == 0 ? 0x7F0600D0 : 0x7F0600F4)), this.u(0x7F060159), this.u(0x7F060158)};  // string:disable "Disable"
        this.N(new e.z().u(arr_charSequence), 1);
    }
}


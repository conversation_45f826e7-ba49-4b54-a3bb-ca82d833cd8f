package d;

import a.p.a;
import android.view.View;
import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.CompoundButton;
import e.j.k;
import e.j;

public class z extends j implements a {
    private a.z M;

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F060023);  // string:action_game_mode "Game mode"
    }

    @Override  // e.j
    protected int B0() {
        this.M = this.g().h();
        return 2;
    }

    @Override  // e.j
    protected boolean N0(int v) {
        return true;
    }

    @Override  // e.j
    protected View h1(int v) {
        return v == 0 ? new k(this, this.u(0x7F060128)) : new k(this, this.u(0x7F06012E));  // string:ignore_edges "Ignore edges"
    }

    @Override  // e.j
    protected View i1(int v) {
        class d.z.a implements CompoundButton.OnCheckedChangeListener {
            final int a;
            final z b;

            d.z.a(int v) {
                this.a = v;
                super();
            }

            @Override  // android.widget.CompoundButton$OnCheckedChangeListener
            public void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
                z.this.M.q0(compoundButton0.getContext(), this.a, z);
            }
        }

        int v2;
        int v1;
        if(this.I0() == 0) {
            v1 = a.a[v];
            v2 = a.c[v];
        }
        else {
            v1 = a.b[v];
            v2 = a.d[v];
        }
        View view0 = new e.j.j(this, this.u(v1), null, this.M.g0(v2));
        ((e.j.j)view0).setOnCheckedChangeListener(new d.z.a(this, v2));
        return view0;
    }

    @Override  // e.j
    protected void k1(int v) {
        this.y1(v, (v == 0 ? a.a.length : a.b.length));
    }

    @Override  // e.j
    protected void o1(int v) {
        ((e.j.j)this.K0(v)).h();
    }
}


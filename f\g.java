package f;

import android.content.Context;
import android.os.Build.VERSION;
import android.provider.Settings.System;

public class g {
    public static final int a;
    public static final int b;

    static {
        int v3;
        int v2;
        int v1;
        int v;
        try {
            v = Build.VERSION.SDK_INT < 24 ? 10 : 1;
            v1 = 0xFF;
            v2 = v;
            v2 = p0.m("config_screenBrightnessSettingMinimum", -1);
            if(v2 == -1) {
                v2 = p0.m("config_screenBrightnessDim", v);
            }
            v3 = p0.m("config_screenBrightnessSettingMaximum", 0xFF);
        }
        catch(Throwable throwable0) {
            goto label_16;
        }
        if(v3 < 0xFF) {
            try {
                v.c(("Got max brightness " + v3 + ", force set to 255."));
                v = v2;
                g.a = v;
                g.b = v1;
                return;
            }
            catch(Throwable throwable1) {
                throwable0 = throwable1;
                v1 = v3;
            }
        label_16:
            v.d(throwable0);
            if(v2 != -1) {
                v = v2;
            }
        }
        else {
            v1 = v3;
            v = v2;
        }
        g.a = v;
        g.b = v1;
    }

    public static int a(Context context0) {
        return Settings.System.getInt(context0.getContentResolver(), "screen_brightness", (g.a + g.b) / 2);
    }
}


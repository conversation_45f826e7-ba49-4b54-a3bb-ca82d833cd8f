package a;

import android.content.Context;
import android.net.Uri;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import f.d0;
import f.i;
import f.l;
import f.s;
import f.v;
import f.z;

public abstract class h implements Parcelable, l {
    public static final class a0 extends h {
        public final boolean C;

        public a0(boolean z) {
            super(33, 0x7F0600B6);  // string:condition_orientation "Orientation"
            this.C = z;
        }

        // 去混淆评级： 低(20)
        @Override  // a.h
        public String b(Context context0) {
            return '(' + context0.getString((this.C ? 0x7F060168 : 0x7F060169)) + ')';  // string:orientation_landscape "Landscape"
        }

        @Override  // a.h
        public void e(s s0) {
            super.e(s0);
            s0.d(((int)this.C));
        }
    }

    class a implements Parcelable.Creator {
        a() {
            super();
        }

        public h a(Parcel parcel0) {
            return h.d(new d0(parcel0));
        }

        public h[] b(int v) {
            return new h[v];
        }

        @Override  // android.os.Parcelable$Creator
        public Object createFromParcel(Parcel parcel0) {
            return this.a(parcel0);
        }

        @Override  // android.os.Parcelable$Creator
        public Object[] newArray(int v) {
            return this.b(v);
        }
    }

    public static final class b0 extends h {
        public b0() {
            super(18, 0x7F0600B7);  // string:condition_power_connected "Power connected"
        }
    }

    public static final class b extends h {
        public b() {
            super(15, 0x7F06009E);  // string:condition_airplane_mode_on "Airplane mode on"
        }
    }

    public static final class c0 extends h {
        public c0() {
            super(24, 0x7F0600B8);  // string:condition_ringing "Ringing"
        }
    }

    public static final class c extends h {
        public final h C;
        public final h D;

        public c(h h0, h h1) {
            super(1, 0x7F06009F);  // string:condition_and "And"
            this.C = h0;
            this.D = h1;
        }

        @Override  // a.h
        public String b(Context context0) {
            return this.C == null || this.D == null ? super.b(context0) : '(' + this.C.b(context0) + " && " + this.D.b(context0) + ')';
        }

        @Override  // a.h
        public void e(s s0) {
            super.e(s0);
            this.C.e(s0);
            this.D.e(s0);
        }
    }

    public static final class a.h.d0 extends h {
        public a.h.d0() {
            super(4, 0x7F0600B9);  // string:condition_screen_on "Screen on"
        }
    }

    public static final class d extends h {
        public final String C;

        private d() {
            this(null);
        }

        d(a h$a0) {
        }

        public d(String s) {
            super(0x20, 0x7F0600A0);  // string:condition_app_focused "App focused"
            this.C = s;
        }

        @Override  // a.h
        public String b(Context context0) {
            String s;
            try {
                s = this.C;
                return "(" + z.K(context0).G(this.C).toString() + ' ' + "focused" + ')';
            }
            catch(Throwable throwable0) {
                v.c(throwable0.toString());
                return "(" + s + ' ' + "focused" + ')';
            }
        }

        @Override  // a.h
        public void e(s s0) {
            super.e(s0);
            s0.c(this.C);
        }
    }

    public static final class e0 extends h {
        public e0() {
            super(34, 0x7F0600BA);  // string:condition_soft_keyboard_showing "Soft keyboard showing"
        }
    }

    public static final class e extends h {
        public e() {
            super(6, 0x7F0600A1);  // string:condition_auto_brightness "Auto brightness"
        }
    }

    public static final class f0 extends h {
        public f0() {
            super(35, 0x7F0600BB);  // string:condition_status_bar_showing "Status bar showing"
        }
    }

    public static final class f extends h {
        public f() {
            super(7, 0x7F0600A2);  // string:condition_auto_rotation "Auto rotation"
        }
    }

    public static final class g0 extends h {
        public g0() {
            super(13, 0x7F0600BC);  // string:condition_sync_on "Sync on"
        }
    }

    public static final class g extends h {
        public final int C;
        public final int D;
        public static final String[] E;

        static {
            g.E = new String[]{" == ", " > ", " >= ", " < ", " <= ", " != "};
        }

        private g() {
            this(-1, -1);
        }

        public g(int v, int v1) {
            super(26, 0x7F0600A3);  // string:condition_battery_level "Battery level"
            this.C = v;
            this.D = v1;
        }

        g(a h$a0) {
        }

        @Override  // a.h
        public String b(Context context0) {
            return '(' + super.c(context0) + g.E[this.C] + this.D + ')';
        }

        @Override  // a.h
        public void e(s s0) {
            super.e(s0);
            s0.d(this.C).d(this.D);
        }
    }

    public static final class h0 extends h {
        public h0() {
            super(22, 0x7F0600BE);  // string:condition_usb_plugged "Usb plugged"
        }
    }

    public static final class a.h.h extends h {
        public a.h.h() {
            super(11, 0x7F0600A4);  // string:condition_bluetooth_on "Bluetooth on"
        }
    }

    public static final class i0 extends h {
        private final String C;
        private final String D;
        private final String E;
        private i F;
        private i G;
        public static final String[] H;

        static {
            i0.H = new String[]{" == ", " > ", " >= ", " < ", " <= ", " != "};
        }

        private i0() {
            this(null, null, null);
        }

        i0(a h$a0) {
        }

        private i0(s s0) {
            this(s0.e(), s0.a(), s0.e());
        }

        i0(s s0, a h$a0) {
            this(s0);
        }

        public i0(String s, String s1, String s2) {
            super(37, 0x7F0600BF);  // string:condition_variable "Variable"
            this.F = null;
            this.G = null;
            this.C = s;
            this.D = s1;
            this.E = s2;
        }

        @Override  // a.h
        public String b(Context context0) {
            return this.C == null ? super.b(context0) : "((" + this.C + ") " + this.D + " (" + this.E + "))";
        }

        @Override  // a.h
        public void e(s s0) {
            super.e(s0);
            s0.b(this.C).c(this.D).b(this.E);
        }

        private int f(f.i.f i$f0) {
            if(this.F == null) {
                this.F = i.d(this.C);
            }
            return this.F.a(i$f0);
        }

        private int g(f.i.f i$f0) {
            if(this.G == null) {
                this.G = i.d(this.E);
            }
            return this.G.a(i$f0);
        }

        public boolean h(f.i.f i$f0) {
            this.D.hashCode();
            switch(this.D) {
                case "!=": {
                    return this.f(i$f0) != this.g(i$f0);
                }
                case "<": {
                    return this.f(i$f0) < this.g(i$f0);
                }
                case "<=": {
                    return this.f(i$f0) <= this.g(i$f0);
                }
                case "==": {
                    return this.f(i$f0) == this.g(i$f0);
                }
                case ">": {
                    return this.f(i$f0) > this.g(i$f0);
                }
                case ">=": {
                    return this.f(i$f0) >= this.g(i$f0);
                }
                default: {
                    throw new RuntimeException("Invalid operator: " + this.D);
                }
            }
        }
    }

    public static final class a.h.i extends h {
        public a.h.i() {
            super(16, 0x7F0600A5);  // string:condition_gesture_enabled "Gesture control on"
        }
    }

    public static final class j0 extends h {
        public j0() {
            super(14, 0x7F0600C0);  // string:condition_wifi_ap_on "Wifi ap on"
        }
    }

    public static final class j extends h {
        public j() {
            super(21, 0x7F0600A6);  // string:condition_headset_plugged "Headset plugged"
        }
    }

    public static final class k0 extends h {
        public k0() {
            super(19, 0x7F0600C1);  // string:condition_wifi_connected "Wifi connected"
        }
    }

    public static final class k extends h {
        public final int C;

        public k() {
            this(0);
        }

        public k(int v) {
            super(29, 0x7F0600A7);  // string:condition_in_days_of_month "In days of month"
            this.C = v;
        }

        @Override  // a.h
        public String b(Context context0) {
            return "(" + this.c(context0) + "(" + f.j.f(context0, this.C) + "))";
        }

        @Override  // a.h
        public void e(s s0) {
            super.e(s0);
            s0.d(this.C);
        }
    }

    public static final class l0 extends h {
        public final String C;

        private l0() {
            this(null);
        }

        l0(a h$a0) {
        }

        public l0(s s0) {
            this(Uri.decode(s0.a()));
        }

        public l0(String s) {
            super(38, 0x7F0600C2);  // string:condition_wifi_connected_to "Wifi connected to"
            this.C = s;
        }

        @Override  // a.h
        public String b(Context context0) {
            return "(" + this.c(context0) + ": " + this.C + ')';
        }

        @Override  // a.h
        public void e(s s0) {
            super.e(s0);
            s0.c(Uri.encode(this.C));
        }
    }

    public static final class a.h.l extends h {
        public final int C;

        private a.h.l() {
            this(0);
        }

        public a.h.l(int v) {
            super(28, 0x7F0600A8);  // string:condition_in_days_of_week "In days of week"
            this.C = v;
        }

        a.h.l(a h$a0) {
        }

        @Override  // a.h
        public String b(Context context0) {
            return "(" + this.c(context0) + "(" + f.j.p(this.C) + "))";
        }

        @Override  // a.h
        public void e(s s0) {
            super.e(s0);
            s0.d(this.C);
        }
    }

    public static final class m0 extends h {
        public final String C;

        private m0(String s) {
            super(0x1F, 0x7F0600C2);  // string:condition_wifi_connected_to "Wifi connected to"
            this.C = s;
        }

        m0(String s, a h$a0) {
            this(s);
        }

        @Override  // a.h
        public String b(Context context0) {
            return "(" + this.c(context0) + ": " + this.C + ')';
        }

        @Override  // a.h
        public void e(s s0) {
            super.e(s0);
            s0.c(this.C);
        }

        public l0 f() {
            return new l0(this.C);
        }
    }

    public static final class m extends h {
        public final int C;

        public m() {
            this(0);
        }

        public m(int v) {
            super(30, 0x7F0600A9);  // string:condition_in_months "In months"
            this.C = v;
        }

        @Override  // a.h
        public String b(Context context0) {
            return "(" + this.c(context0) + "(" + f.j.k(this.C) + "))";
        }

        @Override  // a.h
        public void e(s s0) {
            super.e(s0);
            s0.d(this.C);
        }
    }

    public static final class n0 extends h {
        public n0() {
            super(8, 0x7F0600C3);  // string:condition_wifi_on "Wifi on"
        }
    }

    public static final class n extends h {
        public final int C;
        public final int D;
        public final int E;
        public final int F;

        private n() {
            this(0, 0, 0, 0);
        }

        public n(int v, int v1, int v2, int v3) {
            super(27, 0x7F0600BD);  // string:condition_time_period "Time period"
            this.C = v;
            this.D = v1;
            this.E = v2;
            this.F = v3;
        }

        n(a h$a0) {
        }

        private n(s s0) {
            this(s0.h(), s0.h(), s0.h(), s0.h());
        }

        n(s s0, a h$a0) {
            this(s0);
        }

        @Override  // a.h
        public String b(Context context0) {
            StringBuilder stringBuilder0 = new StringBuilder();
            stringBuilder0.append((this.C >= 10 ? "(" : "(0"));
            stringBuilder0.append(this.C);
            String s = ":0";
            stringBuilder0.append((this.D >= 10 ? ":" : ":0"));
            stringBuilder0.append(this.D);
            stringBuilder0.append((this.E >= 10 ? " - " : " - 0"));
            stringBuilder0.append(this.E);
            if(this.F >= 10) {
                s = ":";
            }
            stringBuilder0.append(s);
            stringBuilder0.append(this.F);
            stringBuilder0.append(")");
            return stringBuilder0.toString();
        }

        @Override  // a.h
        public void e(s s0) {
            super.e(s0);
            s0.d(this.C).d(this.D).d(this.E).d(this.F);
        }
    }

    public static final class o extends h {
        public o() {
            super(0, 0x7F0600AA);  // string:condition_invalid "Invalid"
        }
    }

    public static final class p extends h {
        public p() {
            super(17, 0x7F0600AB);  // string:condition_key_enabled "Key control on"
        }
    }

    public static final class q extends h {
        public q() {
            super(5, 0x7F0600AC);  // string:condition_keyguard_on "Keyguard on"
        }
    }

    public static final class r extends h {
        public r() {
            super(10, 0x7F0600AD);  // string:condition_location_on "Location on"
        }
    }

    public static final class a.h.s extends h {
        public a.h.s() {
            super(9, 0x7F0600AE);  // string:condition_mobile_data_on "Mobile data on"
        }
    }

    public static final class t extends h {
        public t() {
            super(23, 0x7F0600AF);  // string:condition_music_active "Music active"
        }
    }

    public static final class u extends h {
        public u() {
            super(36, 0x7F0600B0);  // string:condition_nav_bar_showing "Navigation bar showing"
        }
    }

    public static final class a.h.v extends h {
        public a.h.v() {
            super(20, 0x7F0600B1);  // string:condition_net_connected "Net connected"
        }
    }

    public static final class w extends h {
        public w() {
            super(12, 0x7F0600B2);  // string:condition_nfc_on "Nfc on"
        }
    }

    public static final class x extends h {
        public final h C;

        public x(h h0) {
            super(3, 0x7F0600B3);  // string:condition_not "Not"
            this.C = h0;
        }

        @Override  // a.h
        public String b(Context context0) {
            return this.C == null ? super.b(context0) : "(!" + this.C.b(context0) + ')';
        }

        @Override  // a.h
        public void e(s s0) {
            super.e(s0);
            this.C.e(s0);
        }
    }

    public static final class y extends h {
        public y() {
            super(25, 0x7F0600B4);  // string:condition_off_hook "Off hook"
        }
    }

    public static final class a.h.z extends h {
        public final h C;
        public final h D;

        public a.h.z(h h0, h h1) {
            super(2, 0x7F0600B5);  // string:condition_or "Or"
            this.C = h0;
            this.D = h1;
        }

        @Override  // a.h
        public String b(Context context0) {
            return this.C == null || this.D == null ? super.b(context0) : '(' + this.C.b(context0) + " || " + this.D.b(context0) + ')';
        }

        @Override  // a.h
        public void e(s s0) {
            super.e(s0);
            this.C.e(s0);
            this.D.e(s0);
        }
    }

    private final int A;
    private static h[] B;
    public static final Parcelable.Creator CREATOR;
    public final int z;

    static {
        h.CREATOR = new a();
    }

    protected h(int v, int v1) {
        this.z = v;
        this.A = v1;
    }

    public static h[] a() {
        synchronized(h.class) {
            if(h.B == null) {
                h.B = new h[]{new c(null, null), new a.h.z(null, null), new x(null), new a.h.d0(), new q(), new e(), new f(), new n0(), new a.h.s(), new r(), new a.h.h(), new w(), new g0(), new j0(), new b(), new a.h.i(), new p(), new b0(), new k0(), new a.h.v(), new j(), new h0(), new t(), new c0(), new y(), new a0(false), new e0(), new f0(), new u(), new g(null), new i0(null), new n(null), new a.h.l(null), new k(), new m(), new l0(null), new d(null)};
            }
            return h.B;
        }
    }

    public String b(Context context0) {
        return '(' + context0.getString(this.A) + ')';
    }

    public CharSequence c(Context context0) {
        return context0.getText(this.A);
    }

    public static h d(s s0) {
        if(!l.r) {
            return new o();
        }
        int v = s0.h();
        switch(v) {
            case 1: {
                return new c(h.d(s0), h.d(s0));
            }
            case 2: {
                return new a.h.z(h.d(s0), h.d(s0));
            }
            case 3: {
                return new x(h.d(s0));
            }
            case 4: {
                return new a.h.d0();
            }
            case 5: {
                return new q();
            }
            case 6: {
                return new e();
            }
            case 7: {
                return new f();
            }
            case 8: {
                return new n0();
            }
            case 9: {
                return new a.h.s();
            }
            case 10: {
                return new r();
            }
            case 11: {
                return new a.h.h();
            }
            case 12: {
                return new w();
            }
            case 13: {
                return new g0();
            }
            case 14: {
                return new j0();
            }
            case 15: {
                return new b();
            }
            case 16: {
                return new a.h.i();
            }
            case 17: {
                return new p();
            }
            case 18: {
                return new b0();
            }
            case 19: {
                return new k0();
            }
            case 20: {
                return new a.h.v();
            }
            case 21: {
                return new j();
            }
            case 22: {
                return new h0();
            }
            case 23: {
                return new t();
            }
            case 24: {
                return new c0();
            }
            case 25: {
                return new y();
            }
            case 26: {
                return new g(s0.h(), s0.h());
            }
            case 27: {
                return new n(s0, null);
            }
            case 28: {
                return new a.h.l(s0.h());
            }
            case 29: {
                return new k(s0.h());
            }
            case 30: {
                return new m(s0.h());
            }
            case 0x1F: {
                return new m0(s0.a(), null).f();
            }
            case 0x20: {
                return new d(s0.a());
            }
            case 33: {
                return s0.h() == 0 ? new a0(false) : new a0(true);
            }
            case 34: {
                return new e0();
            }
            case 35: {
                return new f0();
            }
            case 36: {
                return new u();
            }
            case 37: {
                return new i0(s0, null);
            }
            case 38: {
                return new l0(s0);
            }
            default: {
                v.c(("Unknown condition id: " + v));
                return new o();
            }
        }
    }

    @Override  // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    public void e(s s0) {
        s0.d(this.z);
    }

    @Override  // android.os.Parcelable
    public void writeToParcel(Parcel parcel0, int v) {
        this.e(new d0(parcel0).j());
    }
}


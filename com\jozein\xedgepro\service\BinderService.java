package com.jozein.xedgepro.service;

import a.b.a0;
import a.b.s0;
import a.b.y0;
import a.e;
import a.g.b;
import a.j;
import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Bundle;
import android.os.IBinder;
import android.os.Parcelable;
import android.os.Process;
import android.util.Pair;
import android.widget.Toast;
import com.jozein.xedgepro.ui.ActivityPerformAction;
import f.h0;
import f.i0;
import f.l;
import f.m;
import f.o0;
import f.o;
import f.v;
import g.q;
import java.util.ArrayList;
import java.util.List;

public class BinderService extends Service implements l {
    public static class a extends b {
        private final BinderService c;

        a(BinderService binderService0) {
            this.c = binderService0;
            int v = this.e();
            if(v > 0 && v != Process.myUid()) {
                s0.A(binderService0, "BinderService running on uid " + Process.myUid() + ", but package uid " + v + ".");
            }
        }

        @Override  // a.g
        public Parcelable a(String s) {
            f.q.b q$b0 = new f.q.b(s);
            if(q$b0.d()) {
                byte[] arr_b = q$b0.j();
                return new o(s, q$b0.f(), arr_b);
            }
            return new o(s, q$b0.f(), null);
        }

        @Override  // a.g
        public void b(String s, Parcelable parcelable0) {
            Context context0 = this.c.getApplicationContext();
            try {
                switch(s) {
                    case "COMMIT_COMMAND": {
                        BinderService.l(context0, ((Bundle)parcelable0));
                        return;
                    }
                    case "STOP_ALL": {
                        BinderService.E();
                        return;
                    }
                    default: {
                        h0.f(h0.a(), new b.b(context0, s, parcelable0));
                    }
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        public static boolean g(Context context0, ServiceConnection serviceConnection0) {
            Intent intent0 = new Intent(BinderService.z);
            intent0.setComponent(new ComponentName(l.j, BinderService.class.getName()));
            intent0.setPackage(l.j);
            intent0.addFlags(0x20);
            return context0.bindService(intent0, serviceConnection0, 1);
        }
    }

    public static final String A;
    private static final Parcelable B;
    private static int C;
    private static boolean D;
    private static final String z;

    static {
        BinderService.z = l.k + "REMOTE_ACCESS";
        BinderService.A = l.j + ".action.COMMIT";
        BinderService.B = Bundle.EMPTY;
        BinderService.C = 0;
        BinderService.D = false;
    }

    private static void A(Context context0) {
        j.h(context0);
    }

    public static void B(boolean z) {
        BinderService.C = z ? 2 : 0;
    }

    public static void C(Context context0, Intent intent0) {
        intent0.setComponent(new ComponentName(l.j, ActivityPerformAction.class.getName()));
        intent0.setFlags(0x18000000);
        try {
            context0.startActivity(intent0);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    public static void D(Context context0) {
        if(BinderService.D) {
            BinderService.D = false;
            BinderService.g(context0, "STOP_ALL", BinderService.B);
        }
    }

    private static void E() {
        i0.g();
    }

    private static Bundle F(a.b b0) {
        Bundle bundle0 = new Bundle();
        m m0 = new m(bundle0);
        m0.j();
        b0.y(m0);
        m0.l();
        return bundle0;
    }

    static void G(Context context0, int v) {
        h0.f(h0.a(), () -> Toast.makeText(context0, v, 0).show());
    }

    private static Intent H(String s, Parcelable parcelable0) {
        return new Intent(BinderService.A).setPackage(l.j).putExtra("action", s).putExtra("data", parcelable0);
    }

    private static Intent I(Pair[] arr_pair) {
        if(arr_pair.length == 1) {
            return BinderService.H(((String)arr_pair[0].first), ((Parcelable)arr_pair[0].second));
        }
        String[] arr_s = new String[arr_pair.length];
        Parcelable[] arr_parcelable = new Parcelable[arr_pair.length];
        for(int v = 0; v < arr_pair.length; ++v) {
            arr_s[v] = (String)arr_pair[v].first;
            arr_parcelable[v] = (Parcelable)arr_pair[v].second;
        }
        return new Intent(BinderService.A).setPackage(l.j).putExtra("actions", arr_s).putExtra("data", arr_parcelable);
    }

    public static boolean f(Context context0) {
        return BinderService.t(context0, "CHECK_SERVICE", BinderService.B);
    }

    private static void g(Context context0, String s, Parcelable parcelable0) {
        if(BinderService.C != 1 && o0.d() == 0) {
            if(BinderService.C == 2) {
                BinderService.t(context0, s, parcelable0);
                return;
            }
            BinderService.r(context0, s, parcelable0);
            return;
        }
        BinderService.s(context0, s, parcelable0);
    }

    public static void h(Context context0, a.b b0) {
        BinderService.g(context0, "COMMIT_ACTION", BinderService.F(b0));
    }

    private static void i(Context context0, Bundle bundle0) {
        a.b b0 = BinderService.v(bundle0);
        if(b0 != null && b0.z != 0) {
            e.A().E(context0, b0);
            BinderService.G(context0, 0x7F06006C);  // string:added_to_collection "Added to action collection"
        }
    }

    private static void j(Context context0) {
        j.h(context0);
    }

    public static void k(Context context0, a0 b$a00, boolean z, boolean z1) {
        Bundle bundle0 = BinderService.F(b$a00);
        bundle0.putBoolean("show_toast", z);
        bundle0.putBoolean("toast_output", z1);
        BinderService.g(context0, "COMMIT_COMMAND", bundle0);
        BinderService.D = true;
    }

    private static void l(Context context0, Bundle bundle0) {
        a0 b$a00 = (a0)BinderService.v(bundle0);
        if(b$a00 != null) {
            boolean z = bundle0.getBoolean("show_toast", false);
            boolean z1 = bundle0.getBoolean("toast_output", false);
            i0.c(b$a00, context0, h0.a(), z, z1);
        }
    }

    public static void m(Context context0, boolean z, ArrayList arrayList0) {
        if(BinderService.w(arrayList0)) {
            Bundle bundle0 = new Bundle();
            bundle0.putBoolean("landscape", z);
            m m0 = new m(bundle0);
            m0.j();
            int v = arrayList0.size();
            m0.o(v);
            for(int v1 = 0; v1 < v; ++v1) {
                ((q)arrayList0.get(v1)).b(m0);
            }
            m0.l();
            BinderService.g(context0, "COMMIT_GESTURE", bundle0);
            return;
        }
        v.c("Invalid gesture!");
    }

    private static void n(Context context0, Bundle bundle0) {
        if(bundle0 == null) {
            return;
        }
        m m0 = new m(bundle0);
        List list0 = null;
        boolean z = false;
        if(m0.i()) {
            int v = m0.h();
            ArrayList arrayList0 = new ArrayList(v);
            for(int v1 = 0; v1 < v; ++v1) {
                arrayList0.add(new q(m0));
            }
            list0 = arrayList0;
        }
        m0.k();
        if(BinderService.w(list0)) {
            if(context0.getResources().getConfiguration().orientation == 2) {
                z = true;
            }
            y0.O(context0, list0, bundle0.getBoolean("landscape", z));
            BinderService.G(context0, 0x7F060195);  // string:saved "Saved"
            context0.sendBroadcast(new Intent(y0.K).setPackage(l.j));
            return;
        }
        v.c("Invalid gesture!");
    }

    private static void o(Context context0, String s, Parcelable parcelable0) {
        if(s == null) {
            v.c("action == null");
            return;
        }
        switch(s) {
            case "CHECK_SERVICE": {
                BinderService.j(context0);
                return;
            }
            case "COMMIT_ACTION": {
                BinderService.i(context0, ((Bundle)parcelable0));
                return;
            }
            case "COMMIT_COMMAND": {
                BinderService.l(context0, ((Bundle)parcelable0));
                return;
            }
            case "COMMIT_GESTURE": {
                BinderService.n(context0, ((Bundle)parcelable0));
                return;
            }
            case "COMMIT_SERVICE": {
                BinderService.q(context0, ((Intent)parcelable0));
                return;
            }
            case "STOP_ALL": {
                BinderService.E();
                return;
            }
            default: {
                if(j.D.equals(s)) {
                    BinderService.A(context0);
                    return;
                }
                v.c(("Unknown action: " + s));
            }
        }
    }

    @Override  // android.app.Service
    public IBinder onBind(Intent intent0) {
        return new a(this);
    }

    @Override  // android.app.Service
    public void onRebind(Intent intent0) {
    }

    @Override  // android.app.Service
    public boolean onUnbind(Intent intent0) {
        return true;
    }

    public static void p(Context context0, Intent intent0) {
        if(BinderService.C == 2) {
            com.jozein.xedgepro.service.a.e(context0, intent0);
            return;
        }
        BinderService.r(context0, "COMMIT_SERVICE", intent0);
    }

    private static void q(Context context0, Intent intent0) {
        if(intent0 == null) {
            return;
        }
        com.jozein.xedgepro.service.a.e(context0, intent0);
    }

    public static void r(Context context0, String s, Parcelable parcelable0) {
        BinderService.C(context0, BinderService.H(s, parcelable0));
    }

    public static boolean s(Context context0, String s, Parcelable parcelable0) {
        return a.g.a.f(context0).d(s, parcelable0);
    }

    private static boolean t(Context context0, String s, Parcelable parcelable0) {
        Intent intent0 = BinderService.H(s, parcelable0);
        intent0.setComponent(new ComponentName(l.j, AdapterService.class.getName()));
        return com.jozein.xedgepro.service.a.e(context0, intent0);
    }

    public static boolean u(Context context0, Intent intent0) {
        String s = intent0.getAction();
        if(BinderService.A.equals(s)) {
            Context context1 = context0.getApplicationContext();
            String s1 = intent0.getStringExtra("action");
            if(s1 == null) {
                String[] arr_s = intent0.getStringArrayExtra("actions");
                Parcelable[] arr_parcelable = intent0.getParcelableArrayExtra("data");
                if(arr_s != null && arr_parcelable != null) {
                    for(int v = 0; v < arr_s.length; ++v) {
                        BinderService.o(context1, arr_s[v], arr_parcelable[v]);
                    }
                }
            }
            else {
                Parcelable parcelable0 = intent0.getParcelableExtra("data");
                if(parcelable0 != null) {
                    BinderService.o(context1, s1, parcelable0);
                    return true;
                }
            }
            return true;
        }
        return false;
    }

    private static a.b v(Bundle bundle0) {
        return bundle0 == null ? null : m.n(bundle0);
    }

    private static boolean w(List list0) {
        if(list0 == null) {
            return false;
        }
        int v = list0.size();
        return v > 1 && (((q)list0.get(0)).A & 0xFF) == 0 && (((q)list0.get(v - 1)).A & 0xFF) == 1;
    }

    // 检测为 Lambda 实现
    private static void x(Context context0, int v) [...]

    public static void y(Context context0, Pair[] arr_pair) {
        if(arr_pair != null && arr_pair.length != 0) {
            try {
                BinderService.C(context0, BinderService.I(arr_pair));
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    public static void z(Context context0) {
        BinderService.r(context0, j.D, BinderService.B);
    }
}


package com.jozein.xedgepro.xposed;

import android.view.MotionEvent;
import android.webkit.WebView;
import android.widget.AbsListView;
import android.widget.GridView;
import android.widget.ScrollView;
import de.robv.android.xposed.XC_MethodHook.MethodHookParam;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedHelpers;

class i2 extends p2 {
    i2() {
        class a extends XC_MethodHook {
            final i2 a;

            protected void beforeHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                float f = ((MotionEvent)xC_MethodHook$MethodHookParam0.args[0]).getAxisValue(9);
                if(f == 0xC7C35000 || f == 0x47C35000) {
                    ScrollView scrollView0 = (ScrollView)xC_MethodHook$MethodHookParam0.thisObject;
                    if(f == 100000.0f) {
                        scrollView0.smoothScrollTo(0, 0);
                    }
                    else {
                        scrollView0.smoothScrollTo(0, ((int)(((Integer)XposedHelpers.findMethodBestMatch(ScrollView.class, "computeVerticalScrollRange", new Class[0]).invoke(scrollView0)))));
                    }
                    xC_MethodHook$MethodHookParam0.setResult(Boolean.TRUE);
                }
            }
        }


        class b extends XC_MethodHook {
            final i2 a;

            protected void beforeHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                float f = ((MotionEvent)xC_MethodHook$MethodHookParam0.args[0]).getAxisValue(9);
                if(f == 0xC7C35000 || f == 0x47C35000) {
                    AbsListView absListView0 = (AbsListView)xC_MethodHook$MethodHookParam0.thisObject;
                    if(f != 100000.0f) {
                        absListView0.smoothScrollToPosition(absListView0.getCount() - 1);
                    }
                    else if(absListView0 instanceof GridView) {
                        absListView0.smoothScrollToPositionFromTop(0, 0);
                    }
                    else {
                        absListView0.smoothScrollToPosition(0);
                    }
                    xC_MethodHook$MethodHookParam0.setResult(Boolean.TRUE);
                }
            }
        }


        class c extends XC_MethodHook {
            final i2 a;

            int a(int v) {
                return v == 0 ? 0 : v * 200 / ((int)Math.sqrt(v));
            }

            protected void beforeHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                float f = ((MotionEvent)xC_MethodHook$MethodHookParam0.args[0]).getAxisValue(9);
                if(f == 0xC7C35000 || f == 0x47C35000) {
                    WebView webView0 = (WebView)xC_MethodHook$MethodHookParam0.thisObject;
                    int v = (int)(((Integer)XposedHelpers.findMethodBestMatch(WebView.class, "computeVerticalScrollOffset", new Class[0]).invoke(webView0)));
                    int v1 = f == 100000.0f ? -this.a(v) : this.a(((int)(((Integer)XposedHelpers.findMethodBestMatch(WebView.class, "computeVerticalScrollRange", new Class[0]).invoke(webView0)))) - v);
                    if(v1 != 0) {
                        webView0.flingScroll(0, v1);
                    }
                    xC_MethodHook$MethodHookParam0.setResult(Boolean.TRUE);
                }
            }
        }

        try {
            Object[] arr_object = {MotionEvent.class, new a(this)};
            XposedHelpers.findAndHookMethod(ScrollView.class, "onGenericMotionEvent", arr_object);
            Object[] arr_object1 = {MotionEvent.class, new b(this)};
            XposedHelpers.findAndHookMethod(AbsListView.class, "onGenericMotionEvent", arr_object1);
            Object[] arr_object2 = {MotionEvent.class, new c(this)};
            XposedHelpers.findAndHookMethod(WebView.class, "onGenericMotionEvent", arr_object2);
        }
        catch(Throwable throwable0) {
            p2.g(throwable0);
        }
    }
}


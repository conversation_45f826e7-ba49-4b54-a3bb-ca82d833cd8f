package com.jozein.xedgepro.xposed;

import a.b.f3;
import a.b.h3;
import a.b.i3;
import a.b.q0;
import a.b.z2;
import a.p.b;
import a.p;
import android.graphics.Point;
import android.os.Handler;
import android.os.SystemClock;
import android.view.MotionEvent.PointerCoords;
import android.view.MotionEvent.PointerProperties;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import de.robv.android.xposed.XC_MethodHook.MethodHookParam;
import f.l;
import f.v;

class b0 extends z implements b {
    interface a {
        void b(float arg1, float arg2, long arg3);

        void c();

        void d(float arg1, float arg2, long arg3);

        void e(int arg1, float arg2, float arg3, long arg4);
    }

    interface com.jozein.xedgepro.xposed.b0.b {
        void a(float arg1, float arg2);
    }

    private final MotionEvent.PointerProperties[] F;
    private final MotionEvent.PointerCoords[] G;
    private final p H;
    private final w1 I;
    private final m2 J;
    private final Point K;
    private float L;
    private float M;
    private float N;
    private float O;
    private float P;
    private long Q;
    private long R;
    private float S;
    private float T;
    private XC_MethodHook.MethodHookParam U;
    private XC_MethodHook.MethodHookParam V;
    private float W;
    private float X;
    private int Y;
    private int Z;
    private boolean a0;
    private boolean b0;
    private boolean c0;
    private float d0;
    private boolean e0;
    private a f0;
    private int g0;
    private float h0;
    private float i0;
    private boolean j0;
    private boolean k0;
    private com.jozein.xedgepro.xposed.b0.b l0;
    private boolean m0;
    private static final float n0;

    static {
        b0.n0 = -((float)Math.tan(0.872665));
    }

    b0(w1 w10, y0 y00, Handler handler0) {
        super(y00, handler0);
        this.F = new MotionEvent.PointerProperties[]{new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties()};
        this.G = new MotionEvent.PointerCoords[]{new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords()};
        this.Q = 500L;
        this.R = 300L;
        this.U = null;
        this.V = null;
        this.c0 = false;
        this.e0 = false;
        this.f0 = null;
        this.g0 = -1;
        this.h0 = 0.0f;
        this.i0 = 0.0f;
        this.j0 = false;
        this.k0 = false;
        this.l0 = null;
        this.m0 = false;
        this.I = w10;
        this.J = w10.g2();
        this.H = w10.b2();
        this.K = new Point();
    }

    private boolean A(MotionEvent motionEvent0) {
        float f = b0.q(this.W, this.X, motionEvent0.getRawX(), motionEvent0.getRawY());
        return f > this.P && Math.sqrt(this.d0) - Math.sqrt(f) < ((double)this.N);
    }

    private int B(float f, float f1) {
        if(this.m0) {
            this.m0 = false;
            this.I.x4();
            return -2;
        }
        if(this.H.o(8) && this.I.v2(((int)f), ((int)f1))) {
            return -1;
        }
        float f2 = this.S;
        if(f <= f2) {
            float f3 = this.T;
            if(f1 > f3) {
                int v = this.K.y;
                if(f1 < ((float)v) - f3) {
                    float f4 = (((float)v) - f3 * 2.0f) / 3.0f;
                    float f5 = f1 - f3;
                    if(f5 < f4) {
                        return 4;
                    }
                    return f5 > f4 * 2.0f ? 6 : 5;
                }
            }
        }
        else {
            Point point0 = this.K;
            int v1 = point0.x;
            if(f >= ((float)v1) - f2) {
                float f6 = this.T;
                if(f1 > f6) {
                    int v2 = point0.y;
                    if(f1 < ((float)v2) - f6) {
                        float f7 = (((float)v2) - f6 * 2.0f) / 3.0f;
                        float f8 = f1 - f6;
                        if(f8 < f7) {
                            return 7;
                        }
                        return f8 > f7 * 2.0f ? 9 : 8;
                    }
                }
            }
            else {
                float f9 = this.T;
                if(f1 <= f9) {
                    if(f > f2 && f < ((float)v1) - f2) {
                        float f10 = (((float)v1) - f2 * 2.0f) / 3.0f;
                        float f11 = f - f2;
                        if(f11 < f10) {
                            return 10;
                        }
                        return f11 > f10 * 2.0f ? 12 : 11;
                    }
                }
                else if(f1 >= ((float)point0.y) - f9 && f > f2 && f < ((float)v1) - f2) {
                    float f12 = (((float)v1) - f2 * 2.0f) / 3.0f;
                    float f13 = f - f2;
                    if(f13 < f12) {
                        return 13;
                    }
                    return f13 > f12 * 2.0f ? 15 : 14;
                }
            }
        }
        return -1;
    }

    private int C(float f, float f1, float f2, float f3) {
        float f4 = f2 - f;
        float f5 = f3 - f1;
        float f6 = f4 * f4;
        float f7 = f5 * f5;
        if(f6 + f7 < this.L) {
            return 0;
        }
        if(f6 > f7) {
            return f4 < 0.0f ? 3 : 4;
        }
        return f5 < 0.0f ? 5 : 6;
    }

    private XC_MethodHook.MethodHookParam D(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
        if(z.E) {
            Object[] arr_object = xC_MethodHook$MethodHookParam0.args;
            MotionEvent motionEvent0 = (MotionEvent)arr_object[0];
            arr_object[0] = MotionEvent.obtain(motionEvent0);
            motionEvent0.recycle();
        }
        return xC_MethodHook$MethodHookParam0;
    }

    private boolean E(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
        if(z.E) {
            this.g(this.D(xC_MethodHook$MethodHookParam0));
            return true;
        }
        return false;
    }

    private boolean F(MotionEvent motionEvent0) {
        this.p();
        motionEvent0.recycle();
        return true;
    }

    private boolean G(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, MotionEvent motionEvent0) {
        this.Z = 0;
        this.a0 = false;
        this.c0 = false;
        if(this.e0) {
            this.e0 = false;
            return false;
        }
        if(this.I.t2() && this.H.o(33)) {
            return false;
        }
        this.z();
        this.W = motionEvent0.getRawX();
        float f = motionEvent0.getRawY();
        this.X = f;
        this.Y = this.B(this.W, f);
        this.d0 = 0.0f;
        if(this.I.q2() && this.H.F(this.Y)) {
            return false;
        }
        if(this.Y != -2 && !this.H.H(this.Y)) {
            return false;
        }
        this.b0 = true;
        this.c0 = z.E;
        this.U = this.D(xC_MethodHook$MethodHookParam0);
        long v = (long)this.H.u(11);
        this.Q = v;
        if(v == 0L) {
            this.Q = (long)ViewConfiguration.getLongPressTimeout();
        }
        long v1 = (long)this.H.u(10);
        this.R = v1;
        if(v1 == 0L) {
            this.R = (long)ViewConfiguration.getDoubleTapTimeout();
        }
        this.m(this.Q);
        return true;
    }

    private boolean H(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, MotionEvent motionEvent0) {
        a.b b0 = this.w(this.Z);
        if(b0.z == 0) {
            this.b0 = false;
            XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam1 = this.U;
            if(xC_MethodHook$MethodHookParam1 != null) {
                this.g(xC_MethodHook$MethodHookParam1);
                this.U = null;
            }
            return this.E(xC_MethodHook$MethodHookParam0);
        }
        motionEvent0.recycle();
        XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam2 = this.U;
        if(xC_MethodHook$MethodHookParam2 != null) {
            ((MotionEvent)xC_MethodHook$MethodHookParam2.args[0]).recycle();
            this.U = null;
        }
        switch(b0.z) {
            case 1: 
            case 51: 
            case 68: 
            case 106: {
                break;
            }
            case 107: {
                this.N(((h3)b0).I[b0.v(this.Z)], b0.u(this.Z));
                break;
            }
            default: {
                this.N(b0, b0.u(this.Z));
                return true;
            }
        }
        return true;
    }

    private boolean I(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, MotionEvent motionEvent0) {
        float f = motionEvent0.getRawX();
        float f1 = motionEvent0.getRawY();
        int v = this.C(this.W, this.X, f, f1);
        int v1 = this.Z;
        if(v1 != 0) {
            if(v1 != v) {
                return this.F(motionEvent0);
            }
            float f2 = b0.q(this.W, this.X, motionEvent0.getRawX(), motionEvent0.getRawY());
            if(f2 > this.d0) {
                this.d0 = f2;
                if(f2 > this.P && (this.H.o(27) || this.O(this.Z).z == 107)) {
                    this.b0 = true;
                    this.a0 = true;
                    XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam2 = this.U;
                    if(xC_MethodHook$MethodHookParam2 != null) {
                        ((MotionEvent)xC_MethodHook$MethodHookParam2.args[0]).recycle();
                        this.U = null;
                    }
                    this.s(this.w(this.Z), b0.u(this.Z), this.Z, motionEvent0.getEventTime());
                }
            }
        }
        else if(v != 0) {
            this.Z = v;
            this.o();
            if(this.O(this.Z).z == 0) {
                this.b0 = false;
                XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam1 = this.U;
                if(xC_MethodHook$MethodHookParam1 != null) {
                    this.g(xC_MethodHook$MethodHookParam1);
                    this.U = null;
                }
                return this.E(xC_MethodHook$MethodHookParam0);
            }
        }
        motionEvent0.recycle();
        return true;
    }

    private boolean J(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, MotionEvent motionEvent0) {
        if(this.Z == 0) {
            this.o();
            this.b0 = false;
            this.a0 = false;
            this.g(this.U);
            this.U = null;
            return this.E(xC_MethodHook$MethodHookParam0);
        }
        return this.F(motionEvent0);
    }

    private boolean K(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, MotionEvent motionEvent0) {
        this.n();
        MotionEvent motionEvent1 = (MotionEvent)this.V.args[0];
        if(b0.q(this.W, this.X, motionEvent0.getRawX(), motionEvent0.getRawY()) < this.M) {
            long v = motionEvent0.getEventTime();
            long v1 = v - motionEvent1.getEventTime();
            if(v1 < 40L) {
                motionEvent1.recycle();
                this.V = null;
                XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam1 = this.U;
                if(xC_MethodHook$MethodHookParam1 != null) {
                    ((MotionEvent)xC_MethodHook$MethodHookParam1.args[0]).recycle();
                    this.U = null;
                }
                return this.G(xC_MethodHook$MethodHookParam0, motionEvent0);
            }
            if(v1 < this.R) {
                a.b b0 = this.w(1);
                if(b0.z == 0) {
                    this.g(this.U);
                    this.g(this.V);
                    this.b0 = false;
                    this.a0 = false;
                    this.U = null;
                    this.V = null;
                    return false;
                }
                this.b0 = true;
                this.a0 = true;
                motionEvent0.recycle();
                motionEvent1.recycle();
                this.V = null;
                XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam2 = this.U;
                if(xC_MethodHook$MethodHookParam2 != null) {
                    ((MotionEvent)xC_MethodHook$MethodHookParam2.args[0]).recycle();
                    this.U = null;
                }
                this.s(b0, -1, 1, v);
                return true;
            }
        }
        this.L();
        return this.G(xC_MethodHook$MethodHookParam0, motionEvent0);
    }

    private void L() {
        a.b b0 = this.w(0);
        if(b0.z == 0) {
            this.g(this.U);
            this.g(this.V);
            this.b0 = false;
            this.a0 = false;
            this.U = null;
            this.V = null;
            return;
        }
        this.b0 = true;
        this.a0 = true;
        XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0 = this.U;
        if(xC_MethodHook$MethodHookParam0 != null) {
            ((MotionEvent)xC_MethodHook$MethodHookParam0.args[0]).recycle();
            this.U = null;
        }
        XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam1 = this.V;
        if(xC_MethodHook$MethodHookParam1 != null) {
            ((MotionEvent)xC_MethodHook$MethodHookParam1.args[0]).recycle();
            this.V = null;
        }
        this.N(b0, -1);
    }

    private boolean M(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, MotionEvent motionEvent0) {
        int v = this.C(this.W, this.X, motionEvent0.getRawX(), motionEvent0.getRawY());
        int v1 = this.Z;
        if(v1 == 0) {
            this.o();
            if(v == 0) {
                if(motionEvent0.getEventTime() - motionEvent0.getDownTime() < this.Q) {
                    if(this.O(1).z == 0) {
                        a.b b0 = this.w(0);
                        if(b0.z == 0) {
                            this.g(this.U);
                            this.U = null;
                            this.b0 = false;
                            return this.E(xC_MethodHook$MethodHookParam0);
                        }
                        motionEvent0.recycle();
                        XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam1 = this.U;
                        if(xC_MethodHook$MethodHookParam1 != null) {
                            ((MotionEvent)xC_MethodHook$MethodHookParam1.args[0]).recycle();
                            this.U = null;
                        }
                        this.V = null;
                        this.N(b0, -1);
                        return true;
                    }
                    this.V = this.D(xC_MethodHook$MethodHookParam0);
                    this.l(this.R);
                    return true;
                }
                this.Z = 2;
                return this.H(xC_MethodHook$MethodHookParam0, motionEvent0);
            }
            return this.c0 ? this.E(xC_MethodHook$MethodHookParam0) : false;
        }
        if(v1 == v && this.A(motionEvent0)) {
            return this.H(xC_MethodHook$MethodHookParam0, motionEvent0);
        }
        this.b0 = false;
        motionEvent0.recycle();
        XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam2 = this.U;
        if(xC_MethodHook$MethodHookParam2 != null) {
            ((MotionEvent)xC_MethodHook$MethodHookParam2.args[0]).recycle();
            this.U = null;
        }
        return true;
    }

    private void N(a.b b0, int v) {
        if(b0.z != 0x10001) {
            this.I.v3(b0, v, null);
            return;
        }
        this.I.t4(this.W, this.X);
    }

    private a.b O(int v) {
        return this.x(v, true);
    }

    void P(float f, float f1, com.jozein.xedgepro.xposed.b0.b b0$b0) {
        this.g0 = ((int)f) << 16 | ((int)f1);
        this.k0 = false;
        this.l0 = b0$b0;
    }

    void Q(boolean z) {
        this.m0 = z;
    }

    void R() {
        this.e0 = true;
    }

    void S() {
        this.g0 = -1;
        this.k0 = true;
    }

    private void T(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, MotionEvent motionEvent0) {
        int v = motionEvent0.getPointerCount();
        for(int v1 = 0; v1 < v; ++v1) {
            MotionEvent.PointerProperties motionEvent$PointerProperties0 = this.F[v1];
            motionEvent$PointerProperties0.id = motionEvent0.getPointerId(v1);
            this.F[v1].toolType = 1;
            MotionEvent.PointerCoords motionEvent$PointerCoords0 = this.G[v1];
            motionEvent$PointerCoords0.x = motionEvent0.getX(v1) - this.h0;
            MotionEvent.PointerCoords motionEvent$PointerCoords1 = this.G[v1];
            motionEvent$PointerCoords1.y = motionEvent0.getY(v1) - this.i0;
        }
        Object[] arr_object = xC_MethodHook$MethodHookParam0.args;
        long v2 = motionEvent0.getDownTime();
        long v3 = motionEvent0.getEventTime();
        int v4 = motionEvent0.getAction();
        int v5 = motionEvent0.getMetaState();
        int v6 = motionEvent0.getButtonState();
        float f = motionEvent0.getXPrecision();
        float f1 = motionEvent0.getYPrecision();
        int v7 = motionEvent0.getDeviceId();
        int v8 = motionEvent0.getSource();
        arr_object[0] = MotionEvent.obtain(v2, v3, v4, v, this.F, this.G, v5, v6, f, f1, v7, 0, v8, 0);
        this.g(xC_MethodHook$MethodHookParam0);
        com.jozein.xedgepro.xposed.b0.b b0$b0 = this.l0;
        if(b0$b0 != null) {
            b0$b0.a(motionEvent0.getRawX() - this.h0, motionEvent0.getRawY() - this.i0);
        }
        motionEvent0.recycle();
    }

    @Override  // com.jozein.xedgepro.xposed.z
    protected boolean d(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0, MotionEvent motionEvent0) {
        boolean z;
        int v = motionEvent0.getActionMasked();
        if(v == 0) {
            this.I.T3();
            this.b0 = false;
            if(this.k0) {
                this.j0 = false;
                this.g0 = -1;
            }
            int v1 = this.g0;
            boolean z1 = false;
            if(v1 != -1) {
                z1 = true;
                this.j0 = true;
                this.k0 = false;
                this.g0 = -1;
                this.W = motionEvent0.getRawX();
                float f = motionEvent0.getRawY();
                this.X = f;
                this.h0 = this.W - ((float)(v1 >>> 16));
                this.i0 = f - ((float)(v1 & 0xFFFF));
            }
            else if(this.j0) {
                z1 = true;
                this.k0 = true;
            }
            if(z1) {
                this.I.I3();
                this.T(xC_MethodHook$MethodHookParam0, motionEvent0);
                return true;
            }
            z = this.V == null ? this.G(xC_MethodHook$MethodHookParam0, motionEvent0) : this.K(xC_MethodHook$MethodHookParam0, motionEvent0);
            return z || !this.c0 ? z : this.E(xC_MethodHook$MethodHookParam0);
        }
        if(this.j0) {
            this.T(xC_MethodHook$MethodHookParam0, motionEvent0);
            switch(v) {
                case 1: {
                    if(!this.k0) {
                        long v2 = (long)ViewConfiguration.getDoubleTapTimeout();
                        this.I.w4(v2);
                        return true;
                    }
                    this.r();
                    return true;
                }
                case 2: {
                    if(!this.k0) {
                        float f1 = motionEvent0.getRawX() - this.W;
                        float f2 = motionEvent0.getRawY() - this.X;
                        float f3 = (float)ViewConfiguration.get(this.I.T1()).getScaledTouchSlop();
                        if(f1 * f1 + f2 * f2 > f3 * f3) {
                            this.k0 = true;
                            return true;
                        }
                    }
                    break;
                }
                case 3: {
                    this.r();
                    break;
                }
                case 5: {
                    this.k0 = true;
                    return true;
                }
                default: {
                    return true;
                }
            }
            return true;
        }
        if(this.b0) {
            if(this.a0) {
                if(this.f0 != null) {
                    this.t(v, motionEvent0);
                }
                motionEvent0.recycle();
                return true;
            }
            switch(v) {
                case 1: {
                    this.b0 = false;
                    this.a0 = false;
                    z = this.M(xC_MethodHook$MethodHookParam0, motionEvent0);
                    return z || !this.c0 ? z : this.E(xC_MethodHook$MethodHookParam0);
                }
                case 2: {
                    z = this.I(xC_MethodHook$MethodHookParam0, motionEvent0);
                    return z || !this.c0 ? z : this.E(xC_MethodHook$MethodHookParam0);
                }
                case 3: {
                    this.b0 = false;
                    break;
                }
                case 5: {
                    z = this.J(xC_MethodHook$MethodHookParam0, motionEvent0);
                    return z || !this.c0 ? z : this.E(xC_MethodHook$MethodHookParam0);
                }
                case 4: 
                case 6: {
                    break;
                }
                default: {
                    return false;
                }
            }
            z = this.F(motionEvent0);
            return z || !this.c0 ? z : this.E(xC_MethodHook$MethodHookParam0);
        }
        return this.c0 ? this.E(xC_MethodHook$MethodHookParam0) : false;
    }

    @Override  // com.jozein.xedgepro.xposed.z
    protected void e() {
        this.L();
    }

    @Override  // com.jozein.xedgepro.xposed.z
    protected void f() {
        a.b b0 = this.w(2);
        if(b0.z == 0) {
            this.b0 = false;
            this.g(this.U);
            this.U = null;
            return;
        }
        this.a0 = true;
        this.b0 = true;
        XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0 = this.U;
        if(xC_MethodHook$MethodHookParam0 != null) {
            ((MotionEvent)xC_MethodHook$MethodHookParam0.args[0]).recycle();
            this.U = null;
        }
        this.s(b0, -1, 2, SystemClock.uptimeMillis());
    }

    void p() {
        this.a0 = true;
        this.g0 = -1;
        if(this.Z == 0) {
            this.o();
        }
        this.r();
        try {
            a b0$a0 = this.f0;
            if(b0$a0 != null) {
                b0$a0.c();
            }
            XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0 = this.U;
            if(xC_MethodHook$MethodHookParam0 != null) {
                ((MotionEvent)xC_MethodHook$MethodHookParam0.args[0]).recycle();
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        this.f0 = null;
        this.U = null;
    }

    static float q(float f, float f1, float f2, float f3) {
        return (f2 - f) * (f2 - f) + (f3 - f1) * (f3 - f1);
    }

    private void r() {
        if(this.j0 || this.l0 != null) {
            this.j0 = false;
            this.h0 = 0.0f;
            this.i0 = 0.0f;
            this.g0 = -1;
            this.I.w4(0L);
        }
    }

    private void s(a.b b0, int v, int v1, long v2) {
        int v6;
        try {
            switch(b0.z) {
                case 0: 
                case 1: {
                    this.f0 = null;
                    return;
                }
                case 51: {
                    this.f0 = this.I.Z1();
                    break;
                }
                case 68: {
                    this.f0 = this.I.S1(((z2)b0));
                    break;
                }
                case 86: {
                    this.f0 = this.I.f2(((i3)b0));
                    break;
                }
                case 106: {
                    this.f0 = this.I.a2(((q0)b0).I);
                    break;
                }
                case 107: {
                    if(l.r) {
                        long v3 = (long)this.H.v(29, 300);
                        float f = this.O;
                        boolean z = this.H.o(40);
                        this.f0 = new m3(((h3)b0), this.I, b0.v(v1), v3, f, z);
                        break;
                    }
                    this.f0 = null;
                    this.N(b0, v);
                    return;
                }
                default: {
                    this.f0 = null;
                    this.N(b0, v);
                    return;
                }
            }
            int v4 = (int)this.W;
            int v5 = (int)this.X;
            switch(this.Y) {
                case 1: 
                case 7: 
                case 8: 
                case 9: {
                    v4 = this.K.x;
                    v6 = 1;
                    break;
                }
                case 2: 
                case 10: 
                case 11: 
                case 12: {
                    v5 = 0;
                    v6 = 2;
                    break;
                }
                case 3: 
                case 13: 
                case 14: 
                case 15: {
                    v5 = this.K.y;
                    v6 = 3;
                    break;
                }
                default: {
                    v4 = 0;
                    v6 = 0;
                }
            }
            this.f0.e(v6, ((float)v4), ((float)v5), v2);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            a b0$a0 = this.f0;
            if(b0$a0 != null) {
                try {
                    b0$a0.c();
                    this.f0 = null;
                }
                catch(Throwable throwable1) {
                    v.d(throwable1);
                }
            }
        }
    }

    private void t(int v, MotionEvent motionEvent0) {
        try {
            switch(v) {
                case 1: {
                    this.f0.b(((float)(((int)motionEvent0.getRawX()))), ((float)(((int)motionEvent0.getRawY()))), motionEvent0.getEventTime());
                    break;
                }
                case 3: {
                    this.f0.c();
                    break;
                }
                default: {
                    this.f0.d(((float)(((int)motionEvent0.getRawX()))), ((float)(((int)motionEvent0.getRawY()))), motionEvent0.getEventTime());
                    return;
                }
            }
            this.f0 = null;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private static int u(int v) {
        switch(v) {
            case 3: {
                return 1;
            }
            case 4: {
                return 0;
            }
            case 5: {
                return 3;
            }
            case 6: {
                return 2;
            }
            default: {
                return -1;
            }
        }
    }

    private static int v(int v) {
        switch(v) {
            case 3: {
                return 1;
            }
            case 4: {
                return 2;
            }
            case 5: {
                return 3;
            }
            case 6: {
                return 4;
            }
            default: {
                return 0;
            }
        }
    }

    private a.b w(int v) {
        return this.x(v, false);
    }

    private a.b x(int v, boolean z) {
        a.b b1;
        int v1 = this.Y;
        if(v1 == -2) {
            return v == 0 ? new f3() : a.b.e();
        }
        if(this.H.s(v1)) {
            a.b b0 = this.H.i(this.Y, v);
            b1 = this.I.y4(b0, z);
        }
        else {
            b1 = a.b.e();
        }
        if(b1.z == 0) {
            int v2 = p.h(this.Y);
            if(this.H.s(v2)) {
                a.b b2 = this.H.i(v2, v);
                return this.I.y4(b2, z);
            }
        }
        return b1;
    }

    Point y() {
        float f = this.J.N();
        float f1 = this.H.t(36, 0.12f);
        return new Point(((int)(f1 * f)), ((int)(this.H.t(36, f1) * f)));
    }

    private void z() {
        float f = this.J.N();
        float f1 = f * f;
        this.L = 0.0225f * f1;
        this.M = 0.09f * f1;
        this.P = 0.09f * f1;
        this.N = f * 0.3f;
        this.O = 0.0064f * f1;
        this.J.I(this.K);
        float f2 = this.H.t(36, 0.12f);
        float f3 = this.H.t(36, f2);
        this.S = f2 * f;
        this.T = f3 * f;
        float f4 = this.H.t(38, 0.3f);
        this.P = f4 * f4 * f1;
    }
}


package a;

import android.content.Context;
import f.h0;
import f.s;
import f.v;
import java.util.ArrayList;
import java.util.List;

public class e extends l {
    private boolean E;
    private List F;
    private static final String G;
    private static volatile e H;

    static {
        e.G = f.l.m + "collection";
        e.H = null;
    }

    private e() {
        super(null, e.G);
        this.E = false;
    }

    public static e A() {
        synchronized(e.class) {
            if(e.H == null) {
                e.H = new e();
            }
            return e.H;
        }
    }

    // 检测为 Lambda 实现
    private void B(Context context0) [...]

    public void C() {
        this.E = true;
    }

    public static void D() {
        synchronized(e.class) {
            e.H = null;
        }
    }

    public void E(Context context0, b b0) {
        if(this.E) {
            if(this.F == null) {
                this.F = new ArrayList();
            }
            this.F.add(b0);
            return;
        }
        this.e(context0, b0);
    }

    public void F(Context context0) {
        this.E = false;
        if(this.F == null) {
            return;
        }
        h0.a().post(() -> if(!this.E) {
            List list0 = this.F;
            if(list0 != null) {
                int v = list0.size();
                for(int v1 = 0; v1 < v; ++v1) {
                    try {
                        this.e(context0, ((b)this.F.get(v1)));
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                        if(true) {
                            break;
                        }
                    }
                }
                this.F = null;
            }
        });
    }

    protected void G(s s0, b b0) {
        s0.f(b0);
    }

    @Override  // a.l
    protected Object g(s s0) {
        return this.z(s0);
    }

    @Override  // a.l
    protected void x(s s0, Object object0) {
        this.G(s0, ((b)object0));
    }

    protected b z(s s0) {
        return s0.g();
    }
}


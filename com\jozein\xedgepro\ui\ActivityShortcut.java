package com.jozein.xedgepro.ui;

import a.b;
import android.os.Bundle;
import android.view.View;
import d.d;
import e.j0.c;
import e.j0;
import f.l0;
import f.v;

public class ActivityShortcut extends j0 {
    public static class a extends d {
        @Override  // e.j0$c
        protected void A() {
        }

        @Override  // e.j0$c
        protected void U(Bundle bundle0) {
            ((ActivityShortcut)this.getActivity()).A(bundle0);
        }

        @Override  // e.j0$c
        protected void z(View view0) {
        }
    }

    public void A(Bundle bundle0) {
        if(bundle0 == null) {
            v.c("Result == null");
            this.setResult(0);
        }
        else {
            b b0 = (b)bundle0.getParcelable("result");
            if(b0 == null) {
                v.c("Result action: null!");
                this.setResult(0);
            }
            else {
                this.setResult(-1, l0.b(this, b0));
            }
        }
        this.finish();
    }

    @Override  // e.j0
    protected c v() {
        return new a().G1(3);
    }
}


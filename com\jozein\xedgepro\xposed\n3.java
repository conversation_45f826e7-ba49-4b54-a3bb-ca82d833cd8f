package com.jozein.xedgepro.xposed;

import f.l;
import f.p;
import f.v;

class n3 {
    private final p a;
    private final int[] b;
    private static final String c;

    static {
        n3.c = p2.A + (l.r ? "sys_prefs_pro" : "sys_prefs");
    }

    n3() {
        p p0 = new p(n3.c);
        this.a = p0;
        this.b = new int[1];
        if(p0.E()) {
            this.b[0] = this.a.h();
        }
        this.a.m();
    }

    public int a(int v) {
        return this.b[v];
    }

    private void b() {
        for(int v1 = 0; true; ++v1) {
            boolean z = true;
            if(v1 >= 1) {
                break;
            }
            if(this.b[v1] != 0) {
                z = false;
                break;
            }
        }
        if(z) {
            try {
                this.a.H().delete();
                return;
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        this.a.F();
        for(int v = 0; v < 1; ++v) {
            this.a.w(this.b[v]);
        }
        this.a.n();
    }

    public void c(int v, int v1) {
        int[] arr_v = this.b;
        if(arr_v[v] != v1) {
            arr_v[v] = v1;
            this.b();
        }
    }
}


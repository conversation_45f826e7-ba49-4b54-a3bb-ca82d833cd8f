package c;

import a.h.i0;
import android.app.Activity;
import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView.OnItemSelectedListener;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.Spinner;
import android.widget.TextView;
import e.j0.b;
import f.i;
import g.x;

public class d extends b {
    private String[] C;
    private Spinner D;
    private EditText E;
    private Spinner F;
    private EditText G;

    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        class a implements AdapterView.OnItemSelectedListener {
            final d a;

            @Override  // android.widget.AdapterView$OnItemSelectedListener
            public void onItemSelected(AdapterView adapterView0, View view0, int v, long v1) {
                d.this.E.setEnabled(v == 0);
            }

            @Override  // android.widget.AdapterView$OnItemSelectedListener
            public void onNothingSelected(AdapterView adapterView0) {
            }
        }

        Activity activity0 = this.getActivity();
        Bundle bundle1 = this.e();
        LinearLayout linearLayout0 = new LinearLayout(activity0);
        linearLayout0.setOrientation(1);
        x x0 = this.g();
        int v = x0.f;
        linearLayout0.setPadding(v, v, v, v);
        a.v.a v$a0 = a.v.a.g();
        int v1 = v$a0.k();
        String[] arr_s = new String[v1 + 1];
        this.C = arr_s;
        arr_s[0] = "Input expression";
        for(int v2 = 0; v2 < v1; ++v2) {
            String[] arr_s1 = this.C;
            arr_s1[v2 + 1] = v$a0.f(v2);
        }
        int v3 = bundle1.getInt("variable", 0);
        this.D = new Spinner(activity0);
        ArrayAdapter arrayAdapter0 = new ArrayAdapter(activity0, 0x1090008, this.C);
        arrayAdapter0.setDropDownViewResource(0x1090009);
        this.D.setAdapter(arrayAdapter0);
        this.D.setSelection(v3);
        linearLayout0.addView(this.D);
        EditText editText0 = v.y(activity0, bundle1.getCharSequence("expression1", "1"));
        this.E = editText0;
        if(v3 != 0) {
            editText0.setEnabled(false);
        }
        linearLayout0.addView(this.E);
        this.D.setOnItemSelectedListener(new a(this));
        this.F = new Spinner(activity0);
        ArrayAdapter arrayAdapter1 = new ArrayAdapter(activity0, 0x1090008, i0.H);
        arrayAdapter1.setDropDownViewResource(0x1090009);
        this.F.setAdapter(arrayAdapter1);
        this.F.setSelection(bundle1.getInt("operator", 0));
        linearLayout0.addView(this.F, -2, -2);
        EditText editText1 = v.y(activity0, bundle1.getCharSequence("expression2", "1"));
        this.G = editText1;
        linearLayout0.addView(editText1);
        TextView textView0 = new TextView(activity0);
        textView0.setText(0x7F060214);  // string:variable_expression_hint "Variable:\n 32 bit signed integer.\nSupported operators:\n 
                                        // +, -, *, /, %, <<, >>, &, ^, |, ~, ++, --.\n Same as C/C++/Java…\nExample:\n (var 
                                        // + 1) % 2"
        textView0.setTextSize(0, ((float)x0.e));
        textView0.setPadding(v, v, v, v);
        linearLayout0.addView(textView0);
        ScrollView scrollView0 = new ScrollView(activity0);
        scrollView0.setFillViewport(true);
        scrollView0.addView(linearLayout0);
        return new AlertDialog.Builder(activity0).setView(scrollView0).setNegativeButton(0x1040000, b.B).setPositiveButton(0x104000A, (DialogInterface dialogInterface0, int v) -> {
            int v1 = this.D.getSelectedItemPosition();
            String s = v1 == 0 ? this.E.getText().toString() : this.C[v1];
            String s1 = this.G.getText().toString();
            try {
                i.d(s);
                i.d(s1);
                this.q("result", new i0(s, i0.H[this.F.getSelectedItemPosition()].trim(), s1));
            }
            catch(NumberFormatException numberFormatException0) {
                this.t(numberFormatException0.getMessage());
            }
        }).create();
    }

    @Override  // e.j0$b
    public void onSaveInstanceState(Bundle bundle0) {
        Bundle bundle1 = this.e();
        bundle1.putInt("variable", this.D.getSelectedItemPosition());
        bundle1.putInt("operator", this.F.getSelectedItemPosition());
        bundle1.putCharSequence("expression1", this.E.getText());
        bundle1.putCharSequence("expression2", this.G.getText());
        super.onSaveInstanceState(bundle0);
    }

    // 检测为 Lambda 实现
    private void w(DialogInterface dialogInterface0, int v) [...]
}


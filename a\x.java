package a;

import android.appwidget.AppWidgetProviderInfo;
import android.content.Context;
import android.graphics.Point;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.util.SparseArray;
import f.l;
import f.p;
import f.v;
import g.a0;
import java.util.ArrayList;

public class x implements l {
    public static class a {
        public final String a;
        public int b;
        public int c;
        public int d;
        public int e;

        public a(String s, int v, int v1, int v2, int v3) {
            this.a = s;
            this.d = v;
            this.e = v1;
            this.b = v2;
            this.c = v3;
        }
    }

    private final SparseArray A;
    private static x B;
    private static int C;
    private final p z;

    static {
    }

    private x() {
        p p0 = new p(l.q);
        this.z = p0;
        this.A = new SparseArray();
        if(p0.E()) {
            while(true) {
                int v = this.z.h();
                a x$a0 = new a(this.z.a(), this.z.h(), this.z.h(), this.z.h(), this.z.h());
                this.A.append(v, x$a0);
                if(!this.z.t()) {
                    break;
                }
            }
        }
        this.z.m();
    }

    public void a(int v, AppWidgetProviderInfo appWidgetProviderInfo0, Context context0) {
        int v4;
        int v3;
        int v6;
        int v1 = appWidgetProviderInfo0.minWidth;
        int v2 = appWidgetProviderInfo0.minHeight;
        String s = appWidgetProviderInfo0.provider.flattenToShortString();
        if(v1 <= 1 || v2 <= 1) {
            a x$a0 = this.g(s);
            int v5 = 0;
            if(x$a0 == null) {
                v.c("Error load size!");
                v6 = 0;
            }
            else {
                v5 = x$a0.d;
                v6 = x$a0.e;
            }
            v4 = v6;
            v3 = v5;
        }
        else {
            v3 = x.c(context0, v1);
            v4 = x.c(context0, v2);
        }
        a x$a1 = new a(s, v3, v4, 0, 0);
        synchronized(this.A) {
            this.A.put(v, x$a1);
        }
        this.n();
    }

    public void b() {
        synchronized(this.A) {
            this.A.clear();
        }
        this.z.F().n();
    }

    public static int c(Context context0, int v) {
        return ((a0.t(v) - 30) / 70 + 1) * x.d(context0);
    }

    public static int d(Context context0) {
        if(x.C == 0) {
            g.x x0 = g.x.b(context0);
            Point point0 = a0.n(context0, new Point());
            x.C = (x0.a + Math.min(point0.x, point0.y) / 4) / 2;
        }
        return x.C;
    }

    public int e(String s) {
        synchronized(this.A) {
            int v1 = this.A.size();
            for(int v2 = 0; v2 < v1; ++v2) {
                if(s.equals(((a)this.A.valueAt(v2)).a)) {
                    return this.A.keyAt(v2);
                }
            }
            return 0;
        }
    }

    public a f(int v) {
        synchronized(this.A) {
        }
        return (a)this.A.get(v);
    }

    private a g(String s) {
        synchronized(this.A) {
            int v1 = this.A.size();
            for(int v2 = 0; v2 < v1; ++v2) {
                if(s.equals(((a)this.A.valueAt(v2)).a)) {
                    return (a)this.A.valueAt(v2);
                }
            }
            return null;
        }
    }

    public static x h() {
        synchronized(x.class) {
            if(x.B == null) {
                x.B = new x();
            }
            return x.B;
        }
    }

    public ArrayList i() {
        ArrayList arrayList0 = new ArrayList();
        synchronized(this.A) {
            int v1 = this.A.size();
            for(int v2 = 0; v2 < v1; ++v2) {
                arrayList0.add(this.A.keyAt(v2));
            }
            return arrayList0;
        }
    }

    public static String j(Context context0, AppWidgetProviderInfo appWidgetProviderInfo0) {
        try {
            if(appWidgetProviderInfo0 == null) {
                return "-";
            }
            if(Build.VERSION.SDK_INT >= 21) {
                return appWidgetProviderInfo0.loadLabel(context0.getPackageManager());
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        return appWidgetProviderInfo0.label;
    }

    public static Drawable k(Context context0, AppWidgetProviderInfo appWidgetProviderInfo0) {
        if(appWidgetProviderInfo0 != null) {
            if(Build.VERSION.SDK_INT >= 21) {
                return appWidgetProviderInfo0.loadPreviewImage(context0, -1);
            }
            try {
                String s = appWidgetProviderInfo0.provider.getPackageName();
                return context0.getPackageManager().getResourcesForApplication(s).getDrawable((appWidgetProviderInfo0.previewImage > 0 ? appWidgetProviderInfo0.previewImage : appWidgetProviderInfo0.icon));
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        return null;
    }

    public static void l() {
        synchronized(x.class) {
            x.B = null;
        }
    }

    public void m(int v) {
        synchronized(this.A) {
            this.A.delete(v);
        }
        this.n();
    }

    private void n() {
        this.z.F();
        int v = this.A.size();
        for(int v1 = 0; v1 < v; ++v1) {
            a x$a0 = (a)this.A.valueAt(v1);
            int v2 = this.A.keyAt(v1);
            this.z.w(v2).y(x$a0.a).w(x$a0.d).w(x$a0.e).w(x$a0.b).w(x$a0.c).C();
        }
        this.z.n();
    }

    public void o(int v, int v1, int v2) {
        a x$a0;
        synchronized(this.A) {
            x$a0 = (a)this.A.get(v, null);
        }
        if(x$a0 != null) {
            x$a0.b = v1;
            x$a0.c = v2;
            this.n();
        }
    }

    public void p(int v, int v1, int v2) {
        a x$a0;
        synchronized(this.A) {
            x$a0 = (a)this.A.get(v, null);
        }
        if(x$a0 != null) {
            x$a0.d = v1;
            x$a0.e = v2;
            this.n();
        }
    }
}


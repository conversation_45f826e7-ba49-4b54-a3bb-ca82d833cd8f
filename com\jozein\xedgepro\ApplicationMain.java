package com.jozein.xedgepro;

import a.b.e1;
import a.b.f0;
import a.b.k2;
import a.b.s0;
import a.b.v0;
import a.b.y0;
import a.e;
import a.f;
import a.j;
import a.k;
import a.m;
import a.u;
import a.v.a;
import a.w;
import a.x;
import a.z.b;
import a.z;
import android.app.Activity;
import android.app.Application.ActivityLifecycleCallbacks;
import android.app.Application.OnProvideAssistDataListener;
import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ShortcutInfo.Builder;
import android.content.pm.ShortcutInfo;
import android.content.pm.ShortcutManager;
import android.graphics.drawable.Icon;
import android.os.Build.VERSION;
import android.os.Bundle;
import com.jozein.xedgepro.ui.ActivityMain;
import f.h0;
import f.l0;
import f.l;
import f.p0;
import f.q0;
import f.q;
import f.v;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import jeb.synthetic.FIN;

public class ApplicationMain extends Application implements l {
    private List A;
    private static boolean B = false;
    private static boolean C = false;
    private static String D;
    private static String E;
    private z z;

    static {
    }

    public ApplicationMain() {
        this.A = null;
    }

    public void e(b z$b0) {
        if(z$b0 == null) {
            return;
        }
        if(this.A == null) {
            this.A = new ArrayList();
        }
        this.A.add(z$b0);
    }

    private ShortcutInfo f(String s, a.b b0, int v, int v1) {
        Intent intent0 = l0.k(b0);
        CharSequence charSequence0 = b0.n(this);
        return new ShortcutInfo.Builder(this, s).setIntent(intent0).setLongLabel(charSequence0).setShortLabel(charSequence0).setIcon(Icon.createWithResource(this, v)).setRank(v1).build();
    }

    public static void g() {
        ApplicationMain.B = true;
    }

    public z h() {
        return this.z;
    }

    public static String i() {
        return ApplicationMain.E;
    }

    public static boolean isModuleActivated() {
        if(Build.VERSION.SDK_INT < 26 && !ApplicationMain.B && !ApplicationMain.C) {
            ApplicationMain.C = true;
            ApplicationMain.B = ApplicationMain.p();
        }
        return ApplicationMain.B;
    }

    public static String j() {
        return ApplicationMain.D;
    }

    private void k() {
        class com.jozein.xedgepro.ApplicationMain.b implements Application.ActivityLifecycleCallbacks {
            final ApplicationMain a;

            // 检测为 Lambda 实现
            private static void b() [...]

            @Override  // android.app.Application$ActivityLifecycleCallbacks
            public void onActivityCreated(Activity activity0, Bundle bundle0) {
            }

            @Override  // android.app.Application$ActivityLifecycleCallbacks
            public void onActivityDestroyed(Activity activity0) {
                if(activity0 instanceof ActivityMain && activity0.isFinishing()) {
                    try {
                        ApplicationMain.this.unregisterActivityLifecycleCallbacks(this);
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                    h0.a().post(() -> {
                        f.g();
                        a.s.f.h();
                        f0.A();
                        k2.A();
                        e.A();
                        v0.d();
                    });
                }
            }

            @Override  // android.app.Application$ActivityLifecycleCallbacks
            public void onActivityPaused(Activity activity0) {
            }

            @Override  // android.app.Application$ActivityLifecycleCallbacks
            public void onActivityResumed(Activity activity0) {
            }

            @Override  // android.app.Application$ActivityLifecycleCallbacks
            public void onActivitySaveInstanceState(Activity activity0, Bundle bundle0) {
            }

            @Override  // android.app.Application$ActivityLifecycleCallbacks
            public void onActivityStarted(Activity activity0) {
            }

            @Override  // android.app.Application$ActivityLifecycleCallbacks
            public void onActivityStopped(Activity activity0) {
            }
        }

        boolean z = z.e0();
        z.w0();
        if(z) {
            s0.B(this, 25);
        }
        v0.a();
        z z1 = new z();
        this.z = z1;
        w.a(z1, this);
        this.registerActivityLifecycleCallbacks(new com.jozein.xedgepro.ApplicationMain.b(this));
    }

    public static boolean l() {
        return ApplicationMain.isModuleActivated() || p0.p() != 0;
    }

    private void m() {
        class c extends BroadcastReceiver {
            final ApplicationMain a;

            @Override  // android.content.BroadcastReceiver
            public void onReceive(Context context0, Intent intent0) {
                ApplicationMain.B = true;
                if(intent0 != null) {
                    try {
                        int[] arr_v = intent0.getIntArrayExtra("alias");
                        if(arr_v != null) {
                            q0.e(arr_v);
                        }
                        ApplicationMain.D = intent0.getStringExtra("build_time");
                        ApplicationMain.E = intent0.getStringExtra("time");
                        if(intent0.getBooleanExtra("send_files", false)) {
                            j.h(context0);
                        }
                        ApplicationMain.this.unregisterReceiver(this);
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }
        }

        this.registerReceiver(new c(this), new IntentFilter(s0.N));
        s0.B(this, 17);
    }

    public void n() {
        q.b();
        if(Build.VERSION.SDK_INT >= 24) {
            u.j();
        }
        m.c();
        e.D();
        x.l();
        v0.f();
        this.z.g();
        this.z.L();
        w.a(this.z, this);
        a.h();
        k.k();
        f.l();
        a.s.f.k();
        f0.B();
        k2.B();
        y0.K();
        v.c("Settings reloaded.");
    }

    public void o(b z$b0) {
        this.A.remove(z$b0);
    }

    @Override  // android.app.Application
    public void onCreate() {
        class com.jozein.xedgepro.ApplicationMain.a implements b {
            final ApplicationMain a;

            @Override  // a.z$b
            public void a(boolean z) {
                ApplicationMain.B = true;
                if(ApplicationMain.this.A != null) {
                    for(Object object0: ApplicationMain.this.A) {
                        ((b)object0).a(z);
                    }
                }
            }

            @Override  // a.z$b
            public void b(boolean z) {
                ApplicationMain.B = true;
                if(ApplicationMain.this.A != null) {
                    for(Object object0: ApplicationMain.this.A) {
                        ((b)object0).b(z);
                    }
                }
            }
        }

        this.k();
        f.z.K(this);
        super.onCreate();
        this.z.y0(this, new com.jozein.xedgepro.ApplicationMain.a(this));
        this.m();
        try {
            this.q();
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private static boolean p() {
        try {
            if(Build.VERSION.SDK_INT >= 28) {
                return false;
            }
            ClassLoader classLoader0 = ClassLoader.getSystemClassLoader();
            Field field0 = Class.forName("de.robv.android.xposed.XposedBridge", false, classLoader0).getDeclaredField("sHookedMethodCallbacks");
            field0.setAccessible(true);
            Map map0 = (Map)field0.get(null);
            Field field1 = Class.forName("de.robv.android.xposed.XposedBridge$CopyOnWriteSortedSet", false, classLoader0).getDeclaredField("elements");
            field1.setAccessible(true);
            String s = l.j + '.';
            if(map0 != null) {
                __monitor_enter(map0);
                int v = FIN.finallyOpen$NT();
                Iterator iterator0 = map0.values().iterator();
                while(true) {
                    if(!iterator0.hasNext()) {
                        FIN.finallyCodeBegin$NT(v);
                        __monitor_exit(map0);
                        FIN.finallyCodeEnd$NT(v);
                        return false;
                    }
                    Object object0 = iterator0.next();
                    Object[] arr_object = (Object[])field1.get(object0);
                    for(int v1 = 0; v1 < arr_object.length; ++v1) {
                        if(arr_object[v1].getClass().getName().startsWith(s)) {
                            FIN.finallyExec$NT(v);
                            return true;
                        }
                    }
                }
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        return false;
    }

    private void q() {
        if(Build.VERSION.SDK_INT < 25) {
            return;
        }
        ShortcutManager shortcutManager0 = (ShortcutManager)this.getSystemService("shortcut");
        if(shortcutManager0 == null) {
            return;
        }
        List list0 = shortcutManager0.getDynamicShortcuts();
        if(list0.size() > 0) {
            ShortcutInfo shortcutInfo0 = (ShortcutInfo)list0.get(0);
            String s = shortcutInfo0.getId();
            CharSequence charSequence0 = shortcutInfo0.getShortLabel();
            if("gesture_control".equals(s) && new a.b.p0(0).n(this).equals(charSequence0) || "key_control".equals(s) && new e1(0).n(this).equals(charSequence0)) {
                return;
            }
        }
        ArrayList arrayList0 = new ArrayList();
        arrayList0.add(this.f("gesture_control", new a.b.p0(0), 0x7F0400A8, 1));  // drawable:ic_shortcut_toggle_gesture
        arrayList0.add(this.f("key_control", new e1(0), 0x7F0400A9, 0));  // drawable:ic_shortcut_toggle_key
        shortcutManager0.addDynamicShortcuts(arrayList0);
    }

    @Override  // android.app.Application
    public void registerOnProvideAssistDataListener(Application.OnProvideAssistDataListener application$OnProvideAssistDataListener0) {
        f.f0.r(application$OnProvideAssistDataListener0);
        super.registerOnProvideAssistDataListener(application$OnProvideAssistDataListener0);
    }
}


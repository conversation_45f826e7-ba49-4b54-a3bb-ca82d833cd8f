package d;

import a.b.d;
import a.b.s0;
import a.u.b;
import a.u;
import android.content.ComponentName;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.view.View;
import android.widget.CompoundButton;
import e.j.f;
import e.j;
import e.z;
import f.l;
import f.v;

public class c2 extends j {
    private b M;

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F060202);  // string:tiles "Quick settings tiles"
    }

    @Override  // e.j
    protected int B0() {
        b u$b0 = u.d();
        this.M = u$b0;
        return u$b0.i();
    }

    private void C1(String s) {
        s0.D(this.f(), 22, s);
    }

    private CharSequence D1(String s) {
        PackageManager packageManager0 = this.f().getPackageManager();
        try {
            return packageManager0.getServiceInfo(new ComponentName(l.j, s), 0x200).loadLabel(packageManager0);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return s;
        }
    }

    private boolean E1(String s) {
        try {
            int v = this.f().getPackageManager().getComponentEnabledSetting(new ComponentName(l.j, s));
            return v == 0 || v == 1;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return true;
        }
    }

    // 检测为 Lambda 实现
    private void F1(String s, CompoundButton compoundButton0, boolean z) [...]

    private void G1(String s) {
        s0.D(this.f(), 23, s);
    }

    private void H1(String s, boolean z) {
        try {
            if(!z) {
                this.M.p(s);
                this.G1(s);
            }
            this.f().getPackageManager().setComponentEnabledSetting(new ComponentName(l.j, s), (z ? 1 : 2), 1);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    @Override  // e.j0$c
    protected void J(Bundle bundle0, int v) {
        d b$d0;
        b u$b0;
        switch(v) {
            case 1: {
                a.b b0 = (a.b)bundle0.getParcelable("result");
                if(b0 == null) {
                    return;
                }
                try {
                    int v1 = this.E0();
                    if(b0 instanceof d) {
                        u$b0 = this.M;
                        b$d0 = (d)b0;
                    }
                    else {
                        u$b0 = this.M;
                        b$d0 = new d(b0);
                    }
                    u$b0.n(v1, b$d0);
                    f j$f0 = (f)this.L0(v1);
                    j$f0.setSubText(b0.n(this.M0()));
                    j$f0.setImageDrawable(this.D0(b0));
                }
                catch(Throwable throwable0) {
                    this.g0(throwable0);
                }
                return;
            }
            case 2: {
                int v2 = bundle0.getInt("result", -1);
                if(v2 >= 0) {
                    int v3 = this.E0();
                    switch(v2) {
                        case 0: {
                            this.k1(v3);
                            return;
                        }
                        case 1: {
                            ((f)this.L0(v3)).setChecked(true);
                            this.C1(this.M.h(v3));
                            return;
                        }
                        case 2: {
                            this.G1(this.M.h(v3));
                            return;
                        label_32:
                            if(v2 == 3) {
                                ((f)this.L0(v3)).h();
                                return;
                            }
                            break;
                        }
                        default: {
                            goto label_32;
                        }
                    }
                }
            }
        }
    }

    @Override  // e.j
    protected View h1(int v) {
        d b$d0 = this.M.a(v);
        String s = this.M.h(v);
        boolean z = this.E1(s);
        View view0 = new f(this, this.D1(s), b$d0.n(this.M0()), this.D0(b$d0), z);
        ((e.j.j)view0).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> this.H1(s, z));
        return view0;
    }

    @Override  // e.j
    protected void k1(int v) {
        this.P(new t().K1(this.M.a(v), 6), 1);
    }

    @Override  // e.j
    protected boolean l1(int v) {
        f j$f0 = (f)this.L0(v);
        CharSequence[] arr_charSequence = {this.u(0x7F0600F0), this.u(0x7F06006A), this.u(0x7F060181), this.u((j$f0.g() ? 0x7F0600D4 : 0x7F0600F5))};  // string:edit_tile "Edit tile"
        this.N(new z().u(arr_charSequence), 2);
        return true;
    }
}


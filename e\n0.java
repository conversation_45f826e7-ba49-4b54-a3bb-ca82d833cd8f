package e;

import android.app.Activity;
import android.app.Dialog;
import android.app.TimePickerDialog.OnTimeSetListener;
import android.app.TimePickerDialog;
import android.content.Context;
import android.os.Bundle;
import android.widget.TimePicker;

public class n0 extends b {
    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        class a extends TimePickerDialog {
            final n0 A;
            final Bundle z;

            a(Context context0, TimePickerDialog.OnTimeSetListener timePickerDialog$OnTimeSetListener0, int v, int v1, boolean z, Bundle bundle0) {
                this.z = bundle0;
                super(context0, timePickerDialog$OnTimeSetListener0, v, v1, z);
            }

            @Override  // android.app.TimePickerDialog
            public void onTimeChanged(TimePicker timePicker0, int v, int v1) {
                this.z.putInt("hour", v);
                this.z.putInt("minute", v1);
            }
        }

        Activity activity0 = this.getActivity();
        Bundle bundle1 = this.e();
        Dialog dialog0 = new a(this, activity0, (TimePicker timePicker0, int v, int v1) -> {
            bundle1.putInt("hour", v);
            bundle1.putInt("minute", v1);
            this.o(bundle1);
        }, bundle1.getInt("hour", 0), bundle1.getInt("minute", 0), true, bundle1);
        ((TimePickerDialog)dialog0).setTitle(bundle1.getCharSequence("title"));
        return dialog0;
    }

    // 检测为 Lambda 实现
    private void v(Bundle bundle0, TimePicker timePicker0, int v, int v1) [...]

    public n0 w(CharSequence charSequence0, int v, int v1) {
        Bundle bundle0 = this.e();
        bundle0.putCharSequence("title", charSequence0);
        bundle0.putInt("hour", v);
        bundle0.putInt("minute", v1);
        return this;
    }
}


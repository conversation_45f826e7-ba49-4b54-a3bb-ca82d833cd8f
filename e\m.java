package e;

import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface;
import android.os.Bundle;

public class m extends b {
    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        class a implements DialogInterface.OnClickListener {
            final m z;

            @Override  // android.content.DialogInterface$OnClickListener
            public void onClick(DialogInterface dialogInterface0, int v) {
                boolean z = true;
                Bundle bundle0 = new Bundle(1);
                if(v != -1) {
                    z = false;
                }
                bundle0.putBoolean("result", z);
                m.this.o(bundle0);
            }
        }

        a m$a0 = new a(this);
        Bundle bundle1 = this.e();
        CharSequence charSequence0 = bundle1.getCharSequence("title");
        if(charSequence0 == null) {
            charSequence0 = this.k(0x1040014);
        }
        CharSequence charSequence1 = bundle1.getCharSequence("message");
        return new AlertDialog.Builder(this.getActivity()).setTitle(charSequence0).setIcon(0x1080027).setMessage(charSequence1).setPositiveButton(0x1040013, m$a0).setNegativeButton(0x1040000, m$a0).create();
    }

    public m u(CharSequence charSequence0) {
        return this.v(null, charSequence0);
    }

    public m v(CharSequence charSequence0, CharSequence charSequence1) {
        Bundle bundle0 = this.e();
        bundle0.putCharSequence("title", charSequence0);
        bundle0.putCharSequence("message", charSequence1);
        return this;
    }
}


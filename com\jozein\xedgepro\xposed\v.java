package com.jozein.xedgepro.xposed;

import android.hardware.display.DisplayManager.DisplayListener;
import android.hardware.display.DisplayManager;
import android.os.Build.VERSION;
import android.view.Display;
import java.util.ArrayList;
import java.util.List;

class v {
    private final w1 a;
    private final List b;
    private DisplayManager c;
    private DisplayManager.DisplayListener d;
    private int[] e;
    static final boolean f;
    private static final int[] g;

    static {
        v.f = Build.VERSION.SDK_INT >= 29;
        v.g = new int[]{0};
    }

    v(w1 w10) {
        this.b = new ArrayList();
        this.c = null;
        this.d = null;
        this.e = v.g;
        this.a = w10;
    }

    int b() {
        return this.e.length;
    }

    DisplayManager c() {
        if(this.c == null) {
            this.c = (DisplayManager)this.a.T1().getSystemService("display");
        }
        return this.c;
    }

    boolean d() {
        return this.b() > 1;
    }

    void e() {
        class a implements DisplayManager.DisplayListener {
            final v a;

            @Override  // android.hardware.display.DisplayManager$DisplayListener
            public void onDisplayAdded(int v) {
                v.this.f();
            }

            @Override  // android.hardware.display.DisplayManager$DisplayListener
            public void onDisplayChanged(int v) {
                v.this.f();
            }

            @Override  // android.hardware.display.DisplayManager$DisplayListener
            public void onDisplayRemoved(int v) {
                v.this.f();
            }
        }

        if(v.f && this.d == null) {
            a v$a0 = new a(this);
            try {
                this.c().registerDisplayListener(v$a0, this.a.U1());
                this.d = v$a0;
            }
            catch(Throwable throwable0) {
                f.v.d(throwable0);
            }
        }
    }

    private void f() {
        int[] arr_v;
        try {
            Display[] arr_display = this.c().getDisplays();
            List list0 = this.b;
            for(int v1 = 0; v1 < arr_display.length; ++v1) {
                Display display0 = arr_display[v1];
                if(display0.getDisplayId() == 0 || display0.isValid()) {
                    list0.add(display0.getDisplayId());
                }
            }
            int v2 = list0.size();
            if(v2 != 0 && (v2 != 1 || ((int)(((Integer)list0.get(0)))) != 0)) {
                arr_v = new int[v2];
                for(int v = 0; v < v2; ++v) {
                    arr_v[v] = (int)(((Integer)list0.get(v)));
                }
            }
            else {
                arr_v = v.g;
            }
            list0.clear();
            this.e = arr_v;
        }
        catch(Throwable throwable0) {
            f.v.d(throwable0);
        }
    }
}


package c;

import a.b.c3;
import android.app.Activity;
import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter.LengthFilter;
import android.text.InputFilter;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import e.j0.b;
import e.y;
import g.p;
import g.x;

public class r extends b {
    private EditText C;

    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        CharSequence charSequence0 = this.e().getCharSequence("content", "");
        Activity activity0 = this.getActivity();
        LinearLayout linearLayout0 = new LinearLayout(activity0);
        linearLayout0.setOrientation(1);
        x x0 = this.g();
        linearLayout0.setPadding(x0.f, x0.f, x0.f, x0.f);
        p p0 = this.m(this.f(0x7F040083));  // drawable:ic_play
        p0.setOnClickListener((View view0) -> {
            Editable editable0 = this.C.getText();
            a.b.t(view0.getContext(), new c3(editable0.toString()));
        });
        linearLayout0.addView(p0, x0.b, x0.b);
        EditText editText0 = new EditText(activity0);
        this.C = editText0;
        this.r(editText0);
        this.C.setInputType(0x20001);
        this.C.setHint(0x7F0601C1);  // string:speech_hint "Text to speech"
        this.C.setFilters(new InputFilter[]{new InputFilter.LengthFilter(0x1000)});
        this.C.setText(charSequence0);
        this.C.setSelection(charSequence0.length());
        linearLayout0.addView(this.C);
        TextView textView0 = new TextView(activity0);
        textView0.setText(0x7F0601C0);  // string:speech_description "%t: time. %w: day of week. %d: date. %b: battery level. 
                                        // Empty triggers stop speech. Note: Maybe you should install a engine for local language(e.g. 
                                        // Google text to speech engine)."
        linearLayout0.addView(textView0);
        y.L(x0, textView0, this.C);
        return new AlertDialog.Builder(activity0).setTitle(0x7F06005A).setView(linearLayout0).setPositiveButton(0x104000A, (DialogInterface dialogInterface0, int v) -> this.q("result", new c3(this.C.getText().toString()))).setNegativeButton(0x1040000, b.B).create();
    }

    @Override  // e.j0$b
    public void onSaveInstanceState(Bundle bundle0) {
        super.onSaveInstanceState(bundle0);
        this.e().putCharSequence("content", this.C.getText());
    }

    // 检测为 Lambda 实现
    private void w(View view0) [...]

    // 检测为 Lambda 实现
    private void x(DialogInterface dialogInterface0, int v) [...]

    public r y(CharSequence charSequence0) {
        this.e().putCharSequence("content", charSequence0);
        return this;
    }
}


package d;

import android.os.Bundle;
import f.c0.d;

public class u0 extends a {
    @Override  // e.j0$c
    protected void B() {
        super.B();
        CharSequence charSequence0 = this.h().getCharSequence("title");
        if(charSequence0 != null) {
            this.d0(charSequence0);
            return;
        }
        this.c0(0x7F0601A4);  // string:select_app "Select app"
    }

    public u0 C1(CharSequence charSequence0) {
        this.h().putCharSequence("title", charSequence0);
        return this;
    }

    @Override  // e.j
    protected void k1(int v) {
        Bundle bundle0 = new Bundle(1);
        bundle0.putString("result", ((d)this.M.get(v)).i());
        this.U(bundle0);
        this.L();
    }
}


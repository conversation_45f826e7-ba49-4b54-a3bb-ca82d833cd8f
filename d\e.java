package d;

import a.b;
import android.os.Bundle;
import com.jozein.xedgepro.ui.ActivityPerformAction;
import e.j;
import e.q;
import e.z;
import f.l0;
import f.v;

public abstract class e extends j {
    protected abstract void B1(Bundle arg1, int arg2);

    protected void C1(b b0) {
        this.W("result", b0);
        this.L();
    }

    protected void D1(b b0) {
        this.h().putParcelable("action", b0);
        CharSequence[] arr_charSequence = b0.z == 0 || b0.z == 1 || (b0.z == 51 || b0.z == 68 || b0.z == 106 || b0.z == 107) ? new CharSequence[]{this.u(0x7F0601A1)} : new CharSequence[]{this.u(0x7F0601A1), this.u(0x7F06016F), this.u(0x7F06017D), this.u(0x7F0600FB)};  // string:select "Select"
        z z0 = new z();
        z0.u(arr_charSequence);
        this.N(z0, 0xFFFF);
    }

    @Override  // e.j0$c
    protected final void J(Bundle bundle0, int v) {
        String s;
        if(bundle0 == null) {
            return;
        }
        if(v == 0xFFFF) {
            int v1 = bundle0.getInt("result", -1);
            b b0 = (b)this.h().getParcelable("action");
            if(b0 == null) {
                return;
            }
            switch(v1) {
                case 0: {
                    this.C1(b0);
                    return;
                }
                case 1: {
                    ActivityPerformAction.a(this.f(), b0);
                    return;
                }
                case 2: {
                    l0.i(this.f(), b0);
                    return;
                }
                case 3: {
                    try {
                        s = null;
                        a.z z0 = this.g().h();
                        if(z0 == null || !z0.o(0x20)) {
                            s = "#" + this.u(0x7F060170);  // string:perform_by_broadcast_not_allowed "Perform by broadcast not allowed!"
                        }
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                    this.N(new q().w(b0.n(this.f()), b.g(b0), s), 0x10000);
                    return;
                }
                default: {
                    return;
                }
            }
        }
        this.B1(bundle0, v);
    }
}


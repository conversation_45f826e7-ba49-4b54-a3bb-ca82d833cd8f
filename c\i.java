package c;

import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.CompoundButton;

public final class i implements CompoundButton.OnCheckedChangeListener {
    public final j a;

    public i(j j0) {
        this.a = j0;
    }

    @Override  // android.widget.CompoundButton$OnCheckedChangeListener
    public final void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
        this.a.C(compoundButton0, z);
    }
}


package com.jozein.xedgepro.xposed;

import android.view.View.OnClickListener;
import android.view.View;
import android.widget.FrameLayout;

public final class l implements View.OnClickListener {
    public final FrameLayout A;
    public final FrameLayout B;
    public final f C;
    public final r z;

    public l(r r0, FrameLayout frameLayout0, FrameLayout frameLayout1, f r$f0) {
        this.z = r0;
        this.A = frameLayout0;
        this.B = frameLayout1;
        this.C = r$f0;
    }

    @Override  // android.view.View$OnClickListener
    public final void onClick(View view0) {
        this.z.c0(this.A, this.B, this.C, view0);
    }
}


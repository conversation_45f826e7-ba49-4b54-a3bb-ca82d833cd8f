package d;

import a.b.h3;
import a.b;
import android.content.Context;
import android.os.Bundle;
import android.view.View.OnClickListener;
import android.view.View;
import e.j.g;
import e.m;

public class z1 extends c implements a {
    private b[] M;
    private CharSequence[] N;

    @Override  // e.j0$c
    protected void B() {
        class d.z1.a implements View.OnClickListener {
            final z1 z;

            @Override  // android.view.View$OnClickListener
            public void onClick(View view0) {
                if(z1.this.M != null) {
                    h3 b$h30 = new h3(z1.this.M);
                    z1.this.W("result", b$h30);
                }
                z1.this.L();
            }
        }

        super.B();
        this.c0(0x7F06005D);  // string:action_sub_gestures "Sub gestures"
        this.S(0x7F040071, new d.z1.a(this));  // drawable:ic_ok
    }

    @Override  // e.j
    protected int B0() {
        b[] arr_b = (b[])this.h().getParcelableArray("actions");
        this.M = arr_b;
        if(arr_b == null) {
            this.M = h3.K;
        }
        this.N = new CharSequence[h3.J.length];
        for(int v = 0; v < h3.J.length; ++v) {
            CharSequence[] arr_charSequence = this.N;
            arr_charSequence[v] = this.u(h3.J[v]);
        }
        return h3.J.length;
    }

    @Override  // d.c
    protected void C1(Bundle bundle0, int v) {
        switch(v) {
            case 1: {
                Context context0 = this.M0();
                b b0 = (b)bundle0.getParcelable("result");
                if(b0 != null) {
                    int v1 = this.E0();
                    this.M[v1] = b0;
                    this.h().putBoolean("changed", true);
                    g j$g0 = (g)this.L0(v1);
                    j$g0.setSubText(b0.n(context0));
                    j$g0.setImageDrawable(this.D0(b0));
                }
                return;
            }
            case 2: {
                if(bundle0.getBoolean("result", false)) {
                    this.W("result", new h3(this.M));
                }
                this.L();
            }
        }
    }

    public z1 L1(b[] arr_b) {
        b[] arr_b1;
        if(arr_b == null) {
            arr_b1 = null;
        }
        else {
            arr_b1 = new b[arr_b.length];
            System.arraycopy(arr_b, 0, arr_b1, 0, arr_b.length);
        }
        this.h().putParcelableArray("actions", arr_b1);
        return this;
    }

    @Override  // e.j
    protected View h1(int v) {
        b b0 = this.M[v];
        Context context0 = this.M0();
        return new g(this, this.N[v], b0.n(context0), this.D0(b0));
    }

    @Override  // e.j
    protected void k1(int v) {
        this.P(new d().I1(6, this.o(), this.N[v]), 1);
    }

    @Override  // e.j
    protected boolean l1(int v) {
        this.F1(this.M[v], 1, 1);
        return true;
    }

    @Override  // e.j
    public void onSaveInstanceState(Bundle bundle0) {
        this.h().putParcelableArray("actions", this.M);
        super.onSaveInstanceState(bundle0);
    }

    @Override  // e.j0$c
    protected void y() {
        if(this.h().getBoolean("changed", false)) {
            this.N(new m().u(this.u(0x7F06008F)), 2);  // string:check_save_message "Do you want to save changes?"
            return;
        }
        super.y();
    }
}


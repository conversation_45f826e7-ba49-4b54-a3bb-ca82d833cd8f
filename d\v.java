package d;

import a.b.s0;
import android.os.Bundle;
import android.view.View;
import e.j.g;
import e.j;
import e.z;

public class v extends j {
    static final String[] M;

    static {
        v.M = new String[]{"FX_KEY_CLICK", "FX_FOCUS_NAVIGATION_UP", "FX_FOCUS_NAVIGATION_DOWN", "FX_FOCUS_NAVIGATION_LEFT", "FX_FOCUS_NAVIGATION_RIGHT", "FX_KEYPRESS_STANDARD", "FX_KEYPRESS_SPACEBAR", "FX_KEYPRESS_DELETE", "FX_KEYPRESS_RETURN"};
    }

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F0600F2);  // string:effect_sound "Effect sound"
    }

    @Override  // e.j
    protected int B0() {
        return v.M.length;
    }

    // 检测为 Lambda 实现
    private void C1(int v, View view0) [...]

    @Override  // e.j0$c
    protected void J(Bundle bundle0, int v) {
        if(bundle0 != null && bundle0.getInt("result", -1) == 0) {
            this.V("result", this.E0());
            this.L();
        }
    }

    @Override  // e.j
    protected View h1(int v) {
        View view0 = new g(this, v.M[v], null, this.C0(0x7F0400A4));  // drawable:ic_select
        u u0 = (View view0) -> {
            this.V("result", v);
            this.L();
        };
        view0.E.setOnClickListener(u0);
        return view0;
    }

    @Override  // e.j
    protected void k1(int v) {
        s0.C(this.f(), 18, v);
    }

    @Override  // e.j
    protected boolean l1(int v) {
        this.M(new z().u(new CharSequence[]{this.u(0x7F0601A1)}));  // string:select "Select"
        return true;
    }
}


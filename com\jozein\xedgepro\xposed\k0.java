package com.jozein.xedgepro.xposed;

import android.app.ActivityManager.RunningTaskInfo;
import java.util.Comparator;

public final class k0 implements Comparator {
    public static final k0 z;

    static {
        k0.z = new k0();
    }

    @Override
    public final int compare(Object object0, Object object1) {
        return l0.L0(((ActivityManager.RunningTaskInfo)object0), ((ActivityManager.RunningTaskInfo)object1));
    }
}


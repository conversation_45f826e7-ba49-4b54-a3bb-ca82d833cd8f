package e;

import android.app.Activity;
import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter.AllCaps;
import android.text.InputFilter.LengthFilter;
import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout.LayoutParams;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.SeekBar.OnSeekBarChangeListener;
import android.widget.SeekBar;
import f.v;
import g.x;
import java.util.Locale;

public class o extends b {
    static class d extends Drawable {
        private int a;
        private final Paint b;

        public d(int v) {
            Paint paint0 = new Paint(1);
            this.b = paint0;
            this.a = v;
            paint0.setColor(v);
        }

        @Override  // android.graphics.drawable.Drawable
        public void draw(Canvas canvas0) {
            Paint paint0;
            float f3;
            float f2;
            float f1;
            float f;
            Rect rect0 = this.getBounds();
            int v = Math.min(rect0.width(), rect0.height());
            int v1 = rect0.centerX();
            int v2 = rect0.centerY();
            if(this.a == 0) {
                this.b.setColor(0xFFCCCCCC);
                float f4 = (float)(v1 - v / 2);
                float f5 = (float)(v2 - v / 2);
                canvas0.drawRect(f4, f5, ((float)v1), ((float)v2), this.b);
                float f6 = (float)(v1 + v / 2);
                float f7 = (float)(v2 + v / 2);
                canvas0.drawRect(((float)v1), ((float)v2), f6, f7, this.b);
                this.b.setColor(0xFF888888);
                canvas0.drawRect(((float)v1), f5, f6, ((float)v2), this.b);
                paint0 = this.b;
                f = f4;
                f1 = (float)v2;
                f2 = (float)v1;
                f3 = f7;
            }
            else {
                f = (float)(v1 - v / 2);
                f1 = (float)(v2 - v / 2);
                f2 = (float)(v1 + v / 2);
                f3 = (float)(v2 + v / 2);
                paint0 = this.b;
            }
            canvas0.drawRect(f, f1, f2, f3, paint0);
        }

        @Override  // android.graphics.drawable.Drawable
        public int getOpacity() {
            return -3;
        }

        @Override  // android.graphics.drawable.Drawable
        public void setAlpha(int v) {
        }

        @Override  // android.graphics.drawable.Drawable
        public void setColorFilter(ColorFilter colorFilter0) {
        }
    }

    private int C;
    private int D;
    private int E;
    private ImageView F;
    private EditText G;
    private ColorDrawable H;
    private final SeekBar[] I;
    private boolean J;

    public o() {
        this.I = new SeekBar[4];
        this.J = true;
    }

    private View D(Context context0, int v, int v1) {
        class c implements SeekBar.OnSeekBarChangeListener {
            final o A;
            final int z;

            c(int v) {
                this.z = v;
                super();
            }

            @Override  // android.widget.SeekBar$OnSeekBarChangeListener
            public void onProgressChanged(SeekBar seekBar0, int v, boolean z) {
                if(!z) {
                    return;
                }
                o.this.C = v << this.z | o.this.C & ~(0xFF << this.z);
                o.this.G.setText(o.this.E());
            }

            @Override  // android.widget.SeekBar$OnSeekBarChangeListener
            public void onStartTrackingTouch(SeekBar seekBar0) {
            }

            @Override  // android.widget.SeekBar$OnSeekBarChangeListener
            public void onStopTrackingTouch(SeekBar seekBar0) {
            }
        }

        View view0 = new LinearLayout(context0);
        ((LinearLayout)view0).setOrientation(0);
        ImageView imageView0 = new ImageView(context0);
        imageView0.setImageDrawable(new d(v));
        imageView0.setPadding(this.D, this.D, 0, this.D);
        ((LinearLayout)view0).addView(imageView0, new LinearLayout.LayoutParams(-1, -1, 6.0f));
        SeekBar seekBar0 = new SeekBar(context0);
        seekBar0.setMax((v1 == 24 ? this.e().getInt("max_alpha", 0xFF) : 0xFF));
        seekBar0.setProgress(0xFF & this.C >>> v1);
        seekBar0.setOnSeekBarChangeListener(new c(this, v1));
        seekBar0.setPadding(this.E, this.D, this.E, this.D);
        ((LinearLayout)view0).addView(seekBar0, new LinearLayout.LayoutParams(-1, -2, 1.0f));
        this.I[v1 / 8] = seekBar0;
        return view0;
    }

    // 去混淆评级： 低(20)
    private String E() {
        return this.J ? String.format(Locale.ENGLISH, "%08X", this.C) : String.format(Locale.ENGLISH, "%06X", ((int)(this.C & 0xFFFFFF)));
    }

    // 检测为 Lambda 实现
    private void F(DialogInterface dialogInterface0, int v) [...]

    public o G(int v) {
        this.e().putInt("color", v);
        return this;
    }

    public o H(int v, int v1) {
        Bundle bundle0 = this.e();
        bundle0.putInt("color", v);
        bundle0.putInt("max_alpha", v1);
        return this;
    }

    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        class a implements InputFilter {
            final o a;

            @Override  // android.text.InputFilter
            public CharSequence filter(CharSequence charSequence0, int v, int v1, Spanned spanned0, int v2, int v3) {
                if(v == v1) {
                    return null;
                }
                while(v < v1) {
                    int v4 = charSequence0.charAt(v);
                    if(v4 >= 65 && v4 <= 70 || v4 >= 97 && v4 <= 102 || v4 >= 0x30 && v4 <= 57) {
                        ++v;
                        continue;
                    }
                    return "";
                }
                return null;
            }
        }


        class e.o.b implements TextWatcher {
            final o z;

            @Override  // android.text.TextWatcher
            public void afterTextChanged(Editable editable0) {
                if(editable0.length() > 0) {
                    try {
                        int v1 = (int)Long.parseLong(editable0.toString(), 16);
                        if(!o.this.J) {
                            v1 |= 0xFF000000;
                        }
                        for(int v = 0; v < 4; ++v) {
                            SeekBar seekBar0 = o.this.I[v];
                            if(seekBar0 != null) {
                                seekBar0.setProgress(v1 >>> v * 8 & 0xFF);
                            }
                        }
                        o.this.C = v1;
                    }
                    catch(Throwable throwable0) {
                        o.this.t(throwable0.getMessage());
                        v.d(throwable0);
                        return;
                    }
                }
                else {
                    for(int v2 = 0; v2 < 4; ++v2) {
                        SeekBar seekBar1 = o.this.I[v2];
                        if(seekBar1 != null) {
                            seekBar1.setProgress(0);
                        }
                    }
                    o.this.C = 0;
                }
                o.this.H.setColor(o.this.C);
                o.this.F.invalidate();
            }

            @Override  // android.text.TextWatcher
            public void beforeTextChanged(CharSequence charSequence0, int v, int v1, int v2) {
            }

            @Override  // android.text.TextWatcher
            public void onTextChanged(CharSequence charSequence0, int v, int v1, int v2) {
            }
        }

        Activity activity0 = this.getActivity();
        this.C = this.e().getInt("color", 0xFF888888);
        x x0 = this.g();
        this.D = x0.f * 2;
        this.E = x0.f * 4;
        this.J = this.e().getInt("max_alpha", 0xFF) > 0;
        LinearLayout linearLayout0 = new LinearLayout(activity0);
        linearLayout0.setOrientation(1);
        EditText editText0 = new EditText(activity0);
        this.G = editText0;
        y.L(x0, null, editText0);
        this.G.setInputType(1);
        InputFilter[] arr_inputFilter = {new InputFilter.LengthFilter((this.J ? 8 : 6)), new InputFilter.AllCaps(), new a(this)};
        this.G.setFilters(arr_inputFilter);
        this.G.setText(this.E());
        this.G.addTextChangedListener(new e.o.b(this));
        linearLayout0.addView(this.G);
        this.F = new ImageView(activity0);
        ColorDrawable colorDrawable0 = new ColorDrawable(this.C);
        this.H = colorDrawable0;
        this.F.setImageDrawable(colorDrawable0);
        this.F.setPadding(this.E, this.D, this.E, 0);
        linearLayout0.addView(this.F, -1, this.D * 4);
        if(this.J) {
            linearLayout0.addView(this.D(activity0, 0, 24));
        }
        linearLayout0.addView(this.D(activity0, 0xFFFF0000, 16));
        linearLayout0.addView(this.D(activity0, 0xFF00FF00, 8));
        linearLayout0.addView(this.D(activity0, 0xFF0000FF, 0));
        ScrollView scrollView0 = new ScrollView(activity0);
        scrollView0.setFillViewport(true);
        scrollView0.addView(linearLayout0);
        return new AlertDialog.Builder(activity0).setView(scrollView0).setPositiveButton(0x104000A, (DialogInterface dialogInterface0, int v) -> {
            Bundle bundle0 = new Bundle(1);
            bundle0.putInt("result", this.C);
            this.o(bundle0);
        }).setNegativeButton(0x1040000, b.B).create();
    }

    @Override  // e.j0$b
    public void onSaveInstanceState(Bundle bundle0) {
        this.e().putInt("color", this.C);
        super.onSaveInstanceState(bundle0);
    }
}


package f;

import java.util.AbstractSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.Map.Entry;
import java.util.Map;
import java.util.RandomAccess;
import java.util.Set;

public class u implements Map, RandomAccess {
    private Object[] A;
    private int B;
    private Object[] z;

    public u() {
        this(16);
    }

    public u(int v) {
        this.z = new Object[v];
        this.A = new Object[v];
        this.B = 0;
    }

    @Override
    public void clear() {
        for(int v = 0; v < this.B; ++v) {
            this.z[v] = null;
            this.A[v] = null;
        }
        this.B = 0;
    }

    @Override
    public boolean containsKey(Object object0) {
        if(object0 != null) {
            for(int v = 0; v < this.B; ++v) {
                if(object0.equals(this.z[v])) {
                    return true;
                }
            }
            return false;
        }
        for(int v1 = 0; v1 < this.B; ++v1) {
            if(this.z[v1] == null) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean containsValue(Object object0) {
        if(object0 != null) {
            for(int v = 0; v < this.B; ++v) {
                if(object0.equals(this.A[v])) {
                    return true;
                }
            }
            return false;
        }
        for(int v1 = 0; v1 < this.B; ++v1) {
            if(this.A[v1] == null) {
                return true;
            }
        }
        return false;
    }

    public int d(Object object0) {
        return p0.b(this.z, object0, this.B);
    }

    public Object e(int v) {
        if(v < 0 || v >= this.B) {
            throw new IndexOutOfBoundsException("Invalid index " + v + ", size is " + this.B);
        }
        return this.z[v];
    }

    @Override
    public Set entrySet() {
        class a extends AbstractSet {
            final u z;

            @Override
            public void clear() {
                u.this.clear();
            }

            @Override
            public Iterator iterator() {
                class f.u.a.a implements Iterator {
                    int a;
                    final a b;

                    f.u.a.a() {
                        this.a = 0;
                    }

                    public Map.Entry a() {
                        class f.u.a.a.a implements Map.Entry {
                            final f.u.a.a A;
                            final int z;

                            f.u.a.a.a() {
                                int v = u$a$a0.a;
                                u$a$a0.a = v + 1;
                                this.z = v;
                            }

                            @Override
                            public Object getKey() {
                                return u.this.e(this.z);
                            }

                            @Override
                            public Object getValue() {
                                return u.this.k(this.z);
                            }

                            @Override
                            public Object setValue(Object object0) {
                                if(this.z >= u.this.B) {
                                    throw new IndexOutOfBoundsException("Invalid index " + this.z + ", size is " + u.this.B);
                                }
                                Object object1 = u.this.A[this.z];
                                u.this.A[this.z] = object0;
                                return object1;
                            }
                        }

                        return new f.u.a.a.a(this);
                    }

                    @Override
                    public boolean hasNext() {
                        return this.a < u.this.B;
                    }

                    @Override
                    public Object next() {
                        return this.a();
                    }

                    @Override
                    public void remove() {
                        u.this.i(this.a);
                    }
                }

                return new f.u.a.a(this);
            }

            @Override
            public int size() {
                return u.this.B;
            }
        }

        return new a(this);
    }

    public ArrayList f() {
        ArrayList arrayList0 = new ArrayList(this.B);
        for(int v = 0; v < this.B; ++v) {
            arrayList0.add(this.z[v]);
        }
        return arrayList0;
    }

    public void g(int v) {
        if(v >= 0 && v + 1 < this.B) {
            Object[] arr_object = this.z;
            Object object0 = arr_object[v];
            arr_object[v] = arr_object[v + 1];
            arr_object[v + 1] = object0;
            Object[] arr_object1 = this.A;
            Object object1 = arr_object1[v];
            arr_object1[v] = arr_object1[v + 1];
            arr_object1[v + 1] = object1;
            return;
        }
        v.c(("Invalid move up map item: " + v + ", size: " + this.B));
    }

    @Override
    public Object get(Object object0) {
        int v = p0.b(this.z, object0, this.B);
        return v == -1 ? null : this.A[v];
    }

    public void h(int v) {
        if(v > 0 && v < this.B) {
            Object[] arr_object = this.z;
            Object object0 = arr_object[v];
            arr_object[v] = arr_object[v - 1];
            arr_object[v - 1] = object0;
            Object[] arr_object1 = this.A;
            Object object1 = arr_object1[v];
            arr_object1[v] = arr_object1[v - 1];
            arr_object1[v - 1] = object1;
            return;
        }
        v.c(("Invalid move up map item: " + v + ", size: " + this.B));
    }

    public Object i(int v) {
        int v1 = this.B;
        if(v >= v1) {
            throw new IndexOutOfBoundsException("Invalid index " + v + ", size is " + this.B);
        }
        Object object0 = this.A[v];
        this.B = v1 - 1;
        while(v < this.B) {
            this.z[v] = this.z[v + 1];
            this.A[v] = this.A[v + 1];
            ++v;
        }
        return object0;
    }

    @Override
    public boolean isEmpty() {
        return this.B == 0;
    }

    public Object j(int v, Object object0) {
        if(v < 0 || v >= this.B) {
            throw new IndexOutOfBoundsException("Invalid index " + v + ", size is " + this.B);
        }
        Object[] arr_object = this.A;
        Object object1 = arr_object[v];
        arr_object[v] = object0;
        return object1;
    }

    public Object k(int v) {
        if(v < 0 || v >= this.B) {
            throw new IndexOutOfBoundsException("Invalid index " + v + ", size is " + this.B);
        }
        return this.A[v];
    }

    @Override
    public Set keySet() {
        class b extends AbstractSet {
            final u z;

            @Override
            public void clear() {
                u.this.clear();
            }

            @Override
            public Iterator iterator() {
                class f.u.b.a implements Iterator {
                    int a;
                    final b b;

                    f.u.b.a() {
                        this.a = 0;
                    }

                    @Override
                    public boolean hasNext() {
                        return this.a < u.this.B;
                    }

                    @Override
                    public Object next() {
                        Object[] arr_object = u.this.z;
                        int v = this.a;
                        this.a = v + 1;
                        return arr_object[v];
                    }

                    @Override
                    public void remove() {
                        u.this.i(this.a);
                    }
                }

                return new f.u.b.a(this);
            }

            @Override
            public int size() {
                return u.this.B;
            }
        }

        return new b(this);
    }

    public ArrayList l() {
        ArrayList arrayList0 = new ArrayList(this.B);
        for(int v = 0; v < this.B; ++v) {
            arrayList0.add(this.A[v]);
        }
        return arrayList0;
    }

    @Override
    public Object put(Object object0, Object object1) {
        int v = this.d(object0);
        if(v != -1) {
            Object[] arr_object = this.A;
            Object object2 = arr_object[v];
            arr_object[v] = object1;
            return object2;
        }
        Object[] arr_object1 = this.z;
        int v1 = this.B;
        if(arr_object1.length == v1) {
            this.z = new Object[v1 * 2];
            Object[] arr_object2 = this.A;
            this.A = new Object[v1 * 2];
            for(int v2 = 0; v2 < this.B; ++v2) {
                this.z[v2] = arr_object1[v2];
                this.A[v2] = arr_object2[v2];
            }
        }
        int v3 = this.B;
        this.z[v3] = object0;
        this.A[v3] = object1;
        this.B = v3 + 1;
        return null;
    }

    @Override
    public void putAll(Map map0) {
        int v = this.B + map0.size();
        Object[] arr_object = this.z;
        if(arr_object.length < v) {
            this.z = new Object[v];
            Object[] arr_object1 = this.A;
            this.A = new Object[v];
            for(int v1 = 0; v1 < this.B; ++v1) {
                this.z[v1] = arr_object[v1];
                this.A[v1] = arr_object1[v1];
            }
        }
        for(Object object0: map0.entrySet()) {
            this.put(((Map.Entry)object0).getKey(), ((Map.Entry)object0).getValue());
        }
    }

    @Override
    public Object remove(Object object0) {
        int v = p0.b(this.z, object0, this.B);
        return v == -1 ? null : this.i(v);
    }

    @Override
    public int size() {
        return this.B;
    }

    @Override
    public String toString() {
        StringBuilder stringBuilder0 = new StringBuilder(0x400);
        stringBuilder0.append("Map{ ");
        for(int v = 0; v < this.B; ++v) {
            stringBuilder0.append('[');
            stringBuilder0.append(this.z[v]);
            stringBuilder0.append(", ");
            stringBuilder0.append(this.A[v]);
            stringBuilder0.append("] ");
        }
        stringBuilder0.append('}');
        return stringBuilder0.toString();
    }

    @Override
    public Collection values() {
        return this.l();
    }
}


package g;

import android.content.Context;
import android.os.Build.VERSION;
import android.view.View;
import android.view.WindowManager.LayoutParams;
import android.view.WindowManager;
import f.l;

public class m implements l {
    private final WindowManager.LayoutParams A;
    private View B;
    private final WindowManager z;

    public m(Context context0) {
        int v1;
        this.B = null;
        this.z = (WindowManager)context0.getSystemService("window");
        int v = Build.VERSION.SDK_INT;
        if(v >= 26) {
            v1 = 0x7F6;
        }
        else {
            v1 = v >= 24 ? 2002 : 2005;
        }
        this.A = new WindowManager.LayoutParams(v1, 0x1080508, 1);
    }

    public void a(int v, int v1) {
        View view0 = this.B;
        if(view0 != null) {
            this.A.x = v;
            this.A.y = v1;
            this.z.updateViewLayout(view0, this.A);
        }
    }

    public void b() {
        View view0 = this.B;
        if(view0 != null) {
            this.z.removeView(view0);
            this.B = null;
        }
    }

    public void c(int v, int v1) {
        View view0 = this.B;
        if(view0 != null) {
            this.A.width = v;
            this.A.height = v1;
            this.z.updateViewLayout(view0, this.A);
        }
    }

    public void d(View view0, int v, int v1, int v2, int v3) {
        this.b();
        this.A.width = v;
        this.A.height = v1;
        this.A.x = v2;
        this.A.y = v3;
        this.z.addView(view0, this.A);
        this.B = view0;
    }
}


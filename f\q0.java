package f;

import android.content.Context;
import android.content.res.Resources;
import android.media.AudioManager;

public class q0 implements l {
    private static final int[] A;
    private static int[] B;
    private static String[] z;

    static {
        q0.z = new String[]{"volume_call", 0, "volume_ringtone", "volume_music", "volume_alarm", "volume_notification", "volume_bluetooth_call", 0, 0, 0};
        int[] arr_v = {0x7F060230, 0x7F06022D, 0x7F06022C, 0x7F06022A, 0x7F060226, 0x7F06022B, 0x7F060227, 0x7F06022E, 0x7F060229, 0x7F06022F};  // string:volume_stream_voice_call "Voice call volume"
        q0.A = arr_v;
        q0.B = arr_v;
    }

    public static int a(Context context0, int v) {
        return ((AudioManager)context0.getSystemService("audio")).getStreamMaxVolume(v);
    }

    public static int b(Context context0, int v) {
        return ((AudioManager)context0.getSystemService("audio")).getStreamVolume(v);
    }

    public static int[] c() {
        return q0.B;
    }

    public static CharSequence d(Context context0, int v) {
        if(v >= 0) {
            int[] arr_v = q0.B;
            if(v < arr_v.length) {
                int v1 = arr_v[v];
                Resources resources0 = context0.getResources();
                String[] arr_s = q0.z;
                if(arr_s != null) {
                    q0.z = null;
                    for(int v2 = 0; v2 < arr_s.length; ++v2) {
                        try {
                            if(arr_s[v2] != null) {
                                int v3 = resources0.getIdentifier(arr_s[v2], "string", "android");
                                if(v3 > 0) {
                                    q0.A[v2] = v3;
                                }
                            }
                        }
                        catch(Throwable throwable0) {
                            v.d(throwable0);
                            if(true) {
                                break;
                            }
                        }
                    }
                }
                if(v1 != v) {
                    int[] arr_v1 = q0.A;
                    if(v1 < arr_v1.length) {
                        return context0.getString(arr_v1[v]) + '(' + context0.getString(arr_v1[v1]) + ')';
                    }
                }
                return context0.getText(q0.A[v]);
            }
        }
        return context0.getText(0x7F060228);  // string:volume_stream_default "Default volume"
    }

    public static void e(int[] arr_v) {
        if(arr_v != null && arr_v.length >= q0.A.length) {
            q0.B = arr_v;
            return;
        }
        v.c(("Invalid alias: " + (arr_v == null ? "null" : "length " + arr_v.length)));
    }
}


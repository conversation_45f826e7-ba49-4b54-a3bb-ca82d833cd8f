package com.jozein.xedgepro.xposed;

import android.app.AppOpsManager;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.net.Uri;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiManager;
import android.os.Build.VERSION;
import android.os.Environment;
import android.os.PowerManager;
import android.util.Xml;
import de.robv.android.xposed.XposedHelpers;
import f.l;
import f.p0;
import f.v;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import org.xmlpull.v1.XmlPullParser;

class n2 {
    private static final String[] a;

    static {
        n2.a = new String[]{"allowed", "ignored", "denied", "default", "foreground"};
    }

    static String[] a(Context context0) {
        try {
            ArrayList arrayList0 = n2.b();
            return (String[])arrayList0.toArray(new String[arrayList0.size()]);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            try {
                List list0 = ((WifiManager)context0.getSystemService("wifi")).getConfiguredNetworks();
                int v = list0.size();
                String[] arr_s = new String[v];
                for(int v1 = 0; v1 < v; ++v1) {
                    arr_s[v1] = n2.f(((WifiConfiguration)list0.get(v1)).SSID);
                }
                return arr_s;
            }
            catch(Throwable throwable1) {
                v.d(throwable1);
                return null;
            }
        }
    }

    private static ArrayList b() {
        String s5;
        ArrayList arrayList1;
        BufferedReader bufferedReader1;
        InputStreamReader inputStreamReader0;
        FileInputStream fileInputStream1;
        String s;
        int v1;
        ArrayList arrayList0;
        FileInputStream fileInputStream0;
        int v = Build.VERSION.SDK_INT;
        BufferedReader bufferedReader0 = null;
        if(v >= 26) {
            File file0 = new File(Environment.getDataDirectory() + (v <= 29 ? "/misc/wifi/WifiConfigStore.xml" : "/misc/apexdata/com.android.wifi/WifiConfigStore.xml"));
            XmlPullParser xmlPullParser0 = Xml.newPullParser();
            try {
                fileInputStream0 = new FileInputStream(file0);
            }
            catch(Throwable throwable0) {
                goto label_30;
            }
            try {
                xmlPullParser0.setInput(fileInputStream0, "UTF-8");
                arrayList0 = new ArrayList();
                v1 = xmlPullParser0.getEventType();
                s = null;
                while(true) {
                label_13:
                    switch(v1) {
                        case 1: {
                            goto label_15;
                        }
                        case 0: 
                        case 2: {
                            goto label_17;
                        }
                        case 3: {
                            s = null;
                            v1 = xmlPullParser0.next();
                            break;
                        }
                        case 4: {
                            goto label_22;
                        }
                    }
                }
            }
            catch(Throwable throwable0) {
                goto label_29;
            }
            v1 = xmlPullParser0.next();
            goto label_13;
        label_15:
            fileInputStream0.close();
            return arrayList0;
            try {
            label_17:
                if(xmlPullParser0.getAttributeCount() > 0) {
                    s = xmlPullParser0.getAttributeValue(0);
                    v1 = xmlPullParser0.next();
                    goto label_13;
                label_22:
                    if("SSID".equals(s)) {
                        String s1 = xmlPullParser0.getText();
                        if(s1 != null) {
                            arrayList0.add(n2.f(s1.trim()));
                        }
                    }
                }
                v1 = xmlPullParser0.next();
                goto label_13;
            }
            catch(Throwable throwable0) {
            label_29:
                bufferedReader0 = fileInputStream0;
            }
        label_30:
            if(bufferedReader0 != null) {
                ((FileInputStream)bufferedReader0).close();
            }
            throw throwable0;
        }
        File file1 = new File(Environment.getDataDirectory() + "/misc/wifi/wpa_supplicant.conf");
        try {
            fileInputStream1 = new FileInputStream(file1);
        }
        catch(Throwable throwable1) {
            inputStreamReader0 = null;
            fileInputStream1 = null;
            goto label_69;
        }
        try {
            inputStreamReader0 = null;
            inputStreamReader0 = new InputStreamReader(fileInputStream1);
            bufferedReader1 = new BufferedReader(inputStreamReader0);
        }
        catch(Throwable throwable1) {
            goto label_69;
        }
        try {
            arrayList1 = new ArrayList();
            while(true) {
                String s2 = bufferedReader1.readLine();
                if(s2 == null) {
                    goto label_76;
                }
                String s3 = s2.trim();
                if(s3.startsWith("ssid=")) {
                    if(s3.charAt(5) == 34) {
                        s5 = s3.substring(6, s3.length() - 1);
                    }
                    else {
                        StringBuilder stringBuilder0 = new StringBuilder();
                        String s4 = s3.substring(5);
                        int v2 = s4.length();
                        for(int v3 = 0; v3 < v2; ++v3) {
                            if(v3 % 2 == 0) {
                                stringBuilder0.append('%');
                            }
                            stringBuilder0.append(s4.charAt(v3));
                        }
                        s5 = Uri.decode(stringBuilder0.toString());
                    }
                    arrayList1.add(s5);
                }
            }
        }
        catch(Throwable throwable1) {
            bufferedReader0 = bufferedReader1;
        }
    label_69:
        if(bufferedReader0 != null) {
            bufferedReader0.close();
        }
        if(inputStreamReader0 != null) {
            inputStreamReader0.close();
        }
        if(fileInputStream1 != null) {
            fileInputStream1.close();
        }
        throw throwable1;
    label_76:
        bufferedReader1.close();
        inputStreamReader0.close();
        fileInputStream1.close();
        return arrayList1;
    }

    static void c(Context context0, String s, int v, int v1) {
        try {
            AppOpsManager appOpsManager0 = (AppOpsManager)context0.getSystemService("appops");
            ApplicationInfo applicationInfo0 = context0.getPackageManager().getApplicationInfo(s, 0);
            if(((int)(((Integer)XposedHelpers.callMethod(appOpsManager0, "checkOpNoThrow", new Object[]{v, applicationInfo0.uid, s})))) != v1) {
                XposedHelpers.callMethod(appOpsManager0, "setMode", new Object[]{v, applicationInfo0.uid, s, v1});
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    static void d(Context context0, String s, boolean z) {
        int v;
        if(z) {
            v = 0;
        }
        else {
            v = Build.VERSION.SDK_INT < 21 ? 1 : 3;
        }
        n2.c(context0, s, 24, v);
        int v1 = Build.VERSION.SDK_INT;
        if(v1 >= 28) {
            n2.c(context0, s, 70, v);
            n2.c(context0, s, 76, v);
            if(v1 >= 30) {
                if(p0.q(context0, s, "android.permission.QUERY_ALL_PACKAGES")) {
                    n2.c(context0, s, 91, v);
                }
                if(p0.q(context0, s, "android.permission.MANAGE_EXTERNAL_STORAGE")) {
                    n2.c(context0, l.j, 92, v);
                }
            }
        }
        n2.e(context0, s, z);
    }

    static void e(Context context0, String s, boolean z) {
        int v = Build.VERSION.SDK_INT;
        if(v >= 23) {
            try {
                if(((PowerManager)context0.getSystemService("power")).isIgnoringBatteryOptimizations(s) != z) {
                    if(v >= 30) {
                        Object object0 = g3.t("com.android.server.DeviceIdleInternal");
                        if(z) {
                            ArrayList arrayList0 = new ArrayList(1);
                            arrayList0.add(s);
                            XposedHelpers.callMethod(object0, "addPowerSaveWhitelistAppsInternal", new Object[]{arrayList0});
                            return;
                        }
                        XposedHelpers.callMethod(object0, "removePowerSaveWhitelistAppInternal", new Object[]{s});
                        return;
                    }
                    Object object1 = g3.t("com.android.server.DeviceIdleController$LocalService");
                    if(z) {
                        XposedHelpers.callMethod(object1, "addPowerSaveWhitelistAppInternal", new Object[]{s});
                        return;
                    }
                    XposedHelpers.callMethod(object1, "removePowerSaveWhitelistAppInternal", new Object[]{s});
                    return;
                }
                return;
            }
            catch(Throwable throwable0) {
            }
        }
        else {
            return;
        }
        v.d(throwable0);
    }

    static String f(String s) {
        return s == null || s.length() < 2 || s.charAt(0) != 34 ? s : s.substring(1, s.length() - 1);
    }
}


package g;

import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.graphics.ColorFilter;
import android.graphics.PorterDuff.Mode;
import android.graphics.PorterDuffColorFilter;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;

public abstract class h {
    static class a {
    }

    static class b extends h {
        private int a;
        private ColorFilter b;
        private final ColorFilter c;
        private final ColorFilter d;

        private b() {
            this.c = new PorterDuffColorFilter(0xFF888888, PorterDuff.Mode.SRC_ATOP);
            this.d = new PorterDuffColorFilter(0xFF000000, PorterDuff.Mode.SRC_ATOP);
        }

        b(a h$a0) {
        }

        @Override  // g.h
        void c(Drawable drawable0) {
            drawable0.setColorFilter(null);
        }

        @Override  // g.h
        void f(Drawable drawable0, int v) {
            ColorFilter colorFilter1;
            switch(v) {
                case 0xFF000000: {
                    colorFilter1 = this.d;
                    break;
                }
                case 0xFF888888: {
                    colorFilter1 = this.c;
                    break;
                }
                default: {
                    ColorFilter colorFilter0 = this.b;
                    if(colorFilter0 == null || v != this.a) {
                        colorFilter0 = new PorterDuffColorFilter(v, PorterDuff.Mode.SRC_ATOP);
                    }
                    colorFilter1 = colorFilter0;
                }
            }
            drawable0.setColorFilter(colorFilter1);
        }

        @Override  // g.h
        public void h(Drawable drawable0, Resources resources0) {
            if(this.b == null) {
                this.a = resources0.getColor(0x7F030003);  // color:colorPrimary
                this.b = new PorterDuffColorFilter(this.a, PorterDuff.Mode.SRC_ATOP);
            }
            if(drawable0 != null) {
                drawable0.setColorFilter(this.b);
            }
        }
    }

    static class c {
        static final h a;

        static {
            d h$d0 = Build.VERSION.SDK_INT >= 21 ? new d(null) : new b(null);
            c.a = h$d0;
        }
    }

    static class d extends h {
        private int a;
        private ColorStateList b;
        private final ColorStateList c;
        private final ColorStateList d;

        private d() {
            this.c = ColorStateList.valueOf(0xFF888888);
            this.d = ColorStateList.valueOf(0xFF000000);
        }

        d(a h$a0) {
        }

        @Override  // g.h
        void c(Drawable drawable0) {
            drawable0.setTintList(null);
        }

        @Override  // g.h
        void f(Drawable drawable0, int v) {
            ColorStateList colorStateList1;
            switch(v) {
                case 0xFF000000: {
                    colorStateList1 = this.d;
                    break;
                }
                case 0xFF888888: {
                    colorStateList1 = this.c;
                    break;
                }
                default: {
                    ColorStateList colorStateList0 = this.b;
                    colorStateList1 = colorStateList0 == null || v != this.a ? ColorStateList.valueOf(v) : colorStateList0;
                }
            }
            drawable0.setTintList(colorStateList1);
        }

        @Override  // g.h
        public void h(Drawable drawable0, Resources resources0) {
            if(this.b == null) {
                int v = resources0.getColor(0x7F030003);  // color:colorPrimary
                this.a = v;
                this.b = ColorStateList.valueOf(v);
            }
            if(drawable0 != null) {
                drawable0.setTintList(this.b);
            }
        }
    }

    public static h a() {
        return c.a;
    }

    public final void b(Drawable drawable0) {
        if(drawable0 != null) {
            this.c(drawable0);
        }
    }

    abstract void c(Drawable arg1);

    public final void d(Drawable drawable0) {
        if(drawable0 != null) {
            this.e(drawable0, 0xFF000000);
        }
    }

    public final void e(Drawable drawable0, int v) {
        if(drawable0 != null) {
            if(v == 0) {
                this.b(drawable0);
                return;
            }
            this.f(drawable0, v);
        }
    }

    abstract void f(Drawable arg1, int arg2);

    public final void g(Drawable drawable0) {
        if(drawable0 != null) {
            this.e(drawable0, 0xFF888888);
        }
    }

    public abstract void h(Drawable arg1, Resources arg2);
}


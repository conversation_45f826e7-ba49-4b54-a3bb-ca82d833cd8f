package g;

import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;

public class t extends i {
    private final boolean d;
    private final float e;
    private final Drawable f;
    private final Drawable g;

    public t(Drawable drawable0, Drawable drawable1, float f) {
        this(drawable0, drawable1, f, false);
    }

    public t(Drawable drawable0, Drawable drawable1, float f, boolean z) {
        this.d = z;
        this.f = drawable0;
        this.g = drawable1;
        this.e = f;
    }

    @Override  // android.graphics.drawable.Drawable
    public void draw(Canvas canvas0) {
        this.a(canvas0, this.f);
        this.a(canvas0, this.g);
    }

    @Override  // android.graphics.drawable.Drawable
    public int getIntrinsicHeight() {
        Drawable drawable0 = this.g;
        if(drawable0 != null) {
            return drawable0.getIntrinsicHeight();
        }
        return this.f == null ? super.getIntrinsicHeight() : ((int)(((float)this.f.getIntrinsicHeight()) / this.e));
    }

    @Override  // android.graphics.drawable.Drawable
    public int getIntrinsicWidth() {
        Drawable drawable0 = this.g;
        if(drawable0 != null) {
            return drawable0.getIntrinsicWidth();
        }
        return this.f == null ? super.getIntrinsicWidth() : ((int)(((float)this.f.getIntrinsicWidth()) / this.e));
    }

    @Override  // android.graphics.drawable.Drawable
    protected void onBoundsChange(Rect rect0) {
        super.onBoundsChange(rect0);
        Drawable drawable0 = this.f;
        if(drawable0 != null) {
            if(this.d) {
                drawable0.setBounds(rect0.left, rect0.top, ((int)(((float)(rect0.right - rect0.left)) * this.e)) + rect0.left, ((int)(((float)(rect0.bottom - rect0.top)) * this.e)) + rect0.top);
            }
            else {
                int v = rect0.centerX();
                int v1 = rect0.centerY();
                int v2 = (int)(((float)rect0.width()) * this.e / 2.0f);
                int v3 = (int)(((float)rect0.height()) * this.e / 2.0f);
                this.f.setBounds(v - v2, v1 - v3, v + v2, v1 + v3);
            }
        }
        Drawable drawable1 = this.g;
        if(drawable1 != null) {
            drawable1.setBounds(rect0);
        }
    }
}


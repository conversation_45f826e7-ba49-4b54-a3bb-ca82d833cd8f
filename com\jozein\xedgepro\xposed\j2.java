package com.jozein.xedgepro.xposed;

import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import f.f0.d;
import f.v;
import java.lang.reflect.Member;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

class j2 extends g {
    private volatile Object G;

    j2(Class class0) {
        super(class0);
        this.G = null;
    }

    j2(Class class0, Object object0) {
        super(class0);
        this.G = object0;
    }

    j2(String s, ClassLoader classLoader0) {
        super(s, classLoader0);
        this.G = null;
    }

    void A(Object object0) {
        if(object0 != null && this.G == null && this.s(object0)) {
            this.G = object0;
        }
    }

    final Object r(String s, Object[] arr_object) {
        return XposedHelpers.findMethodBestMatch(this.m(), s, arr_object).invoke(this.v(), arr_object);
    }

    boolean s(Object object0) {
        if(object0 != null && this.m().isInstance(object0)) {
            return true;
        }
        v.d(new RuntimeException("Object " + (object0 == null ? "null" : object0.getClass().getName()) + " not instance of " + this.m().getName()));
        return false;
    }

    final int t(String s) {
        return XposedHelpers.getIntField(this.v(), s);
    }

    final Object u(String s) {
        return XposedHelpers.getObjectField(this.v(), s);
    }

    final Object v() {
        if(this.G == null) {
            Object object0 = this.x();
            if(this.s(object0)) {
                this.G = object0;
            }
        }
        return this.G;
    }

    final Object w(Member member0, Object[] arr_object) {
        return XposedBridge.invokeOriginalMethod(member0, this.v(), arr_object);
    }

    protected Object x() {
        throw new NullPointerException("Instance not got for " + this.m().getName());
    }

    final Object y() {
        return this.G;
    }

    final void z(String s, d f0$d0, Object[] arr_object) {
        Method method0 = this.i(s);
        Class class0 = method0.getParameterTypes()[0];
        Object object0 = Proxy.newProxyInstance(class0.getClassLoader(), new Class[]{class0}, f0$d0);
        Object object1 = this.v();
        switch(arr_object.length) {
            case 0: {
                method0.invoke(object1, object0);
                return;
            }
            case 1: {
                method0.invoke(object1, object0, arr_object[0]);
                return;
            }
            case 2: {
                method0.invoke(object1, object0, arr_object[0], arr_object[1]);
                return;
            }
            case 3: {
                method0.invoke(object1, object0, arr_object[0], arr_object[1], arr_object[2]);
                return;
            }
            default: {
                switch(arr_object.length) {
                    case 4: {
                        method0.invoke(object1, object0, arr_object[0], arr_object[1], arr_object[2], arr_object[3]);
                        return;
                    }
                    case 5: {
                        method0.invoke(object1, object0, arr_object[0], arr_object[1], arr_object[2], arr_object[3], arr_object[4]);
                        return;
                    }
                    default: {
                        Object[] arr_object1 = new Object[arr_object.length + 1];
                        arr_object1[0] = object0;
                        System.arraycopy(arr_object, 0, arr_object1, 1, arr_object.length);
                        method0.invoke(object1, arr_object1);
                    }
                }
            }
        }
    }
}


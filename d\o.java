package d;

import a.b.d0;
import a.b;
import a.h;
import android.content.Context;
import android.os.Bundle;
import android.view.View.OnClickListener;
import android.view.View;
import e.j.g;
import e.j.k;

public class o extends c {
    @Override  // e.j0$c
    protected void B() {
        class a implements View.OnClickListener {
            final o z;

            @Override  // android.view.View$OnClickListener
            public void onClick(View view0) {
                o.this.J1();
            }
        }

        super.B();
        this.c0(0x7F0600C4);  // string:conditional "Conditional"
        this.S(0x7F040071, (/* 缺少LAMBDA参数 */) -> {
            Bundle bundle0 = o.this.h();
            h h0 = (h)bundle0.getParcelable("if");
            b b0 = (b)bundle0.getParcelable("then");
            b b1 = (b)bundle0.getParcelable("else");
            if(h0 == null || h0.z == 0 || (b0 == null || b0.z == 0) && (b1 == null || b1.z == 0)) {
                o.this.e0(0x7F0600AA);  // string:condition_invalid "Invalid"
            }
            else {
                o.this.W("result", new d0(h0, b0, b1));
            }
            o.this.L();
        });
    }

    @Override  // e.j
    protected int B0() {
        return 3;
    }

    @Override  // d.c
    protected void C1(Bundle bundle0, int v) {
        String s;
        Bundle bundle1;
        b b0;
        switch(v) {
            case 0: {
                h h0 = (h)bundle0.getParcelable("result");
                if(h0 == null) {
                    h0 = new a.h.o();
                }
                this.h().putParcelable("if", h0);
                ((k)this.L0(0)).setSubText(h0.b(this.M0()));
                return;
            }
            case 1: {
                b0 = (b)bundle0.getParcelable("result");
                if(b0 == null) {
                    b0 = b.e();
                }
                bundle1 = this.h();
                s = "then";
                break;
            }
            default: {
                if(v == 2) {
                    b0 = (b)bundle0.getParcelable("result");
                    if(b0 == null) {
                        b0 = b.e();
                    }
                    bundle1 = this.h();
                    s = "else";
                    break;
                }
                return;
            }
        }
        bundle1.putParcelable(s, b0);
        g j$g0 = (g)this.L0(1);
        j$g0.setSubText(b0.n(j$g0.getContext()));
        j$g0.setImageDrawable(this.D0(b0));
    }

    // 检测为 Lambda 实现
    private void J1() [...]

    public o K1(h h0, b b0, b b1) {
        Bundle bundle0 = this.h();
        bundle0.putParcelable("if", h0);
        bundle0.putParcelable("then", b0);
        bundle0.putParcelable("else", b1);
        return this;
    }

    @Override  // e.j
    protected View h1(int v) {
        Context context0 = this.M0();
        Bundle bundle0 = this.h();
        switch(v) {
            case 0: {
                h h0 = (h)bundle0.getParcelable("if");
                if(h0 == null) {
                    h0 = new a.h.o();
                }
                return new k(this, this.u(0x7F0600C6), h0.b(context0));  // string:conditional_if "if"
            }
            case 1: {
                b b1 = (b)bundle0.getParcelable("then");
                if(b1 == null) {
                    b1 = b.r();
                }
                return new g(this, this.u(0x7F0600C7), b1.n(context0), this.D0(b1));  // string:conditional_then "then"
            }
            default: {
                b b0 = (b)bundle0.getParcelable("else");
                if(b0 == null) {
                    b0 = b.r();
                }
                return new g(this, this.u(0x7F0600C5), b0.n(context0), this.D0(b0));  // string:conditional_else "else"
            }
        }
    }

    @Override  // e.j
    protected void k1(int v) {
        int v1;
        CharSequence charSequence0;
        d d0;
        switch(v) {
            case 0: {
                this.P(new p(), 0);
                return;
            }
            case 1: {
                d0 = new d();
                charSequence0 = this.u(0x7F0600C4);  // string:conditional "Conditional"
                v1 = 0x7F0600C7;  // string:conditional_then "then"
                break;
            }
            case 2: {
                d0 = new d();
                charSequence0 = this.u(0x7F0600C4);  // string:conditional "Conditional"
                v1 = 0x7F0600C5;  // string:conditional_else "else"
                break;
            }
            default: {
                return;
            }
        }
        this.P(d0.I1(5, charSequence0, this.u(v1)), 1);
    }

    @Override  // e.j
    protected boolean l1(int v) {
        if(v == 1 || v == 2) {
            b b0 = (b)this.h().getParcelable((v == 1 ? "then" : "else"));
            if(b0 == null) {
                b0 = b.r();
            }
            this.F1(b0, v, 5);
        }
        return true;
    }
}


package d;

import a.b.k2;
import a.b.q1;
import a.b;
import a.n;
import android.os.Bundle;
import e.j;

public class r1 extends t0 {
    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F060197);  // string:saved_multi_actions "Saved multi-actions"
    }

    @Override  // d.t0
    protected n B1() {
        return k2.A();
    }

    @Override  // d.t0
    protected b[] C1(Bundle bundle0) {
        q1 b$q10 = (q1)bundle0.getParcelable("result");
        return b$q10 == null ? new b[0] : b$q10.I;
    }

    @Override  // d.t0
    protected b D1(int v) {
        return new k2(v);
    }

    @Override  // d.t0
    protected j E1(CharSequence charSequence0, b[] arr_b, boolean z) {
        return new s0().S1(arr_b, z);
    }

    @Override  // d.t0
    protected void F1() {
        this.H1(this.E1(null, new b[0], true));
    }
}


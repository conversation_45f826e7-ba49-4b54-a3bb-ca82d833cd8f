package e;

import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface;

public final class s implements DialogInterface.OnClickListener {
    public final y z;

    public s(y y0) {
        this.z = y0;
    }

    @Override  // android.content.DialogInterface$OnClickListener
    public final void onClick(DialogInterface dialogInterface0, int v) {
        this.z.I(dialogInterface0, v);
    }
}


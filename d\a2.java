package d;

import a.b.i3;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import e.j.k;

public class a2 extends e {
    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F06005E);  // string:action_successive_adjust "Successive adjust"
    }

    @Override  // e.j
    protected int B0() {
        return 15;
    }

    @Override  // d.e
    protected void B1(Bundle bundle0, int v) {
    }

    @Override  // e.j
    protected boolean N0(int v) {
        return true;
    }

    @Override  // e.j
    protected View h1(int v) {
        return new k(this, i3.z(this.M0(), v));
    }

    @Override  // e.j
    protected View i1(int v) {
        Context context0 = this.M0();
        int v1 = this.I0();
        return v == 0 ? new k(this, i3.A(context0, v1, true)) : new k(this, i3.A(context0, v1, false));
    }

    @Override  // e.j
    protected void k1(int v) {
        this.y1(v, 2);
    }

    @Override  // e.j
    protected void o1(int v) {
        this.C1(new i3(this.I0(), v == 0));
    }

    @Override  // e.j
    protected boolean p1(int v) {
        this.D1(new i3(this.I0(), v == 0));
        return true;
    }
}


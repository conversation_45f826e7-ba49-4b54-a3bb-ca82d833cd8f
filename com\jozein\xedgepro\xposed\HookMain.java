package com.jozein.xedgepro.xposed;

import a.b.f0;
import a.b.k2;
import a.b.y0;
import a.f;
import a.k;
import a.p;
import a.s.e;
import a.v;
import android.content.ClipData;
import android.os.Build.VERSION;
import android.os.Build;
import android.os.Process;
import android.view.View.DragShadowBuilder;
import android.view.View;
import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.IXposedHookZygoteInit.StartupParam;
import de.robv.android.xposed.IXposedHookZygoteInit;
import de.robv.android.xposed.SELinuxHelper;
import de.robv.android.xposed.XC_MethodReplacement;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage.LoadPackageParam;
import f.l;
import f.p0;
import f.r;
import java.io.File;

public class HookMain extends p2 implements IXposedHookLoadPackage, IXposedHookZygoteInit {
    private p E;
    private k F;
    private f G;
    private e H;
    private v I;
    private static boolean J = false;

    static {
    }

    private void h(ClassLoader classLoader0) {
        if(!HookMain.J) {
            HookMain.J = true;
            new i2();
            new l2(classLoader0);
        }
    }

    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam xC_LoadPackage$LoadPackageParam0) {
        try {
            if(Build.VERSION.SDK_INT >= 21 && "android".equals(xC_LoadPackage$LoadPackageParam0.packageName)) {
                this.i(xC_LoadPackage$LoadPackageParam0.classLoader);
                return;
            }
            if(l.j.equals(xC_LoadPackage$LoadPackageParam0.packageName)) {
                new z0(xC_LoadPackage$LoadPackageParam0.classLoader);
            }
        }
        catch(Throwable throwable0) {
            p2.g(throwable0);
        }
    }

    private void i(ClassLoader classLoader0) {
        if(new File("/data/system_de/disable_xedge").exists()) {
            f.v.c("Found file /data/system_de/disable_xedge, disable hook!!!");
            return;
        }
        this.l();
        this.m();
        p2.e(classLoader0);
        if(this.E == null) {
            this.j();
        }
        new w1(classLoader0, this.E, this.G, this.F, this.H, this.I);
        p2.f("System hooked.");
        int v = Build.VERSION.SDK_INT;
        if(v == 29 || v >= 21 && v <= 23) {
            XposedHelpers.findAndHookMethod(View.class, (v < 24 ? "startDrag" : "startDragAndDrop"), new Object[]{ClipData.class, View.DragShadowBuilder.class, Object.class, Integer.TYPE, XC_MethodReplacement.returnConstant(Boolean.FALSE)});
        }
    }

    public void initZygote(IXposedHookZygoteInit.StartupParam iXposedHookZygoteInit$StartupParam0) {
        try {
            if(this.E == null && Process.myUid() == 0) {
                this.l();
                this.j();
            }
            ClassLoader classLoader0 = ClassLoader.getSystemClassLoader();
            if(Build.VERSION.SDK_INT < 21) {
                this.i(classLoader0);
            }
            this.h(classLoader0);
        }
        catch(Throwable throwable0) {
            p2.g(throwable0);
        }
    }

    private void j() {
        if(Build.VERSION.SDK_INT >= 21) {
            x3.l();
            try {
                String s = l.l;
                if((r.k(s) & 1) == 0) {
                    if(SELinuxHelper.isSELinuxEnforced() || Process.myUid() != 0) {
                        p2.f((s + " not executable!"));
                    }
                    else {
                        f.v.c((new File(s).setExecutable(true, false) ? "Set app data folder executable." : "Failed to set app data folder executable."));
                    }
                }
            }
            catch(Throwable throwable0) {
                p2.g(throwable0);
            }
        }
        this.E = new p();
        this.F = k.h();
        this.G = f.g();
        this.H = new e();
        this.I = new v();
        f0.A();
        k2.A();
        y0.L();
    }

    private boolean k() {
        if(Build.VERSION.SDK_INT >= 21) {
            try {
                return SELinuxHelper.isSELinuxEnforced();
            }
            catch(Throwable unused_ex) {
            }
        }
        return false;
    }

    private void l() {
        if(this.E == null) {
            f.v.c(("---------------------------------------- Starting " + l.x + " ----------------------------------------"));
        }
    }

    private void m() {
        String s;
        f.v.a();
        StringBuilder stringBuilder0 = new StringBuilder(0x100);
        stringBuilder0.append(l.u);
        switch(p0.p()) {
            case 1: {
                s = ". ";
                break;
            }
            case 2: {
                s = ", activated by EdXposed. ";
                break;
            }
            case 3: {
                s = ", activated by LSPosed. ";
                break;
            }
            default: {
                s = "? ";
            }
        }
        stringBuilder0.append(s);
        stringBuilder0.append("\n    ");
        stringBuilder0.append(Build.MANUFACTURER);
        stringBuilder0.append('-');
        stringBuilder0.append(Build.MODEL);
        stringBuilder0.append(", ");
        stringBuilder0.append(Build.DEVICE);
        stringBuilder0.append(", Android ");
        stringBuilder0.append(Build.VERSION.RELEASE);
        stringBuilder0.append(", SDK ");
        stringBuilder0.append(Build.VERSION.SDK_INT);
        stringBuilder0.append(", ROM: ");
        stringBuilder0.append(Build.DISPLAY);
        stringBuilder0.append('(');
        stringBuilder0.append(Build.ID);
        stringBuilder0.append(')');
        if(this.k()) {
            stringBuilder0.append(", SELinux enforced.");
        }
        else {
            stringBuilder0.append('.');
        }
        p2.f(stringBuilder0.toString());
    }
}


package e;

import android.view.MotionEvent;
import android.view.View.OnTouchListener;
import android.view.View;

public final class c implements View.OnTouchListener {
    public static final c z;

    static {
        c.z = new c();
    }

    @Override  // android.view.View$OnTouchListener
    public final boolean onTouch(View view0, MotionEvent motionEvent0) {
        return j.j0(view0, motionEvent0);
    }
}


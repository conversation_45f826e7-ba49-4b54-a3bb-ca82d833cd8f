package d;

import android.os.Bundle;
import android.view.View;
import e.j.d;
import e.j.g;
import e.j.i.b;
import e.j.i;
import f.z;
import java.util.List;

public class k extends e implements d {
    private List M;

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F0601A4);  // string:select_app "Select app"
    }

    @Override  // e.j
    protected int B0() {
        if(this.M == null) {
            this.M = z.K(this.f()).M();
        }
        return this.M.size();
    }

    @Override  // d.e
    protected void B1(Bundle bundle0, int v) {
    }

    protected e.j.k E1(int v) {
        f.c0.e c0$e0 = (f.c0.e)this.M.get(v);
        e.j.k j$k0 = new g(this, c0$e0.d(), c0$e0.f(), null);
        if(!c0$e0.m()) {
            j$k0.e();
        }
        return j$k0;
    }

    @Override  // e.j$d
    public String[] a() {
        return z.L(this.M);
    }

    @Override  // e.j
    protected void g1(Object object0) {
        this.M = (List)object0;
    }

    @Override  // e.j
    protected View h1(int v) {
        return this.E1(v);
    }

    @Override  // e.j
    protected void k1(int v) {
        this.C1(((f.c0.e)this.M.get(v)).q());
    }

    @Override  // e.j
    protected boolean l1(int v) {
        if(v < this.M.size()) {
            this.D1(((f.c0.e)this.M.get(v)).q());
            return true;
        }
        return super.l1(v);
    }

    @Override  // e.j
    protected i m1() {
        List list0 = this.m(f.c0.e.class);
        this.M = list0;
        return list0 != null ? null : new b();
    }

    @Override  // e.j
    public void onSaveInstanceState(Bundle bundle0) {
        super.onSaveInstanceState(bundle0);
        this.Z(this.M);
    }
}


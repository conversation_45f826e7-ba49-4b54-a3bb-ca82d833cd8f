package d;

import a.b;
import android.content.ComponentName;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import e.j.g;
import e.j.i;
import e.j.k;
import e.j;
import f.c0.e;
import f.l0;
import f.v;
import f.z;
import java.util.List;

public class v1 extends j {
    private List M;

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F0601AC);  // string:select_shortcut "Select shortcut"
    }

    @Override  // e.j
    protected int B0() {
        if(this.M == null) {
            this.M = z.K(this.f()).T();
        }
        return this.M.size();
    }

    protected k B1(int v) {
        e c0$e0 = (e)this.M.get(v);
        return new g(this, c0$e0.d(), c0$e0.f(), null);
    }

    @Override  // e.j
    protected void g1(Object object0) {
        this.M = (List)object0;
    }

    @Override  // e.j
    protected View h1(int v) {
        return this.B1(v);
    }

    @Override  // e.j
    protected void k1(int v) {
        e c0$e0 = (e)this.M.get(v);
        Intent intent0 = new Intent("android.intent.action.CREATE_SHORTCUT");
        intent0.setComponent(new ComponentName(c0$e0.i(), c0$e0.r()));
        this.startActivityForResult(intent0, 0);
    }

    @Override  // e.j
    protected i m1() {
        List list0 = this.m(e.class);
        this.M = list0;
        return list0 != null ? null : new e.j.i.e();
    }

    @Override  // android.app.Fragment
    public void onActivityResult(int v, int v1, Intent intent0) {
        if(v1 == -1) {
            try {
                b b0 = l0.e(intent0, this.M0(), ((e)this.M.get(this.E0())).i());
                if(b0 != null) {
                    this.W("result", b0);
                    this.L();
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    @Override  // e.j
    public void onSaveInstanceState(Bundle bundle0) {
        super.onSaveInstanceState(bundle0);
        this.Z(this.M);
    }
}


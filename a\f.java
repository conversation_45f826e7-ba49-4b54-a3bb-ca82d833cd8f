package a;

import android.content.Context;
import android.content.Intent;
import f.l;
import f.m;
import f.p;
import f.v;
import java.util.HashMap;
import java.util.Map.Entry;
import java.util.Map;

public class f extends r {
    private final p B;
    private Map C;
    private static final String D;
    public static final int[] E;
    public static final int[] F;
    private static f G;

    static {
        f.D = l.m + "app_triggers";
        f.E = new int[]{1, 2, 3, 4};
        f.F = new int[]{0x7F06007C, 0x7F06007E, 0x7F060076, 0x7F060079};  // string:app_started "started"
        f.G = null;
    }

    private f() {
        super(l.k + "APP_STATES");
        this.B = new p(f.D);
        this.m();
    }

    @Override  // a.r
    protected void b(Intent intent0) {
        m m0 = new m(intent0);
        if(m0.i()) {
            int v = m0.h();
            if(v == 1) {
                String s = m0.a();
                int v1 = m0.h();
                b b0 = m0.g();
                b[] arr_b = (b[])this.C.get(s);
                if(arr_b != null) {
                    arr_b[v1 - 1] = b0;
                }
                else if(b0 != null) {
                    b[] arr_b1 = new b[4];
                    arr_b1[v1 - 1] = b0;
                    HashMap hashMap0 = new HashMap(this.C);
                    hashMap0.put(s, arr_b1);
                    this.C = hashMap0;
                }
            }
            else if(v == 2) {
                this.C = new HashMap();
            }
        }
        m0.k();
    }

    public void e(Context context0) {
        this.C.clear();
        this.n();
        Intent intent0 = new Intent();
        new m(intent0).j().o(2).l();
        this.c(context0, intent0);
    }

    public b f(String s, int v) {
        b[] arr_b = (b[])this.C.get(s);
        if(arr_b != null) {
            b b0 = arr_b[v - 1];
            if(b0 != null) {
                return b0;
            }
        }
        return b.r();
    }

    public static f g() {
        synchronized(f.class) {
            if(f.G == null) {
                f.G = new f();
            }
            return f.G;
        }
    }

    public boolean h(String s) {
        return ((b[])this.C.get(s)) != null;
    }

    public boolean i() {
        return this.C.isEmpty();
    }

    public void j(Context context0, String s, int v, b b0) {
        this.k(s, v, b0);
        this.n();
        Intent intent0 = new Intent();
        new m(intent0).j().o(1).q(s).o(v).p(b0).l();
        this.c(context0, intent0);
    }

    private void k(String s, int v, b b0) {
        if(b0.z == 0 || b0.z == 1) {
            b0 = null;
        }
        b[] arr_b = (b[])this.C.get(s);
        if(arr_b != null) {
            arr_b[v - 1] = b0;
            for(int v1 = 0; v1 < arr_b.length; ++v1) {
                if(arr_b[v1] != null) {
                    return;
                }
            }
            this.C.remove(s);
        }
        else if(b0 != null) {
            b[] arr_b1 = new b[4];
            arr_b1[v - 1] = b0;
            this.C.put(s, arr_b1);
        }
    }

    public static void l() {
        synchronized(f.class) {
            f.G = null;
        }
    }

    public void m() {
        this.C = new HashMap();
        try {
            if(this.B.E()) {
                int v = this.B.h();
                String s = this.B.a();
                for(b b0 = this.B.g(); true; b0 = this.B.g()) {
                    this.k(s, v, b0);
                    if(!this.B.t()) {
                        break;
                    }
                    v = this.B.h();
                    s = this.B.a();
                }
            }
            this.B.m();
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private void n() {
        this.B.F();
        for(Object object0: this.C.entrySet()) {
            String s = (String)((Map.Entry)object0).getKey();
            b[] arr_b = (b[])((Map.Entry)object0).getValue();
            for(int v = 0; v < 4; ++v) {
                b b0 = arr_b[v];
                if(b0 != null) {
                    this.B.w(v + 1).y(s).x(b0).C();
                }
            }
        }
        this.B.n();
    }
}


package d;

import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.CompoundButton;

public final class g0 implements CompoundButton.OnCheckedChangeListener {
    public final h0 a;
    public final String b;

    public g0(h0 h00, String s) {
        this.a = h00;
        this.b = s;
    }

    @Override  // android.widget.CompoundButton$OnCheckedChangeListener
    public final void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
        this.a.E1(this.b, compoundButton0, z);
    }
}


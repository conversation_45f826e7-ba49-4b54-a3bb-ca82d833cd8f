package g;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.view.ActionProvider;
import android.view.ContextMenu.ContextMenuInfo;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem.OnActionExpandListener;
import android.view.MenuItem.OnMenuItemClickListener;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import java.util.ArrayList;

class u implements Menu {
    final class a implements MenuItem {
        private final int a;
        private final int b;
        private CharSequence c;
        final u d;

        a(int v, int v1, CharSequence charSequence0) {
            this.a = v;
            this.b = v1;
            this.c = charSequence0;
        }

        @Override  // android.view.MenuItem
        public boolean collapseActionView() {
            return false;
        }

        @Override  // android.view.MenuItem
        public boolean expandActionView() {
            return false;
        }

        @Override  // android.view.MenuItem
        public ActionProvider getActionProvider() {
            return null;
        }

        @Override  // android.view.MenuItem
        public View getActionView() {
            return null;
        }

        @Override  // android.view.MenuItem
        public char getAlphabeticShortcut() {
            return '\u0000';
        }

        @Override  // android.view.MenuItem
        public int getGroupId() {
            return 0;
        }

        @Override  // android.view.MenuItem
        public Drawable getIcon() {
            return null;
        }

        @Override  // android.view.MenuItem
        public Intent getIntent() {
            return null;
        }

        @Override  // android.view.MenuItem
        public int getItemId() {
            return this.a;
        }

        @Override  // android.view.MenuItem
        public ContextMenu.ContextMenuInfo getMenuInfo() {
            return null;
        }

        @Override  // android.view.MenuItem
        public char getNumericShortcut() {
            return '\u0000';
        }

        @Override  // android.view.MenuItem
        public int getOrder() {
            return this.b;
        }

        @Override  // android.view.MenuItem
        public SubMenu getSubMenu() {
            return null;
        }

        @Override  // android.view.MenuItem
        public CharSequence getTitle() {
            return this.c;
        }

        @Override  // android.view.MenuItem
        public CharSequence getTitleCondensed() {
            return null;
        }

        @Override  // android.view.MenuItem
        public boolean hasSubMenu() {
            return false;
        }

        @Override  // android.view.MenuItem
        public boolean isActionViewExpanded() {
            return false;
        }

        @Override  // android.view.MenuItem
        public boolean isCheckable() {
            return false;
        }

        @Override  // android.view.MenuItem
        public boolean isChecked() {
            return false;
        }

        @Override  // android.view.MenuItem
        public boolean isEnabled() {
            return false;
        }

        @Override  // android.view.MenuItem
        public boolean isVisible() {
            return false;
        }

        @Override  // android.view.MenuItem
        public MenuItem setActionProvider(ActionProvider actionProvider0) {
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setActionView(int v) {
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setActionView(View view0) {
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setAlphabeticShortcut(char c) {
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setCheckable(boolean z) {
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setChecked(boolean z) {
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setEnabled(boolean z) {
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setIcon(int v) {
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setIcon(Drawable drawable0) {
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setIntent(Intent intent0) {
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setNumericShortcut(char c) {
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setOnActionExpandListener(MenuItem.OnActionExpandListener menuItem$OnActionExpandListener0) {
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setOnMenuItemClickListener(MenuItem.OnMenuItemClickListener menuItem$OnMenuItemClickListener0) {
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setShortcut(char c, char c1) {
            return this;
        }

        @Override  // android.view.MenuItem
        public void setShowAsAction(int v) {
        }

        @Override  // android.view.MenuItem
        public MenuItem setShowAsActionFlags(int v) {
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setTitle(int v) {
            this.c = u.this.c.getText(v);
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setTitle(CharSequence charSequence0) {
            this.c = charSequence0;
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setTitleCondensed(CharSequence charSequence0) {
            return this;
        }

        @Override  // android.view.MenuItem
        public MenuItem setVisible(boolean z) {
            return this;
        }
    }

    private final a a;
    private final ArrayList b;
    private final Context c;

    u(Context context0) {
        this.a = new a(this, 0, 0, "");
        this.b = new ArrayList(4);
        this.c = context0;
    }

    @Override  // android.view.Menu
    public MenuItem add(int v) {
        return this.a;
    }

    @Override  // android.view.Menu
    public MenuItem add(int v, int v1, int v2, int v3) {
        return this.add(v, v1, v2, this.c.getText(v3));
    }

    @Override  // android.view.Menu
    public MenuItem add(int v, int v1, int v2, CharSequence charSequence0) {
        MenuItem menuItem0 = new a(this, v1, v2, charSequence0);
        for(int v3 = this.b.size() - 1; v3 >= 0; --v3) {
            if(((a)this.b.get(v3)).b <= v2) {
                this.b.add(v3 + 1, menuItem0);
                return menuItem0;
            }
        }
        this.b.add(0, menuItem0);
        return menuItem0;
    }

    @Override  // android.view.Menu
    public MenuItem add(CharSequence charSequence0) {
        return this.a;
    }

    @Override  // android.view.Menu
    public int addIntentOptions(int v, int v1, int v2, ComponentName componentName0, Intent[] arr_intent, Intent intent0, int v3, MenuItem[] arr_menuItem) {
        return 0;
    }

    @Override  // android.view.Menu
    public SubMenu addSubMenu(int v) {
        return null;
    }

    @Override  // android.view.Menu
    public SubMenu addSubMenu(int v, int v1, int v2, int v3) {
        return null;
    }

    @Override  // android.view.Menu
    public SubMenu addSubMenu(int v, int v1, int v2, CharSequence charSequence0) {
        return null;
    }

    @Override  // android.view.Menu
    public SubMenu addSubMenu(CharSequence charSequence0) {
        return null;
    }

    @Override  // android.view.Menu
    public void clear() {
        this.b.clear();
    }

    @Override  // android.view.Menu
    public void close() {
    }

    @Override  // android.view.Menu
    public MenuItem findItem(int v) {
        for(Object object0: this.b) {
            MenuItem menuItem0 = (a)object0;
            if(((a)menuItem0).a == v) {
                return menuItem0;
            }
            if(false) {
                break;
            }
        }
        return null;
    }

    @Override  // android.view.Menu
    public MenuItem getItem(int v) {
        return v >= this.b.size() ? null : ((MenuItem)this.b.get(v));
    }

    @Override  // android.view.Menu
    public boolean hasVisibleItems() {
        return this.b.size() > 0;
    }

    @Override  // android.view.Menu
    public boolean isShortcutKey(int v, KeyEvent keyEvent0) {
        return false;
    }

    @Override  // android.view.Menu
    public boolean performIdentifierAction(int v, int v1) {
        return false;
    }

    @Override  // android.view.Menu
    public boolean performShortcut(int v, KeyEvent keyEvent0, int v1) {
        return false;
    }

    @Override  // android.view.Menu
    public void removeGroup(int v) {
    }

    @Override  // android.view.Menu
    public void removeItem(int v) {
        for(int v1 = this.b.size() - 1; v1 >= 0; --v1) {
            if(((a)this.b.get(v1)).a == v) {
                this.b.remove(v1);
                return;
            }
        }
    }

    @Override  // android.view.Menu
    public void setGroupCheckable(int v, boolean z, boolean z1) {
    }

    @Override  // android.view.Menu
    public void setGroupEnabled(int v, boolean z) {
    }

    @Override  // android.view.Menu
    public void setGroupVisible(int v, boolean z) {
    }

    @Override  // android.view.Menu
    public void setQwertyMode(boolean z) {
    }

    @Override  // android.view.Menu
    public int size() {
        return this.b.size();
    }
}


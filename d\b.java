package d;

import a.e;
import android.os.Parcelable;

public class b extends s0 {
    private e O;

    @Override  // d.s0
    protected int B0() {
        e e0 = e.A();
        this.O = e0;
        e0.C();
        int v = this.O.v();
        a.b[] arr_b = new a.b[v];
        for(int v1 = 0; v1 < v; ++v1) {
            arr_b[v1] = (a.b)this.O.w(v1);
        }
        this.S1(arr_b, !this.h().getBoolean("select", false));
        return super.B0();
    }

    @Override  // e.j0$c
    protected void G() {
        super.G();
        e.A().F(this.f());
    }

    @Override  // d.s0
    protected int K1() {
        return 7;
    }

    @Override  // d.s0
    protected void M1(int v, a.b b0) {
        try {
            if(v == this.O.v()) {
                this.O.e(this.M0(), b0);
            }
            else {
                this.O.f(this.M0(), v, b0);
            }
            super.M1(v, b0);
        }
        catch(Throwable throwable0) {
            this.g0(throwable0);
        }
    }

    @Override  // d.s0
    protected void O1(int v, boolean z) {
        try {
            if(z) {
                this.O.m(v);
            }
            else {
                this.O.k(v);
            }
            super.O1(v, z);
        }
        catch(Throwable throwable0) {
            this.g0(throwable0);
        }
    }

    @Override  // d.s0
    protected void P1(int v) {
        try {
            this.O.q(this.M0(), v);
            super.P1(v);
        }
        catch(Throwable throwable0) {
            this.g0(throwable0);
        }
    }

    @Override  // d.s0
    protected void Q1(int v, a.b b0) {
        try {
            this.O.u(this.M0(), v, b0);
            super.Q1(v, b0);
        }
        catch(Throwable throwable0) {
            this.g0(throwable0);
        }
    }

    @Override  // d.s0
    protected void R1() {
        this.c0(0x7F060015);  // string:action_collection "Action collection"
    }

    public b T1(boolean z) {
        this.h().putBoolean("select", z);
        return this;
    }

    @Override  // d.s0
    protected void k1(int v) {
        if(this.h().getBoolean("select", false)) {
            this.W("result", ((Parcelable)this.O.w(v)));
            this.L();
            return;
        }
        super.k1(v);
    }

    @Override  // d.s0
    protected void y() {
        this.L();
    }
}


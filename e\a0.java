package e;

import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.os.Bundle;

public class a0 extends b {
    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        Bundle bundle1 = this.e();
        return new AlertDialog.Builder(this.getActivity()).setTitle(bundle1.getCharSequence("title", "")).setMessage(bundle1.getCharSequence("message", "")).setPositiveButton(0x104000A, b.B).create();
    }

    public a0 u(CharSequence charSequence0, CharSequence charSequence1) {
        Bundle bundle0 = this.e();
        bundle0.putCharSequence("title", charSequence0);
        bundle0.putCharSequence("message", charSequence1);
        return this;
    }
}


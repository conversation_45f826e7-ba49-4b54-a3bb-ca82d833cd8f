package g;

import android.content.res.ColorStateList;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;

public abstract class i extends Drawable {
    private int a;
    private ColorFilter b;
    private ColorStateList c;

    public i() {
        this.a = 0xFF;
    }

    protected void a(Canvas canvas0, Drawable drawable0) {
        if(canvas0 != null && drawable0 != null) {
            drawable0.setAlpha(this.a);
            drawable0.setColorFilter(this.b);
            int v = Build.VERSION.SDK_INT;
            if(v >= 21) {
                drawable0.setTintList(this.c);
            }
            drawable0.draw(canvas0);
            drawable0.setAlpha(0xFF);
            drawable0.setColorFilter(null);
            if(v >= 21) {
                drawable0.setTintList(null);
            }
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public int getAlpha() {
        return this.a;
    }

    @Override  // android.graphics.drawable.Drawable
    public final ColorFilter getColorFilter() {
        return this.b;
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getOpacity() {
        return -2;
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setAlpha(int v) {
        this.a = v;
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setColorFilter(ColorFilter colorFilter0) {
        this.b = colorFilter0;
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setTintList(ColorStateList colorStateList0) {
        this.c = colorStateList0;
    }
}


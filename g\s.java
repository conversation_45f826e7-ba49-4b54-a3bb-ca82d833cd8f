package g;

import android.content.res.ColorStateList;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Outline;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;

public class s extends Drawable {
    private final Paint a;
    private final float b;
    private final int c;
    private static final RectF d;

    static {
        s.d = new RectF();
    }

    public s(int v, float f) {
        Paint paint0 = new Paint(1);
        this.a = paint0;
        this.c = v;
        this.b = f;
        paint0.setColor(v);
    }

    @Override  // android.graphics.drawable.Drawable
    public void draw(Canvas canvas0) {
        Rect rect0 = this.getBounds();
        float f = (float)rect0.width();
        float f1 = (float)rect0.height();
        s.d.set(0.0f, 0.0f, f, f1);
        canvas0.drawRoundRect(s.d, this.b, this.b, this.a);
    }

    @Override  // android.graphics.drawable.Drawable
    public int getAlpha() {
        return this.a.getAlpha();
    }

    @Override  // android.graphics.drawable.Drawable
    public int getOpacity() {
        return -2;
    }

    @Override  // android.graphics.drawable.Drawable
    public void getOutline(Outline outline0) {
        outline0.setRoundRect(this.getBounds(), this.b);
    }

    @Override  // android.graphics.drawable.Drawable
    public void setAlpha(int v) {
        this.a.setAlpha(v);
    }

    @Override  // android.graphics.drawable.Drawable
    public void setColorFilter(ColorFilter colorFilter0) {
        this.a.setColorFilter(colorFilter0);
    }

    @Override  // android.graphics.drawable.Drawable
    public void setTint(int v) {
        this.a.setColor(v);
    }

    @Override  // android.graphics.drawable.Drawable
    public void setTintList(ColorStateList colorStateList0) {
        if(colorStateList0 == null) {
            this.a.setColor(this.c);
            return;
        }
        int v = colorStateList0.getDefaultColor();
        this.a.setColor(v);
    }
}


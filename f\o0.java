package f;

import android.content.Context;
import android.os.Build.VERSION;
import android.os.Parcel;
import android.os.Process;
import android.os.UserHandle;
import android.os.UserManager;
import android.util.SparseArray;
import java.lang.reflect.Method;
import java.util.List;

public class o0 {
    public static class a {
        private final int a;
        private int[] b;

        a(Context context0) {
            this.a = 0;
            this.b = null;
        }

        public boolean a(int v) {
            int[] arr_v = this.b;
            if(arr_v == null) {
                return true;
            }
            if(arr_v.length == 0) {
                return v == this.a;
            }
            for(int v1 = 0; v1 < arr_v.length; ++v1) {
                if(arr_v[v1] == v) {
                    return true;
                }
            }
            return false;
        }

        public boolean b() {
            return this.b == null;
        }
    }

    private static final int[] a;
    private static int b;
    private static int[] c;
    private static boolean d;
    private static SparseArray e;
    private static boolean f;
    private static Method g;
    private static boolean h;
    private static Method i;

    static {
        boolean z = false;
        o0.a = new int[0];
        int v = o0.m();
        o0.b = v;
        o0.c = new int[]{v};
        o0.d = false;
        o0.f = Build.VERSION.SDK_INT >= 24;
        o0.g = null;
        if(Build.VERSION.SDK_INT >= 24) {
            z = true;
        }
        o0.h = z;
        o0.i = null;
    }

    static int a() [...] // 潜在的解密器

    static boolean b() [...] // 潜在的解密器

    static int[] c() {
        return o0.a;
    }

    public static int d() [...] // 潜在的解密器

    public static int[] e(Context context0) {
        if(Build.VERSION.SDK_INT >= 21) {
            try {
                UserManager userManager0 = (UserManager)context0.getSystemService("user");
                if(userManager0 != null) {
                    if(o0.h) {
                        try {
                            if(o0.i == null) {
                                o0.i = f0.n().h(UserManager.class, "getEnabledProfileIds", new Class[]{Integer.TYPE});
                            }
                            return (int[])o0.i.invoke(userManager0, 0);
                        }
                        catch(Throwable throwable1) {
                            o0.h = false;
                            v.c(throwable1.toString());
                        }
                    }
                    if(0 == o0.m()) {
                        List list0 = userManager0.getUserProfiles();
                        int v1 = list0.size();
                        int[] arr_v = new int[v1];
                        for(int v = 0; v < v1; ++v) {
                            arr_v[v] = ((UserHandle)list0.get(v)).hashCode();
                        }
                        return arr_v;
                    }
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        return o0.c;
    }

    public static a f(Context context0) {
        return new a(context0);
    }

    public static int g(int v) {
        if(Build.VERSION.SDK_INT >= 21 && v != 0) {
            return v >= 500 ? 2 : 1;
        }
        return 0;
    }

    public static int h(int v) [...] // Inlined contents

    public static boolean i(Context context0, int v) {
        if(v == 0 || !o0.d || Build.VERSION.SDK_INT < 21) {
            return true;
        }
        if(o0.f) {
            UserManager userManager0 = (UserManager)context0.getSystemService("user");
            if(userManager0 != null) {
                try {
                    if(o0.g == null) {
                        o0.g = f0.n().h(UserManager.class, "isSameProfileGroup", new Class[]{Integer.TYPE, Integer.TYPE});
                    }
                    return ((Boolean)o0.g.invoke(userManager0, v, 0)).booleanValue();
                }
                catch(Throwable throwable0) {
                    o0.f = false;
                    v.c(throwable0.toString());
                }
            }
        }
        int[] arr_v = o0.e(context0);
        if(arr_v != null && arr_v.length > 0) {
            for(int v1 = 0; v1 < arr_v.length; ++v1) {
                if(arr_v[v1] == v) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean j(int v) {
        return Build.VERSION.SDK_INT >= 21 && v != 0;
    }

    public static boolean k(int[] arr_v) {
        return arr_v == null || arr_v.length == 0 || arr_v.length == 1 && arr_v[0] == 0;
    }

    public static boolean l() {
        return Process.myUid() <= 1000;
    }

    public static int m() {
        return o0.h(Process.myUid());
    }

    private static UserHandle n(int v) {
        Parcel parcel0 = Parcel.obtain();
        parcel0.writeInt(v);
        UserHandle userHandle0 = new UserHandle(parcel0);
        parcel0.recycle();
        return userHandle0;
    }

    public static void o(int v) {
        if(v == 0) {
            return;
        }
        o0.b = v;
        o0.d = true;
        o0.c = new int[]{v};
        v.c(("Switching to user " + v + ", multi-user not full supported."));
    }

    public static UserHandle p() {
        return o0.q(-1);
    }

    public static UserHandle q(int v) {
        SparseArray sparseArray0 = o0.e;
        if(sparseArray0 != null) {
            UserHandle userHandle0 = (UserHandle)sparseArray0.get(v);
            if(userHandle0 != null) {
                return userHandle0;
            }
        }
        UserHandle userHandle1 = o0.n(v);
        SparseArray sparseArray1 = sparseArray0 == null ? new SparseArray() : sparseArray0.clone();
        sparseArray1.append(v, userHandle1);
        o0.e = sparseArray1;
        return userHandle1;
    }

    public static UserHandle r() {
        return o0.q(0);
    }
}


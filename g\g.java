package g;

import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorFilter;
import android.graphics.Outline;
import android.graphics.Paint.Cap;
import android.graphics.Paint.Style;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Drawable.ConstantState;
import android.graphics.drawable.Drawable;
import f.v;

public final class g extends Drawable {
    static class a {
    }

    public static final class b extends Drawable.ConstantState {
        Drawable.ConstantState a;
        Bitmap b;
        Bitmap c;
        int d;

        private b(int v) {
            if(v != 0 && v != 1 && v != 2) {
                v.d(new IllegalArgumentException("badge=" + v));
                v = 0;
            }
            this.d = v;
        }

        b(int v, a g$a0) {
            this(v);
        }

        public g a() {
            return new g(null, this, null);
        }

        public g b(int v) {
            return v == this.d ? this.a() : new g(this.a.newDrawable(), v);
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public int getChangingConfigurations() {
            return 0;
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public Drawable newDrawable() {
            return this.a();
        }
    }

    private Drawable a;
    private final b b;
    private int c;
    private static final int d;
    private static final int e;
    private static final Rect f;
    private static final RectF g;
    private static final Paint h;

    static {
        g.d = g.h(-15043609);
        g.e = g.h(0xFFFF9126);
        g.f = new Rect();
        g.g = new RectF();
        Paint paint0 = new Paint(1);
        g.h = paint0;
        paint0.setStrokeCap(Paint.Cap.ROUND);
    }

    public g(Drawable drawable0) {
        this(drawable0, new b(0, null));
    }

    public g(Drawable drawable0, int v) {
        this(drawable0, new b(v, null));
    }

    private g(Drawable drawable0, b g$b0) {
        this.c = 0xFF;
        this.a = drawable0;
        this.b = g$b0;
    }

    g(Drawable drawable0, b g$b0, a g$a0) {
        this(drawable0, g$b0);
    }

    private void a(Canvas canvas0, int v) {
        Rect rect0 = this.getBounds();
        float f = ((float)rect0.width()) * 0.375f / 2.0f;
        float f1 = ((float)rect0.left) + f;
        float f2 = ((float)rect0.bottom) - f;
        float f3 = 0.5f * (0.35f * f);
        Paint paint0 = this.g();
        paint0.setColor(v);
        paint0.setStyle(Paint.Style.FILL);
        canvas0.drawCircle(f1, f2, f, paint0);
        paint0.setColor(-1);
        paint0.setStyle(Paint.Style.STROKE);
        paint0.setStrokeWidth(f3);
        g.g.set(f1 - 0.35f * f + f3, f2 - 0.35f * f - f3, f1 + 0.35f * f + f3, f2 + 0.35f * f - f3);
        canvas0.drawArc(g.g, 30.0f, -150.0f, false, paint0);
        g.g.offset(-f3 * 2.0f, f3 * 2.0f);
        paint0.setStyle(Paint.Style.FILL_AND_STROKE);
        canvas0.drawCircle(g.g.centerX(), g.g.centerY(), 0.35f * f, paint0);
    }

    private void b(Canvas canvas0) {
        boolean z = this.i();
        int v = this.getAlpha();
        Rect rect0 = this.getBounds();
        Bitmap bitmap0 = z ? this.b.b : this.b.c;
        if(bitmap0 != null && bitmap0.getWidth() * 2 < rect0.width()) {
            bitmap0 = null;
        }
        if(bitmap0 == null && this.k()) {
            Drawable drawable0 = this.f();
            if(drawable0 == null) {
                return;
            }
            if(z) {
                drawable0.setColorFilter(a0.A);
            }
            Bitmap bitmap1 = a0.i(drawable0, rect0.width(), rect0.height());
            if(z) {
                this.b.b = bitmap1;
                drawable0.clearColorFilter();
            }
            else {
                this.b.c = bitmap1;
            }
            bitmap0 = bitmap1;
        }
        if(bitmap0 != null) {
            int v1 = bitmap0.getWidth();
            int v2 = bitmap0.getHeight();
            g.f.set(0, 0, v1, v2);
            Paint paint0 = this.g();
            canvas0.drawBitmap(bitmap0, g.f, rect0, paint0);
            return;
        }
        Drawable drawable1 = this.f();
        if(drawable1 == null) {
            return;
        }
        drawable1.setBounds(rect0);
        if(z) {
            drawable1.setColorFilter(a0.A);
        }
        drawable1.setAlpha(v);
        drawable1.draw(canvas0);
        if(z) {
            drawable1.clearColorFilter();
        }
        drawable1.setAlpha(0xFF);
    }

    public void c() {
        this.c |= 0x200;
    }

    public int d() {
        return this.b.d;
    }

    @Override  // android.graphics.drawable.Drawable
    public void draw(Canvas canvas0) {
        int v1;
        this.b(canvas0);
        int v = this.d();
        if(v == 1 || v == 2) {
            boolean z = this.i();
            if(v != 1) {
                v1 = z ? g.e : 0xFFFF9126;
            }
            else if(z) {
                v1 = g.d;
            }
            else {
                v1 = -15043609;
            }
            this.a(canvas0, v1 & 0xFFFFFF | this.getAlpha() << 24);
        }
    }

    public b e() {
        b g$b0 = this.b;
        if(g$b0.a == null) {
            Drawable drawable0 = this.a;
            if(drawable0 != null) {
                g$b0.a = drawable0.getConstantState();
            }
        }
        return this.b.a == null ? null : this.b;
    }

    public Drawable f() {
        Drawable drawable0 = this.a;
        if(drawable0 == null) {
            Drawable.ConstantState drawable$ConstantState0 = this.b.a;
            if(drawable$ConstantState0 != null) {
                drawable0 = drawable$ConstantState0.newDrawable();
                this.a = drawable0;
            }
        }
        return drawable0;
    }

    private Paint g() {
        g.h.setAlpha(this.getAlpha());
        return g.h;
    }

    @Override  // android.graphics.drawable.Drawable
    public int getAlpha() {
        return this.c & 0xFF;
    }

    @Override  // android.graphics.drawable.Drawable
    public Drawable.ConstantState getConstantState() {
        return this.e();
    }

    @Override  // android.graphics.drawable.Drawable
    public int getIntrinsicHeight() {
        Drawable drawable0 = this.f();
        return drawable0 == null ? -1 : drawable0.getIntrinsicHeight();
    }

    @Override  // android.graphics.drawable.Drawable
    public int getIntrinsicWidth() {
        Drawable drawable0 = this.f();
        return drawable0 == null ? -1 : drawable0.getIntrinsicWidth();
    }

    @Override  // android.graphics.drawable.Drawable
    public int getOpacity() {
        return -2;
    }

    @Override  // android.graphics.drawable.Drawable
    public void getOutline(Outline outline0) {
        Drawable drawable0 = this.f();
        if(drawable0 != null) {
            drawable0.setBounds(this.getBounds());
            drawable0.getOutline(outline0);
            return;
        }
        super.getOutline(outline0);
    }

    private static int h(int v) {
        int v1 = Color.alpha(v);
        int v2 = Color.red(v);
        int v3 = Color.green(v);
        int v4 = Color.blue(v);
        int v5 = v2 + v3 + v4;
        return Color.argb(v1, (v2 * 2 + v5) / 5, (v3 * 2 + v5) / 5, (v4 * 2 + v5) / 5);
    }

    boolean i() {
        return (this.c & 0x100) != 0;
    }

    void j(boolean z) {
        int v = this.c;
        int v1 = z ? 0x100 | v : v & 0xFFFFFEFF;
        this.c = v1;
        if(v != v1) {
            this.invalidateSelf();
        }
    }

    // 去混淆评级： 低(20)
    private boolean k() {
        return this.i() ? true : (this.c & 0x200) != 0;
    }

    @Override  // android.graphics.drawable.Drawable
    public Drawable mutate() {
        Drawable drawable0 = this.f();
        if(drawable0 == null) {
            return this;
        }
        Drawable drawable1 = drawable0.mutate();
        if(drawable1 == drawable0) {
            return this;
        }
        Drawable drawable2 = new g(drawable1, this.b);
        drawable2.c = this.c;
        return drawable2;
    }

    @Override  // android.graphics.drawable.Drawable
    public void setAlpha(int v) {
        this.c = v & 0xFF | this.c & 0xFFFFFF00;
    }

    @Override  // android.graphics.drawable.Drawable
    public void setColorFilter(ColorFilter colorFilter0) {
    }

    @Override  // android.graphics.drawable.Drawable
    public void setTintList(ColorStateList colorStateList0) {
    }
}


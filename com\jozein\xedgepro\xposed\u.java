package com.jozein.xedgepro.xposed;

import android.content.Context;
import android.content.ContextWrapper;
import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import f.l;
import f.v;

public class u extends ContextWrapper implements l {
    private final l0 A;
    private final Context z;

    public u(Context context0, l0 l00) {
        super(context0);
        this.z = p2.d(context0);
        this.A = l00;
    }

    @Override  // android.content.ContextWrapper
    public Context getApplicationContext() {
        return this.getBaseContext();
    }

    @Override  // android.content.ContextWrapper
    public Resources getResources() {
        return this.z.getResources();
    }

    @Override  // android.content.ContextWrapper
    public void startActivity(Intent intent0) {
        this.startActivity(intent0, null);
    }

    @Override  // android.content.ContextWrapper
    public void startActivity(Intent intent0, Bundle bundle0) {
        intent0.addFlags(0x10000000);
        try {
            this.A.q1(intent0, false, bundle0, 0);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }
}


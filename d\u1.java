package d;

import a.s.b;
import a.s.f;
import a.s;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.CompoundButton;
import e.b0;
import e.j.g;
import e.j.k;
import e.m;
import e.n0;
import e.y;
import e.z;
import f.j;

public class u1 extends c implements a {
    private f M;

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F060198);  // string:schedule "Schedule"
        this.S(0x7F04009B, (View view0) -> this.M1());
    }

    @Override  // e.j
    protected int B0() {
        if(this.M == null) {
            this.M = f.h();
        }
        return this.M.n() + 1;
    }

    @Override  // d.c
    protected void C1(Bundle bundle0, int v) {
        a.s.c s$c1;
        f s$f0;
        int v5;
        int v3;
        b0 b00;
        CharSequence charSequence0;
        k j$k0;
        s s1;
        int v1 = this.E0();
        String s = null;
        switch(v) {
            case 1: {
                s1 = this.M.g(v1);
                s1.A = bundle0.getInt("hour", s1.A);
                s1.B = bundle0.getInt("minute", s1.B);
                ((k)this.K0(0)).setText(s1.b());
                j$k0 = (k)this.L0(v1);
                charSequence0 = s1.d();
                j$k0.setText(charSequence0);
                this.h().putBoolean("changed", true);
                return;
            }
            case 2: {
                a.s.c s$c0 = (a.s.c)this.M.g(v1);
                s$c0.F = bundle0.getInt("result", 0);
                j$k0 = (k)this.K0(1);
                charSequence0 = j.p(s$c0.F);
                j$k0.setText(charSequence0);
                this.h().putBoolean("changed", true);
                return;
            }
            case 3: {
                int v2 = bundle0.getInt("result", -1);
                b s$b0 = (b)this.M.g(v1);
                if(v2 == 0) {
                    b00 = new b0().v(j.j(), s$b0.F);
                    v3 = 4;
                }
                else if(v2 == 1) {
                    b00 = new b0().v(j.e(this.M0()), s$b0.G);
                    v3 = 5;
                }
                else {
                    return;
                }
                this.N(b00, v3);
                return;
            }
            case 4: {
                b s$b1 = (b)this.M.g(v1);
                s$b1.F = bundle0.getInt("result", s$b1.F);
                j$k0 = (k)this.K0(1);
                charSequence0 = j.k(s$b1.F);
                j$k0.setText(charSequence0);
                this.h().putBoolean("changed", true);
                return;
            }
            case 5: {
                b s$b2 = (b)this.M.g(v1);
                s$b2.G = bundle0.getInt("result", s$b2.G);
                ((k)this.K0(1)).setSubText(j.f(this.M0(), s$b2.G));
                this.h().putBoolean("changed", true);
                return;
            }
            case 6: {
                a.b b0 = (a.b)bundle0.getParcelable("result");
                if(b0 == null) {
                    return;
                }
                this.M.g(v1).C = b0;
                g j$g0 = (g)this.K0(2);
                Context context0 = j$g0.getContext();
                j$g0.setText(b0.n(context0));
                j$g0.setImageDrawable(this.D0(b0));
                ((k)this.L0(v1)).setSubText(b0.n(context0));
                this.h().putBoolean("changed", true);
                return;
            }
            case 7: {
                int v4 = bundle0.getInt("result");
                int[] arr_v = this.h().getIntArray("dialog_items");
                if(arr_v == null) {
                    return;
                }
                switch(arr_v[v4]) {
                    case 0x7F0600CD: {  // string:delete "Delete"
                        this.M.l(v1);
                        this.u1(v1);
                        this.h().putBoolean("changed", true);
                        return;
                    }
                    case 0x7F0600D0: {  // string:disable "Disable"
                        ((e.j.j)this.L0(v1)).setChecked(false);
                        return;
                    }
                    case 0x7F0600F4: {  // string:enable "Enable"
                        ((e.j.j)this.L0(v1)).setChecked(true);
                        return;
                    }
                    case 0x7F060158: {  // string:move_down "Move down"
                        v5 = v1 + 1;
                        if(v5 >= this.M.n()) {
                            return;
                        }
                        this.M.i(v1);
                        break;
                    }
                    case 0x7F060159: {  // string:move_up "Move up"
                        if(v1 < 1) {
                            return;
                        }
                        this.M.j(v1);
                        v5 = v1 - 1;
                        break;
                    }
                    case 0x7F060182: {  // string:rename "Rename"
                        s s2 = this.M.g(v1);
                        this.N(new y().K(this.u(0x7F0600F8), null, s2.D), 8);  // string:enter_name "Enter name"
                        return;
                    }
                    default: {
                        this.h().putBoolean("changed", true);
                        return;
                    }
                }
                this.a1(v1, v5);
                this.h().putBoolean("changed", true);
                return;
            }
            case 8: {
                s1 = this.M.g(v1);
                CharSequence charSequence1 = bundle0.getCharSequence("result");
                if(charSequence1 != null && charSequence1.length() != 0) {
                    s = charSequence1.toString();
                }
                s1.D = s;
                j$k0 = (k)this.L0(v1);
                charSequence0 = s1.d();
                j$k0.setText(charSequence0);
                this.h().putBoolean("changed", true);
                return;
            }
            case 9: {
                break;
            }
            case 10: {
                if(bundle0.getBoolean("result", false)) {
                    this.M1();
                }
                this.L();
                return;
            }
            default: {
                this.h().putBoolean("changed", true);
                return;
            }
        }
        int v6 = bundle0.getInt("result", -1);
        if(v6 == 0) {
            s$f0 = this.M;
            s$c1 = new a.s.c();
        }
        else if(v6 == 1) {
            s$f0 = this.M;
            s$c1 = new b();
        }
        else {
            return;
        }
        s$f0.f(s$c1);
        this.x0(this.M.n() - 1);
        this.h().putBoolean("changed", true);
    }

    // 检测为 Lambda 实现
    private void K1(s s0, CompoundButton compoundButton0, boolean z) [...]

    // 检测为 Lambda 实现
    private void L1(View view0) [...]

    private void M1() {
        if(this.h().getBoolean("changed", false)) {
            try {
                this.M.m(this.f());
            }
            catch(Throwable throwable0) {
                this.g0(throwable0);
            }
            this.h().putBoolean("changed", false);
        }
    }

    @Override  // e.j
    protected boolean N0(int v) {
        return v < this.M.n();
    }

    @Override  // e.j
    protected View h1(int v) {
        if(v < this.M.n()) {
            s s0 = this.M.g(v);
            Context context0 = this.M0();
            View view0 = new e.j.j(this, s0.d(), s0.C.n(context0), s0.z);
            ((e.j.j)view0).setOnCheckedChangeListener((CompoundButton compoundButton0, boolean z) -> if(s0.z != z) {
                s0.z = z;
                this.h().putBoolean("changed", true);
            });
            return view0;
        }
        return this.b1();
    }

    @Override  // e.j
    protected View i1(int v) {
        s s0 = this.M.g(this.I0());
        switch(v) {
            case 0: {
                return new k(this, s0.b());
            }
            case 1: {
                if(s0 instanceof a.s.c) {
                    return new k(this, j.p(((a.s.c)s0).F));
                }
                if(s0 instanceof b) {
                    return new k(this, j.k(((b)s0).F), j.f(this.M0(), ((b)s0).G));
                }
                break;
            }
            case 2: {
                break;
            }
            default: {
                return new k(this, null);
            }
        }
        return new g(this, s0.C.n(this.M0()), null, this.D0(s0.C));
    }

    @Override  // e.j
    protected void k1(int v) {
        if(v < this.M.n()) {
            this.y1(v, 3);
            return;
        }
        this.N(new z().u(new CharSequence[]{this.u(0x7F060082), this.u(0x7F060080)}), 9);  // string:as_week "As week"
    }

    @Override  // e.j
    protected boolean l1(int v) {
        if(v < this.M.n()) {
            int[] arr_v = {(this.M.g(v).z ? 0x7F0600D0 : 0x7F0600F4), 0x7F060182, 0x7F060159, 0x7F060158, 0x7F0600CD};  // string:disable "Disable"
            this.h().putIntArray("dialog_items", arr_v);
            CharSequence[] arr_charSequence = new CharSequence[5];
            for(int v1 = 0; v1 < 5; ++v1) {
                arr_charSequence[v1] = this.u(arr_v[v1]);
            }
            this.N(new z().u(arr_charSequence), 7);
            return true;
        }
        return super.l1(v);
    }

    @Override  // e.j
    protected void o1(int v) {
        s s0 = this.M.g(this.I0());
        if(v == 0) {
            this.N(new n0().w(null, s0.A, s0.B), 1);
        }
        else {
            switch(v) {
                case 1: {
                    if(s0 instanceof a.s.c) {
                        this.N(new b0().v(j.o(), ((a.s.c)s0).F), 2);
                        return;
                    }
                    break;
                }
                case 2: {
                    this.P(new d().I1(6, this.u(0x7F060198), this.M.g(this.I0()).d()), 6);  // string:schedule "Schedule"
                    return;
                }
                default: {
                    return;
                }
            }
            if(s0 instanceof b) {
                this.N(new z().u(new CharSequence[]{this.u(0x7F060154), this.u(0x7F060153)}), 3);  // string:modify_months "Modify months"
            }
        }
    }

    @Override  // e.j
    protected boolean p1(int v) {
        if(v == 2) {
            this.F1(this.M.g(this.I0()).C, 6, 6);
        }
        return true;
    }

    @Override  // e.j0$c
    protected void y() {
        if(this.h().getBoolean("changed", false)) {
            this.N(new m().u(this.u(0x7F06008F)), 10);  // string:check_save_message "Do you want to save changes?"
            return;
        }
        super.y();
    }
}


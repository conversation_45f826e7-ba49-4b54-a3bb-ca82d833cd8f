package com.jozein.xedgepro.xposed;

import android.os.Build.VERSION;
import de.robv.android.xposed.SELinuxHelper;
import de.robv.android.xposed.services.BaseService;
import f.q;
import f.v;
import java.io.FileNotFoundException;

class x3 extends q {
    static class a {
    }

    static class b extends f.q.a {
        private b() {
        }

        b(a x3$a0) {
        }

        @Override  // f.q$a
        public q a(String s) {
            return new x3(s, null);
        }
    }

    private final BaseService E;
    private final String F;

    private x3(String s) {
        this.F = s;
        this.E = SELinuxHelper.getAppDataFileService();
    }

    x3(String s, a x3$a0) {
        this(s);
    }

    @Override  // f.q
    public boolean d() {
        try {
            return this.E.checkFileExists(this.F);
        }
        catch(Throwable unused_ex) {
            return false;
        }
    }

    @Override  // f.q
    public long f() {
        try {
            return this.E.getFileModificationTime(this.F);
        }
        catch(Throwable unused_ex) {
            return 0L;
        }
    }

    @Override  // f.q
    public byte[] j() {
        try {
            return this.E.readFile(this.F);
        }
        catch(FileNotFoundException fileNotFoundException0) {
            v.c(fileNotFoundException0.toString());
        }
        catch(Throwable unused_ex) {
        }
        return q.z;
    }

    static void l() {
        try {
            if(Build.VERSION.SDK_INT < 28 && SELinuxHelper.isSELinuxEnforced()) {
                q.k(new b(null));
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }
}


package d;

import android.content.Context;
import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.CompoundButton;

public final class c1 implements CompoundButton.OnCheckedChangeListener {
    public final o1 a;
    public final Context b;

    public c1(o1 o10, Context context0) {
        this.a = o10;
        this.b = context0;
    }

    @Override  // android.widget.CompoundButton$OnCheckedChangeListener
    public final void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
        this.a.r2(this.b, compoundButton0, z);
    }
}


package g;

import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import android.view.MotionEvent.PointerCoords;
import android.view.MotionEvent.PointerProperties;
import android.view.MotionEvent;
import f.d0;
import f.s;

public class q implements Parcelable {
    class a implements Parcelable.Creator {
        a() {
            super();
        }

        public q a(Parcel parcel0) {
            return new q(new d0(parcel0));
        }

        public q[] b(int v) {
            return new q[v];
        }

        @Override  // android.os.Parcelable$Creator
        public Object createFromParcel(Parcel parcel0) {
            return this.a(parcel0);
        }

        @Override  // android.os.Parcelable$Creator
        public Object[] newArray(int v) {
            return this.b(v);
        }
    }

    public static class b {
        public int a;
        public int b;
        public int c;

        public b(int v, float f, float f1) {
            this.a = v;
            this.b = (int)f;
            this.c = (int)f1;
        }
    }

    public final int A;
    public final b[] B;
    private static final MotionEvent.PointerProperties[] C;
    public static final Parcelable.Creator CREATOR;
    private static final MotionEvent.PointerCoords[] D;
    public final int z;

    static {
        q.C = new MotionEvent.PointerProperties[]{new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties(), new MotionEvent.PointerProperties()};
        q.D = new MotionEvent.PointerCoords[]{new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords(), new MotionEvent.PointerCoords()};
        q.CREATOR = new a();
    }

    public q(MotionEvent motionEvent0, long v) {
        this.z = (int)(motionEvent0.getEventTime() - v);
        this.A = motionEvent0.getAction();
        int v1 = motionEvent0.getPointerCount();
        this.B = new b[v1];
        for(int v2 = 0; v2 < v1; ++v2) {
            this.B[v2] = new b(motionEvent0.getPointerId(v2), motionEvent0.getX(v2), motionEvent0.getY(v2));
        }
    }

    public q(s s0) {
        this.z = s0.h();
        this.A = s0.h();
        int v = s0.h();
        this.B = new b[v];
        for(int v1 = 0; v1 < v; ++v1) {
            this.B[v1] = new b(s0.h(), ((float)s0.h()), ((float)s0.h()));
        }
    }

    public MotionEvent a(long v, long v1) {
        b[] arr_q$b;
        for(int v2 = 0; true; ++v2) {
            arr_q$b = this.B;
            if(v2 >= arr_q$b.length) {
                break;
            }
            q.C[v2].id = arr_q$b[v2].a;
            q.C[v2].toolType = 1;
            q.D[v2].x = (float)arr_q$b[v2].b;
            q.D[v2].y = (float)arr_q$b[v2].c;
        }
        return MotionEvent.obtain(v1, v + ((long)this.z), this.A, arr_q$b.length, q.C, q.D, 0, 0, 1.0f, 1.0f, 0, 0, 0x1002, 0);
    }

    public void b(s s0) {
        s0.d(this.z).d(this.A).d(this.B.length);
        b[] arr_q$b = this.B;
        for(int v = 0; v < arr_q$b.length; ++v) {
            b q$b0 = arr_q$b[v];
            s0.d(q$b0.a).d(q$b0.b).d(q$b0.c);
        }
    }

    @Override  // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    @Override  // android.os.Parcelable
    public void writeToParcel(Parcel parcel0, int v) {
        this.b(new d0(parcel0).j());
    }
}


package g;

import android.content.Context;
import android.util.DisplayMetrics;

public final class x {
    public final int a;
    public final int b;
    public final int c;
    public final int d;
    public final int e;
    public final int f;
    public final int g;

    private x(int v, float f, float f1, float f2) {
        this.a = v;
        this.b = (int)(f * ((float)v));
        int v1 = (int)(f1 * ((float)v));
        this.d = v1;
        int v2 = v1 * 4 / 3;
        this.c = v2;
        this.e = (v2 + v1) / 2;
        int v3 = (int)(((float)v) * f2);
        this.f = v3;
        this.g = v3 * 3;
    }

    public static x a(Context context0) {
        return new x(x.d(context0), 0.75f, 0.2f, 0.1f);
    }

    public static x b(Context context0) {
        return new x(x.d(context0) * 4 / 3, 0.56f, 0.14f, 0.1f);
    }

    public static x c(Context context0) {
        return new x(x.d(context0), 0.65f, 0.2f, 0.1f);
    }

    private static int d(Context context0) {
        DisplayMetrics displayMetrics0 = context0.getResources().getDisplayMetrics();
        int v = Math.min(displayMetrics0.widthPixels, displayMetrics0.heightPixels);
        float f = displayMetrics0.xdpi * 0.375f;
        float f1 = "android".equals("com.jozein.xedgepro") ? ((float)v) * 0.15f + f * 3.0f : ((float)v) * 0.15f + f + displayMetrics0.density * 120.0f;
        int v1 = ((int)(f1 / 4.0f)) >= v / 12 ? ((int)(f1 / 4.0f)) : v / 12;
        return v1 <= v / 5 ? v1 : v / 5;
    }
}


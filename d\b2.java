package d;

import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.CompoundButton;

public final class b2 implements CompoundButton.OnCheckedChangeListener {
    public final c2 a;
    public final String b;

    public b2(c2 c20, String s) {
        this.a = c20;
        this.b = s;
    }

    @Override  // android.widget.CompoundButton$OnCheckedChangeListener
    public final void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
        this.a.F1(this.b, compoundButton0, z);
    }
}


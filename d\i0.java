package d;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import e.j.d;
import e.j.k;

public class i0 extends e implements d {
    private static final int M;

    static {
        int v = Math.max(337, 320);
        int v1;
        for(v1 = 220; v1 <= v && KeyEvent.keyCodeToString(v1).charAt(0) > 57; ++v1) {
        }
        i0.M = v1 - 1;
    }

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F0601AA);  // string:select_key_event "Select key event"
    }

    @Override  // e.j
    protected int B0() {
        return i0.M;
    }

    @Override  // d.e
    protected void B1(Bundle bundle0, int v) {
        this.U(bundle0);
        this.L();
    }

    protected k E1(int v) {
        return new k(this, KeyEvent.keyCodeToString(v + 1), this.u(0x7F060096) + Integer.toString(v + 1));  // string:code "Code: "
    }

    @Override  // e.j$d
    public String[] a() {
        String[] arr_s = new String[i0.M];
        for(int v = 0; v < i0.M; ++v) {
            arr_s[v] = KeyEvent.keyCodeToString(v + 1);
            if(arr_s[v].startsWith("KEYCODE_")) {
                arr_s[v] = arr_s[v].substring(8);
            }
        }
        return arr_s;
    }

    @Override  // e.j
    protected View h1(int v) {
        return this.E1(v);
    }

    @Override  // e.j
    protected void k1(int v) {
        this.N(new c.k().D(v + 1, 0, 0), 1);
    }
}


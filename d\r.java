package d;

import a.b.f0;
import a.b;
import a.n;
import android.os.Bundle;
import e.j;
import e.z;

public class r extends t0 {
    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F0600CA);  // string:custom_panels "Custom panels"
    }

    @Override  // d.t0
    protected n B1() {
        return f0.A();
    }

    @Override  // d.t0
    protected b[] C1(Bundle bundle0) {
        return (b[])bundle0.getParcelableArray("result");
    }

    @Override  // d.t0
    protected b D1(int v) {
        return new f0(v);
    }

    @Override  // d.t0
    protected j E1(CharSequence charSequence0, b[] arr_b, boolean z) {
        return new q().R1(charSequence0, arr_b, z);
    }

    @Override  // d.t0
    protected void F1() {
        this.N(new z().u(new CharSequence[]{"2 × 2", "3 × 3", "4 × 4"}), 1);
    }

    @Override  // d.t0
    protected void J(Bundle bundle0, int v) {
        if(bundle0 == null) {
            return;
        }
        if(v == 1) {
            switch(bundle0.getInt("result", -1)) {
                case -1: {
                    break;
                }
                case 1: {
                    this.H1(this.E1(null, new b[9], true));
                    return;
                }
                case 2: {
                    this.H1(this.E1(null, new b[16], true));
                    break;
                }
                default: {
                    this.H1(this.E1(null, new b[4], true));
                    return;
                }
            }
            return;
        }
        super.J(bundle0, v);
    }
}


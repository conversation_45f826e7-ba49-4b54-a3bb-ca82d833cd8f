package com.jozein.xedgepro.xposed;

import android.content.Intent;
import android.media.AudioManager;
import android.media.session.MediaController;
import android.media.session.MediaSessionManager;
import android.os.Build.VERSION;
import android.os.Handler;
import android.os.SystemClock;
import android.view.KeyEvent;
import de.robv.android.xposed.XposedHelpers;
import f.v;
import java.util.List;

class e {
    private final w1 a;
    private final Handler b;
    private AudioManager c;
    private Object d;
    private final Runnable e;

    e(w1 w10) {
        this.d = null;
        this.e = () -> if(Build.VERSION.SDK_INT >= 21 && !this.g()) {
            try {
                List list0 = ((MediaSessionManager)this.a.T1().getSystemService("media_session")).getActiveSessions(null);
                if(list0.size() > 0) {
                    ((MediaController)list0.get(0)).getTransportControls().play();
                    return;
                }
                l0 l00 = this.a.R1();
                String s = l00.K.k("android.intent.category.APP_MUSIC", false);
                if(s != null) {
                    long v = SystemClock.uptimeMillis();
                    Intent intent0 = new Intent("android.intent.action.MEDIA_BUTTON");
                    intent0.setPackage(s);
                    intent0.addFlags(0x20);
                    intent0.putExtra("android.intent.extra.KEY_EVENT", new KeyEvent(v - 10L, v - 10L, 0, 0x7E, 0));
                    l00.Y(intent0);
                    Intent intent1 = new Intent("android.intent.action.MEDIA_BUTTON");
                    intent1.setPackage(s);
                    intent1.addFlags(0x20);
                    intent1.putExtra("android.intent.extra.KEY_EVENT", new KeyEvent(v - 10L, v, 1, 0x7E, 0));
                    l00.Y(intent1);
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        };
        this.a = w10;
        this.b = w10.U1();
        try {
            this.c = (AudioManager)w10.T1().getSystemService("audio");
            this.d = XposedHelpers.callStaticMethod(AudioManager.class, "getService", new Object[0]);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    void a(int v, int v1, int v2) {
        if(v >= 0) {
            this.c.adjustStreamVolume(v, v1, v2);
            return;
        }
        this.c.adjustVolume(v1, v2);
    }

    void b(int v) {
        long v1 = SystemClock.uptimeMillis();
        this.c.dispatchMediaKeyEvent(new KeyEvent(v1 - 10L, v1 - 10L, 0, v, 0));
        this.c.dispatchMediaKeyEvent(new KeyEvent(v1 - 10L, v1, 1, v, 0));
        if(Build.VERSION.SDK_INT >= 21) {
            this.b.removeCallbacks(this.e);
            if(v == 0x7E && !this.g()) {
                this.b.postDelayed(this.e, 1000L);
            }
        }
    }

    int c() {
        try {
            return (int)(((Integer)XposedHelpers.callMethod(this.d, "getActiveStreamType", new Object[]{0x80000000})));
        }
        catch(Throwable unused_ex) {
            return this.c.isMusicActive() ? 3 : 2;
        }
    }

    int d() {
        return this.c.getRingerMode();
    }

    int e(int v) {
        return this.c.getStreamMaxVolume(v);
    }

    int[] f() {
        try {
            return (int[])XposedHelpers.getObjectField(this.d, "mStreamVolumeAlias");
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return null;
        }
    }

    boolean g() {
        return this.c.isMusicActive();
    }

    // 检测为 Lambda 实现
    void h() [...]

    void i(int v) {
        this.c.playSoundEffect(v, -1.0f);
    }

    void j(int v) {
        this.c.setRingerMode(v);
    }

    void k(int v, int v1, int v2) {
        this.c.setStreamVolume(v, v1, v2);
    }
}


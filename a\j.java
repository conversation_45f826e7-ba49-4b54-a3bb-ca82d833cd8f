package a;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import com.jozein.xedgepro.ui.ActivityPerformAction;
import f.d;
import f.l;
import f.q.b;
import f.v;
import java.io.File;

public class j implements l {
    public static final String A;
    public static final String B;
    public static final int C;
    public static final String D;
    public static final String E;
    public static final String[] F;
    private static final String[] G;
    private static final String[] H;
    private static File[] I;
    private static boolean J;
    public static final String z;

    static {
        j.z = l.v + ".IMPORT";
        j.A = l.w + ".IMPORT_REPLY";
        String s = l.l.replace(l.w, l.v);
        j.B = s;
        j.C = s.length();
        j.D = l.j + ".READ_FILES";
        j.E = l.j + ".READ_FILES_REPLY";
        j.F = new String[]{l.m, l.n, l.o, l.p, l.q};
        j.G = new String[]{l.m, l.n, l.o};
        j.H = new String[]{"prefs/collection", "prefs/tiles"};
        j.I = null;
        j.J = false;
    }

    private static void b(Context context0, File file0) {
        if(!file0.exists()) {
            return;
        }
        byte[] arr_b = new b(file0).j();
        if(arr_b == null) {
            return;
        }
        context0.sendBroadcast(new Intent(j.A).setPackage(l.w).putExtra("name", file0.getPath().substring(j.C)).putExtra("buffer", arr_b));
    }

    public static void c(Context context0) {
        if(j.I == null) {
            j.I = new File[j.F.length];
            int v = 0;
            if(l.r) {
                while(true) {
                    String[] arr_s = j.F;
                    if(v >= arr_s.length) {
                        break;
                    }
                    File[] arr_file = j.I;
                    arr_file[v] = new File(arr_s[v].replace(l.w, l.v));
                    ++v;
                }
            }
            else {
                while(true) {
                    String[] arr_s1 = j.F;
                    if(v >= arr_s1.length) {
                        break;
                    }
                    File[] arr_file1 = j.I;
                    arr_file1[v] = new File(arr_s1[v]);
                    ++v;
                }
            }
        }
        try {
            j.d(context0, j.I);
        }
        finally {
            context0.sendBroadcast(new Intent(j.A).setPackage(l.w));
        }
    }

    private static void d(Context context0, File[] arr_file) {
        if(arr_file == null) {
            return;
        }
        for(int v = 0; v < arr_file.length; ++v) {
            File file0 = arr_file[v];
            if(file0.exists()) {
                if(file0.isDirectory()) {
                    j.d(context0, file0.listFiles());
                }
                else {
                    j.b(context0, file0);
                }
            }
        }
    }

    // 检测为 Lambda 实现
    private static void e(boolean z, Context context0) [...]

    public static void f(Context context0) {
        Intent intent0 = new Intent(j.z);
        intent0.setFlags(0x18000000);
        intent0.setComponent(new ComponentName(l.v, ActivityPerformAction.class.getName().replace(l.w, l.v)));
        context0.startActivity(intent0);
    }

    public static void g(Context context0, boolean z) {
        d.b(() -> {
            int v1;
            Intent intent0 = new Intent(j.E);
            intent0.setPackage("android");
            if(z) {
                intent0.putExtra("extra", 1);
                context0.sendBroadcast(intent0);
            }
            try {
                v1 = 3;
                String[] arr_s = j.G;
                for(int v2 = 0; v2 < arr_s.length; ++v2) {
                    j.j(context0, arr_s[v2]);
                }
            }
            finally {
                if(!z) {
                    v1 = 2;
                }
                intent0.putExtra("extra", v1);
                context0.sendBroadcast(intent0);
            }
        });
    }

    public static void h(Context context0) {
        try {
            if(!j.J && j.m(context0)) {
                j.J = true;
                j.g(context0, false);
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private static void i(Context context0, File file0) {
        if(file0 != null && file0.exists()) {
            String s = file0.getAbsolutePath();
            String[] arr_s = j.H;
            for(int v = 0; v < arr_s.length; ++v) {
                if(s.endsWith(arr_s[v])) {
                    return;
                }
            }
            byte[] arr_b = new b(file0).j();
            if(arr_b == null) {
                return;
            }
            context0.sendBroadcast(new Intent(j.E).setPackage("android").putExtra("name", s).putExtra("buffer", arr_b).putExtra("last_modified", file0.lastModified()));
            if(arr_b.length > 0x10000) {
                v.c(("sending " + s + ", size: " + arr_b.length));
            }
        }
    }

    private static void j(Context context0, String s) {
        if(s != null) {
            File file0 = new File(s);
            if(file0.isDirectory()) {
                j.l(context0, file0.listFiles());
                return;
            }
            j.i(context0, file0);
        }
    }

    public static void k(Context context0, String s) {
        try {
            if(j.m(context0)) {
                j.j(context0, s);
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private static void l(Context context0, File[] arr_file) {
        if(arr_file == null) {
            return;
        }
        for(int v = 0; v < arr_file.length; ++v) {
            File file0 = arr_file[v];
            if(file0.exists()) {
                if(file0.isDirectory()) {
                    j.l(context0, file0.listFiles());
                }
                else {
                    j.i(context0, file0);
                }
            }
        }
    }

    public static boolean m(Context context0) {
        return context0 != null && p.q(context0, 8);
    }
}


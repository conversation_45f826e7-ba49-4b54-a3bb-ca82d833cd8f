package c;

import android.app.Activity;
import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.ClipboardManager;
import android.content.DialogInterface;
import android.os.Bundle;
import android.widget.FrameLayout.LayoutParams;
import android.widget.FrameLayout;
import android.widget.HorizontalScrollView;
import android.widget.ScrollView;
import android.widget.TextView;

public class b extends e.j0.b {
    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        Activity activity0 = this.getActivity();
        HorizontalScrollView horizontalScrollView0 = new HorizontalScrollView(activity0);
        int v = this.g().f;
        horizontalScrollView0.setPadding(v, v, v, v);
        horizontalScrollView0.setFillViewport(true);
        ScrollView scrollView0 = new ScrollView(activity0);
        scrollView0.setFillViewport(true);
        horizontalScrollView0.addView(scrollView0);
        FrameLayout frameLayout0 = new FrameLayout(activity0);
        scrollView0.addView(frameLayout0);
        TextView textView0 = new TextView(activity0);
        this.r(textView0);
        a.b b0 = (a.b)this.e().getParcelable("action");
        StringBuilder stringBuilder0 = b0 == null ? null : b0.k(activity0);
        textView0.setText(stringBuilder0);
        textView0.setTextIsSelectable(true);
        FrameLayout.LayoutParams frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(-1, -1, 17);
        frameLayout$LayoutParams0.leftMargin = v * 2;
        frameLayout$LayoutParams0.topMargin = v * 2;
        frameLayout$LayoutParams0.rightMargin = v * 2;
        frameLayout$LayoutParams0.bottomMargin = v * 2;
        frameLayout0.addView(textView0, frameLayout$LayoutParams0);
        return new AlertDialog.Builder(activity0).setTitle(0x7F06002A).setView(horizontalScrollView0).setPositiveButton(0x104000A, e.j0.b.B).setNegativeButton(0x1040001, (DialogInterface dialogInterface0, int v) -> {
            ClipboardManager clipboardManager0 = (ClipboardManager)this.getActivity().getSystemService("clipboard");
            if(clipboardManager0 != null) {
                clipboardManager0.setText(stringBuilder0);
                this.s(0x7F0601F2);  // string:text_copied "Text copied."
            }
        }).create();
    }

    // 检测为 Lambda 实现
    private void v(CharSequence charSequence0, DialogInterface dialogInterface0, int v) [...]

    public b w(a.b b0) {
        this.e().putParcelable("action", b0);
        return this;
    }
}


package com.jozein.xedgepro.xposed;

import android.content.Context;
import android.hardware.display.DisplayManager;
import android.os.Build.VERSION;
import de.robv.android.xposed.XposedHelpers;
import f.g;
import f.v;
import java.lang.reflect.Method;

abstract class f {
    static abstract class a extends f {
        private final int e;

        a(int v) {
            this.e = v;
        }

        @Override  // com.jozein.xedgepro.xposed.f
        public void b(Context context0, boolean z) {
            float f1;
            float f = this.m(context0);
            int v = this.l(f);
            if(z) {
                f1 = this.n(v + 1);
                if(f1 <= f) {
                    return;
                }
            }
            else {
                f1 = this.n(v);
                if(f1 >= f) {
                    f1 = this.n(v - 1);
                }
                if(f1 >= f) {
                    return;
                }
            }
            this.o(context0, f1);
        }

        @Override  // com.jozein.xedgepro.xposed.f
        public int f() {
            return this.e;
        }

        @Override  // com.jozein.xedgepro.xposed.f
        public void k(Context context0, int v) {
            this.o(context0, this.n(v));
        }

        private int l(float f) {
            return this.c(((int)Math.sqrt(f * ((float)this.e) * ((float)this.e))));
        }

        protected abstract float m(Context arg1);

        private float n(int v) {
            float f = ((float)(v * v)) / ((float)(this.e * this.e));
            if(f <= 0.0f) {
                return 0.0f;
            }
            return f >= 1.0f ? 1.0f : f;
        }

        protected abstract void o(Context arg1, float arg2);
    }

    static class b extends f {
        private final int[] e;
        private static final int f;
        private static final int g;

        static {
            b.f = g.a;
            b.g = g.b;
        }

        b(boolean z) {
            this.e = b.m((z ? 0x20 : 16));
        }

        @Override  // com.jozein.xedgepro.xposed.f
        public void b(Context context0, boolean z) {
            int v2;
            int v = c.a(context0.getContentResolver(), "screen_brightness", this.e[this.e.length / 2]);
            int v1 = this.l(v);
            if(z) {
                v2 = this.n(v1 + 1);
                if(v2 <= v) {
                    return;
                }
            }
            else {
                v2 = this.n(v1);
                if(v2 >= v) {
                    v2 = this.n(v1 - 1);
                }
                if(v2 >= v) {
                    return;
                }
            }
            c.b(context0.getContentResolver(), "screen_brightness", v2);
        }

        @Override  // com.jozein.xedgepro.xposed.f
        public int f() {
            return this.e.length - 1;
        }

        @Override  // com.jozein.xedgepro.xposed.f
        public void k(Context context0, int v) {
            int v1 = this.n(v);
            c.b(context0.getContentResolver(), "screen_brightness", v1);
        }

        private int l(int v) {
            int[] arr_v;
            for(int v1 = 0; true; ++v1) {
                arr_v = this.e;
                if(v1 >= arr_v.length) {
                    break;
                }
                if(arr_v[v1] >= v) {
                    return v1;
                }
            }
            return arr_v.length - 1;
        }

        private static int[] m(int v) {
            int[] arr_v = new int[v + 1];
            float f = ((float)(b.g - b.f)) / ((float)(v * v));
            arr_v[0] = b.f;
            arr_v[v] = b.g;
            for(int v1 = 1; v1 < v; ++v1) {
                arr_v[v1] = ((int)(f * ((float)v1) * ((float)v1) + ((float)b.f))) > arr_v[v1 - 1] ? ((int)(f * ((float)v1) * ((float)v1) + ((float)b.f))) : arr_v[v1 - 1] + 1;
            }
            return arr_v;
        }

        private int n(int v) {
            if(v <= 0) {
                return this.e[0];
            }
            return v < this.e.length ? this.e[v] : this.e[this.e.length - 1];
        }
    }

    static class com.jozein.xedgepro.xposed.f.c extends a {
        private final Method f;
        private final Method g;

        com.jozein.xedgepro.xposed.f.c(boolean z) {
            super((z ? 0x40 : 16));
            Class[] arr_class = new Class[1];
            Class class0 = Integer.TYPE;
            arr_class[0] = class0;
            this.f = XposedHelpers.findMethodExact(DisplayManager.class, "getBrightness", arr_class);
            this.g = XposedHelpers.findMethodExact(DisplayManager.class, "setBrightness", new Class[]{class0, Float.TYPE});
        }

        @Override  // com.jozein.xedgepro.xposed.f$a
        protected float m(Context context0) {
            DisplayManager displayManager0 = (DisplayManager)context0.getSystemService("display");
            if(displayManager0 != null) {
                try {
                    return (float)(((Float)this.f.invoke(displayManager0, 0)));
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
            f.d();
            return 0.5f;
        }

        @Override  // com.jozein.xedgepro.xposed.f$a
        protected void o(Context context0, float f) {
            DisplayManager displayManager0 = (DisplayManager)context0.getSystemService("display");
            if(displayManager0 != null) {
                try {
                    this.g.invoke(displayManager0, 0, f);
                    float f1 = this.m(context0);
                    if(f1 != f) {
                        f.d();
                        v.c(("Failed to set brightness to " + f + ", brightness: " + f1));
                    }
                    return;
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
            f.d();
        }
    }

    private static final boolean a;
    private static boolean b;
    private static f c;
    private static f d;

    static {
        f.a = Build.VERSION.SDK_INT >= 30;
        f.b = Build.VERSION.SDK_INT >= 30;
        f.c = null;
        f.d = null;
    }

    public abstract void b(Context arg1, boolean arg2);

    int c(int v) {
        int v1 = this.f();
        if(v <= 0) {
            return 0;
        }
        return v < v1 ? v : v1;
    }

    private static void d() {
        synchronized(f.class) {
            f.b = false;
            f.c = null;
            f.d = null;
        }
    }

    static f e(Context context0, boolean z) {
        Class class0 = f.class;
        if(z) {
            if(f.d == null) {
                synchronized(class0) {
                    if(f.d == null) {
                        f.d = f.h(context0, true);
                    }
                }
                return f.d;
            }
            return f.d;
        }
        if(f.c == null) {
            synchronized(class0) {
                if(f.c == null) {
                    f.c = f.h(context0, false);
                }
            }
            return f.c;
        }
        return f.c;
    }

    public abstract int f();

    public boolean g(Context context0) {
        return c.a(context0.getContentResolver(), "screen_brightness_mode", 0) == 1;
    }

    private static f h(Context context0, boolean z) {
        if(f.b && Build.VERSION.SDK_INT >= 0x1F) {
            try {
                f f0 = new com.jozein.xedgepro.xposed.f.c(z);
                float f1 = ((com.jozein.xedgepro.xposed.f.c)f0).m(context0);
                if(f1 <= 1.0f) {
                    return f0;
                }
                v.c(("Invalid brightness float: " + f1));
                f.d();
                return new b(z);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                f.d();
                return new b(z);
            }
        }
        return new b(z);
    }

    public void i(Context context0, boolean z) {
        if(this.g(context0) != z) {
            c.b(context0.getContentResolver(), "screen_brightness_mode", ((int)z));
        }
    }

    public static void j(Context context0, int v) {
        c.b(context0.getContentResolver(), "screen_brightness", v);
    }

    public abstract void k(Context arg1, int arg2);
}


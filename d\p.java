package d;

import a.h.a0;
import a.h.c;
import a.h.k;
import a.h.l0;
import a.h.l;
import a.h.m;
import a.h.n;
import a.h.x;
import a.h.z;
import a.h;
import a.y;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import c.e;
import com.jozein.xedgepro.ApplicationMain;
import e.b0;
import e.j.d;
import e.j;
import e.n0;

public class p extends j implements d {
    private y M;
    private static final h[] N;

    static {
        p.N = h.a();
    }

    public p() {
        this.M = null;
    }

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F0601A5);  // string:select_condition "Select condition"
    }

    @Override  // e.j
    protected int B0() {
        return p.N.length;
    }

    private void I1(h h0) {
        this.W("result", h0);
    }

    @Override  // e.j0$c
    protected void J(Bundle bundle0, int v) {
        boolean z = true;
        if(bundle0 == null) {
            return;
        }
        if(v == 1) {
            this.U(bundle0);
        }
        else {
            switch(v) {
                case 7: {
                    int v1 = bundle0.getInt("hour", -1);
                    int v2 = bundle0.getInt("minute", 0);
                    if(v1 != -1) {
                        Bundle bundle1 = this.h();
                        bundle1.putInt("hour", v1);
                        bundle1.putInt("minute", v2);
                        int v3 = v1 + 12;
                        this.N(new n0().w(this.u(0x7F060205), (v3 < 24 ? v1 + 12 : v3 - 24), v2), 8);  // string:time_to "To"
                        return;
                    }
                    break;
                }
                case 8: {
                    int v4 = bundle0.getInt("hour", -1);
                    int v5 = bundle0.getInt("minute", 0);
                    if(v4 != -1) {
                        Bundle bundle2 = this.h();
                        int v6 = bundle2.getInt("hour", 0);
                        int v7 = bundle2.getInt("minute", 0);
                        if(v6 == v4 && v7 == v5) {
                            this.e0(0x7F0600AA);  // string:condition_invalid "Invalid"
                            return;
                        }
                        this.I1(new n(v6, v7, v4, v5));
                    }
                    break;
                }
                case 9: {
                    this.I1(new l(bundle0.getInt("result", 0)));
                    break;
                }
                case 10: {
                    this.I1(new k(bundle0.getInt("result", 0)));
                    break;
                }
                case 11: {
                    this.I1(new m(bundle0.getInt("result", 0)));
                    break;
                }
                case 12: {
                    String[] arr_s = this.h().getStringArray("ssids");
                    if(arr_s != null) {
                        int v8 = bundle0.getInt("result", -1);
                        if(v8 >= 0 && v8 < arr_s.length) {
                            this.I1(new l0(arr_s[v8]));
                        }
                    }
                    break;
                }
                case 13: {
                    CharSequence charSequence0 = bundle0.getCharSequence("result");
                    if(charSequence0 != null) {
                        this.I1(new l0(charSequence0.toString()));
                    }
                    break;
                }
                case 14: {
                    String s = bundle0.getString("result");
                    if(s != null) {
                        this.I1(new a.h.d(s));
                    }
                    break;
                }
                case 15: {
                    int v9 = bundle0.getInt("result", -1);
                    if(v9 == 0 || v9 == 1) {
                        if(v9 != 1) {
                            z = false;
                        }
                        this.I1(new a0(z));
                    }
                    break;
                }
                default: {
                    h h0 = (h)bundle0.getParcelable("result");
                    if(h0 != null) {
                        switch(v) {
                            case 2: {
                                this.J1(h0, 3);
                                return;
                            }
                            case 3: {
                                this.I1(new c(((h)this.h().getParcelable("first")), h0));
                                break;
                            }
                            case 4: {
                                this.J1(h0, 5);
                                return;
                            label_13:
                                if(v == 6) {
                                    this.I1(new x(h0));
                                }
                                break;
                            }
                            case 5: {
                                this.I1(new z(((h)this.h().getParcelable("first")), h0));
                                break;
                            }
                            default: {
                                goto label_13;
                            }
                        }
                    }
                }
            }
        }
        this.L();
    }

    private void J1(h h0, int v) {
        class b implements Runnable {
            final p A;
            final int z;

            b(int v) {
                this.z = v;
                super();
            }

            @Override
            public void run() {
                p p0 = new p();
                p.this.P(p0, this.z);
                p.this.e0(0x7F0601AB);  // string:select_second_condition "Please select the second condition"
            }
        }

        this.h().putParcelable("first", h0);
        View view0 = this.getView();
        if(view0 != null) {
            view0.post(new b(this, v));
        }
    }

    @Override  // e.j$d
    public String[] a() {
        Context context0 = this.M0();
        String[] arr_s = new String[p.N.length];
        for(int v = 0; v < p.N.length; ++v) {
            arr_s[v] = p.N[v].c(context0).toString();
        }
        return arr_s;
    }

    @Override  // e.j
    protected View h1(int v) {
        return new e.j.k(this, p.N[v].c(this.M0()));
    }

    @Override  // e.j
    protected void k1(int v) {
        class a extends y {
            final p c;

            a(Context context0) {
                super(context0);
            }

            @Override  // a.y
            protected void a(String[] arr_s) {
                p.this.M.b();
                p.this.M = null;
                if(arr_s != null && arr_s.length != 0) {
                    p.this.h().putStringArray("ssids", arr_s);
                    e.z z0 = new e.z().u(arr_s);
                    p.this.N(z0, 12);
                    return;
                }
                e.y y0 = new e.y().J(p.this.u(0x7F060233), null, null, 3, 0x20);  // string:wifi_ssid "SSID"
                p.this.N(y0, 13);
            }
        }

        int v2;
        e.z z0;
        c.d d0;
        int v1;
        p p0;
        h[] arr_h = p.N;
        switch(arr_h[v].z) {
            case 1: {
                this.P(new p(), 2);
                this.e0(0x7F0601A6);  // string:select_first_condition "Please select the first condition"
                return;
            }
            case 2: {
                this.P(new p(), 4);
                this.e0(0x7F0601A6);  // string:select_first_condition "Please select the first condition"
                return;
            }
            case 3: {
                p0 = new p();
                v1 = 6;
                this.P(p0, v1);
                return;
            }
            case 26: {
                d0 = new e();
                break;
            }
            case 27: {
                f.j j0 = new f.j();
                z0 = new n0().w(this.u(0x7F060203), j0.g(), j0.h());  // string:time_from "From"
                v2 = 7;
                this.N(z0, v2);
                return;
            }
            case 28: {
                z0 = new b0().v(f.j.o(), 0);
                v2 = 9;
                this.N(z0, v2);
                return;
            }
            case 29: {
                z0 = new b0().v(f.j.e(this.f()), 0);
                v2 = 10;
                this.N(z0, v2);
                return;
            }
            case 30: {
                z0 = new b0().v(f.j.j(), 0);
                v2 = 11;
                this.N(z0, v2);
                return;
            }
            case 0x20: {
                p0 = new u0().C1(this.u(0x7F0601A8));  // string:select_focused_app "Select focused app"
                v1 = 14;
                this.P(p0, v1);
                return;
            }
            case 33: {
                z0 = new e.z().u(new CharSequence[]{this.u(0x7F060169), this.u(0x7F060168)});  // string:orientation_portrait "Portrait"
                v2 = 15;
                this.N(z0, v2);
                return;
            }
            case 37: {
                d0 = new c.d();
                break;
            }
            case 38: {
                if(!ApplicationMain.isModuleActivated()) {
                    this.e0(0x7F060155);  // string:module_not_activated "Module not activated yet(or not updated)! Please activate 
                                          // this module in Xposed installer, and reboot."
                    return;
                }
                this.M = new a(this, this.f());
                return;
            }
            default: {
                this.I1(arr_h[v]);
                this.L();
                return;
            }
        }
        this.N(d0, 1);
    }

    @Override  // e.j
    public void onDestroy() {
        super.onDestroy();
        y y0 = this.M;
        if(y0 != null) {
            y0.b();
            this.M = null;
        }
    }
}


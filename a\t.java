package a;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import f.l;
import f.v;

public class t {
    static class b {
        private c[] a;
        private int[] b;
        private int c;

        private b() {
            this.a = null;
            this.b = null;
            this.c = 0;
        }

        b(a t$a0) {
        }

        void b(String s, int v, c t$c0) {
            int v1 = u.e(s);
            if(v1 < 0) {
                return;
            }
            if(this.a == null) {
                c[] arr_t$c = new c[u.d.length];
                this.a = arr_t$c;
                this.b = new int[arr_t$c.length];
            }
            c[] arr_t$c1 = this.a;
            if(arr_t$c1[v1] != null) {
                --this.c;
            }
            arr_t$c1[v1] = t$c0;
            this.b[v1] = v;
            ++this.c;
        }

        void c(int v, boolean z) {
            if(this.c <= 0) {
                return;
            }
            for(int v1 = 0; true; ++v1) {
                int[] arr_v = this.b;
                if(v1 >= arr_v.length) {
                    break;
                }
                if(arr_v[v1] == v) {
                    c[] arr_t$c = this.a;
                    if(arr_t$c[v1] != null) {
                        arr_t$c[v1].a(z);
                    }
                }
            }
        }

        void d(String s) {
            if(this.a == null) {
                return;
            }
            int v = u.e(s);
            if(v >= 0) {
                c[] arr_t$c = this.a;
                c t$c0 = arr_t$c[v];
                arr_t$c[v] = null;
                if(t$c0 != null) {
                    --this.c;
                }
            }
        }
    }

    public interface c {
        void a(boolean arg1);
    }

    private final Context a;
    private int b;
    private int c;
    private final b d;
    private BroadcastReceiver e;
    private static final String f;

    static {
        t.f = l.k + "STATE_CHANGED";
    }

    public t(Context context0) {
        this.b = 0;
        this.c = 0;
        this.d = new b(null);
        this.e = null;
        this.a = context0;
    }

    public void e() {
        if(this.e != null) {
            try {
                s0.B(this.a, 21);
                this.a.unregisterReceiver(this.e);
                this.e = null;
            }
            catch(Throwable unused_ex) {
            }
        }
    }

    public void f() {
        class a extends BroadcastReceiver {
            final t a;

            @Override  // android.content.BroadcastReceiver
            public void onReceive(Context context0, Intent intent0) {
                if(t.f.equals(intent0.getAction())) {
                    int v = intent0.getIntExtra("bits", -1);
                    if(v >= 0 && t.this.b != v) {
                        int v1 = t.this.b ^ v;
                        t.this.b = v;
                        if(t.this.d.c <= 0) {
                            return;
                        }
                        int v2 = 1;
                        for(int v3 = 0; v3 < 0x1F; ++v3) {
                            if((v1 & v2) != 0) {
                                try {
                                    v1 ^= v2;
                                    t.this.d.c(v2, (v & v2) != 0);
                                }
                                catch(Throwable throwable0) {
                                    v.d(throwable0);
                                    return;
                                }
                            }
                            v2 <<= 1;
                            if(v1 == 0) {
                                break;
                            }
                        }
                    }
                }
            }
        }

        a t$a0 = new a(this);
        this.e = t$a0;
        try {
            IntentFilter intentFilter0 = new IntentFilter(t.f);
            this.a.registerReceiver(t$a0, intentFilter0);
            s0.B(this.a, 21);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    public void g() {
        this.c = 0;
        this.k();
    }

    public boolean h(int v, boolean z) {
        int v1 = t.m(v);
        return v1 > 0 ? (v1 & this.b) != 0 : z;
    }

    public static boolean i(int v) {
        return t.m(v) > 0;
    }

    public void j(String s, int v, c t$c0) {
        int v1 = t.m(v);
        if(v1 <= 0) {
            return;
        }
        this.d.b(s, v1, t$c0);
    }

    private void k() {
        int v = this.b;
        if(this.c == v) {
            return;
        }
        Intent intent0 = new Intent(t.f);
        intent0.putExtra("bits", v);
        try {
            intent0.setPackage("com.android.systemui");
            this.a.sendBroadcast(intent0);
            intent0.setPackage(l.j);
            this.a.sendBroadcast(intent0);
            this.c = v;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    public void l(int v, boolean z) {
        int v1 = t.m(v);
        if(v1 <= 0) {
            return;
        }
        this.b = z ? v1 | this.b : ~v1 & this.b;
        this.k();
    }

    private static int m(int v) {
        switch(v) {
            case 29: {
                return 1;
            }
            case 30: {
                return 4;
            }
            case 0x1F: {
                return 0x20;
            }
            case 0x20: {
                return 8;
            }
            case 33: {
                return 16;
            }
            case 34: {
                return 0x80;
            }
            case 35: {
                return 0x100;
            }
            case 73: {
                return 2;
            }
            case 97: {
                return 0x40;
            }
            default: {
                return -1;
            }
        }
    }

    public void n(String s) {
        this.d.d(s);
    }
}


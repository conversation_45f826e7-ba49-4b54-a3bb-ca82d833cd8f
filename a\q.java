package a;

import f.v;

public final class q {
    public static int a(float f) {
        return f == 0.0f ? 0 : Float.floatToIntBits(f);
    }

    public static float b(int v) {
        return v == 0 ? 0.0f : Float.intBitsToFloat(v);
    }

    public static int c(int v) {
        switch(v) {
            case 36: {
                return 0;
            }
            case 37: {
                return 8;
            }
            case 38: {
                return 9;
            }
            default: {
                v.d(new IllegalArgumentException("Unknown flag: " + v));
                return v;
            }
        }
    }
}


package f;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

public class p extends h {
    protected final File E;
    private boolean F;

    public p(String s) {
        this.F = false;
        this.E = new File(s);
    }

    public boolean E() {
        byte[] arr_b = q.c(this.E.getPath()).j();
        super.i(arr_b);
        return arr_b != null && arr_b.length != 0;
    }

    public p F() {
        return this.G(false);
    }

    public p G(boolean z) {
        this.F = this.E.exists() && z;
        super.j();
        return this;
    }

    public File H() {
        return this.E;
    }

    public long I() {
        return this.E.lastModified();
    }

    protected void J(byte[] arr_b, int v) {
        FileOutputStream fileOutputStream0;
        if(arr_b == null) {
            return;
        }
        File file0 = this.E.getParentFile();
        if(file0 == null || file0.exists() || file0.mkdir() && file0.setExecutable(true, false)) {
            try {
                fileOutputStream0 = null;
                fileOutputStream0 = new FileOutputStream(this.E, this.F);
                fileOutputStream0.write(arr_b, 0, v);
                this.F = true;
            }
            catch(IOException iOException0) {
                v.c(iOException0.toString());
                this.l();
            }
            if(fileOutputStream0 != null) {
                try {
                    fileOutputStream0.close();
                }
                catch(IOException iOException1) {
                    v.d(iOException1);
                }
            }
        }
    }

    @Override  // f.h
    public void i(byte[] arr_b) {
        throw new UnsupportedOperationException("Use beginRead() instead!");
    }

    @Override  // f.h
    public h j() {
        return this.F();
    }

    @Override  // f.h
    public void m() {
        this.l();
    }

    @Override  // f.h
    public void n() {
        this.J(this.p(), this.q());
        this.l();
        this.E.setReadable(true, false);
        this.F = false;
    }

    @Override  // f.h
    protected byte[] u(int v) {
        byte[] arr_b = this.p();
        this.J(arr_b, this.q());
        this.v(0);
        return v <= arr_b.length ? arr_b : new byte[(v + 0x4000) / 0x4000 * 0x4000];
    }
}


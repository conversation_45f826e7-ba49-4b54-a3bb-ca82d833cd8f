package com.jozein.xedgepro.xposed;

import android.accessibilityservice.AccessibilityService;
import android.content.Context;
import android.graphics.Rect;
import android.os.Build.VERSION;
import android.os.Handler;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityManager;
import android.view.accessibility.AccessibilityNodeInfo;
import de.robv.android.xposed.XC_MethodHook.MethodHookParam;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedHelpers;
import f.g0;
import f.v;
import java.lang.reflect.Field;

class v3 extends AccessibilityService {
    interface d {
        void a(boolean arg1, String arg2);
    }

    private final AccessibilityManager A;
    private final Object B;
    private static boolean C = false;
    private static boolean D = false;
    private static final Rect E;
    private final w1 z;

    static {
        v3.E = new Rect();
    }

    v3(w1 w10) {
        this.z = w10;
        Context context0 = w10.T1();
        AccessibilityManager accessibilityManager0 = (AccessibilityManager)context0.getSystemService("accessibility");
        this.A = accessibilityManager0;
        Object object0 = v3.d(accessibilityManager0);
        this.B = object0;
        XposedHelpers.setIntField(this, "mConnectionId", v3.e(object0));
        this.attachBaseContext(context0);
    }

    static boolean a() [...] // 潜在的解密器

    void c(float f, float f1, d v3$d0) {
        class c extends g0 {
            private boolean D;
            private boolean E;
            final float F;
            final float G;
            final d H;
            final v3 I;

            c(Handler handler0, int v, long v1, float f, float f1, d v3$d0) {
                this.F = f;
                this.G = f1;
                this.H = v3$d0;
                super(handler0, v, v1);
                this.D = false;
                this.E = false;
            }

            @Override  // f.g0
            protected boolean a(int v) {
                AccessibilityNodeInfo accessibilityNodeInfo0 = v3.this.getRootInActiveWindow();
                if(accessibilityNodeInfo0 == null) {
                    return true;
                }
                this.D = true;
                a q3$a0 = q3.f(accessibilityNodeInfo0, ((int)this.F), ((int)this.G));
                if(q3$a0 != null) {
                    if(!q3$a0.B.contains("Layout") && !q3$a0.B.contains("WebView")) {
                        this.E = true;
                        String s = q3$a0.b();
                        this.H.a(true, s);
                        return false;
                    }
                    return true;
                }
                this.E = true;
                this.H.a(true, null);
                return false;
            }

            @Override  // f.g0
            protected void b() {
                try {
                    v3.this.i(false);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
                if(!this.E) {
                    this.H.a(this.D, null);
                }
            }
        }

        if(Build.VERSION.SDK_INT >= 26 && this.g(f, f1)) {
            try {
                if(this.A.isEnabled()) {
                    AccessibilityNodeInfo accessibilityNodeInfo0 = this.getRootInActiveWindow();
                    if(accessibilityNodeInfo0 == null) {
                        v3$d0.a(false, null);
                        return;
                    }
                    a q3$a0 = q3.f(accessibilityNodeInfo0, ((int)f), ((int)f1));
                    v3$d0.a(true, (q3$a0 == null ? null : q3$a0.b()));
                    return;
                }
                this.i(true);
                new c(this, this.z.U1(), 3, 150L, f, f1, v3$d0).e();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                v3$d0.a(false, null);
            }
            return;
        }
        v3$d0.a(false, null);
    }

    private static Object d(AccessibilityManager accessibilityManager0) {
        try {
            Object object0 = XposedHelpers.getObjectField(accessibilityManager0, "mService");
            if(object0 != null) {
                return object0;
            }
            Object object1 = XposedHelpers.getObjectField(accessibilityManager0, "mLock");
            return XposedHelpers.callMethod(accessibilityManager0, "getServiceLocked", new Object[0]);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return g3.h();
        }
    }

    private static int e(Object object0) {
        Object object2;
        int v = Build.VERSION.SDK_INT;
        if(v >= 21) {
            if(v >= 26) {
                return XposedHelpers.getIntField(XposedHelpers.callMethod(object0, "getInteractionBridge", new Object[0]), "mConnectionId");
            }
            synchronized(XposedHelpers.getObjectField(object0, "mLock")) {
                object2 = XposedHelpers.callMethod(object0, "getInteractionBridgeLocked", new Object[0]);
            }
            return XposedHelpers.getIntField(object2, "mConnectionId");
        }
        synchronized(XposedHelpers.getObjectField(object0, "mLock")) {
            object2 = XposedHelpers.callMethod(object0, "getQueryBridge", new Object[0]);
        }
        return XposedHelpers.getIntField(object2, "mId");
    }

    static void f(ClassLoader classLoader0) {
        class com.jozein.xedgepro.xposed.v3.a extends XC_MethodHook {
            com.jozein.xedgepro.xposed.v3.a() {
                super();
            }

            // 去混淆评级： 低(30)
            protected void beforeHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
            }
        }


        class b extends XC_MethodHook {
            b() {
                super();
            }

            // 去混淆评级： 低(30)
            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
            }
        }

        com.jozein.xedgepro.xposed.v3.a v3$a0;
        String s;
        g g0;
        int v = Build.VERSION.SDK_INT;
        if(v >= 26) {
            try {
                if(v >= 30) {
                    g0 = new g("com.android.server.accessibility.AccessibilityUserState", classLoader0);
                    s = "getClientStateLocked";
                    v3$a0 = new com.jozein.xedgepro.xposed.v3.a();
                }
                else {
                    g0 = new g("com.android.server.accessibility.AccessibilityManagerService$UserState", classLoader0);
                    s = "getClientState";
                    v3$a0 = new b();
                }
                g0.o(s, v3$a0);
                v3.D = true;
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    private boolean g(float f, float f1) {
        if(Build.VERSION.SDK_INT >= 24) {
            try {
                return u3.h(this.z.g2().Q(f, f1));
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        return true;
    }

    private void h(boolean z) {
        v3.C = z;
        if(Build.VERSION.SDK_INT >= 0x1F && !v3.D) {
            try {
                Object object0 = XposedHelpers.getObjectField(this.B, "mUiAutomationManager");
                Field field0 = XposedHelpers.findField(object0.getClass(), "mUiAutomationFlags");
                int v = field0.getInt(object0);
                field0.setInt(object0, (z ? v | 2 : v & -3));
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    private void i(boolean z) {
        this.h(z);
        if(Build.VERSION.SDK_INT >= 0x1F) {
            Object[] arr_object = {XposedHelpers.callMethod(this.B, "getCurrentUserState", new Object[0])};
            XposedHelpers.callMethod(this.B, "scheduleUpdateClientsIfNeeded", arr_object);
            return;
        }
        synchronized(XposedHelpers.getObjectField(this.B, "mLock")) {
            Object[] arr_object1 = {XposedHelpers.callMethod(this.B, "getCurrentUserStateLocked", new Object[0])};
            XposedHelpers.callMethod(this.B, "scheduleUpdateClientsIfNeededLocked", arr_object1);
        }
    }

    @Override  // android.accessibilityservice.AccessibilityService
    public void onAccessibilityEvent(AccessibilityEvent accessibilityEvent0) {
    }

    @Override  // android.accessibilityservice.AccessibilityService
    public void onInterrupt() {
    }
}


package a;

import com.jozein.xedgepro.service.SuperTileService;
import f.l;
import f.p;

public class u {
    static class a {
    }

    public static class b extends u {
        private final SuperTileService[] f;

        private b() {
            super(null);
            this.f = new SuperTileService[u.d.length];
        }

        b(a u$a0) {
        }

        boolean k() {
            for(int v = 0; v < u.d.length; ++v) {
                if(this.f[v] != null) {
                    return true;
                }
            }
            return false;
        }

        public void l(SuperTileService superTileService0) {
            int v = u.e(superTileService0.getClass().getName());
            if(v >= 0) {
                this.f[v] = superTileService0;
            }
        }

        private void m() {
            p p0 = this.c();
            p0.F();
            p0.w(u.d.length).C();
            for(int v = 0; true; ++v) {
                String[] arr_s = u.d;
                if(v >= arr_s.length) {
                    break;
                }
                p0.y(arr_s[v]).x(this.b[v]).C();
            }
            p0.n();
        }

        public void n(int v, d b$d0) {
            this.b[v] = b$d0;
            this.m();
            SuperTileService superTileService0 = this.f[v];
            if(superTileService0 != null) {
                superTileService0.f();
            }
        }

        public void o(SuperTileService superTileService0) {
            int v = u.e(superTileService0.getClass().getName());
            if(v >= 0) {
                this.f[v] = null;
            }
        }

        public void p(String s) {
            int v = u.e(s);
            if(v >= 0) {
                this.f[v] = null;
            }
        }

        void q() {
            boolean z = true;
            for(int v = 0; v < u.d.length; ++v) {
                if(this.f[v] != null) {
                    if(z) {
                        this.g();
                        z = false;
                    }
                    this.f[v].f();
                }
            }
        }
    }

    private final d a;
    final d[] b;
    private static final String c;
    static final String[] d;
    private static b e;

    static {
        u.c = l.m + "tiles";
        u.d = SuperTileService.F;
        u.e = null;
    }

    private u() {
        this.a = new d(a.b.r());
        this.b = new d[u.d.length];
        this.g();
    }

    u(a u$a0) {
    }

    public d a(int v) {
        return this.b[v];
    }

    public d b(String s) {
        int v = u.e(s);
        return v < 0 ? this.a : this.b[v];
    }

    p c() {
        return new p(u.c);
    }

    public static b d() {
        synchronized(b.class) {
            if(u.e == null) {
                u.e = new b(null);
            }
            return u.e;
        }
    }

    static int e(String s) {
        int v = s.length();
        int v1 = s.charAt(v - 1);
        int v2 = s.charAt(v - 2);
        for(int v3 = 0; v3 < u.d.length; ++v3) {
            String s1 = u.d[v3];
            int v4 = s1.length();
            if(s1.charAt(v4 - 1) == v1 && s1.charAt(v4 - 2) == v2) {
                return v3;
            }
        }
        return -1;
    }

    void f(String s, d b$d0) {
        int v = u.e(s);
        if(v >= 0) {
            this.b[v] = b$d0;
        }
    }

    void g() {
        for(int v1 = 0; v1 < u.d.length; ++v1) {
            this.b[v1] = this.a;
        }
        p p0 = this.c();
        if(p0.E()) {
            int v2 = p0.h();
            for(int v = 0; v < v2 && p0.t(); ++v) {
                this.f(p0.a(), ((d)p0.g()));
            }
        }
        p0.m();
    }

    public String h(int v) {
        return u.d[v];
    }

    public int i() {
        return this.b.length;
    }

    public static void j() {
        b u$b0;
        synchronized(b.class) {
            if(u.e != null && !u.e.k()) {
                u.e = null;
            }
            u$b0 = u.e;
        }
        if(u$b0 != null) {
            u$b0.q();
        }
    }

    @Override
    public String toString() {
        StringBuilder stringBuilder0 = new StringBuilder(0x100);
        stringBuilder0.append('[');
        stringBuilder0.append(u.d.length);
        stringBuilder0.append(" tiles ");
        for(int v = 0; v < u.d.length; ++v) {
            stringBuilder0.append('[');
            stringBuilder0.append(u.d[v]);
            stringBuilder0.append(' ');
            stringBuilder0.append(this.b[v].K);
            stringBuilder0.append("] ");
        }
        stringBuilder0.append(']');
        return stringBuilder0.toString();
    }
}


package d;

import a.b;
import a.f;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import e.j.d;
import e.j.g;
import e.j.i;
import e.j.k;
import e.m;
import f.z;
import java.util.List;

public class j extends c implements a, d {
    private f M;
    private List N;
    private static final int[] O;
    private static final int[] P;

    static {
        j.O = f.F;
        j.P = f.E;
    }

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F06007D);  // string:app_state "App state"
        this.S(0x7F04002C, (View view0) -> if(!this.M.i()) {
            this.N(new m().u(this.u(0x7F06008D)), 2);  // string:check_clear_all "Are you sure to clear all?"
        });
    }

    @Override  // e.j
    protected int B0() {
        this.M = f.g();
        if(this.N == null) {
            this.N = z.K(this.f()).Q(false);
        }
        return this.N.size();
    }

    @Override  // d.c
    protected void C1(Bundle bundle0, int v) {
        switch(v) {
            case 1: {
                b b0 = (b)bundle0.getParcelable("result");
                if(b0 != null) {
                    int v2 = this.E0();
                    int v3 = this.F0();
                    try {
                        String s = ((f.c0.d)this.N.get(v2)).i();
                        this.M.j(this.f(), s, j.P[v3], b0);
                        ((g)this.L0(v2)).setHighLight(this.M.h(s));
                        g j$g0 = (g)this.K0(v3);
                        j$g0.setImageDrawable(this.D0(b0));
                        j$g0.setSubText(b0.n(j$g0.getContext()));
                    }
                    catch(Throwable throwable0) {
                        this.g0(throwable0);
                    }
                    return;
                }
                break;
            }
            case 2: {
                if(bundle0.getBoolean("result", false)) {
                    try {
                        Context context0 = this.f();
                        this.M.e(context0);
                        int v4 = this.I0();
                        for(int v1 = 0; v1 < j.O.length; ++v1) {
                            g j$g1 = (g)this.K0(v1);
                            if(j$g1 != null) {
                                b b1 = this.M.f(((f.c0.d)this.N.get(v4)).i(), j.P[v1]);
                                j$g1.setSubText(b1.n(context0));
                                j$g1.setImageDrawable(this.D0(b1));
                            }
                        }
                        this.t1();
                        this.e0(0x7F060195);  // string:saved "Saved"
                    }
                    catch(Throwable throwable0) {
                        this.g0(throwable0);
                    }
                    return;
                }
                break;
            }
        }
    }

    // 检测为 Lambda 实现
    private void J1(View view0) [...]

    @Override  // e.j
    protected boolean N0(int v) {
        return true;
    }

    @Override  // e.j$d
    public String[] a() {
        return z.L(this.N);
    }

    @Override  // e.j
    protected void g1(Object object0) {
        this.N = (List)object0;
    }

    @Override  // e.j
    protected View h1(int v) {
        f.c0.d c0$d0 = (f.c0.d)this.N.get(v);
        String s = c0$d0.i();
        View view0 = new g(this, c0$d0.e(), c0$d0.f(), s);
        if(!c0$d0.m()) {
            ((k)view0).e();
        }
        ((k)view0).setHighLight(this.M.h(s));
        return view0;
    }

    @Override  // e.j
    protected View i1(int v) {
        b b0 = this.M.f(((f.c0.d)this.N.get(this.I0())).i(), j.P[v]);
        return new g(this, this.u(j.O[v]), b0.n(this.M0()), this.D0(b0));
    }

    @Override  // e.j
    protected void k1(int v) {
        this.y1(v, j.P.length);
    }

    @Override  // e.j
    protected i m1() {
        List list0 = this.m(f.c0.d.class);
        this.N = list0;
        return list0 != null ? null : new e.j.i.d(false);
    }

    @Override  // e.j
    protected void o1(int v) {
        this.P(new d.d().J1(6, this.u(0x7F06007D), ((f.c0.d)this.N.get(this.I0())).i(), this.u(j.O[v])), 1);  // string:app_state "App state"
    }

    @Override  // e.j
    public void onSaveInstanceState(Bundle bundle0) {
        super.onSaveInstanceState(bundle0);
        this.Z(this.N);
    }

    @Override  // e.j
    protected boolean p1(int v) {
        String s = ((f.c0.d)this.N.get(this.I0())).i();
        this.F1(this.M.f(s, j.P[v]), 1, 6);
        return true;
    }
}


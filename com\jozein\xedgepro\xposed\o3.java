package com.jozein.xedgepro.xposed;

import de.robv.android.xposed.XposedHelpers;
import java.lang.reflect.Method;

class o3 {
    static final Class a;
    private static final Method[] b;

    static {
        o3.a = XposedHelpers.findClass("android.os.SystemProperties", o3.class.getClassLoader());
        o3.b = new Method[5];
    }

    private static Method a(String s, Class class0) {
        return XposedHelpers.findMethodExact(o3.a, s, new Class[]{String.class, class0});
    }

    static void b(String s, String s1) {
        Method[] arr_method = o3.b;
        if(arr_method[4] == null) {
            arr_method[4] = o3.a("set", String.class);
        }
        arr_method[4].invoke(null, s, s1);
    }
}


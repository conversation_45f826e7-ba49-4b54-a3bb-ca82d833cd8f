package d;

import android.os.Bundle;
import android.view.View;
import e.j.d;
import e.j.g;
import e.j.i;
import e.j.k;
import e.j;
import f.z;
import java.util.List;

public abstract class a extends j implements d {
    protected List M;

    @Override  // e.j
    protected int B0() {
        if(this.M == null) {
            this.M = z.K(this.f()).Q(false);
        }
        return this.M.size();
    }

    protected k B1(int v) {
        f.c0.d c0$d0 = (f.c0.d)this.M.get(v);
        k j$k0 = new g(this, c0$d0.d(), c0$d0.f(), c0$d0.i());
        if(!c0$d0.m()) {
            j$k0.e();
        }
        return j$k0;
    }

    @Override  // e.j$d
    public final String[] a() {
        return z.L(this.M);
    }

    @Override  // e.j
    protected void g1(Object object0) {
        this.M = (List)object0;
    }

    @Override  // e.j
    protected View h1(int v) {
        return this.B1(v);
    }

    @Override  // e.j
    protected i m1() {
        List list0 = this.m(f.c0.d.class);
        this.M = list0;
        return list0 != null ? null : new e.j.i.d(false);
    }

    @Override  // e.j
    public void onSaveInstanceState(Bundle bundle0) {
        if(this.M != null && this.s() == null) {
            this.Z(this.M);
        }
        super.onSaveInstanceState(bundle0);
    }
}


package a;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import f.l;
import f.v;

public abstract class r implements l {
    private BroadcastReceiver A;
    private final String z;

    r(String s) {
        this.A = null;
        this.z = s;
    }

    protected abstract void b(Intent arg1);

    protected void c(Context context0, Intent intent0) {
        intent0.setAction(this.z).setPackage("android");
        context0.sendBroadcast(intent0);
    }

    public void d(Context context0, Handler handler0) {
        class a extends BroadcastReceiver {
            final r a;

            @Override  // android.content.BroadcastReceiver
            public void onReceive(Context context0, Intent intent0) {
                try {
                    r.this.b(intent0);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        try {
            a r$a0 = new a(this);
            this.A = r$a0;
            context0.registerReceiver(r$a0, new IntentFilter(this.z), null, handler0);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }
}


package f;

import a.b;

public class e implements s {
    private int A;
    public static final byte[] B;
    private byte[] z;

    static {
        e.B = new byte[0];
    }

    public e() {
        this.z = null;
        this.A = 0;
    }

    @Override  // f.s
    public String a() {
        if(this.z.length - this.A < 4) {
            v.c("Out of range.");
            return null;
        }
        int v = this.q();
        if(v == -1) {
            return null;
        }
        if(v == 0) {
            return "";
        }
        if(v > -1) {
            int v1 = this.A;
            byte[] arr_b = this.z;
            if(v + v1 <= arr_b.length) {
                this.A = v1 + v;
                return new String(arr_b, v1, v, p0.z);
            }
            v.c("Error read string: data lost.");
            int v2 = this.A;
            this.A = this.z.length;
            return new String(this.z, v2, this.z.length - v2, p0.z);
        }
        this.A -= 4;
        v.c((v >= -2 ? "Error read string! Original data: int." : "Invalid data."));
        return null;
    }

    @Override  // f.s
    public s b(String s) {
        return this.w(s);
    }

    @Override  // f.s
    public s c(String s) {
        return this.v(s);
    }

    @Override  // f.s
    public s d(int v) {
        return this.t(v);
    }

    @Override  // f.s
    public String e() {
        return this.a();
    }

    @Override  // f.s
    public s f(b b0) {
        return this.u(b0);
    }

    @Override  // f.s
    public b g() {
        return b.s(this);
    }

    @Override  // f.s
    public int h() {
        if(this.z.length - this.A < 8) {
            v.d(new IndexOutOfBoundsException("pos=" + this.A + ", length=" + this.z.length));
            return 0;
        }
        int v = this.q();
        if(v != -2) {
            this.A -= 4;
            v.c((v >= -2 ? "Error read int! Original data: string." : "Invalid data."));
            return 0;
        }
        return this.q();
    }

    public void i(byte[] arr_b) {
        if(arr_b == null) {
            arr_b = e.B;
        }
        this.z = arr_b;
        this.A = 0;
    }

    public e j() {
        if(this.z == null) {
            this.z = new byte[0x4000];
        }
        this.A = 0;
        return this;
    }

    private void k(int v) {
        int v1 = this.A;
        byte[] arr_b = this.z;
        if(v1 + v >= arr_b.length) {
            this.z = this.r(v + v1, arr_b, v1);
        }
    }

    protected void l() {
        this.z = e.B;
        this.A = 0;
    }

    public void m() {
        this.z = e.B;
        this.A = 0;
    }

    public byte[] n() {
        int v = this.A;
        byte[] arr_b = new byte[v];
        System.arraycopy(this.z, 0, arr_b, 0, v);
        this.z = null;
        this.A = 0;
        return arr_b;
    }

    protected byte[] o() {
        return this.z;
    }

    protected int p() {
        return this.A;
    }

    private int q() {
        int v = this.A;
        this.A = v + 4;
        return (this.z[v + 3] & 0xFF) << 24 | (this.z[v] & 0xFF | (this.z[v + 1] & 0xFF) << 8 | (this.z[v + 2] & 0xFF) << 16);
    }

    protected byte[] r(int v, byte[] arr_b, int v1) {
        byte[] arr_b1 = new byte[(v + 0x4000) / 0x4000 * 0x4000];
        System.arraycopy(this.z, 0, arr_b1, 0, this.A);
        return arr_b1;
    }

    protected void s(int v) {
        this.A = v;
    }

    public e t(int v) {
        this.x(-2);
        this.x(v);
        return this;
    }

    public e u(b b0) {
        b0.y(this);
        return this;
    }

    public e v(String s) {
        if(s == null) {
            this.x(-1);
            return this;
        }
        if(s.length() == 0) {
            this.x(0);
            return this;
        }
        byte[] arr_b = s.getBytes(p0.z);
        this.x(arr_b.length);
        this.k(arr_b.length);
        byte[] arr_b1 = this.z;
        for(int v = 0; v < arr_b.length; ++v) {
            int v1 = this.A;
            this.A = v1 + 1;
            arr_b1[v1] = arr_b[v];
        }
        return this;
    }

    public e w(String s) {
        return this.v(s);
    }

    private void x(int v) {
        this.k(4);
        byte[] arr_b = this.z;
        int v1 = this.A;
        this.A = v1 + 1;
        arr_b[v1] = (byte)(v & 0xFF);
        this.A = v1 + 2;
        arr_b[v1 + 1] = (byte)(v >> 8 & 0xFF);
        this.A = v1 + 3;
        arr_b[v1 + 2] = (byte)(v >> 16 & 0xFF);
        this.A = v1 + 4;
        arr_b[v1 + 3] = (byte)(v >> 24 & 0xFF);
    }
}


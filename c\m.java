package c;

import a.b.u1;
import android.app.Activity;
import android.app.AlertDialog.Builder;
import android.app.Dialog;
import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.InputFilter.LengthFilter;
import android.text.InputFilter;
import android.widget.CheckBox;
import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import e.j0.b;
import e.y;
import g.x;

public class m extends b {
    private int C;
    private EditText D;

    @Override  // android.app.DialogFragment
    public Dialog onCreateDialog(Bundle bundle0) {
        class a implements CompoundButton.OnCheckedChangeListener {
            final m a;

            @Override  // android.widget.CompoundButton$OnCheckedChangeListener
            public void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
                m m0 = m.this;
                if(z) {
                    m.w(m0, 1);
                    return;
                }
                m.v(m0, -2);
            }
        }


        class c.m.b implements CompoundButton.OnCheckedChangeListener {
            final m a;

            @Override  // android.widget.CompoundButton$OnCheckedChangeListener
            public void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
                m m0 = m.this;
                if(z) {
                    m.w(m0, 2);
                    return;
                }
                m.v(m0, -3);
            }
        }


        class c implements CompoundButton.OnCheckedChangeListener {
            final m a;

            @Override  // android.widget.CompoundButton$OnCheckedChangeListener
            public void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
                m m0 = m.this;
                if(z) {
                    m.w(m0, 4);
                    return;
                }
                m.v(m0, -5);
            }
        }


        class d implements DialogInterface.OnClickListener {
            final m z;

            @Override  // android.content.DialogInterface$OnClickListener
            public void onClick(DialogInterface dialogInterface0, int v) {
                u1 b$u10 = new u1(m.this.C, m.this.D.getText().toString());
                m.this.q("result", b$u10);
            }
        }

        Bundle bundle1 = this.e();
        this.C = bundle1.getInt("defaults", -1);
        CharSequence charSequence0 = bundle1.getCharSequence("content", "");
        Activity activity0 = this.getActivity();
        LinearLayout linearLayout0 = new LinearLayout(activity0);
        boolean z = true;
        linearLayout0.setOrientation(1);
        x x0 = this.g();
        linearLayout0.setPadding(x0.f, x0.f, x0.f, x0.f);
        TextView textView0 = new TextView(activity0);
        textView0.setText(this.k(0x7F060206));  // string:toast_description "%t: time. %w: day of week. %d: date. %b: battery level."
        linearLayout0.addView(textView0);
        EditText editText0 = new EditText(activity0);
        this.D = editText0;
        this.r(editText0);
        this.D.setInputType(0x20001);
        this.D.setHint(0x7F060162);  // string:notification_hint "Notification content"
        this.D.setFilters(new InputFilter[]{new InputFilter.LengthFilter(0x400)});
        this.D.setText(charSequence0);
        this.D.setSelection(charSequence0.length());
        linearLayout0.addView(this.D);
        y.L(x0, textView0, this.D);
        CheckBox checkBox0 = new CheckBox(activity0);
        checkBox0.setText(0x7F0601BE);  // string:sound "Sound"
        checkBox0.setChecked((this.C & 1) != 0);
        checkBox0.setOnCheckedChangeListener(new a(this));
        linearLayout0.addView(checkBox0);
        CheckBox checkBox1 = new CheckBox(activity0);
        checkBox1.setText(0x7F060218);  // string:vibrate "Vibrate"
        checkBox1.setChecked((this.C & 2) != 0);
        checkBox1.setOnCheckedChangeListener(new c.m.b(this));
        linearLayout0.addView(checkBox1);
        CheckBox checkBox2 = new CheckBox(activity0);
        checkBox2.setText(0x7F060148);  // string:light "Light"
        if((this.C & 4) == 0) {
            z = false;
        }
        checkBox2.setChecked(z);
        checkBox2.setOnCheckedChangeListener(new c(this));
        linearLayout0.addView(checkBox2);
        return new AlertDialog.Builder(activity0).setTitle(0x7F060040).setView(linearLayout0).setPositiveButton(0x104000A, new d(this)).setNegativeButton(0x1040000, b.B).create();  // string:action_notify "Notification"
    }

    @Override  // e.j0$b
    public void onSaveInstanceState(Bundle bundle0) {
        super.onSaveInstanceState(bundle0);
        Bundle bundle1 = this.e();
        bundle1.putCharSequence("content", this.D.getText());
        bundle1.putInt("defaults", this.C);
    }

    static int v(m m0, int v) {
        int v1 = v & m0.C;
        m0.C = v1;
        return v1;
    }

    static int w(m m0, int v) {
        int v1 = v | m0.C;
        m0.C = v1;
        return v1;
    }

    public m z(CharSequence charSequence0, int v) {
        Bundle bundle0 = this.e();
        bundle0.putCharSequence("content", charSequence0);
        bundle0.putInt("defaults", v);
        return this;
    }
}


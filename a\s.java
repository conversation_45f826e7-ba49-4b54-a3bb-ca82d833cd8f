package a;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Handler;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import android.widget.Toast;
import f.d0;
import f.j;
import f.l;
import f.p;
import f.v;
import java.text.DateFormat;
import java.util.ArrayList;
import java.util.Date;

public abstract class s implements Parcelable, l {
    class a implements Parcelable.Creator {
        a() {
            super();
        }

        public s a(Parcel parcel0) {
            return s.f(new d0(parcel0));
        }

        public s[] b(int v) {
            return new s[v];
        }

        @Override  // android.os.Parcelable$Creator
        public Object createFromParcel(Parcel parcel0) {
            return this.a(parcel0);
        }

        @Override  // android.os.Parcelable$Creator
        public Object[] newArray(int v) {
            return this.b(v);
        }
    }

    public static class b extends s {
        public int F;
        public int G;

        public b() {
            j j0 = new j();
            this.z = false;
            this.F = 0;
            this.G = 0;
            this.A = j0.g();
            this.B = j0.h();
            this.C = a.b.r();
            this.D = null;
        }

        private b(boolean z, f.s s0) {
            this.z = z;
            this.F = s0.h();
            this.G = s0.h();
            this.A = s0.h();
            this.B = s0.h();
            this.C = s0.g();
            this.D = Uri.decode(s0.a());
        }

        b(boolean z, f.s s0, a s$a0) {
            this(z, s0);
        }

        @Override  // a.s
        protected int c(f.j.a j$a0) {
            if(this.z && this.F != 0 && this.G != 0) {
                int v = this.A * 60 + this.B;
                int v1 = j$a0.f * 60 + j$a0.g;
                int v2 = j.b(j$a0.b, j$a0.c);
                if((this.F & 1 << j$a0.c) != 0) {
                    int v3 = (this.G & 0x80000000) == 0 ? this.G : this.G | 1 << v2 - 1;
                    int v4 = j$a0.d;
                    if(v < v1) {
                        ++v4;
                    }
                    while(v4 < v2) {
                        if((1 << v4 & v3) != 0) {
                            return (v4 - j$a0.d) * 0x5A0 + v - v1;
                        }
                        ++v4;
                    }
                }
                int v5 = j$a0.b;
                int v6 = v2 * 0x5A0;
                for(int v7 = 1; v7 < 13; ++v7) {
                    int v8 = j$a0.c + v7;
                    if(v8 >= 12) {
                        v8 -= 12;
                        ++v5;
                    }
                    int v9 = j.b(v5, v8);
                    int v10 = (this.G & 0x80000000) == 0 ? this.G : this.G | 1 << v9 - 1;
                    if((1 << v8 & this.F) != 0) {
                        for(int v11 = 0; v11 < v9; ++v11) {
                            if((1 << v11 & v10) != 0) {
                                return v6 + (v11 - j$a0.d) * 0x5A0 + v - v1;
                            }
                        }
                    }
                    v6 += v9 * 0x5A0;
                }
            }
            return 0xFFFFFFF;
        }

        @Override  // a.s
        protected boolean e() {
            return this.z && (this.F & 2) != 0 && (this.G & 0x10000000) != 0;
        }

        @Override  // a.s
        protected void g(f.s s0) {
            s0.d(this.z | 2).d(this.F).d(this.G).d(this.A).d(this.B).f(this.C).c(Uri.encode(this.D));
        }

        @Override
        public String toString() {
            return "AsDate { enabled: " + this.z + ", " + Integer.toBinaryString(this.F) + ", " + Integer.toBinaryString(this.G) + ", " + this.A + ":" + this.B + ", " + this.C + ", " + this.D + " }";
        }
    }

    public static class c extends s {
        public int F;

        public c() {
            j j0 = new j();
            this.z = false;
            this.F = 0x7F;
            this.A = j0.g();
            this.B = j0.h();
            this.C = a.b.r();
            this.D = null;
        }

        private c(boolean z, f.s s0) {
            this.z = z;
            this.F = s0.h();
            this.A = s0.h();
            this.B = s0.h();
            this.C = s0.g();
            this.D = Uri.decode(s0.a());
        }

        c(boolean z, f.s s0, a s$a0) {
            this(z, s0);
        }

        @Override  // a.s
        protected int c(f.j.a j$a0) {
            if(this.z && this.F != 0) {
                int v = this.A * 60 + this.B;
                int v1 = j$a0.f * 60 + j$a0.g;
                for(int v2 = v < v1 ? 1 : 0; v2 < (v < v1 ? 8 : 7); ++v2) {
                    int v3 = j$a0.e + v2;
                    if((1 << (v3 < 7 ? j$a0.e + v2 : v3 - 7) & this.F) != 0) {
                        return v2 * 0x5A0 + v - v1;
                    }
                }
            }
            return 0xFFFFFFF;
        }

        @Override  // a.s
        protected boolean e() {
            return false;
        }

        @Override  // a.s
        protected void g(f.s s0) {
            s0.d(((int)this.z)).d(this.F).d(this.A).d(this.B).f(this.C).c(Uri.encode(this.D));
        }

        @Override
        public String toString() {
            return "AsWeek { enabled: " + this.z + ", " + Integer.toBinaryString(this.F) + ", " + this.A + ":" + this.B + ", " + this.C + ", " + this.D + " }";
        }
    }

    public static abstract class d extends r {
        protected final p B;
        ArrayList C;
        private static final String D;

        static {
            d.D = l.k + "ALARMS_CHANGED";
        }

        d() {
            super(d.D);
            this.B = new p(s.E);
            this.e();
        }

        d(ArrayList arrayList0) {
            super(d.D);
            this.B = new p(s.E);
            this.C = arrayList0;
        }

        @Override  // a.r
        protected void b(Intent intent0) {
            ArrayList arrayList0 = intent0.getParcelableArrayListExtra("data");
            if(arrayList0 != null) {
                this.C = arrayList0;
            }
        }

        public void e() {
            this.C = new ArrayList();
            try {
                if(this.B.E()) {
                    ArrayList arrayList0 = this.C;
                    for(s s0 = s.f(this.B); true; s0 = s.f(this.B)) {
                        arrayList0.add(s0);
                        if(!this.B.t()) {
                            break;
                        }
                        arrayList0 = this.C;
                    }
                }
                this.B.m();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    public static class e extends d implements f.a.c {
        public interface a.s.e.a {
            void a(a.b arg1);
        }

        private Context E;
        private Handler F;
        private BroadcastReceiver G;
        private String H;
        private String I;
        private a.s.e.a J;
        private f.a K;

        public e() {
            this.E = null;
            this.F = null;
            this.G = null;
            this.H = null;
            this.I = null;
            this.J = null;
            this.K = null;
        }

        @Override  // f.a$c
        public void a(f.a a0, long v) {
            this.h(v);
        }

        @Override  // a.s$d
        protected void b(Intent intent0) {
            ArrayList arrayList0 = this.C;
            super.b(intent0);
            if(arrayList0 != this.C) {
                this.n(true);
            }
        }

        @Override  // a.r
        public void d(Context context0, Handler handler0) {
            this.E = context0;
            this.F = handler0;
            super.d(context0, handler0);
            this.n(false);
        }

        @Override  // a.s$d
        public void e() {
            super.e();
            this.n(true);
        }

        private void f() {
            if(this.H == null) {
                try {
                    this.E.createPackageContext(l.j, 2);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
                this.H = "Next alarm: ";
                this.I = "No alarm.";
            }
        }

        private void g(f.j.a j$a0, int v) {
            if(this.J == null) {
                return;
            }
            if(v >= 0 && v <= 20) {
                for(Object object0: this.C) {
                    s s0 = (s)object0;
                    if(s0.c(j$a0) <= v) {
                        this.J.a(s0.C);
                    }
                }
            }
        }

        private void h(long v) {
            long v1 = System.currentTimeMillis() / 60000L * 60000L;
            this.g(new f.j.a(v), ((int)((v1 - v) / 60000L)));
            this.m(new f.j.a(v1 + 60000L), false);
        }

        private void i(long v, boolean z) {
            this.f();
            String s = this.k(v);
            if((this.K == null ? 0L : this.K.b()) == v) {
                if(z) {
                    Toast.makeText(this.E, this.H + s, 0).show();
                }
                return;
            }
            if(this.K == null) {
                this.K = f.a.c(this.E, this.F, this, 0);
            }
            this.K.d(v);
            String s1 = this.H + s;
            if(z) {
                Toast.makeText(this.E, s1, 0).show();
            }
        }

        public void j(a.s.e.a s$e$a0) {
            this.J = s$e$a0;
        }

        private String k(long v) {
            return DateFormat.getDateTimeInstance().format(new Date(v));
        }

        private void l(boolean z) {
            f.a a0 = this.K;
            if(a0 != null) {
                a0.a();
                this.K = null;
                if(z) {
                    this.f();
                    Toast.makeText(this.E, this.I, 0).show();
                }
            }
        }

        private void m(f.j.a j$a0, boolean z) {
            int v = 0xFFFFFFF;
            for(Object object0: this.C) {
                int v1 = ((s)object0).c(j$a0);
                if(v1 < v) {
                    v = v1;
                }
            }
            if(v == 0xFFFFFFF) {
                for(Object object1: this.C) {
                    s s0 = (s)object1;
                    if(s0.e()) {
                        this.i(j.l() + (((long)s0.A) * 60L + ((long)s0.B)) * 60000L, z);
                        return;
                    }
                    if(false) {
                        break;
                    }
                }
                this.l(z);
                return;
            }
            this.i(j$a0.a + ((long)v) * 60000L, z);
        }

        public void n(boolean z) {
            if(this.E != null) {
                try {
                    this.m(new f.j.a(System.currentTimeMillis() / 60000L * 60000L + 60000L), z);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }
    }

    public static class f extends d implements Parcelable {
        class a.s.f.a implements Parcelable.Creator {
            a.s.f.a() {
                super();
            }

            public f a(Parcel parcel0) {
                d0 d00 = new d0(parcel0);
                int v = d00.h();
                ArrayList arrayList0 = new ArrayList(v);
                for(int v1 = 0; v1 < v; ++v1) {
                    arrayList0.add(s.f(d00));
                }
                return new f(arrayList0, null);
            }

            public f[] b(int v) {
                return new f[v];
            }

            @Override  // android.os.Parcelable$Creator
            public Object createFromParcel(Parcel parcel0) {
                return this.a(parcel0);
            }

            @Override  // android.os.Parcelable$Creator
            public Object[] newArray(int v) {
                return this.b(v);
            }
        }

        public static final Parcelable.Creator CREATOR;
        private static f E;

        static {
            f.CREATOR = new a.s.f.a();
        }

        private f() {
        }

        private f(ArrayList arrayList0) {
            super(arrayList0);
        }

        f(ArrayList arrayList0, a s$a0) {
            this(arrayList0);
        }

        @Override  // android.os.Parcelable
        public int describeContents() {
            return 0;
        }

        public void f(s s0) {
            this.C.add(s0);
        }

        public s g(int v) {
            return (s)this.C.get(v);
        }

        public static f h() {
            synchronized(f.class) {
                if(f.E == null) {
                    f.E = new f();
                }
                return f.E;
            }
        }

        public void i(int v) {
            this.C.add(v + 1, ((s)this.C.remove(v)));
        }

        public void j(int v) {
            this.C.add(v - 1, ((s)this.C.remove(v)));
        }

        public static void k() {
            synchronized(f.class) {
                f.E = null;
            }
        }

        public void l(int v) {
            this.C.remove(v);
        }

        public void m(Context context0) {
            this.B.F();
            for(Object object0: this.C) {
                ((s)object0).g(this.B);
                this.B.C();
            }
            this.B.n();
            Intent intent0 = new Intent();
            intent0.putParcelableArrayListExtra("data", this.C);
            this.c(context0, intent0);
        }

        public int n() {
            return this.C.size();
        }

        @Override  // android.os.Parcelable
        public void writeToParcel(Parcel parcel0, int v) {
            d0 d00 = new d0(parcel0).j().m(this.C.size());
            for(Object object0: this.C) {
                ((s)object0).g(d00);
            }
        }
    }

    public int A;
    public int B;
    public a.b C;
    public static final Parcelable.Creator CREATOR;
    public String D;
    private static final String E;
    public boolean z;

    static {
        s.E = l.m + "alarms";
        s.CREATOR = new a();
    }

    public String b() {
        String s1;
        StringBuilder stringBuilder1;
        StringBuilder stringBuilder0 = new StringBuilder();
        int v = this.A;
        String s = v < 10 ? "0" + this.A : v;
        stringBuilder0.append(s);
        if(this.B < 10) {
            stringBuilder1 = new StringBuilder();
            s1 = ":0";
        }
        else {
            stringBuilder1 = new StringBuilder();
            s1 = ":";
        }
        stringBuilder1.append(s1);
        stringBuilder1.append(this.B);
        stringBuilder0.append(stringBuilder1.toString());
        return stringBuilder0.toString();
    }

    protected abstract int c(f.j.a arg1);

    public CharSequence d() {
        return this.D == null || this.D.length() <= 0 ? this.b() : this.D;
    }

    @Override  // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    protected abstract boolean e();

    static s f(f.s s0) {
        int v = s0.h();
        boolean z = (v & 1) != 0;
        return (v & 2) == 0 ? new c(z, s0, null) : new b(z, s0, null);
    }

    protected abstract void g(f.s arg1);

    @Override  // android.os.Parcelable
    public void writeToParcel(Parcel parcel0, int v) {
        this.g(new d0(parcel0).j());
    }
}


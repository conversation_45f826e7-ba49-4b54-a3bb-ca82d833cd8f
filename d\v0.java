package d;

import a.z;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.View.OnClickListener;
import android.view.View;
import e.j.g;
import e.j;
import e.m;
import e.o;
import java.util.ArrayList;

public class v0 extends j {
    private ArrayList M;
    private static final int[] N;

    static {
        v0.N = new int[]{0x7F0600ED, 0x7F06012D, 0x7F060159, 0x7F060158, 0x7F0600CD};  // string:edit "Edit"
    }

    @Override  // e.j0$c
    protected void B() {
        class a implements View.OnClickListener {
            final v0 z;

            @Override  // android.view.View$OnClickListener
            public void onClick(View view0) {
                if(v0.this.h().getBoolean("changed", false)) {
                    v0.this.I1();
                }
                v0.this.L();
            }
        }

        super.B();
        this.c0(0x7F06016E);  // string:pen_colors "Pen colors"
        this.S(0x7F04009B, new a(this));  // drawable:ic_save
    }

    @Override  // e.j
    protected int B0() {
        ArrayList arrayList0 = this.h().getIntegerArrayList("colors");
        this.M = arrayList0;
        if(arrayList0 == null) {
            z z0 = this.g().h();
            if(z0 != null) {
                int[] arr_v = z0.y();
                this.M = new ArrayList(arr_v.length);
                for(int v = 0; v < arr_v.length; ++v) {
                    this.M.add(((int)arr_v[v]));
                }
                return this.M.size() + 1;
            }
            this.M = new ArrayList();
        }
        return this.M.size() + 1;
    }

    protected void E1(int v, int v1) {
        this.M.add(v, v1);
        this.x0(v);
        this.h().putBoolean("changed", true);
    }

    protected void F1(int v, boolean z) {
        int v1 = z ? v - 1 : v + 1;
        this.M.add(v1, ((Integer)this.M.remove(v)));
        this.a1(v, v1);
        this.h().putBoolean("changed", true);
    }

    protected void G1(int v) {
        this.M.remove(v);
        this.u1(v);
        this.h().putBoolean("changed", true);
    }

    protected void H1(int v, int v1) {
        this.M.set(v, v1);
        g j$g0 = (g)this.L0(v);
        j$g0.setImageDrawable(new ColorDrawable(v1));
        j$g0.setText(o1.T1(v1));
        this.h().putBoolean("changed", true);
    }

    private void I1() {
        z z0 = this.g().h();
        if(z0 != null) {
            int v = this.M.size();
            int[] arr_v = new int[v];
            for(int v1 = 0; v1 < v; ++v1) {
                arr_v[v1] = (int)(((Integer)this.M.get(v1)));
            }
            if(z0.u0(this.M0(), arr_v)) {
                this.e0(0x7F060195);  // string:saved "Saved"
            }
        }
        this.h().putBoolean("changed", false);
    }

    @Override  // e.j0$c
    protected void J(Bundle bundle0, int v) {
        if(bundle0 == null) {
            return;
        }
        int v1 = this.E0();
        if(v == 1) {
            int v2 = bundle0.getInt("result", -1);
            if(v2 >= 0) {
                switch(v0.N[v2]) {
                    case 0x7F0600CD: {  // string:delete "Delete"
                        this.G1(v1);
                        return;
                    }
                    case 0x7F0600ED: {  // string:edit "Edit"
                        this.N(new o().G(((int)(((Integer)this.M.get(v1))))), 4);
                        return;
                    }
                    case 0x7F06012D: {  // string:insert "Insert"
                        this.N(new o(), 3);
                        return;
                    }
                    case 0x7F060158: {  // string:move_down "Move down"
                        if(v1 < this.M.size() - 1) {
                            this.F1(v1, false);
                            return;
                        }
                        break;
                    }
                    case 0x7F060159: {  // string:move_up "Move up"
                        if(v1 > 0) {
                            this.F1(v1, true);
                            return;
                        }
                        break;
                    }
                }
            }
        }
        else {
            switch(v) {
                case 2: {
                    this.E1(this.M.size(), bundle0.getInt("result"));
                    return;
                }
                case 3: {
                    this.E1(v1, bundle0.getInt("result"));
                    return;
                }
                case 4: {
                    if(bundle0.getInt("result", 0) != 0) {
                        this.H1(v1, bundle0.getInt("result"));
                        return;
                    }
                    this.e0(0x7F06012F);  // string:invalid_color "Invalid color"
                    return;
                }
            }
            if(v == 5) {
                if(bundle0.getBoolean("result", false)) {
                    this.I1();
                }
                this.L();
            }
        }
    }

    @Override  // e.j
    protected View h1(int v) {
        return v >= this.M.size() ? this.b1() : new g(this, new ColorDrawable(((int)(((Integer)this.M.get(v))))), o1.T1(((int)(((Integer)this.M.get(v))))), null);
    }

    @Override  // e.j
    protected void k1(int v) {
        int v2;
        o o0;
        if(v < this.M.size()) {
            CharSequence[] arr_charSequence = new CharSequence[v0.N.length];
            for(int v1 = 0; true; ++v1) {
                int[] arr_v = v0.N;
                if(v1 >= arr_v.length) {
                    break;
                }
                arr_charSequence[v1] = this.u(arr_v[v1]);
            }
            o0 = new e.z().u(arr_charSequence);
            v2 = 1;
        }
        else {
            o0 = new o();
            v2 = 2;
        }
        this.N(o0, v2);
    }

    @Override  // e.j
    public void onSaveInstanceState(Bundle bundle0) {
        this.h().putIntegerArrayList("colors", this.M);
        super.onSaveInstanceState(bundle0);
    }

    @Override  // e.j0$c
    protected void y() {
        if(this.h().getBoolean("changed", false)) {
            this.N(new m().u(this.u(0x7F06008F)), 5);  // string:check_save_message "Do you want to save changes?"
            return;
        }
        super.y();
    }
}


package d;

import a.b.s0;
import a.b;
import android.os.Bundle;
import android.view.View;
import com.jozein.xedgepro.ApplicationMain;
import e.j.d;
import e.j.g;
import e.j.i;
import e.j;
import e.m;
import f.l;
import f.o0;
import f.p0;
import f.z.e;
import f.z;
import java.lang.ref.WeakReference;
import java.util.List;

public class y extends j implements d {
    static class a implements e {
        final List a;
        private final z b;
        private WeakReference c;

        a(y y0, List list0) {
            this.a = list0;
            z z0 = z.K(y0.f());
            this.b = z0;
            z0.n0(this);
            this.g(y0);
        }

        @Override  // f.z$e
        public void a(f.c0.d c0$d0) {
            this.f(c0$d0);
        }

        @Override  // f.z$e
        public void b(String s, int v) {
            int v1 = y.I1(this.a, s, v);
            if(v1 >= 0) {
                this.a.remove(v1);
                y y0 = this.e();
                if(y0 != null) {
                    y0.u1(v1);
                }
            }
        }

        @Override  // f.z$e
        public void c(f.c0.d c0$d0) {
            this.f(c0$d0);
        }

        static a d(y y0, Object object0) {
            if(object0 instanceof a) {
                z.k(((a)object0).a);
                ((a)object0).g(y0);
                return (a)object0;
            }
            return null;
        }

        y e() {
            return this.c == null ? null : ((y)this.c.get());
        }

        void f(f.c0.d c0$d0) {
            y y0 = this.e();
            int v = this.a.indexOf(c0$d0);
            int v1 = z.i(this.a, c0$d0);
            if(v1 < 0) {
                v1 = ~v1;
            }
            if(v < 0) {
                this.a.add(v1, c0$d0);
                if(y0 != null) {
                    y0.x0(v1);
                }
            }
            else {
                if(v1 > v) {
                    --v1;
                }
                if(v == v1) {
                    this.a.set(v, c0$d0);
                    if(y0 != null) {
                        y0.Q0(v);
                    }
                }
                else {
                    this.a.remove(v);
                    this.a.add(v1, c0$d0);
                    if(y0 != null) {
                        y0.u1(v);
                        y0.x0(v1);
                    }
                }
            }
        }

        void g(y y0) {
            this.c = new WeakReference(y0);
        }

        void h() {
            this.b.n0(null);
        }
    }

    private a M;

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F060102);  // string:freezer "Freezer"
    }

    @Override  // e.j
    protected int B0() {
        if(this.M == null) {
            this.M = new a(this, z.K(this.f()).Q(true));
        }
        return this.M.a.size();
    }

    @Override  // e.j0$c
    protected void G() {
        super.G();
        this.H1();
    }

    private void H1() {
        a y$a0 = this.M;
        if(y$a0 != null) {
            y$a0.h();
            this.M = null;
        }
    }

    static int I1(List list0, String s, int v) {
        if(s != null) {
            int v1 = list0.size();
            for(int v2 = 0; v2 < v1; ++v2) {
                f.c0.d c0$d0 = (f.c0.d)list0.get(v2);
                if(s.equals(c0$d0.i()) && v == c0$d0.k()) {
                    return v2;
                }
            }
        }
        return -1;
    }

    @Override  // e.j0$c
    protected void J(Bundle bundle0, int v) {
        if(bundle0 == null) {
            return;
        }
        Bundle bundle1 = this.h();
        int v1 = y.I1(this.M.a, bundle1.getString("package"), bundle1.getInt("user_id", 0));
        if(v1 < 0) {
            v1 = this.E0();
        }
        f.c0.d c0$d0 = (f.c0.d)this.M.a.get(v1);
        switch(v) {
            case 0: {
                int v2 = bundle0.getInt("result");
                int[] arr_v = bundle1.getIntArray("item_ids");
                if(arr_v != null && v2 >= 0 && v2 < arr_v.length) {
                    this.L1(arr_v[v2], c0$d0);
                    return;
                }
                break;
            }
            case 1: {
                if(bundle0.getBoolean("result", false)) {
                    this.M1(c0$d0, false);
                    return;
                }
                break;
            }
        }
    }

    private static boolean J1(f.c0.d c0$d0) {
        if(!ApplicationMain.isModuleActivated()) {
            return false;
        }
        if(!c0$d0.m()) {
            return true;
        }
        String s = c0$d0.i();
        return (c0$d0.k() != 0 || !l.j.equals(s)) && !"android".equals(s) && !"com.android.systemui".equals(s);
    }

    protected g K1(int v) {
        f.c0.d c0$d0 = (f.c0.d)this.M.a.get(v);
        g j$g0 = new g(this, c0$d0.e(), c0$d0.f(), c0$d0.i());
        if(!c0$d0.m()) {
            j$g0.e();
        }
        return j$g0;
    }

    private void L1(int v, f.c0.d c0$d0) {
        switch(v) {
            case 0x7F060078: {  // string:app_info "App info"
                if(c0$d0.k() != o0.m() && ApplicationMain.isModuleActivated()) {
                    s0.E(this.f(), 29, c0$d0.i(), c0$d0.k());
                    return;
                }
                p0.u(this.f(), c0$d0.i());
                return;
            }
            case 0x7F060146: {  // string:launch "Launch"
                if(ApplicationMain.isModuleActivated()) {
                    b.t(this.f(), new a.b.l(c0$d0.i(), null, c0$d0.k()));
                    return;
                }
                if(c0$d0.m()) {
                    p0.t(this.f(), c0$d0.i());
                    return;
                }
                this.e0(0x7F060155);  // string:module_not_activated "Module not activated yet(or not updated)! Please activate 
                                      // this module in Xposed installer, and reboot."
                return;
            }
            case 0x7F060210: {  // string:unfreeze "Thaw"
                if(ApplicationMain.isModuleActivated()) {
                    this.M1(c0$d0, true);
                    return;
                }
                this.N(new m().u(this.u(0x7F060155)), 2);  // string:module_not_activated "Module not activated yet(or not updated)! Please activate 
                                                           // this module in Xposed installer, and reboot."
                return;
            }
            case 0x7F060223: {  // string:view_on_play_store "View on play store"
                p0.v(this.getActivity(), c0$d0.i());
            }
        }
    }

    private void M1(f.c0.d c0$d0, boolean z) {
        s0.E(this.f(), (z ? 2 : 1), c0$d0.i(), c0$d0.k());
    }

    @Override  // e.j$d
    public final String[] a() {
        return z.L(this.M.a);
    }

    @Override
    protected void finalize() {
        super.finalize();
        this.H1();
    }

    @Override  // e.j
    protected void g1(Object object0) {
        if(object0 instanceof List) {
            this.M = new a(this, ((List)object0));
        }
    }

    @Override  // e.j
    protected View h1(int v) {
        return this.K1(v);
    }

    @Override  // e.j
    protected void k1(int v) {
        f.c0.d c0$d0 = (f.c0.d)this.M.a.get(v);
        boolean z = y.J1(c0$d0);
        Bundle bundle0 = this.h();
        bundle0.putString("package", c0$d0.i());
        bundle0.putInt("user_id", c0$d0.k());
        int[] arr_v = z ? new int[]{(c0$d0.m() ? 0x7F060101 : 0x7F060210), 0x7F060146, 0x7F060223, 0x7F060078} : new int[]{0x7F060146, 0x7F060223, 0x7F060078};  // string:freeze "Freeze"
        bundle0.putIntArray("item_ids", arr_v);
        CharSequence[] arr_charSequence = new CharSequence[arr_v.length];
        for(int v1 = 0; v1 < arr_v.length; ++v1) {
            arr_charSequence[v1] = this.u(arr_v[v1]);
        }
        this.N(new e.z().u(arr_charSequence), 0);
    }

    @Override  // e.j
    protected final i m1() {
        a y$a0 = a.d(this, this.s());
        this.M = y$a0;
        return y$a0 != null ? null : new e.j.i.d(true);
    }

    @Override  // e.j
    public void onSaveInstanceState(Bundle bundle0) {
        a y$a0 = this.M;
        if(y$a0 != null) {
            this.Z(y$a0);
        }
        super.onSaveInstanceState(bundle0);
    }
}


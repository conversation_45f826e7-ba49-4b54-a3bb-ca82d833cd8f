package com.jozein.xedgepro.xposed;

import android.content.Intent;
import android.os.Build.VERSION;
import de.robv.android.xposed.XposedHelpers;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

class p3 {
    private static Field a;
    private static Field b;
    private static Method c;
    private static Method d;
    private static Field e;

    static {
    }

    static Intent a(Object object0) {
        if(Build.VERSION.SDK_INT >= 21) {
            if(p3.d == null) {
                p3.d = p2.b(object0.getClass(), "getBaseIntent", new Class[0]);
            }
            return (Intent)p3.d.invoke(object0);
        }
        Intent intent0 = (Intent)XposedHelpers.getObjectField(object0, "intent");
        return intent0 == null ? ((Intent)XposedHelpers.getObjectField(object0, "affinityIntent")) : intent0;
    }

    static int b(Object object0) {
        if(!v.f) {
            return 0;
        }
        if(Build.VERSION.SDK_INT >= 30) {
            if(p3.c == null) {
                p3.c = p2.b(object0.getClass(), "getDisplayId", new Class[0]);
            }
            int v = (int)(((Integer)p3.c.invoke(object0)));
            return v == -1 ? 0 : v;
        }
        return c.a(p3.c(object0));
    }

    static Object c(Object object0) {
        if(p3.e == null) {
            String s = Build.VERSION.SDK_INT < 26 ? "stack" : "mStack";
            p3.e = XposedHelpers.findField(object0.getClass(), s);
        }
        return p3.e.get(object0);
    }

    static int d(Object object0) {
        if(object0 != null) {
            try {
                if(p3.a == null) {
                    p3.a = XposedHelpers.findField(object0.getClass(), (Build.VERSION.SDK_INT <= 29 ? "taskId" : "mTaskId"));
                }
                return p3.a.getInt(object0);
            }
            catch(Throwable throwable0) {
                f.v.d(throwable0);
            }
        }
        return -1;
    }

    static int e(Object object0) {
        if(object0 != null) {
            try {
                if(p3.b == null) {
                    p3.b = XposedHelpers.findField(object0.getClass(), (Build.VERSION.SDK_INT <= 29 ? "userId" : "mUserId"));
                }
                return p3.b.getInt(object0);
            }
            catch(Throwable throwable0) {
                f.v.d(throwable0);
            }
        }
        return 0;
    }
}


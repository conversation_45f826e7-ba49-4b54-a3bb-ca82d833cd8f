package a;

import android.content.Context;
import android.os.Build.VERSION;
import f.l;
import f.v;

public class w {
    public static void a(z z0, Context context0) {
        m m0 = m.b();
        try {
            if(m0.a(0) == 0) {
                w.b(z0, context0);
                m0.f(0, 1);
            }
            int v = m0.a(2);
            if(v < 80) {
                w.c(z0, context0, m0, v);
                m0.f(2, 80);
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private static void b(z z0, Context context0) {
        z0.d0(context0, 24);
        z0.d0(context0, 25);
        z0.d0(context0, 4);
        z0.d0(context0, 3);
        z0.d0(context0, 0xBB);
        z0.d0(context0, 82);
        z0.d0(context0, 0x4F);
        z0.o0(context0, 4, true);
        z0.o0(context0, 6, true);
        z0.o0(context0, 7, true);
        z0.o0(context0, 8, true);
        z0.o0(context0, 24, true);
        z0.o0(context0, 27, true);
        z0.s0(context0, 7, 67);
        z0.u0(context0, new int[]{0xFFD01716, 0xFFFFA000, 0xFF0A7E07, 0xFF0288D1});
    }

    private static void c(z z0, Context context0, m m0, int v) {
        v.c(("New version: " + l.t));
        if(v < 0x20 && z0.u(24) == 0) {
            z0.s0(context0, 7, z0.u(7) | 0x40);
            z0.s0(context0, 24, 0xF000F);
        }
        if(v < 0x2F) {
            int v1 = z0.v(19, -1);
            z0.s0(context0, 30, v1);
            z0.s0(context0, 0x1F, v1);
            z0.s0(context0, 33, z0.v(2, 0xFF003037));
        }
        if(v < 56) {
            z0.s0(context0, 34, ((int)z0.o(5)));
            try {
                m0.f(4, 1);
                v.c("set style");
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            if(Build.VERSION.SDK_INT >= 29 && m0.a(1) == 0) {
                try {
                    m0.f(1, 4);
                    v.c("set auto dark mode.");
                }
                catch(Throwable throwable1) {
                    v.d(throwable1);
                }
            }
        }
    }
}


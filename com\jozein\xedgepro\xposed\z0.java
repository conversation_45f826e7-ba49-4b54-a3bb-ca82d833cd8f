package com.jozein.xedgepro.xposed;

import android.app.Application.OnProvideAssistDataListener;
import android.app.Application;
import de.robv.android.xposed.XC_MethodHook.MethodHookParam;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedHelpers;
import f.f0;
import f.o0;
import f.v;

class z0 extends p2 {
    z0(ClassLoader classLoader0) {
        class a extends XC_MethodHook {
            final z0 a;

            protected void beforeHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                try {
                    z0.this.h(((Application)xC_MethodHook$MethodHookParam0.thisObject));
                }
                catch(Throwable throwable0) {
                    p2.g(throwable0);
                }
            }
        }

        if(o0.m() != 0 || f0.u(classLoader0)) {
            return;
        }
        try {
            Object[] arr_object = {new a(this)};
            XposedHelpers.findAndHookMethod(Application.class, "onCreate", arr_object);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    void h(Application application0) {
        Application.OnProvideAssistDataListener application$OnProvideAssistDataListener0 = f0.p();
        if(application$OnProvideAssistDataListener0 != null) {
            application0.registerOnProvideAssistDataListener(application$OnProvideAssistDataListener0);
            application0.unregisterOnProvideAssistDataListener(application$OnProvideAssistDataListener0);
        }
    }
}


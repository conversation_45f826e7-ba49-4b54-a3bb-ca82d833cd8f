package com.jozein.xedgepro.xposed;

import android.os.Build.VERSION;
import android.os.Handler.Callback;
import android.os.Handler;
import android.os.Message;
import android.os.SystemClock;
import android.view.KeyEvent;
import android.view.inputmethod.InputMethodInfo;
import android.view.inputmethod.InputMethodManager;
import de.robv.android.xposed.XC_MethodHook.MethodHookParam;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedHelpers;
import f.v;
import java.util.List;

class p0 extends j2 {
    static class b extends NullPointerException {
    }

    private final w1 H;
    private boolean I;
    private Object J;
    private Object K;

    p0(ClassLoader classLoader0, w1 w10) {
        class a extends XC_MethodHook {
            final int a;
            final p0 b;

            a() {
                this.a = Build.VERSION.SDK_INT < 26 || Build.VERSION.SDK_INT > 28 ? 1 : 2;
            }

            protected void afterHookedMethod(XC_MethodHook.MethodHookParam xC_MethodHook$MethodHookParam0) {
                p0.this.A(xC_MethodHook$MethodHookParam0.thisObject);
                boolean z = (((int)(((Integer)xC_MethodHook$MethodHookParam0.args[this.a]))) & 2) != 0;
                if(z != p0.this.I) {
                    p0.this.I = z;
                    p0.this.H.n3(z);
                }
            }
        }

        super((Build.VERSION.SDK_INT < 29 ? "com.android.server.InputMethodManagerService" : "com.android.server.inputmethod.InputMethodManagerService"), classLoader0);
        this.I = false;
        this.K = null;
        this.H = w10;
        try {
            this.o("setImeWindowStatus", new a(this));
        }
        catch(Throwable throwable0) {
            p2.g(throwable0);
        }
    }

    private Object F() {
        Object object0 = this.u("mCurInputContext");
        if(object0 == null) {
            throw new b();
        }
        return object0;
    }

    private String G() {
        return Build.VERSION.SDK_INT < 33 ? ((String)this.u("mCurMethodId")) : ((String)this.r("getSelectedMethodIdLocked", new Object[0]));
    }

    private Object H() {
        if(this.K == null) {
            this.K = XposedHelpers.newInstance(XposedHelpers.findClass("com.android.internal.inputmethod.InputConnectionCommandHeader", this.m().getClassLoader()), new Object[]{0});
        }
        return this.K;
    }

    private Object I() {
        if(this.J == null) {
            Class class0 = Build.VERSION.SDK_INT >= 33 ? XposedHelpers.findClass("com.android.server.inputmethod.ImfLock", this.m().getClassLoader()) : this.u("mMethodMap");
            this.J = class0;
        }
        return this.J;
    }

    void J() {
        synchronized(this.I()) {
            if(Build.VERSION.SDK_INT >= 30) {
                this.r("hideCurrentInputLocked", new Object[]{this.u("mLastImeTargetWindow"), 0, null, 3});
            }
            else {
                this.r("hideCurrentInputLocked", new Object[]{0, null});
            }
        }
    }

    boolean K(int v) {
        try {
            Object object0 = this.F();
            long v1 = SystemClock.uptimeMillis();
            KeyEvent keyEvent0 = new KeyEvent(v1, v1, 0, v, 0, 0, -1, 0, 6);
            KeyEvent keyEvent1 = new KeyEvent(v1, SystemClock.uptimeMillis(), 1, v, 0, 0, -1, 0, 6);
            if(Build.VERSION.SDK_INT >= 33) {
                Object object1 = this.H();
                p2.a(object0, "sendKeyEvent", new Object[]{object1, keyEvent0});
                p2.a(object0, "sendKeyEvent", new Object[]{object1, keyEvent1});
                return true;
            }
            p2.a(object0, "sendKeyEvent", new Object[]{keyEvent0});
            p2.a(object0, "sendKeyEvent", new Object[]{keyEvent1});
            return true;
        }
        catch(b unused_ex) {
            return true;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return false;
        }
    }

    boolean L(String s) {
        if(s != null && s.length() != 0) {
            try {
                Object object0 = this.F();
                if(Build.VERSION.SDK_INT >= 33) {
                    Object object1 = this.H();
                    p2.a(object0, "finishComposingText", new Object[]{object1});
                    p2.a(object0, "commitText", new Object[]{object1, s, 1});
                    return true;
                }
                p2.a(object0, "finishComposingText", new Object[0]);
                p2.a(object0, "commitText", new Object[]{s, 1});
                return true;
            }
            catch(b throwable0) {
            }
            catch(Throwable unused_ex) {
                return true;
            }
            v.d(throwable0);
            return false;
        }
        return true;
    }

    // 检测为 Lambda 实现
    private void M() [...]

    boolean N(int v) {
        try {
            Object object0 = this.F();
            if(Build.VERSION.SDK_INT >= 33) {
                p2.a(object0, "performContextMenuAction", new Object[]{this.H(), v});
                return true;
            }
            p2.a(object0, "performContextMenuAction", new Object[]{v});
            return true;
        }
        catch(b unused_ex) {
            return true;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return false;
        }
    }

    private void O(String s, int v) {
        synchronized(this.I()) {
            try {
                this.r("setInputMethodLocked", new Object[]{s, v});
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
    }

    void P() {
        int v = Build.VERSION.SDK_INT;
        if(v >= 0x1F) {
            ((Handler)this.u("mHandler")).post(() -> try {
                XposedHelpers.callMethod(this.u("mMenuController"), "showInputMethodMenu", new Object[]{Boolean.TRUE, 0});
            }
            catch(Throwable throwable0) {
                v.c(throwable0.toString());
                Message message0 = new Message();
                message0.what = 1;
                message0.arg1 = 1;
                message0.arg2 = 0;
                try {
                    ((Handler.Callback)this.v()).handleMessage(message0);
                }
                catch(Throwable throwable1) {
                    v.d(throwable1);
                }
            });
            return;
        }
        if(v >= 29) {
            this.r("showInputMethodMenu", new Object[]{Boolean.TRUE, 0});
            return;
        }
        if(v >= 21) {
            this.r("showInputMethodMenu", new Object[]{Boolean.TRUE});
            return;
        }
        this.r("showInputMethodMenuInternal", new Object[]{Boolean.TRUE});
    }

    void Q() {
        synchronized(this.I()) {
            if(Build.VERSION.SDK_INT >= 30) {
                this.r("showCurrentInputLocked", new Object[]{this.u("mLastImeTargetWindow"), 0, null, 0});
            }
            else {
                this.r("showCurrentInputLocked", new Object[]{0, null});
            }
        }
    }

    void R() {
        Object object0;
        try {
            int v = Build.VERSION.SDK_INT;
            if(v >= 33) {
                object0 = this.r("getCurTokenLocked", new Object[0]);
            }
            else {
                object0 = v < 29 ? null : this.u("mCurToken");
            }
            if(((Boolean)this.r((v < 28 ? "switchToLastInputMethod" : "switchToPreviousInputMethod"), new Object[]{object0})).booleanValue()) {
                return;
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        InputMethodManager inputMethodManager0 = (InputMethodManager)this.H.T1().getSystemService("input_method");
        if(inputMethodManager0 == null) {
            return;
        }
        List list0 = inputMethodManager0.getEnabledInputMethodList();
        int v1 = list0.size();
        if(v1 > 1) {
            String s = this.G();
            String s1 = ((InputMethodInfo)list0.get(0)).getId();
            if(s1.equals(s)) {
                s1 = ((InputMethodInfo)list0.get(1)).getId();
            }
            this.O(s1, -1);
            return;
        }
        if(v1 <= 0) {
            v.c("No IME.");
        }
    }

    @Override  // com.jozein.xedgepro.xposed.j2
    protected Object x() {
        Object object0;
        try {
            if(Build.VERSION.SDK_INT >= 29) {
                object0 = XposedHelpers.getObjectField(g3.q("com.android.server.inputmethod.InputMethodManagerInternal"), "mService");
                return object0 == null ? g3.m() : object0;
            }
            object0 = this.H.g2().u("mInputMethodManager");
            return object0 == null ? g3.m() : object0;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            return g3.m();
        }
    }
}


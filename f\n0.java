package f;

import android.content.Context;
import android.graphics.SurfaceTexture;
import android.hardware.Camera.Parameters;
import android.hardware.Camera;
import android.hardware.camera2.CameraCharacteristics;
import android.hardware.camera2.CameraManager.TorchCallback;
import android.hardware.camera2.CameraManager;
import android.os.Build.VERSION;
import android.os.Handler;
import android.os.PowerManager.WakeLock;
import android.os.PowerManager;

public abstract class n0 implements l {
    static class a {
    }

    public interface b {
        void a();

        void b();
    }

    static class c extends n0 {
        private Camera B;

        private c(Handler handler0, Context context0, b n0$b0) {
            super(handler0, context0, n0$b0, null);
            this.B = null;
        }

        c(Handler handler0, Context context0, b n0$b0, a n0$a0) {
            this(handler0, context0, n0$b0);
        }

        @Override  // f.n0
        public boolean a() {
            return this.B != null;
        }

        @Override  // f.n0
        protected void f() {
            try {
                Camera camera0 = this.B;
                if(camera0 == null) {
                    return;
                }
                Camera.Parameters camera$Parameters0 = camera0.getParameters();
                camera$Parameters0.setFlashMode("off");
                this.B.setParameters(camera$Parameters0);
                this.B.stopPreview();
                this.c();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            this.B.release();
            this.B = null;
        }

        @Override  // f.n0
        protected boolean h() {
            if(this.B != null) {
                return true;
            }
            try {
                Camera camera0 = Camera.open();
                this.B = camera0;
                Camera.Parameters camera$Parameters0 = camera0.getParameters();
                camera$Parameters0.setFlashMode("torch");
                this.B.setParameters(camera$Parameters0);
                this.B.setPreviewTexture(new SurfaceTexture(0));
                this.B.startPreview();
                this.d(true);
                return true;
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                Camera camera1 = this.B;
                if(camera1 != null) {
                    camera1.release();
                    this.B = null;
                }
                return false;
            }
        }
    }

    class d implements Runnable {
        private final PowerManager.WakeLock A;
        final n0 B;
        protected final Handler z;

        d(Context context0, Handler handler0) {
            this.z = handler0;
            PowerManager.WakeLock powerManager$WakeLock0 = ((PowerManager)context0.getSystemService("power")).newWakeLock(1, l.x + ":torch");
            this.A = powerManager$WakeLock0;
            powerManager$WakeLock0.setReferenceCounted(false);
        }

        void a() {
            try {
                this.z.removeCallbacks(this);
                this.z.postDelayed(this, 1200000L);
                this.A.acquire(1200000L);
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        void b() {
            try {
                this.z.removeCallbacks(this);
                this.A.release();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        @Override
        public void run() {
            n0.this.e();
        }
    }

    static class e extends n0 {
        private final Context B;
        private final Handler C;
        private CameraManager D;
        private String E;
        private boolean F;
        private boolean G;
        private boolean H;

        private e(Handler handler0, Context context0, b n0$b0) {
            super(handler0, context0, n0$b0, null);
            this.E = null;
            this.F = false;
            this.G = false;
            this.H = false;
            this.B = context0;
            this.C = handler0;
            this.p();
        }

        e(Handler handler0, Context context0, b n0$b0, a n0$a0) {
            this(handler0, context0, n0$b0);
        }

        @Override  // f.n0
        public boolean a() {
            return this.G;
        }

        @Override  // f.n0
        protected void f() {
            String s = this.E;
            if(s == null) {
                return;
            }
            try {
                this.H = false;
                this.D.setTorchMode(s, false);
            }
            catch(Throwable unused_ex) {
            }
        }

        @Override  // f.n0
        protected boolean h() {
            if(this.E == null) {
                this.p();
            }
            String s = this.E;
            if(s != null) {
                try {
                    this.H = true;
                    this.D.setTorchMode(s, true);
                    return true;
                }
                catch(Throwable unused_ex) {
                    if(this.F) {
                        this.E = null;
                    }
                }
            }
            return false;
        }

        private static boolean o(CameraManager cameraManager0, String s) {
            if(s == null) {
                return false;
            }
            CameraCharacteristics cameraCharacteristics0 = cameraManager0.getCameraCharacteristics(s);
            Boolean boolean0 = (Boolean)cameraCharacteristics0.get(CameraCharacteristics.FLASH_INFO_AVAILABLE);
            Integer integer0 = (Integer)cameraCharacteristics0.get(CameraCharacteristics.LENS_FACING);
            return boolean0 != null && boolean0.booleanValue() && integer0 != null && ((int)integer0) == 1;
        }

        private void p() {
            class f.n0.e.a extends CameraManager.TorchCallback {
                final e a;

                @Override  // android.hardware.camera2.CameraManager$TorchCallback
                public void onTorchModeChanged(String s, boolean z) {
                    try {
                        if(e.this.E == null && z) {
                            e.this.E = s;
                        }
                        if(e.this.G != z && s.equals(e.this.E)) {
                            e.this.G = z;
                            if(z) {
                                e.this.d(e.this.H);
                            }
                            else {
                                e.this.c();
                            }
                            e.this.H = false;
                        }
                    }
                    catch(Throwable throwable0) {
                        v.d(throwable0);
                    }
                }
            }

            String[] arr_s;
            if(this.D == null) {
                CameraManager cameraManager0 = (CameraManager)this.B.getSystemService("camera");
                this.D = cameraManager0;
                if(cameraManager0 == null) {
                    return;
                }
                cameraManager0.registerTorchCallback(new f.n0.e.a(this), this.C);
            }
            try {
                arr_s = null;
                arr_s = this.D.getCameraIdList();
            label_9:
                for(int v = 0; v < arr_s.length; ++v) {
                    String s = arr_s[v];
                    if(s != null && e.o(this.D, s)) {
                        this.E = s;
                        return;
                    }
                }
            }
            catch(NullPointerException nullPointerException0) {
                v.c(nullPointerException0.toString());
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                if(true) {
                    goto label_22;
                }
                goto label_9;
            }
        label_22:
            if(arr_s != null && arr_s.length > 0) {
                this.E = arr_s[0];
                this.F = true;
            }
        }
    }

    private final b A;
    private final d z;

    private n0(Handler handler0, Context context0, b n0$b0) {
        this.z = new d(this, context0, handler0);
        this.A = n0$b0;
    }

    n0(Handler handler0, Context context0, b n0$b0, a n0$a0) {
        this(handler0, context0, n0$b0);
    }

    public abstract boolean a();

    public static n0 b(Handler handler0, Context context0, b n0$b0) {
        return Build.VERSION.SDK_INT < 23 ? new c(handler0, context0, n0$b0, null) : new e(handler0, context0, n0$b0, null);
    }

    void c() {
        this.z.b();
        b n0$b0 = this.A;
        if(n0$b0 != null) {
            n0$b0.a();
        }
    }

    void d(boolean z) {
        if(z) {
            this.z.a();
        }
        b n0$b0 = this.A;
        if(n0$b0 != null) {
            n0$b0.b();
        }
    }

    public final void e() {
        if(this.a()) {
            try {
                this.f();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }
        this.z.b();
    }

    protected abstract void f();

    public final boolean g() {
        if(this.a()) {
            this.z.a();
            return true;
        }
        try {
            return this.h();
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            this.e();
            return false;
        }
    }

    protected abstract boolean h();
}


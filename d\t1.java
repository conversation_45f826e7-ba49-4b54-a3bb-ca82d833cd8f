package d;

import a.s;
import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.CompoundButton;

public final class t1 implements CompoundButton.OnCheckedChangeListener {
    public final u1 a;
    public final s b;

    public t1(u1 u10, s s0) {
        this.a = u10;
        this.b = s0;
    }

    @Override  // android.widget.CompoundButton$OnCheckedChangeListener
    public final void onCheckedChanged(CompoundButton compoundButton0, boolean z) {
        this.a.K1(this.b, compoundButton0, z);
    }
}


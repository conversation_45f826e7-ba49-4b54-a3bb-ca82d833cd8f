package d;

import a.b.m0;
import a.b;
import android.appwidget.AppWidgetHost;
import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProviderInfo;
import android.os.Bundle;
import android.view.View;
import e.a0;
import e.m;
import e.z;
import f.v;
import java.util.ArrayList;

public class x extends e {
    private AppWidgetManager M;
    private a.x N;
    private ArrayList O;

    @Override  // e.j0$c
    protected void B() {
        super.B();
        this.c0(0x7F0601A7);  // string:select_floating_widget "Select floating widget"
        this.S(0x7F040002, (View view0) -> this.N(new a0().u(this.u(0x7F060021), this.u(0x7F060231)), 0));  // string:action_floating_widget "Floating widget"
    }

    @Override  // e.j
    protected int B0() {
        this.M = AppWidgetManager.getInstance(this.f());
        a.x x0 = a.x.h();
        this.N = x0;
        ArrayList arrayList0 = x0.i();
        this.O = arrayList0;
        for(int v = arrayList0.size() - 1; v >= 0; --v) {
            if(this.M.getAppWidgetInfo(((int)(((Integer)this.O.get(v))))) == null) {
                try {
                    this.N.m(((int)(((Integer)this.O.remove(v)))));
                }
                catch(Throwable throwable0) {
                    this.g0(throwable0);
                }
            }
        }
        return this.O.size() + 1;
    }

    @Override  // d.e
    protected void B1(Bundle bundle0, int v) {
        if(v != 1) {
            switch(v) {
                case 2: {
                    int v1 = bundle0.getInt("result", -1);
                    int v2 = this.E0();
                    m0 b$m00 = new m0(((int)(((Integer)this.O.get(this.E0())))));
                    switch(v1) {
                        case 0: {
                            this.C1(b$m00);
                            return;
                        }
                        case 1: {
                            b.t(this.f(), b$m00);
                            return;
                        }
                        case 2: {
                            try {
                                int v3 = (int)(((Integer)this.O.get(v2)));
                                new AppWidgetHost(this.f(), 1).deleteAppWidgetId(v3);
                                this.N.m(v3);
                                this.O.remove(v2);
                                this.u1(v2);
                            }
                            catch(Throwable throwable0) {
                                this.g0(throwable0);
                            }
                            return;
                        }
                        case 3: {
                            this.N(new m().u(this.u(0x7F06008D)), 3);  // string:check_clear_all "Are you sure to clear all?"
                            return;
                        }
                        default: {
                            return;
                        }
                    }
                }
                case 3: {
                    try {
                        new AppWidgetHost(this.f(), 1).deleteHost();
                        this.N.b();
                        this.O.clear();
                        this.w1();
                    }
                    catch(Throwable throwable0) {
                        this.g0(throwable0);
                    }
                    return;
                }
                default: {
                    return;
                }
            }
        }
        int v4 = bundle0.getInt("result", 0);
        if(v4 != 0) {
            AppWidgetProviderInfo appWidgetProviderInfo0 = this.M.getAppWidgetInfo(v4);
            if(appWidgetProviderInfo0 != null && !this.O.contains(v4)) {
                try {
                    this.N.a(v4, appWidgetProviderInfo0, this.f());
                    this.O.add(v4);
                    this.x0(this.O.size() - 1);
                }
                catch(Throwable throwable0) {
                    this.g0(throwable0);
                }
            }
        }
    }

    // 检测为 Lambda 实现
    private void F1(View view0) [...]

    @Override  // e.j
    protected View h1(int v) {
        if(v < this.O.size()) {
            int v1 = (int)(((Integer)this.O.get(v)));
            try {
                return this.f1(this.M.getAppWidgetInfo(v1));
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
                return this.f1(null);
            }
        }
        return this.b1();
    }

    @Override  // e.j
    protected void k1(int v) {
        if(v < this.O.size()) {
            this.C1(new m0(((int)(((Integer)this.O.get(v))))));
            return;
        }
        this.P(new f2().b0(this.o()), 1);
    }

    @Override  // e.j
    protected boolean l1(int v) {
        if(v < this.O.size()) {
            z z0 = new z();
            z0.u(new CharSequence[]{this.u(0x7F0601A1), this.u(0x7F06016F), this.u(0x7F0600CD), this.u(0x7F0600CE)});  // string:select "Select"
            this.N(z0, 2);
            return true;
        }
        return super.l1(v);
    }
}


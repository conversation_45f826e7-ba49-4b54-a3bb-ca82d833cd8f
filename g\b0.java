package g;

import a.x;
import android.appwidget.AppWidgetHost;
import android.appwidget.AppWidgetHostView;
import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProviderInfo;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint.Style;
import android.graphics.Paint;
import android.graphics.Point;
import android.graphics.RectF;
import android.os.Bundle;
import android.os.Handler;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup.LayoutParams;
import android.widget.FrameLayout;
import android.widget.Toast;
import f.h0;
import f.v;

public class b0 {
    static class a extends FrameLayout {
        private RectF A;
        private final float B;
        private Paint z;

        public a(Context context0) {
            super(context0);
            this.z = null;
            this.A = null;
            this.B = a0.m(context0) * 0.015f / 2.0f;
        }

        public void a(boolean z) {
            this.A = z ? new RectF(this.B, this.B, 0.0f, 0.0f) : null;
            this.setBackgroundColor((z ? 0x33FFA000 : 0));
        }

        @Override  // android.view.View
        protected void onDraw(Canvas canvas0) {
            if(this.A == null) {
                return;
            }
            float f = this.B * 2.0f;
            if(this.z == null) {
                Paint paint0 = new Paint(1);
                this.z = paint0;
                paint0.setStrokeWidth(f);
                this.z.setStyle(Paint.Style.STROKE);
                this.z.setColor(0xFFFFA000);
            }
            RectF rectF0 = this.A;
            rectF0.right = ((float)this.getRight()) - this.B;
            RectF rectF1 = this.A;
            rectF1.bottom = ((float)this.getBottom()) - this.B;
            canvas0.drawRoundRect(this.A, f, f, this.z);
        }
    }

    class b extends View {
        class g.b0.b.a implements Runnable {
            final b z;

            @Override
            public void run() {
                if(b.this.G != null && b.this.H != null) {
                    b.this.C.dispatchTouchEvent(b.this.G);
                    b.this.C.dispatchTouchEvent(b.this.H);
                }
                b.this.g();
                b.this.D = 0;
            }
        }

        private final long A;
        private final float B;
        private final AppWidgetHostView C;
        private int D;
        private float E;
        private float F;
        private MotionEvent G;
        private MotionEvent H;
        private int I;
        private final Runnable J;
        private final Runnable K;
        final b0 L;
        private final long z;

        public b(Context context0, AppWidgetHostView appWidgetHostView0) {
            super(context0);
            this.z = (long)ViewConfiguration.getLongPressTimeout();
            this.A = (long)ViewConfiguration.getDoubleTapTimeout();
            this.D = 0;
            this.E = 0.0f;
            this.F = 0.0f;
            this.G = null;
            this.H = null;
            this.I = -1;
            this.J = () -> {
                this.D = 1;
                this.setBackgroundColor(2030209233);
            };
            this.K = new g.b0.b.a(this);
            this.C = appWidgetHostView0;
            int v = ViewConfiguration.get(context0).getScaledTouchSlop();
            this.B = (float)(v * v);
        }

        private void g() {
            this.D = -1;
            MotionEvent motionEvent0 = this.G;
            if(motionEvent0 != null) {
                motionEvent0.recycle();
                this.G = null;
            }
            MotionEvent motionEvent1 = this.H;
            if(motionEvent1 != null) {
                motionEvent1.recycle();
                this.H = null;
            }
        }

        // 检测为 Lambda 实现
        private void h() [...]

        private void i(MotionEvent motionEvent0) {
            switch(this.D) {
                case 0: {
                    this.removeCallbacks(this.J);
                    break;
                }
                case 1: {
                    b0.this.l(0, 0);
                    this.setBackgroundColor(0);
                    break;
                }
                case 2: {
                    b0.this.o(0, 0);
                    if(b0.this.m != null) {
                        b0.this.m.a(false);
                    }
                    break;
                }
                case 3: {
                    this.C.dispatchTouchEvent(motionEvent0);
                }
            }
            this.g();
        }

        private void j(MotionEvent motionEvent0) {
            this.removeCallbacks(this.K);
            this.I = -1;
            this.E = motionEvent0.getRawX();
            this.F = motionEvent0.getRawY();
            MotionEvent motionEvent1 = this.G;
            if(motionEvent1 != null) {
                motionEvent1.recycle();
                this.G = null;
            }
            MotionEvent motionEvent2 = this.H;
            if(motionEvent2 == null) {
                this.G = MotionEvent.obtain(motionEvent0);
                this.D = 0;
                this.postDelayed(this.J, this.z);
                return;
            }
            motionEvent2.recycle();
            this.H = null;
            this.D = 2;
            if(b0.this.m != null) {
                b0.this.m.a(true);
            }
        }

        private void k(MotionEvent motionEvent0) {
            switch(this.D) {
                case 0: {
                    float f1 = motionEvent0.getRawX() - this.E;
                    float f2 = motionEvent0.getRawY() - this.F;
                    if(f1 * f1 + f2 * f2 > this.B) {
                        this.D = 3;
                        this.removeCallbacks(this.J);
                        MotionEvent motionEvent1 = this.G;
                        if(motionEvent1 != null) {
                            this.C.dispatchTouchEvent(motionEvent1);
                            this.G.recycle();
                            this.G = null;
                        }
                        this.C.dispatchTouchEvent(motionEvent0);
                        return;
                    }
                    break;
                }
                case 1: {
                    int v = (int)(motionEvent0.getRawX() - this.E);
                    float f = motionEvent0.getRawY();
                    b0.this.l(v, ((int)(f - this.F)));
                    return;
                }
                case 2: {
                    if(this.I == -1) {
                        float f3 = motionEvent0.getRawX() - this.E;
                        float f4 = motionEvent0.getRawY() - this.F;
                        float f5 = f3 * f3;
                        float f6 = f4 * f4;
                        if(f5 + f6 > this.B) {
                            this.I = f5 > f6 ? 1 : 0;
                            goto label_25;
                        }
                    }
                    else {
                    label_25:
                        int v1 = this.I;
                        if(v1 == 0) {
                            float f7 = motionEvent0.getRawY();
                            b0.this.o(0, ((int)(f7 - this.F)));
                            return;
                        }
                        if(v1 == 1) {
                            float f8 = motionEvent0.getRawX();
                            b0.this.o(((int)(f8 - this.E)), 0);
                            return;
                        }
                    }
                    break;
                }
                case 3: {
                    this.C.dispatchTouchEvent(motionEvent0);
                }
            }
        }

        private void l(MotionEvent motionEvent0) {
            switch(this.D) {
                case 0: {
                    this.removeCallbacks(this.J);
                    b0.this.j();
                    break;
                }
                case 1: 
                case 2: {
                    b0.this.j();
                    break;
                }
                case 3: {
                    this.C.dispatchTouchEvent(motionEvent0);
                }
            }
            this.g();
        }

        private void m(MotionEvent motionEvent0) {
            int v5;
            MotionEvent motionEvent1;
            int v = 0;
            this.removeCallbacks(this.J);
            int v1 = this.D;
            if(v1 == 0) {
                this.H = MotionEvent.obtain(motionEvent0);
                this.postDelayed(this.K, this.A);
            }
            else {
                switch(v1) {
                    case 1: {
                        int v2 = (int)(motionEvent0.getRawX() - this.E);
                        int v3 = (int)(motionEvent0.getRawY() - this.F);
                        b0.this.l(v2, v3);
                        b0.this.p(v2, v3);
                        this.setBackgroundColor(0);
                        motionEvent1 = this.G;
                        if(motionEvent1 != null) {
                            motionEvent1.recycle();
                            this.G = null;
                            return;
                        }
                        break;
                    }
                    case 2: {
                        if(b0.this.m != null) {
                            b0.this.m.a(false);
                        }
                        int v4 = this.I;
                        if(v4 != -1) {
                            if(v4 == 0) {
                                v5 = (int)(motionEvent0.getRawY() - this.F);
                            }
                            else {
                                v = (int)(motionEvent0.getRawX() - this.E);
                                v5 = 0;
                            }
                            b0.this.o(v, v5);
                            b0.this.q(this.C, v, v5);
                            ViewGroup.LayoutParams viewGroup$LayoutParams0 = this.getLayoutParams();
                            viewGroup$LayoutParams0.width = b0.this.n;
                            viewGroup$LayoutParams0.height = b0.this.o;
                            this.setLayoutParams(viewGroup$LayoutParams0);
                            ViewGroup.LayoutParams viewGroup$LayoutParams1 = this.C.getLayoutParams();
                            viewGroup$LayoutParams1.width = b0.this.n;
                            viewGroup$LayoutParams1.height = b0.this.o;
                            this.C.setLayoutParams(viewGroup$LayoutParams1);
                            motionEvent1 = this.G;
                            if(motionEvent1 != null) {
                                motionEvent1.recycle();
                                this.G = null;
                                return;
                            }
                        }
                        break;
                    }
                    case 3: {
                        this.C.dispatchTouchEvent(motionEvent0);
                    }
                }
            }
        }

        @Override  // android.view.View
        public boolean onTouchEvent(MotionEvent motionEvent0) {
            switch(motionEvent0.getActionMasked()) {
                case 0: {
                    b0.this.m();
                    this.j(motionEvent0);
                    return true;
                }
                case 1: {
                    this.m(motionEvent0);
                    b0.this.n();
                    return true;
                }
                case 2: {
                    this.k(motionEvent0);
                    return true;
                }
                case 3: {
                    this.i(motionEvent0);
                    b0.this.n();
                    return true;
                }
                case 5: {
                    this.l(motionEvent0);
                    return true;
                }
                case 6: {
                    break;
                }
                default: {
                    this.C.dispatchTouchEvent(motionEvent0);
                    return true;
                }
            }
            if(this.D == 3) {
                this.C.dispatchTouchEvent(motionEvent0);
                return true;
            }
            return true;
        }
    }

    private final Context a;
    private final Context b;
    private final m c;
    private final AppWidgetManager d;
    private final AppWidgetHost e;
    private final x f;
    private final int g;
    private final int h;
    private Runnable i;
    private int j;
    private long k;
    private final Handler l;
    private a m;
    private int n;
    private int o;
    private int p;
    private int q;

    public b0(Context context0) {
        this(context0, context0);
    }

    public b0(Context context0, Context context1) {
        this.i = null;
        this.j = 0;
        this.k = 0L;
        this.l = h0.a();
        this.m = null;
        this.n = 0;
        this.o = 0;
        this.p = 0;
        this.q = 0;
        this.a = context0;
        this.b = context1;
        this.c = new m(context0);
        this.d = AppWidgetManager.getInstance(context1);
        this.e = new AppWidgetHost(context1, 1);
        this.f = x.h();
        this.g = x.d(context0);
        this.h = a0.n(context0, new Point()).x;
    }

    public void j() {
        if(this.j != 0) {
            this.c.b();
            this.j = 0;
            this.e.stopListening();
            Runnable runnable0 = this.i;
            if(runnable0 != null) {
                runnable0.run();
            }
        }
        this.m = null;
    }

    public int k() {
        return this.j;
    }

    private void l(int v, int v1) {
        this.c.a(this.p + v, this.q + v1);
    }

    private void m() {
        Runnable runnable0 = this.i;
        if(runnable0 != null) {
            this.l.removeCallbacks(runnable0);
        }
    }

    private void n() {
        long v = this.k;
        if(v > 0L) {
            Runnable runnable0 = this.i;
            if(runnable0 != null) {
                this.l.postDelayed(runnable0, v);
            }
        }
    }

    private void o(int v, int v1) {
        int v2 = v + this.n;
        int v3 = v1 + this.o;
        int v4 = this.g;
        if(v2 < v4) {
            v2 = v4;
        }
        else {
            int v5 = this.h;
            if(v2 > v5) {
                v2 = v5;
            }
        }
        if(v3 >= v4) {
            v4 = this.h;
            if(v3 > v4) {
                v3 = v4;
            }
        }
        else {
            v3 = v4;
        }
        this.c.c(v2, v3);
    }

    private void p(int v, int v1) {
        try {
            int v2 = v + this.p;
            int v3 = v1 + this.q;
            this.f.o(this.j, v2, v3);
            this.p = v2;
            this.q = v3;
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    private void q(AppWidgetHostView appWidgetHostView0, int v, int v1) {
        try {
            int v2 = v + this.n;
            int v3 = v1 + this.o;
            int v4 = this.g;
            if(v2 < v4) {
                v2 = v4;
            }
            else {
                int v5 = this.h;
                if(v2 > v5) {
                    v2 = v5;
                }
            }
            if(v3 >= v4) {
                v4 = this.h;
                if(v3 > v4) {
                    v3 = v4;
                }
            }
            else {
                v3 = v4;
            }
            this.f.p(this.j, v2, v3);
            this.n = v2;
            this.o = v3;
            this.t(appWidgetHostView0, v2, v3);
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    public void r(Runnable runnable0) {
        this.i = runnable0;
    }

    public boolean s(int v, int v1) {
        AppWidgetProviderInfo appWidgetProviderInfo0 = this.d.getAppWidgetInfo(v);
        if(appWidgetProviderInfo0 != null) {
            a.x.a x$a0 = this.f.f(v);
            if(x$a0 != null) {
                int v2 = x$a0.d;
                int v3 = x$a0.e;
                if(v2 <= 1 || v3 <= 1) {
                    int v4 = appWidgetProviderInfo0.minWidth;
                    if(v4 > 1 && appWidgetProviderInfo0.minHeight > 1) {
                        v2 = x.c(this.a, v4);
                        v3 = x.c(this.a, appWidgetProviderInfo0.minHeight);
                        goto label_14;
                    }
                    v.c("Error read widget size!");
                    return false;
                }
            label_14:
                this.e.startListening();
                AppWidgetHostView appWidgetHostView0 = this.e.createView(this.b, v, appWidgetProviderInfo0);
                a b0$a0 = new a(this.a);
                b0$a0.addView(appWidgetHostView0, v2, v3);
                b0$a0.addView(new b(this, this.a, appWidgetHostView0), v2, v3);
                try {
                    this.c.d(b0$a0, v2, v3, x$a0.b, x$a0.c);
                    this.u(v, v2, v3);
                    this.j = v;
                    this.m = b0$a0;
                    this.n = v2;
                    this.o = v3;
                    this.p = x$a0.b;
                    this.q = x$a0.c;
                    this.k = ((long)v1) * 1000L;
                    this.n();
                    return true;
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                    Toast.makeText(this.b, throwable0.getMessage(), 0).show();
                    this.e.stopListening();
                    this.j = 0;
                    return false;
                }
            }
        }
        this.j = 0;
        v.c("Invalid widget!");
        Toast.makeText(this.b, 0x7F060131, 0).show();  // string:invalid_widget "Invalid widget!"
        return false;
    }

    private void t(AppWidgetHostView appWidgetHostView0, int v, int v1) {
        if(appWidgetHostView0 != null) {
            int v2 = a0.t(v);
            int v3 = a0.t(v1);
            appWidgetHostView0.updateAppWidgetSize(null, v2, v3, v2, v3);
        }
    }

    private void u(int v, int v1, int v2) {
        int v3 = a0.t(v1);
        int v4 = a0.t(v2);
        Bundle bundle0 = new Bundle();
        bundle0.putInt("appWidgetMinWidth", v3);
        bundle0.putInt("appWidgetMinHeight", v4);
        bundle0.putInt("appWidgetMaxWidth", v3);
        bundle0.putInt("appWidgetMaxHeight", v4);
        this.d.updateAppWidgetOptions(v, bundle0);
    }
}


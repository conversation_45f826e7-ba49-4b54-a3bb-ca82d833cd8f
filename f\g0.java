package f;

import android.os.Handler;

public abstract class g0 implements Runnable {
    private final int A;
    private final long B;
    private int C;
    private final Handler z;

    public g0(Handler handler0, int v, long v1) {
        this.C = 0;
        this.z = handler0;
        this.A = v;
        this.B = v1;
    }

    protected abstract boolean a(int arg1);

    protected abstract void b();

    private void c() {
        try {
            this.b();
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
        this.C = this.A;
    }

    public final void d() {
        this.C = 0;
        this.z.removeCallbacks(this);
        this.e();
    }

    public final void e() {
        if(this.C < this.A) {
            this.z.postDelayed(this, this.B);
        }
    }

    public final void f() {
        this.C = this.A;
        this.z.removeCallbacks(this);
    }

    @Override
    public void run() {
        try {
            if(this.a(this.C)) {
                int v = this.C + 1;
                this.C = v;
                if(v < this.A) {
                    this.z.postDelayed(this, this.B);
                    return;
                }
            }
            this.c();
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
            this.c();
        }
    }
}


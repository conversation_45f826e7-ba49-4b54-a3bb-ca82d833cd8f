package g;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.text.TextUtils.TruncateAt;
import android.view.MotionEvent;
import android.view.View.OnTouchListener;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.FrameLayout.LayoutParams;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;
import f.v;

public abstract class y extends ListView {
    class a extends BaseAdapter implements View.OnTouchListener, AdapterView.OnItemClickListener {
        private final int A;
        private final Context B;
        private float C;
        private float D;
        private boolean E;
        private boolean F;
        private long G;
        private float H;
        private final int[] I;
        private int J;
        private final Rect K;
        private b L;
        private int M;
        final y N;
        private final float z;

        a(Context context0) {
            this.C = 0.0f;
            this.D = 0.0f;
            this.E = false;
            this.F = true;
            this.G = 0L;
            this.H = 0.0f;
            this.I = new int[3];
            this.J = 0;
            this.K = new Rect();
            this.M = 0;
            this.B = context0;
            this.A = ViewConfiguration.get(context0).getScaledMinimumFlingVelocity() * 2;
            this.z = a0.m(context0) * 0.15f;
        }

        private void b() {
            b y$b0 = this.L;
            if(y$b0 != null) {
                y$b0.c(this.M);
                if(this.M == 0) {
                    this.L.b();
                    this.L = null;
                }
            }
        }

        private void c() {
            this.M = 0;
            b y$b0 = this.L;
            if(y$b0 != null) {
                y$b0.c(0);
                this.L.b();
                this.L = null;
            }
        }

        private void d(int v) {
            b y$b0 = this.L;
            if(y$b0 != null) {
                y$b0.c(v + this.M);
            }
        }

        private void e(int v) {
            y.this.v(v);
            this.notifyDataSetChanged();
        }

        private void f(int v, int v1, int v2) {
            b y$b0;
            Rect rect0 = this.K;
            for(int v3 = y.this.getChildCount() - 1; true; --v3) {
                y$b0 = null;
                if(v3 < 0) {
                    break;
                }
                View view0 = y.this.getChildAt(v3);
                view0.getHitRect(rect0);
                if(rect0.contains(v, v1)) {
                    y$b0 = (b)view0;
                    break;
                }
            }
            b y$b1 = this.L;
            if(y$b1 == null) {
                this.M = 0;
            }
            else if(y$b1 != y$b0) {
                this.c();
            }
            if(y$b0 != null) {
                y$b0.e();
                y$b0.c(v2 + this.M);
                this.L = y$b0;
            }
        }

        private void g(int v, int v1) {
            if(this.L == null) {
                return;
            }
            int v2 = v + this.M;
            int v3 = y.this.F * 3 / 4;
            int v4 = y.this.getWidth();
            int v5 = 0;
            if((v2 >= v3 || v2 <= -v3) && ((v1 <= this.A || v2 >= 0) && (v1 >= -this.A || v2 <= 0))) {
                if(v2 <= v4 / 2 && v2 >= -(v4 / 2)) {
                    if(v2 <= 0) {
                        if(y.this.D != null) {
                            v5 = -y.this.F;
                        }
                    }
                    else if(y.this.C != null) {
                        v5 = y.this.F;
                    }
                    this.M = v5;
                    this.L.c(v5);
                    if(this.M == 0) {
                        this.L.b();
                        this.L = null;
                        return;
                    }
                    return;
                }
                b y$b0 = this.L;
                this.L = null;
                y$b0.c(0);
                y$b0.b();
                this.e(y$b0.D);
            }
            else {
                this.L.c(0);
                this.L.b();
                this.L = null;
            }
            this.M = 0;
        }

        @Override  // android.widget.Adapter
        public int getCount() {
            return y.this.q(this.B);
        }

        @Override  // android.widget.Adapter
        public Object getItem(int v) {
            return null;
        }

        @Override  // android.widget.Adapter
        public long getItemId(int v) {
            return (long)v;
        }

        @Override  // android.widget.Adapter
        public View getView(int v, View view0, ViewGroup viewGroup0) {
            View view1 = view0 == null ? new b(y.this, this.B) : ((b)view0);
            try {
                ((b)view1).d(y.this.r(v), v, y.this.s(v));
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            return view1;
        }

        @Override  // android.widget.AdapterView$OnItemClickListener
        public void onItemClick(AdapterView adapterView0, View view0, int v, long v1) {
            try {
                if(view0 != this.L) {
                    y.this.u(v);
                }
                this.c();
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
        }

        @Override  // android.view.View$OnTouchListener
        public boolean onTouch(View view0, MotionEvent motionEvent0) {
            try {
                int v = motionEvent0.getActionMasked();
                if(v != 0) {
                    switch(v) {
                        case 1: {
                            b y$b0 = this.L;
                            if(y$b0 != null) {
                                y$b0.setPressed(false);
                            }
                            if(this.F && this.E) {
                                float f = motionEvent0.getX() - this.C;
                                int[] arr_v = this.I;
                                int v2 = 0;
                                for(int v1 = 0; v1 < arr_v.length; ++v1) {
                                    int v3 = arr_v[v1];
                                    if(v3 != 0x7FFFFFFF) {
                                        if(v2 != 0) {
                                            v3 = (v3 + v2) / 2;
                                        }
                                        v2 = v3;
                                    }
                                }
                                this.g(((int)f), v2);
                                return true;
                            }
                            return false;
                        }
                        case 3: {
                            if(this.F && this.E) {
                                this.b();
                                this.F = false;
                                this.E = false;
                            }
                            return false;
                        }
                        case 2: 
                        case 5: 
                        case 6: {
                            if(!this.F) {
                                return false;
                            }
                            float f1 = motionEvent0.getX();
                            float f2 = motionEvent0.getY();
                            float f3 = f1 - this.C;
                            float f4 = f2 - this.D;
                            if(!this.E) {
                                float f5 = this.z;
                                if(f4 <= f5 && f4 >= -f5) {
                                    if(f3 <= f5 && f3 >= -f5) {
                                        return false;
                                    }
                                    this.E = true;
                                    this.G = motionEvent0.getEventTime();
                                    this.H = f1;
                                    this.J = 0;
                                    this.I[0] = 0;
                                    this.f(((int)this.C), ((int)this.D), ((int)f3));
                                    return true;
                                }
                                this.F = false;
                                this.c();
                                return false;
                            }
                            int v4 = this.J + 1;
                            this.J = v4;
                            int[] arr_v1 = this.I;
                            if(v4 >= arr_v1.length) {
                                this.J = 0;
                            }
                            int v5 = this.J;
                            arr_v1[v5] = (int)((f1 - this.H) * 1000.0f / ((float)(motionEvent0.getEventTime() - this.G)));
                            this.G = motionEvent0.getEventTime();
                            this.H = f1;
                            this.d(((int)f3));
                            return true;
                        }
                        default: {
                            return false;
                        }
                    }
                }
                this.C = motionEvent0.getX();
                this.D = motionEvent0.getY();
                this.E = false;
                this.F = true;
                this.J = 0;
                for(int v6 = this.I.length - 1; v6 >= 0; --v6) {
                    this.I[v6] = 0x7FFFFFFF;
                }
            }
            catch(Throwable throwable0) {
                v.d(throwable0);
            }
            return false;
        }
    }

    class b extends FrameLayout {
        private final FrameLayout A;
        private FrameLayout B;
        private ImageView C;
        private int D;
        final y E;
        private final TextView z;

        public b(Context context0) {
            super(context0);
            this.B = null;
            this.C = null;
            TextView textView0 = new TextView(context0);
            this.z = textView0;
            textView0.setGravity(16);
            textView0.setSingleLine();
            textView0.setTextSize(0, ((float)y0.G));
            textView0.setEllipsize(TextUtils.TruncateAt.END);
            textView0.setTextColor(y0.A);
            FrameLayout frameLayout0 = new FrameLayout(context0);
            this.A = frameLayout0;
            frameLayout0.addView(textView0, y0.H);
            frameLayout0.setForeground(a0.l());
            frameLayout0.setBackgroundColor(y0.B);
            this.addView(frameLayout0, y0.I);
        }

        public void b() {
            this.setPressed(false);
            FrameLayout frameLayout0 = this.B;
            if(frameLayout0 != null) {
                frameLayout0.setVisibility(4);
            }
        }

        public void c(int v) {
            this.A.setLeft(v);
            int v1 = y.this.getWidth();
            this.A.setRight(v + v1);
        }

        public void d(CharSequence charSequence0, int v, boolean z) {
            this.D = v;
            this.z.setText(charSequence0);
            ImageView imageView0 = this.C;
            if(z) {
                if(imageView0 == null) {
                    ImageView imageView1 = new ImageView(this.getContext());
                    this.C = imageView1;
                    imageView1.setImageDrawable(y.this.E);
                    this.A.addView(this.C, y.this.L);
                }
                this.C.setVisibility(0);
                return;
            }
            if(imageView0 != null) {
                imageView0.setVisibility(4);
            }
        }

        public void e() {
            class g.y.b.a extends c {
                final b D;

                g.y.b.a(Context context0) {
                    super(context0);
                }

                @Override  // g.y$c
                void a() {
                    y.this.w(b.this.D);
                    y.this.M.c();
                }
            }


            class g.y.b.b extends c {
                final b D;

                g.y.b.b(Context context0) {
                    super(context0);
                }

                @Override  // g.y$c
                void a() {
                    y.this.x(b.this.D);
                    y.this.M.c();
                }
            }

            this.setPressed(true);
            FrameLayout frameLayout0 = this.B;
            if(frameLayout0 == null) {
                if(y.this.C == null && y.this.D == null) {
                    return;
                }
                Context context0 = this.getContext();
                this.B = new FrameLayout(context0);
                if(y.this.C != null) {
                    ImageView imageView0 = new ImageView(context0);
                    imageView0.setImageDrawable(new t(y.this.C, null, 0.5f));
                    imageView0.setOnTouchListener(new g.y.b.a(this, context0));
                    this.B.addView(imageView0, y.this.J);
                }
                if(y.this.D != null) {
                    ImageView imageView1 = new ImageView(context0);
                    imageView1.setImageDrawable(new t(y.this.D, null, 0.5f));
                    imageView1.setOnTouchListener(new g.y.b.b(this, context0));
                    this.B.addView(imageView1, y.this.K);
                }
                this.addView(this.B, 0, y.this.I);
                if(Build.VERSION.SDK_INT < 21) {
                    this.B.setBackgroundColor(y.this.z);
                }
            }
            else {
                frameLayout0.setVisibility(0);
            }
        }
    }

    static abstract class c implements View.OnTouchListener {
        private float A;
        private float B;
        private boolean C;
        private final float z;

        c(Context context0) {
            this.A = 0.0f;
            this.B = 0.0f;
            this.C = true;
            float f = a0.m(context0);
            this.z = f * f * 0.0225f;
        }

        abstract void a();

        @Override  // android.view.View$OnTouchListener
        public boolean onTouch(View view0, MotionEvent motionEvent0) {
            switch(motionEvent0.getActionMasked()) {
                case 0: {
                    this.A = motionEvent0.getX();
                    this.B = motionEvent0.getY();
                    this.C = true;
                    return true;
                }
                case 1: {
                    if(this.C && y.h(this.A, this.B, motionEvent0.getX(), motionEvent0.getY()) < this.z) {
                        try {
                            this.a();
                        }
                        catch(Throwable throwable0) {
                            v.d(throwable0);
                        }
                        return true;
                    }
                    break;
                }
                case 2: {
                    if(this.C && y.h(this.A, this.B, motionEvent0.getX(), motionEvent0.getY()) > this.z) {
                        this.C = false;
                        return true;
                    }
                    break;
                }
                case 3: 
                case 5: {
                    this.C = false;
                    return true;
                }
                default: {
                    return true;
                }
            }
            return true;
        }
    }

    private final int A;
    private final int B;
    private final Drawable C;
    private final Drawable D;
    private final Drawable E;
    private final int F;
    private final int G;
    private final FrameLayout.LayoutParams H;
    private final FrameLayout.LayoutParams I;
    private final FrameLayout.LayoutParams J;
    private final FrameLayout.LayoutParams K;
    private final FrameLayout.LayoutParams L;
    private final a M;
    private final int z;

    public y(Context context0, Drawable drawable0, Drawable drawable1, Drawable drawable2, int v, int v1) {
        super(context0);
        int v7;
        this.C = drawable0;
        this.D = drawable1;
        this.E = drawable2;
        this.F = v;
        this.G = (int)(0.3f * ((float)v));
        int v2 = (int)(0.275f * ((float)v));
        int v3 = (int)(((float)v) * 0.225f);
        FrameLayout.LayoutParams frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(-1, -2, 16);
        this.H = frameLayout$LayoutParams0;
        this.I = new FrameLayout.LayoutParams(-1, v);
        this.J = new FrameLayout.LayoutParams(v, v, 0x800003);
        this.K = new FrameLayout.LayoutParams(v, v, 0x800005);
        FrameLayout.LayoutParams frameLayout$LayoutParams1 = new FrameLayout.LayoutParams(v2, v2, 0x800035);
        this.L = frameLayout$LayoutParams1;
        frameLayout$LayoutParams0.rightMargin = v3;
        frameLayout$LayoutParams0.leftMargin = v3;
        frameLayout$LayoutParams1.rightMargin = v3 / 2;
        frameLayout$LayoutParams1.topMargin = v3 / 2;
        frameLayout$LayoutParams1.leftMargin = v3 / 2;
        this.z = v1 | 0xFF000000;
        int v4 = Color.red(v1 | 0xFF000000);
        int v5 = Color.green(v1 | 0xFF000000);
        int v6 = Color.blue(v1 | 0xFF000000);
        h h0 = h.a();
        if(a0.r(v1 | 0xFF000000)) {
            this.A = -1;
            h0.b(drawable0);
            h0.b(drawable1);
            h0.b(drawable2);
            v7 = 6;
        }
        else {
            this.A = 0xFF000000;
            h0.d(drawable0);
            h0.d(drawable1);
            h0.d(drawable2);
            v7 = -6;
        }
        this.B = a0.d(0xFF, v4, v5, v6, v7);
        a y$a0 = new a(this, context0);
        this.M = y$a0;
        this.setAdapter(y$a0);
        this.setOnItemClickListener(y$a0);
        this.setOnTouchListener(y$a0);
        this.setDividerHeight(0);
        this.setBackgroundColor(v1 | 0xFF000000);
        this.setSelector(new ColorDrawable(0));
    }

    public final int getItemColor() {
        return this.B;
    }

    static float h(float f, float f1, float f2, float f3) {
        return (f2 - f) * (f2 - f) + (f3 - f1) * (f3 - f1);
    }

    @Override  // android.view.View
    public final boolean hasWindowFocus() {
        return true;
    }

    private static float p(float f, float f1, float f2, float f3) [...] // Inlined contents

    protected abstract int q(Context arg1);

    protected abstract CharSequence r(int arg1);

    protected abstract boolean s(int arg1);

    public void t() {
        this.M.c();
        this.M.notifyDataSetChanged();
    }

    protected abstract void u(int arg1);

    protected abstract void v(int arg1);

    protected abstract void w(int arg1);

    protected abstract void x(int arg1);
}


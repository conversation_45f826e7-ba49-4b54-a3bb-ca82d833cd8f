package a;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import com.jozein.xedgepro.ApplicationMain;
import f.v;

public abstract class y {
    private Context a;
    private BroadcastReceiver b;

    public y(Context context0) {
        class a extends BroadcastReceiver {
            final y a;

            @Override  // android.content.BroadcastReceiver
            public void onReceive(Context context0, Intent intent0) {
                y.this.b();
                try {
                    String[] arr_s = intent0.getStringArrayExtra("list");
                    y.this.a(arr_s);
                }
                catch(Throwable throwable0) {
                    v.d(throwable0);
                }
            }
        }

        this.b = null;
        this.a = context0;
        if(!ApplicationMain.isModuleActivated()) {
            this.a(null);
            return;
        }
        a y$a0 = new a(this);
        this.b = y$a0;
        this.a.registerReceiver(y$a0, new IntentFilter(s0.M));
        s0.B(this.a, 9);
    }

    protected abstract void a(String[] arg1);

    public void b() {
        BroadcastReceiver broadcastReceiver0 = this.b;
        if(broadcastReceiver0 != null) {
            this.a.unregisterReceiver(broadcastReceiver0);
            this.a = null;
            this.b = null;
        }
    }
}


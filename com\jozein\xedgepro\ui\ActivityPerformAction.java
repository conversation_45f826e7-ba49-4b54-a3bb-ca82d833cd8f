package com.jozein.xedgepro.ui;

import a.b.e;
import a.b.f;
import a.b.g;
import a.b.h;
import a.b.l;
import a.b.m;
import a.b.s2;
import a.b;
import a.j;
import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import com.jozein.xedgepro.ApplicationMain;
import com.jozein.xedgepro.service.BinderService;
import f.l0;
import f.o0;
import f.v;

public class ActivityPerformAction extends Activity {
    public static void a(Context context0, b b0) {
        ActivityPerformAction.b(context0, b0, 0L);
    }

    public static void b(Context context0, b b0, long v) {
        if(!ApplicationMain.isModuleActivated()) {
            try {
                switch(b0.z) {
                    case 0x2F: {
                        ActivityPerformAction.e(context0, ((m)b0).K);
                        return;
                    }
                    case 0x30: {
                        ActivityPerformAction.d(context0, ((e)b0).K, ((e)b0).L);
                        return;
                    }
                    case 76: {
                        ActivityPerformAction.c(context0, ((g)b0).K);
                        goto label_30;
                    }
                    case 78: {
                        ActivityPerformAction.c(context0, ((s2)b0).K);
                        return;
                    }
                    case 0x71: {
                        int v1 = o0.m();
                        if(((f)b0).M == v1) {
                            ActivityPerformAction.d(context0, ((f)b0).K, ((f)b0).L);
                        }
                        return;
                    }
                    case 0x72: {
                        int v2 = o0.m();
                        if(((h)b0).L == v2) {
                            ActivityPerformAction.c(context0, ((h)b0).K);
                        }
                        return;
                    }
                    case 0x73: {
                        l b$l0 = (l)b0;
                        try {
                            String s = b$l0.L;
                            if(s != null) {
                                ActivityPerformAction.d(context0, b$l0.K, s);
                                return;
                            }
                            ActivityPerformAction.e(context0, b$l0.K);
                        }
                        catch(Throwable unused_ex) {
                            ActivityPerformAction.e(context0, b$l0.K);
                        }
                        return;
                    }
                    default: {
                        goto label_30;
                    }
                }
            }
            catch(ActivityNotFoundException throwable0) {
            }
            catch(SecurityException | Throwable unused_ex) {
                goto label_30;
            }
            v.d(throwable0);
        }
    label_30:
        if(v <= 0L) {
            b.t(context0, b0);
            return;
        }
        b.v(context0, b0, v);
    }

    public static void c(Context context0, Intent intent0) {
        intent0.addFlags(0x30000000);
        context0.startActivity(intent0);
    }

    public static void d(Context context0, String s, String s1) {
        Intent intent0 = new Intent();
        intent0.setComponent(new ComponentName(s, s1));
        ActivityPerformAction.c(context0, intent0);
    }

    public static void e(Context context0, String s) {
        Intent intent0 = context0.getPackageManager().getLaunchIntentForPackage(s);
        if(intent0 != null) {
            ActivityPerformAction.c(context0, intent0);
            return;
        }
        v.c(("Main activity not found: " + s));
    }

    @Override  // android.app.Activity
    protected void onCreate(Bundle bundle0) {
        super.onCreate(bundle0);
        Intent intent0 = this.getIntent();
        try {
            if(!f.l.r) {
                String s = intent0.getAction();
                if(j.z.equals(s)) {
                    j.c(this.getApplicationContext());
                    return;
                }
            }
            if(!BinderService.u(this, intent0)) {
                b b0 = l0.j(intent0);
                if(b0 != null && b0.z != 0) {
                    ActivityPerformAction.b(this.getApplicationContext(), b0, 200L);
                    return;
                }
                v.c(("No data for action found in " + intent0.toUri(0)));
            }
        }
        catch(Throwable throwable0) {
            v.d(throwable0);
        }
    }

    @Override  // android.app.Activity
    protected void onResume() {
        super.onResume();
        this.finish();
    }
}


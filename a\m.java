package a;

import f.l;
import f.p;

public class m implements l {
    private final int[] A;
    private static m B;
    private final p z;

    static {
    }

    private m() {
        this.z = new p(l.p);
        this.A = new int[6];
        this.d();
    }

    public int a(int v) {
        return this.A[v];
    }

    public static m b() {
        synchronized(m.class) {
            if(m.B == null) {
                m.B = new m();
            }
            return m.B;
        }
    }

    public static void c() {
        synchronized(m.class) {
            m.B = null;
        }
    }

    public void d() {
        if(this.z.E()) {
            for(int v = 0; v < 6; ++v) {
                this.A[v] = this.z.h();
            }
        }
        this.z.m();
    }

    private void e() {
        this.z.F();
        for(int v = 0; v < 6; ++v) {
            this.z.w(this.A[v]);
        }
        this.z.n();
    }

    public void f(int v, int v1) {
        int[] arr_v = this.A;
        if(arr_v[v] != v1) {
            arr_v[v] = v1;
            this.e();
        }
    }
}


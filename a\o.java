package a;

import android.net.Uri;
import f.p0;
import f.s;

public class o {
    public final String a;
    public final b[] b;
    private static final b[] c;

    static {
        o.c = new b[0];
    }

    public o(s s0) {
        this.a = Uri.decode(s0.a());
        int v = s0.h();
        if(v > 0) {
            this.b = new b[v];
            for(int v1 = 0; v1 < v; ++v1) {
                this.b[v1] = s0.g();
            }
            return;
        }
        this.b = o.c;
    }

    public o(String s, b[] arr_b) {
        if(s == null) {
            s = p0.f();
        }
        this.a = s;
        if(arr_b == null) {
            arr_b = o.c;
        }
        this.b = arr_b;
    }

    public void a(s s0) {
        s0.c(Uri.encode(this.a)).d(this.b.length);
        b[] arr_b = this.b;
        for(int v = 0; v < arr_b.length; ++v) {
            arr_b[v].y(s0);
        }
    }
}


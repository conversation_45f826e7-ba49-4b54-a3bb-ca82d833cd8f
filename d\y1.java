package d;

import android.content.ContentUris;
import android.content.Context;
import android.database.Cursor;
import android.media.Ringtone;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Bundle;
import android.util.Pair;
import android.view.View.OnClickListener;
import android.view.View;
import e.j.g;
import e.j.k;
import e.z;
import java.util.ArrayList;

public class y1 extends e {
    private ArrayList M;
    private ArrayList N;
    private ArrayList O;
    private Ringtone P;

    public y1() {
        this.M = null;
        this.N = null;
        this.O = null;
        this.P = null;
    }

    @Override  // e.j
    protected int B0() {
        this.d0(this.u(0x7F060043));  // string:action_play_sound "Play sound"
        return 4;
    }

    @Override  // d.e
    protected void B1(Bundle bundle0, int v) {
        boolean z = true;
        if(v == 1) {
            int v1 = bundle0.getInt("result", -1);
            if(v1 == 0 || v1 == 1) {
                ArrayList arrayList0 = this.F1(this.I0());
                String s = this.h().getString("uri");
                if(arrayList0 != null && s != null) {
                    int v2 = y1.G1(this.I0());
                    if(v1 != 1) {
                        z = false;
                    }
                    this.C1(new a.b.y1(s, v2, z));
                }
            }
        }
    }

    private ArrayList F1(int v) {
        if(v != 0) {
            switch(v) {
                case 1: {
                    if(this.N == null) {
                        this.N = this.H1(2);
                    }
                    return this.N;
                }
                case 2: {
                    if(this.O == null) {
                        this.O = this.H1(4);
                    }
                    return this.O;
                }
                default: {
                    return null;
                }
            }
        }
        if(this.M == null) {
            this.M = this.H1(1);
        }
        return this.M;
    }

    private static int G1(int v) {
        switch(v) {
            case 0: {
                return 2;
            }
            case 2: {
                return 4;
            }
            default: {
                return 5;
            }
        }
    }

    private ArrayList H1(int v) {
        Context context0 = this.M0();
        ArrayList arrayList0 = new ArrayList();
        arrayList0.add(new Pair(this.u(0x7F0601F1), RingtoneManager.getDefaultUri(v)));  // string:system_default "System default"
        RingtoneManager ringtoneManager0 = new RingtoneManager(context0);
        ringtoneManager0.setType(v);
        Cursor cursor0 = ringtoneManager0.getCursor();
        while(cursor0.moveToNext()) {
            arrayList0.add(new Pair(cursor0.getString(1), ContentUris.withAppendedId(Uri.parse(cursor0.getString(2)), cursor0.getLong(0))));
        }
        cursor0.close();
        return arrayList0;
    }

    private void I1(int v) {
        ArrayList arrayList0 = this.F1(this.I0());
        if(arrayList0 != null) {
            this.h().putString("uri", ((Uri)((Pair)arrayList0.get(v)).second).toString());
            this.N(new z().u(new CharSequence[]{this.u(0x7F06017A), this.u(0x7F060183)}), 1);  // string:play_once "Once"
        }
    }

    @Override  // e.j
    protected boolean N0(int v) {
        return v < 3;
    }

    @Override  // e.j
    protected View h1(int v) {
        switch(v) {
            case 0: {
                return new k(this, this.u(0x7F060189));  // string:ringtone "Ringtone"
            }
            case 1: {
                return new k(this, this.u(0x7F060161));  // string:notification "Notification"
            }
            case 2: {
                return new k(this, this.u(0x7F060073));  // string:alarm "Alarm"
            }
            default: {
                return new k(this, this.u(0x7F0601E8));  // string:stop_play_sound "Stop play sound"
            }
        }
    }

    @Override  // e.j
    protected View i1(int v) {
        class a implements View.OnClickListener {
            final y1 A;
            final int z;

            a(int v) {
                this.z = v;
                super();
            }

            @Override  // android.view.View$OnClickListener
            public void onClick(View view0) {
                y1.this.I1(this.z);
            }
        }

        Context context0 = this.M0();
        ArrayList arrayList0 = this.F1(this.I0());
        if(arrayList0 != null) {
            View view0 = new g(this, ((CharSequence)((Pair)arrayList0.get(v)).first), null, this.C0(0x7F0400A4));  // drawable:ic_select
            a y1$a0 = new a(this, v);
            view0.E.setOnClickListener(y1$a0);
            return view0;
        }
        return new View(context0);
    }

    @Override  // e.j
    protected void k1(int v) {
        ArrayList arrayList0 = this.F1(v);
        if(arrayList0 != null) {
            this.y1(v, arrayList0.size());
            return;
        }
        this.C1(new a.b.y1("", 0, false));
    }

    @Override  // e.j
    protected void o1(int v) {
        ArrayList arrayList0 = this.F1(this.I0());
        if(arrayList0 != null) {
            Ringtone ringtone0 = this.P;
            if(ringtone0 != null) {
                ringtone0.stop();
            }
            Ringtone ringtone1 = RingtoneManager.getRingtone(this.M0(), ((Uri)((Pair)arrayList0.get(v)).second));
            this.P = ringtone1;
            if(ringtone1 != null) {
                ringtone1.setStreamType(y1.G1(this.I0()));
                this.P.play();
            }
        }
    }

    @Override  // e.j
    public void onDestroy() {
        super.onDestroy();
        Ringtone ringtone0 = this.P;
        if(ringtone0 != null) {
            ringtone0.stop();
            this.P = null;
        }
    }

    @Override  // e.j
    protected boolean p1(int v) {
        this.I1(v);
        return true;
    }
}

